{"version": 3, "file": "session-handler.js", "sourceRoot": "", "sources": ["session-handler.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAElC,2BAA4B;AAC5B,2DAAiF;AACjF,+BAA+B;AAG/B,mDAAwI;AAExI,IAAI,OAAgB,CAAC;AAGrB,MAAM,WAAW,GAAG,CAAC,QAAoD,EAAU,EAAE;IACnF,QAAQ,QAAQ,EAAE;QAChB,KAAK,SAAS;YACZ,OAAO,CAAC,CAAC;QACX,KAAK,MAAM;YACT,OAAO,CAAC,CAAC;QACX,KAAK,SAAS;YACZ,OAAO,CAAC,CAAC;QACX,KAAK,OAAO;YACV,OAAO,CAAC,CAAC;QACX,KAAK,OAAO;YACV,OAAO,CAAC,CAAC;QACX;YACE,MAAM,IAAI,KAAK,CAAC,8BAA8B,QAAQ,EAAE,CAAC,CAAC;KAC7D;AACH,CAAC,CAAC;AAGF,MAAa,oCAAoC;IAM/C,KAAK,CAAC,qBAAqB,CAAC,IAAY;QACtC,kFAAkF;QAClF,mCAAmC;QACnC,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC;QACnC,MAAM,WAAW,GAAG,MAAM,QAAQ,CAAC,WAAW,EAAE,CAAC;QACjD,OAAO,IAAA,qCAAqB,EAAC,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC;IAC5D,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,YAA+B,EAAE,OAAyC;QACxF,IAAI,CAAC,OAAO,EAAE;YACZ,MAAM,IAAA,uBAAO,EAAC,wBAAG,CAAC,IAAI,CAAC,UAAW,EAAE,WAAW,CAAC,wBAAG,CAAC,QAAS,CAAC,CAAC,CAAC;YAChE,OAAO,GAAG,IAAI,CAAC;SAChB;QAED,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;YACpC,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;gBAChC,OAAO;gBACP,MAAM,KAAK,GAAG,MAAM,IAAA,gBAAS,EAAC,aAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;gBACtD,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,MAAM,IAAA,6BAAa,EAAC,KAAK,EAAE,OAAO,CAAC,CAAC;aAC3F;iBAAM;gBACL,UAAU;gBACV,qCAAqC;gBACrC,MAAM,SAAS,GAA0B,MAAM,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC;gBACxF,qBAAqB;gBACrB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,MAAM,IAAA,qCAAqB,EAAC,SAAS,EAAE,OAAO,CAAC,CAAC;aACvG;SACF;aAAM;YACL,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,MAAM,IAAA,6BAAa,EAAC,YAAY,EAAE,OAAO,CAAC,CAAC;SAClG;IACH,CAAC;IAED,KAAK,CAAC,OAAO;QACX,OAAO,IAAA,8BAAc,EAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACxC,CAAC;IAED,KAAK,CAAC,GAAG,CAAC,KAA+B,EAAE,OAAmC,EAAE,OAAoC;QAElH,MAAM,UAAU,GAAa,EAAE,CAAC;QAChC,MAAM,YAAY,GAAa,EAAE,CAAC;QAClC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YAClC,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;YACpB,MAAM,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;YACtB,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC5C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;gBAChB,MAAM,IAAI,KAAK,CAAC,kBAAkB,IAAI,GAAG,CAAC,CAAC;aAC5C;YACD,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACxB,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,MAAM,aAAa,GAAa,EAAE,CAAC;QACnC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACpC,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;YACpB,qCAAqC;YACrC,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC7C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;gBAChB,MAAM,IAAI,KAAK,CAAC,mBAAmB,IAAI,GAAG,CAAC,CAAC;aAC7C;YACD,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,MAAM,OAAO,GACT,MAAM,IAAA,mBAAG,EAAC,IAAI,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;QAEnH,MAAM,MAAM,GAA8B,EAAE,CAAC;QAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACvC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,2BAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACtG;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,cAAc;QACZ,4BAA4B;IAC9B,CAAC;IAED,YAAY;QACV,KAAK,IAAA,4BAAY,EAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;CACF;AApFD,oFAoFC"}