{"version": 3, "file": "texture-layout.js", "sourceRoot": "", "sources": ["texture-layout.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAElC,qCAAqC;AAGrC,mCAAmD;AAE5C,MAAM,kCAAkC,GAC3C,CAAC,qBAA4C,EAAE,KAAwB,EACtE,WAAwB,EAAiB,EAAE;IAC1C,MAAM,OAAO,GAAG,CAAC,WAAW,KAAK,mBAAW,CAAC,QAAQ,IAAI,WAAW,KAAK,mBAAW,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/G,MAAM,QAAQ,GAAG,WAAW,KAAK,mBAAW,CAAC,MAAM,CAAC;IACpD,MAAM,SAAS,GAAG,CAAC,WAAW,KAAK,mBAAW,CAAC,gBAAgB,IAAI,WAAW,KAAK,mBAAW,CAAC,MAAM,CAAC,CAAC;IACvG,MAAM,SAAS,GAAG,WAAW,KAAK,mBAAW,CAAC,mBAAmB,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IACjG,MAAM,aAAa,GAAG,WAAW,KAAK,mBAAW,CAAC,mBAAmB,CAAC,CAAC;QACnE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,SAAS,CAAC;IACd,OAAO,IAAA,oCAA4B,EAC/B,qBAAqB,EAAE,KAAK,EAAE,OAAO,EAAE,aAAa,EAAE,EAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAC,CAAC,CAAC;AAC9F,CAAC,CAAC;AAZO,QAAA,kCAAkC,sCAYzC;AAEC,MAAM,8BAA8B,GACvC,CAAC,qBAA4C,EAAE,KAAwB,EAAE,WAAwB,EAC5E,EAAE;IACjB,MAAM,MAAM,GAAG,IAAA,0CAAkC,EAAC,qBAAqB,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;IAC7F,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;AACvC,CAAC,CAAC;AALG,QAAA,8BAA8B,kCAKjC;AAEV;;GAEG;AACI,MAAM,4BAA4B,GACrC,CAAC,qBAA4C,EAAE,KAAwB,EAAE,WAAgB,CAAC,EACzF,aAAiC,EAAE,KAAwB,EAAiB,EAAE;IAC7E,MAAM,QAAQ,GAAG,CAAC,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC7C,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,qBAAqB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,aAAa,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IACjH,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC;IAC1B,IAAI,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAClC,IAAI,IAAI,KAAK,CAAC,EAAE;QACd,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;KACpB;IACD,IAAI,QAAQ,KAAK,CAAC,EAAE;QAClB,oGAAoG;QACpG,aAAa,GAAG,KAAK,CAAC;KACvB;SAAM,IAAI,QAAQ,EAAE;QACnB,IAAI,QAAQ,KAAK,CAAC,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;SACvD;QACD,aAAa,GAAG,KAAK,CAAC;QACtB,IAAI,IAAI,GAAG,CAAC,EAAE;YACZ,YAAY,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;SAChE;QACD,IAAI,IAAI,GAAG,CAAC,EAAE;YACZ,YAAY,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;SAChE;KACF;SAAM,IAAI,CAAC,aAAa,EAAE;QACzB,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;KACrE;IACD,OAAO;QACL,KAAK;QACL,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,KAAK,EAAE,YAAY;QACnB,OAAO,EAAE,gBAAS,CAAC,cAAc,CAAC,YAAY,CAAC;QAC/C,aAAa;QACb,UAAU,EAAE,CAAC,KAAK,IAAI,KAAK,CAAC,SAAS,CAAC;KACvC,CAAC;AACJ,CAAC,CAAC;AArCO,QAAA,4BAA4B,gCAqCnC"}