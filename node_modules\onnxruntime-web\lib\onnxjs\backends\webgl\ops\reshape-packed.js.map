{"version": 3, "file": "reshape-packed.js", "sourceRoot": "", "sources": ["reshape-packed.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAGlC,wCAAwC;AACxC,gDAAuC;AAEvC,oCAAsF;AAEtF,mDAAkD;AAElD,MAAM,oCAAoC,GAAG,CAAC,aAAgC,EAAE,EAAE,CAC9E,CAAC,EAAC,IAAI,EAAE,kBAAkB,EAAE,UAAU,EAAE,CAAC,mBAAW,CAAC,MAAM,CAAC,EAAE,UAAU,EAAE,CAAC,GAAG,CAAC,EAAE,SAAS,EAAE,GAAG,aAAa,EAAE,EAAC,CAAC,CAAC;AAErH,MAAM,gCAAgC,GAClC,CAAC,OAA8B,EAAE,OAAe,EAAE,QAAyB,EAAE,aAAgC,EAC7F,EAAE;IACZ,MAAM,YAAY,GAAG,OAAO,CAAC,IAAgC,CAAC;IAC9D,MAAM,mBAAmB,GAAG,aAAyC,CAAC;IAEtE,IAAI,QAAQ,GAAG,EAAE,CAAC;IAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QAC1B,IAAI,YAAY,GAAG,EAAE,CAAC;QACtB,QAAQ,CAAC,EAAE;YACT,KAAK,CAAC;gBACJ,YAAY,GAAG,oBAAoB,CAAC;gBACpC,MAAM;YACR,KAAK,CAAC;gBACJ,YAAY,GAAG,2CAA2C,CAAC;gBAC3D,MAAM;YACR,KAAK,CAAC;gBACJ,YAAY,GAAG,2CAA2C,CAAC;gBAC3D,MAAM;YACR,KAAK,CAAC;gBACJ,YAAY,GAAG,6CAA6C,CAAC;gBAC7D,MAAM;YACR;gBACE,MAAM,IAAI,KAAK,EAAE,CAAC;SACrB;QAED,QAAQ,IAAI;UACd,YAAY;UACZ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,qDAAqD,CAAC,CAAC,CAAC,EAAE;;;;;;mBAMzD,CAAC;;UAEV,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;OACnB,CAAC;KACG;IACD,MAAM,IAAI,GAAG,IAAA,qBAAO,EAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;IAEhE,MAAM,YAAY,GAAG;QACvB,sBAAsB,CAAC,YAAY,CAAC;QACpC,uBAAuB,CAAC,mBAAmB,CAAC;QAC5C,IAAA,iCAAiB,GAAE;;;;;;;;qBAQN,mBAAmB,CAAC,CAAC,CAAC;qBACtB,mBAAmB,CAAC,CAAC,CAAC;;UAEjC,QAAQ;UACR,IAAI,CAAC,MAAM;;KAEhB,CAAC;IAEI,uCACK,QAAQ,KACX,MAAM,EAAE,EAAC,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,WAAW,EAAE,mBAAW,CAAC,MAAM,EAAC,EACxF,YAAY,EACZ,OAAO,EAAE,IAAI,IACb;AACJ,CAAC,CAAC;AAEH,MAAM,sCAAsC,GAC/C,CAAC,OAA8B,EAAE,OAAe,EAAE,aAAgC,EAAqB,EAAE;IACvG,MAAM,QAAQ,GAAG,oCAAoC,CAAC,aAAa,CAAC,CAAC;IACrE,uCAAW,QAAQ,KAAE,GAAG,EAAE,GAAG,EAAE,CAAC,gCAAgC,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,aAAa,CAAC,IAAE;AAC/G,CAAC,CAAC;AAJO,QAAA,sCAAsC,0CAI7C;AAEN,SAAgB,aAAa,CAAC,KAAwB;IACpD,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;QACtB,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;KAClB;IACD,wCAAwC;IACxC,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;QACzC,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;KACnB;IACD,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;AAC1F,CAAC;AAVD,sCAUC;AAED,yEAAyE;AACzE,wEAAwE;AACxE,yEAAyE;AACzE,uEAAuE;AACvE,0EAA0E;AAC1E,wCAAwC;AACxC,wEAAwE;AACxE,8EAA8E;AAC9E,0EAA0E;AAC1E,2EAA2E;AAC3E,oBAAoB;AACpB,SAAgB,cAAc,CAAC,IAAuB,EAAE,YAA+B;IACrF,IAAI,cAAc,GAAG,KAAK,CAAC;IAC3B,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,EAAG,SAAS;QAC9D,cAAc,GAAG,IAAI,CAAC;KACvB;SAAM,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,EAAG,KAAK;QAC7D,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;KAClF;SAAM,EAAG,OAAO;QACf,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;YAC5E,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;KACrE;IAED,OAAO,cAAc,CAAC;AACxB,CAAC;AAZD,wCAYC;AAED,SAAS,sBAAsB,CAAC,KAA+B;IAC7D,MAAM,OAAO,GAAG,gBAAS,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;IAChD,MAAM,MAAM,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAC/B,MAAM,KAAK,GAAG,OAAO,CAAC;IACtB,MAAM,sBAAsB,GAAG,OAAO;SACF,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACjB,MAAM,KAAK,GAAG,OAAO,MAAM,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,MAAM,EAAE,CAAC;QACxD,MAAM,KAAK,GAAG,CAAC,KAAK,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACpC,OAAO,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,CAAC,MAAM,MAAM,EAAE,CAAC,CAAC;YAC9D,YAAY,MAAM,CAAC,CAAC,CAAC,MAAM,MAAM,EAAE,CAAC;QACxC,OAAO,GAAG,KAAK,KAAK,KAAK,GAAG,CAAC;IAC/B,CAAC,CAAC;SACD,IAAI,CAAC,EAAE,CAAC,CAAC;IAE7C,OAAO;;QAED,sBAAsB;;;GAG3B,CAAC;AACJ,CAAC;AAED,SAAS,uBAAuB,CAAC,KAA+B;IAC9D,MAAM,OAAO,GAAG,gBAAS,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;IAEhD,OAAO;;;wBAGe,OAAO,CAAC,CAAC,CAAC,iBAAiB,OAAO,CAAC,CAAC,CAAC;;CAE5D,CAAC;AACF,CAAC"}