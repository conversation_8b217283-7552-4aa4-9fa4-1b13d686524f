import { OperatorImplementation, OperatorInitialization } from '../../../operators';
import { Tensor } from '../../../tensor';
import { WebGLInferenceHandler } from '../inference-handler';
export declare const squeeze: OperatorImplementation<number[]>;
export declare const squeezeV13: (inferenceHandler: WebGLInferenceHandler, inputs: Tensor[]) => Tensor[];
export declare const parseSqueezeAttributes: OperatorInitialization<number[]>;
