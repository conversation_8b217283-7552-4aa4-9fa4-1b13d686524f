import { OperatorImplementation, OperatorInitialization } from '../../../operators';
import { ConvAttributes } from './conv';
export interface ConvTransposeAttributes extends ConvAttributes {
    readonly outputPadding: readonly number[];
    readonly outputShape: readonly number[];
}
export declare const convTranspose: OperatorImplementation<ConvTransposeAttributes>;
export declare const parseConvTransposeAttributes: OperatorInitialization<ConvTransposeAttributes>;
