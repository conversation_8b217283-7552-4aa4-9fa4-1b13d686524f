"use strict";
// automatically generated by the FlatBuffers compiler, do not modify
/* eslint-disable */
Object.defineProperty(exports, "__esModule", { value: true });
exports.onnxruntime = void 0;
const flatbuffers_1 = require("flatbuffers");
/**
 * @enum {number}
 */
var onnxruntime;
(function (onnxruntime) {
    var experimental;
    (function (experimental) {
        var fbs;
        (function (fbs) {
            let AttributeType;
            (function (AttributeType) {
                AttributeType[AttributeType["UNDEFINED"] = 0] = "UNDEFINED";
                AttributeType[AttributeType["FLOAT"] = 1] = "FLOAT";
                AttributeType[AttributeType["INT"] = 2] = "INT";
                AttributeType[AttributeType["STRING"] = 3] = "STRING";
                AttributeType[AttributeType["TENSOR"] = 4] = "TENSOR";
                AttributeType[AttributeType["GRAPH"] = 5] = "GRAPH";
                AttributeType[AttributeType["FLOATS"] = 6] = "FLOATS";
                AttributeType[AttributeType["INTS"] = 7] = "INTS";
                AttributeType[AttributeType["STRINGS"] = 8] = "STRINGS";
                AttributeType[AttributeType["TENSORS"] = 9] = "TENSORS";
                AttributeType[AttributeType["GRAPHS"] = 10] = "GRAPHS";
                AttributeType[AttributeType["SPARSE_TENSOR"] = 11] = "SPARSE_TENSOR";
                AttributeType[AttributeType["SPARSE_TENSORS"] = 12] = "SPARSE_TENSORS";
            })(AttributeType = fbs.AttributeType || (fbs.AttributeType = {}));
        })(fbs = experimental.fbs || (experimental.fbs = {}));
    })(experimental = onnxruntime.experimental || (onnxruntime.experimental = {}));
})(onnxruntime = exports.onnxruntime || (exports.onnxruntime = {}));
/**
 * @enum {number}
 */
(function (onnxruntime) {
    var experimental;
    (function (experimental) {
        var fbs;
        (function (fbs) {
            let DimensionValueType;
            (function (DimensionValueType) {
                DimensionValueType[DimensionValueType["UNKNOWN"] = 0] = "UNKNOWN";
                DimensionValueType[DimensionValueType["VALUE"] = 1] = "VALUE";
                DimensionValueType[DimensionValueType["PARAM"] = 2] = "PARAM";
            })(DimensionValueType = fbs.DimensionValueType || (fbs.DimensionValueType = {}));
        })(fbs = experimental.fbs || (experimental.fbs = {}));
    })(experimental = onnxruntime.experimental || (onnxruntime.experimental = {}));
})(onnxruntime = exports.onnxruntime || (exports.onnxruntime = {}));
/**
 * @enum {number}
 */
(function (onnxruntime) {
    var experimental;
    (function (experimental) {
        var fbs;
        (function (fbs) {
            let TensorDataType;
            (function (TensorDataType) {
                TensorDataType[TensorDataType["UNDEFINED"] = 0] = "UNDEFINED";
                TensorDataType[TensorDataType["FLOAT"] = 1] = "FLOAT";
                TensorDataType[TensorDataType["UINT8"] = 2] = "UINT8";
                TensorDataType[TensorDataType["INT8"] = 3] = "INT8";
                TensorDataType[TensorDataType["UINT16"] = 4] = "UINT16";
                TensorDataType[TensorDataType["INT16"] = 5] = "INT16";
                TensorDataType[TensorDataType["INT32"] = 6] = "INT32";
                TensorDataType[TensorDataType["INT64"] = 7] = "INT64";
                TensorDataType[TensorDataType["STRING"] = 8] = "STRING";
                TensorDataType[TensorDataType["BOOL"] = 9] = "BOOL";
                TensorDataType[TensorDataType["FLOAT16"] = 10] = "FLOAT16";
                TensorDataType[TensorDataType["DOUBLE"] = 11] = "DOUBLE";
                TensorDataType[TensorDataType["UINT32"] = 12] = "UINT32";
                TensorDataType[TensorDataType["UINT64"] = 13] = "UINT64";
                TensorDataType[TensorDataType["COMPLEX64"] = 14] = "COMPLEX64";
                TensorDataType[TensorDataType["COMPLEX128"] = 15] = "COMPLEX128";
                TensorDataType[TensorDataType["BFLOAT16"] = 16] = "BFLOAT16";
            })(TensorDataType = fbs.TensorDataType || (fbs.TensorDataType = {}));
        })(fbs = experimental.fbs || (experimental.fbs = {}));
    })(experimental = onnxruntime.experimental || (onnxruntime.experimental = {}));
})(onnxruntime = exports.onnxruntime || (exports.onnxruntime = {}));
/**
 * @enum {number}
 */
(function (onnxruntime) {
    var experimental;
    (function (experimental) {
        var fbs;
        (function (fbs) {
            let NodeType;
            (function (NodeType) {
                NodeType[NodeType["Primitive"] = 0] = "Primitive";
                NodeType[NodeType["Fused"] = 1] = "Fused";
            })(NodeType = fbs.NodeType || (fbs.NodeType = {}));
        })(fbs = experimental.fbs || (experimental.fbs = {}));
    })(experimental = onnxruntime.experimental || (onnxruntime.experimental = {}));
})(onnxruntime = exports.onnxruntime || (exports.onnxruntime = {}));
/**
 * @enum {number}
 */
(function (onnxruntime) {
    var experimental;
    (function (experimental) {
        var fbs;
        (function (fbs) {
            let TypeInfoValue;
            (function (TypeInfoValue) {
                TypeInfoValue[TypeInfoValue["NONE"] = 0] = "NONE";
                TypeInfoValue[TypeInfoValue["tensor_type"] = 1] = "tensor_type";
                TypeInfoValue[TypeInfoValue["sequence_type"] = 2] = "sequence_type";
                TypeInfoValue[TypeInfoValue["map_type"] = 3] = "map_type";
            })(TypeInfoValue = fbs.TypeInfoValue || (fbs.TypeInfoValue = {}));
        })(fbs = experimental.fbs || (experimental.fbs = {}));
    })(experimental = onnxruntime.experimental || (onnxruntime.experimental = {}));
})(onnxruntime = exports.onnxruntime || (exports.onnxruntime = {}));
/**
 * @constructor
 */
(function (onnxruntime) {
    var experimental;
    (function (experimental) {
        var fbs;
        (function (fbs) {
            class Shape {
                constructor() {
                    this.bb = null;
                    this.bb_pos = 0;
                }
                /**
                 * @param number i
                 * @param flatbuffers.ByteBuffer bb
                 * @returns Shape
                 */
                __init(i, bb) {
                    this.bb_pos = i;
                    this.bb = bb;
                    return this;
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param Shape= obj
                 * @returns Shape
                 */
                static getRootAsShape(bb, obj) {
                    return (obj || new Shape()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param Shape= obj
                 * @returns Shape
                 */
                static getSizePrefixedRootAsShape(bb, obj) {
                    bb.setPosition(bb.position() + flatbuffers_1.flatbuffers.SIZE_PREFIX_LENGTH);
                    return (obj || new Shape()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                /**
                 * @param number index
                 * @param onnxruntime.experimental.fbs.Dimension= obj
                 * @returns onnxruntime.experimental.fbs.Dimension
                 */
                dim(index, obj) {
                    let offset = this.bb.__offset(this.bb_pos, 4);
                    return offset ? (obj || new onnxruntime.experimental.fbs.Dimension())
                        .__init(this.bb.__indirect(this.bb.__vector(this.bb_pos + offset) + index * 4), this.bb) :
                        null;
                }
                /**
                 * @returns number
                 */
                dimLength() {
                    let offset = this.bb.__offset(this.bb_pos, 4);
                    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
                }
                /**
                 * @param flatbuffers.Builder builder
                 */
                static startShape(builder) {
                    builder.startObject(1);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset dimOffset
                 */
                static addDim(builder, dimOffset) {
                    builder.addFieldOffset(0, dimOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param Array.<flatbuffers.Offset> data
                 * @returns flatbuffers.Offset
                 */
                static createDimVector(builder, data) {
                    builder.startVector(4, data.length, 4);
                    for (let i = data.length - 1; i >= 0; i--) {
                        builder.addOffset(data[i]);
                    }
                    return builder.endVector();
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number numElems
                 */
                static startDimVector(builder, numElems) {
                    builder.startVector(4, numElems, 4);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @returns flatbuffers.Offset
                 */
                static endShape(builder) {
                    let offset = builder.endObject();
                    return offset;
                }
                static createShape(builder, dimOffset) {
                    Shape.startShape(builder);
                    Shape.addDim(builder, dimOffset);
                    return Shape.endShape(builder);
                }
            }
            fbs.Shape = Shape;
        })(fbs = experimental.fbs || (experimental.fbs = {}));
    })(experimental = onnxruntime.experimental || (onnxruntime.experimental = {}));
})(onnxruntime = exports.onnxruntime || (exports.onnxruntime = {}));
/**
 * @constructor
 */
(function (onnxruntime) {
    var experimental;
    (function (experimental) {
        var fbs;
        (function (fbs) {
            class Dimension {
                constructor() {
                    this.bb = null;
                    this.bb_pos = 0;
                }
                /**
                 * @param number i
                 * @param flatbuffers.ByteBuffer bb
                 * @returns Dimension
                 */
                __init(i, bb) {
                    this.bb_pos = i;
                    this.bb = bb;
                    return this;
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param Dimension= obj
                 * @returns Dimension
                 */
                static getRootAsDimension(bb, obj) {
                    return (obj || new Dimension()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param Dimension= obj
                 * @returns Dimension
                 */
                static getSizePrefixedRootAsDimension(bb, obj) {
                    bb.setPosition(bb.position() + flatbuffers_1.flatbuffers.SIZE_PREFIX_LENGTH);
                    return (obj || new Dimension()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                /**
                 * @param onnxruntime.experimental.fbs.DimensionValue= obj
                 * @returns onnxruntime.experimental.fbs.DimensionValue|null
                 */
                value(obj) {
                    let offset = this.bb.__offset(this.bb_pos, 4);
                    return offset ? (obj || new onnxruntime.experimental.fbs.DimensionValue())
                        .__init(this.bb.__indirect(this.bb_pos + offset), this.bb) :
                        null;
                }
                denotation(optionalEncoding) {
                    let offset = this.bb.__offset(this.bb_pos, 6);
                    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
                }
                /**
                 * @param flatbuffers.Builder builder
                 */
                static startDimension(builder) {
                    builder.startObject(2);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset valueOffset
                 */
                static addValue(builder, valueOffset) {
                    builder.addFieldOffset(0, valueOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset denotationOffset
                 */
                static addDenotation(builder, denotationOffset) {
                    builder.addFieldOffset(1, denotationOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @returns flatbuffers.Offset
                 */
                static endDimension(builder) {
                    let offset = builder.endObject();
                    return offset;
                }
                static createDimension(builder, valueOffset, denotationOffset) {
                    Dimension.startDimension(builder);
                    Dimension.addValue(builder, valueOffset);
                    Dimension.addDenotation(builder, denotationOffset);
                    return Dimension.endDimension(builder);
                }
            }
            fbs.Dimension = Dimension;
        })(fbs = experimental.fbs || (experimental.fbs = {}));
    })(experimental = onnxruntime.experimental || (onnxruntime.experimental = {}));
})(onnxruntime = exports.onnxruntime || (exports.onnxruntime = {}));
/**
 * @constructor
 */
(function (onnxruntime) {
    var experimental;
    (function (experimental) {
        var fbs;
        (function (fbs) {
            class DimensionValue {
                constructor() {
                    this.bb = null;
                    this.bb_pos = 0;
                }
                /**
                 * @param number i
                 * @param flatbuffers.ByteBuffer bb
                 * @returns DimensionValue
                 */
                __init(i, bb) {
                    this.bb_pos = i;
                    this.bb = bb;
                    return this;
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param DimensionValue= obj
                 * @returns DimensionValue
                 */
                static getRootAsDimensionValue(bb, obj) {
                    return (obj || new DimensionValue()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param DimensionValue= obj
                 * @returns DimensionValue
                 */
                static getSizePrefixedRootAsDimensionValue(bb, obj) {
                    bb.setPosition(bb.position() + flatbuffers_1.flatbuffers.SIZE_PREFIX_LENGTH);
                    return (obj || new DimensionValue()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                /**
                 * @returns onnxruntime.experimental.fbs.DimensionValueType
                 */
                dimType() {
                    let offset = this.bb.__offset(this.bb_pos, 4);
                    return offset ? /**  */ (this.bb.readInt8(this.bb_pos + offset)) :
                        onnxruntime.experimental.fbs.DimensionValueType.UNKNOWN;
                }
                /**
                 * @returns flatbuffers.Long
                 */
                dimValue() {
                    let offset = this.bb.__offset(this.bb_pos, 6);
                    return offset ? this.bb.readInt64(this.bb_pos + offset) : this.bb.createLong(0, 0);
                }
                dimParam(optionalEncoding) {
                    let offset = this.bb.__offset(this.bb_pos, 8);
                    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
                }
                /**
                 * @param flatbuffers.Builder builder
                 */
                static startDimensionValue(builder) {
                    builder.startObject(3);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param onnxruntime.experimental.fbs.DimensionValueType dimType
                 */
                static addDimType(builder, dimType) {
                    builder.addFieldInt8(0, dimType, onnxruntime.experimental.fbs.DimensionValueType.UNKNOWN);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Long dimValue
                 */
                static addDimValue(builder, dimValue) {
                    builder.addFieldInt64(1, dimValue, builder.createLong(0, 0));
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset dimParamOffset
                 */
                static addDimParam(builder, dimParamOffset) {
                    builder.addFieldOffset(2, dimParamOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @returns flatbuffers.Offset
                 */
                static endDimensionValue(builder) {
                    let offset = builder.endObject();
                    return offset;
                }
                static createDimensionValue(builder, dimType, dimValue, dimParamOffset) {
                    DimensionValue.startDimensionValue(builder);
                    DimensionValue.addDimType(builder, dimType);
                    DimensionValue.addDimValue(builder, dimValue);
                    DimensionValue.addDimParam(builder, dimParamOffset);
                    return DimensionValue.endDimensionValue(builder);
                }
            }
            fbs.DimensionValue = DimensionValue;
        })(fbs = experimental.fbs || (experimental.fbs = {}));
    })(experimental = onnxruntime.experimental || (onnxruntime.experimental = {}));
})(onnxruntime = exports.onnxruntime || (exports.onnxruntime = {}));
/**
 * @constructor
 */
(function (onnxruntime) {
    var experimental;
    (function (experimental) {
        var fbs;
        (function (fbs) {
            class TensorTypeAndShape {
                constructor() {
                    this.bb = null;
                    this.bb_pos = 0;
                }
                /**
                 * @param number i
                 * @param flatbuffers.ByteBuffer bb
                 * @returns TensorTypeAndShape
                 */
                __init(i, bb) {
                    this.bb_pos = i;
                    this.bb = bb;
                    return this;
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param TensorTypeAndShape= obj
                 * @returns TensorTypeAndShape
                 */
                static getRootAsTensorTypeAndShape(bb, obj) {
                    return (obj || new TensorTypeAndShape()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param TensorTypeAndShape= obj
                 * @returns TensorTypeAndShape
                 */
                static getSizePrefixedRootAsTensorTypeAndShape(bb, obj) {
                    bb.setPosition(bb.position() + flatbuffers_1.flatbuffers.SIZE_PREFIX_LENGTH);
                    return (obj || new TensorTypeAndShape()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                /**
                 * @returns onnxruntime.experimental.fbs.TensorDataType
                 */
                elemType() {
                    let offset = this.bb.__offset(this.bb_pos, 4);
                    return offset ? /**  */ (this.bb.readInt32(this.bb_pos + offset)) :
                        onnxruntime.experimental.fbs.TensorDataType.UNDEFINED;
                }
                /**
                 * @param onnxruntime.experimental.fbs.Shape= obj
                 * @returns onnxruntime.experimental.fbs.Shape|null
                 */
                shape(obj) {
                    let offset = this.bb.__offset(this.bb_pos, 6);
                    return offset ? (obj || new onnxruntime.experimental.fbs.Shape())
                        .__init(this.bb.__indirect(this.bb_pos + offset), this.bb) :
                        null;
                }
                /**
                 * @param flatbuffers.Builder builder
                 */
                static startTensorTypeAndShape(builder) {
                    builder.startObject(2);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param onnxruntime.experimental.fbs.TensorDataType elemType
                 */
                static addElemType(builder, elemType) {
                    builder.addFieldInt32(0, elemType, onnxruntime.experimental.fbs.TensorDataType.UNDEFINED);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset shapeOffset
                 */
                static addShape(builder, shapeOffset) {
                    builder.addFieldOffset(1, shapeOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @returns flatbuffers.Offset
                 */
                static endTensorTypeAndShape(builder) {
                    let offset = builder.endObject();
                    return offset;
                }
                static createTensorTypeAndShape(builder, elemType, shapeOffset) {
                    TensorTypeAndShape.startTensorTypeAndShape(builder);
                    TensorTypeAndShape.addElemType(builder, elemType);
                    TensorTypeAndShape.addShape(builder, shapeOffset);
                    return TensorTypeAndShape.endTensorTypeAndShape(builder);
                }
            }
            fbs.TensorTypeAndShape = TensorTypeAndShape;
        })(fbs = experimental.fbs || (experimental.fbs = {}));
    })(experimental = onnxruntime.experimental || (onnxruntime.experimental = {}));
})(onnxruntime = exports.onnxruntime || (exports.onnxruntime = {}));
/**
 * @constructor
 */
(function (onnxruntime) {
    var experimental;
    (function (experimental) {
        var fbs;
        (function (fbs) {
            class MapType {
                constructor() {
                    this.bb = null;
                    this.bb_pos = 0;
                }
                /**
                 * @param number i
                 * @param flatbuffers.ByteBuffer bb
                 * @returns MapType
                 */
                __init(i, bb) {
                    this.bb_pos = i;
                    this.bb = bb;
                    return this;
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param MapType= obj
                 * @returns MapType
                 */
                static getRootAsMapType(bb, obj) {
                    return (obj || new MapType()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param MapType= obj
                 * @returns MapType
                 */
                static getSizePrefixedRootAsMapType(bb, obj) {
                    bb.setPosition(bb.position() + flatbuffers_1.flatbuffers.SIZE_PREFIX_LENGTH);
                    return (obj || new MapType()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                /**
                 * @returns onnxruntime.experimental.fbs.TensorDataType
                 */
                keyType() {
                    let offset = this.bb.__offset(this.bb_pos, 4);
                    return offset ? /**  */ (this.bb.readInt32(this.bb_pos + offset)) :
                        onnxruntime.experimental.fbs.TensorDataType.UNDEFINED;
                }
                /**
                 * @param onnxruntime.experimental.fbs.TypeInfo= obj
                 * @returns onnxruntime.experimental.fbs.TypeInfo|null
                 */
                valueType(obj) {
                    let offset = this.bb.__offset(this.bb_pos, 6);
                    return offset ? (obj || new onnxruntime.experimental.fbs.TypeInfo())
                        .__init(this.bb.__indirect(this.bb_pos + offset), this.bb) :
                        null;
                }
                /**
                 * @param flatbuffers.Builder builder
                 */
                static startMapType(builder) {
                    builder.startObject(2);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param onnxruntime.experimental.fbs.TensorDataType keyType
                 */
                static addKeyType(builder, keyType) {
                    builder.addFieldInt32(0, keyType, onnxruntime.experimental.fbs.TensorDataType.UNDEFINED);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset valueTypeOffset
                 */
                static addValueType(builder, valueTypeOffset) {
                    builder.addFieldOffset(1, valueTypeOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @returns flatbuffers.Offset
                 */
                static endMapType(builder) {
                    let offset = builder.endObject();
                    return offset;
                }
                static createMapType(builder, keyType, valueTypeOffset) {
                    MapType.startMapType(builder);
                    MapType.addKeyType(builder, keyType);
                    MapType.addValueType(builder, valueTypeOffset);
                    return MapType.endMapType(builder);
                }
            }
            fbs.MapType = MapType;
        })(fbs = experimental.fbs || (experimental.fbs = {}));
    })(experimental = onnxruntime.experimental || (onnxruntime.experimental = {}));
})(onnxruntime = exports.onnxruntime || (exports.onnxruntime = {}));
/**
 * @constructor
 */
(function (onnxruntime) {
    var experimental;
    (function (experimental) {
        var fbs;
        (function (fbs) {
            class SequenceType {
                constructor() {
                    this.bb = null;
                    this.bb_pos = 0;
                }
                /**
                 * @param number i
                 * @param flatbuffers.ByteBuffer bb
                 * @returns SequenceType
                 */
                __init(i, bb) {
                    this.bb_pos = i;
                    this.bb = bb;
                    return this;
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param SequenceType= obj
                 * @returns SequenceType
                 */
                static getRootAsSequenceType(bb, obj) {
                    return (obj || new SequenceType()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param SequenceType= obj
                 * @returns SequenceType
                 */
                static getSizePrefixedRootAsSequenceType(bb, obj) {
                    bb.setPosition(bb.position() + flatbuffers_1.flatbuffers.SIZE_PREFIX_LENGTH);
                    return (obj || new SequenceType()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                /**
                 * @param onnxruntime.experimental.fbs.TypeInfo= obj
                 * @returns onnxruntime.experimental.fbs.TypeInfo|null
                 */
                elemType(obj) {
                    let offset = this.bb.__offset(this.bb_pos, 4);
                    return offset ? (obj || new onnxruntime.experimental.fbs.TypeInfo())
                        .__init(this.bb.__indirect(this.bb_pos + offset), this.bb) :
                        null;
                }
                /**
                 * @param flatbuffers.Builder builder
                 */
                static startSequenceType(builder) {
                    builder.startObject(1);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset elemTypeOffset
                 */
                static addElemType(builder, elemTypeOffset) {
                    builder.addFieldOffset(0, elemTypeOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @returns flatbuffers.Offset
                 */
                static endSequenceType(builder) {
                    let offset = builder.endObject();
                    return offset;
                }
                static createSequenceType(builder, elemTypeOffset) {
                    SequenceType.startSequenceType(builder);
                    SequenceType.addElemType(builder, elemTypeOffset);
                    return SequenceType.endSequenceType(builder);
                }
            }
            fbs.SequenceType = SequenceType;
        })(fbs = experimental.fbs || (experimental.fbs = {}));
    })(experimental = onnxruntime.experimental || (onnxruntime.experimental = {}));
})(onnxruntime = exports.onnxruntime || (exports.onnxruntime = {}));
/**
 * @constructor
 */
(function (onnxruntime) {
    var experimental;
    (function (experimental) {
        var fbs;
        (function (fbs) {
            class EdgeEnd {
                constructor() {
                    this.bb = null;
                    this.bb_pos = 0;
                }
                /**
                 * @param number i
                 * @param flatbuffers.ByteBuffer bb
                 * @returns EdgeEnd
                 */
                __init(i, bb) {
                    this.bb_pos = i;
                    this.bb = bb;
                    return this;
                }
                /**
                 * @returns number
                 */
                nodeIndex() {
                    return this.bb.readUint32(this.bb_pos);
                }
                /**
                 * @returns number
                 */
                srcArgIndex() {
                    return this.bb.readInt32(this.bb_pos + 4);
                }
                /**
                 * @returns number
                 */
                dstArgIndex() {
                    return this.bb.readInt32(this.bb_pos + 8);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number node_index
                 * @param number src_arg_index
                 * @param number dst_arg_index
                 * @returns flatbuffers.Offset
                 */
                static createEdgeEnd(builder, node_index, src_arg_index, dst_arg_index) {
                    builder.prep(4, 12);
                    builder.writeInt32(dst_arg_index);
                    builder.writeInt32(src_arg_index);
                    builder.writeInt32(node_index);
                    return builder.offset();
                }
            }
            fbs.EdgeEnd = EdgeEnd;
        })(fbs = experimental.fbs || (experimental.fbs = {}));
    })(experimental = onnxruntime.experimental || (onnxruntime.experimental = {}));
})(onnxruntime = exports.onnxruntime || (exports.onnxruntime = {}));
/**
 * @constructor
 */
(function (onnxruntime) {
    var experimental;
    (function (experimental) {
        var fbs;
        (function (fbs) {
            class NodeEdge {
                constructor() {
                    this.bb = null;
                    this.bb_pos = 0;
                }
                /**
                 * @param number i
                 * @param flatbuffers.ByteBuffer bb
                 * @returns NodeEdge
                 */
                __init(i, bb) {
                    this.bb_pos = i;
                    this.bb = bb;
                    return this;
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param NodeEdge= obj
                 * @returns NodeEdge
                 */
                static getRootAsNodeEdge(bb, obj) {
                    return (obj || new NodeEdge()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param NodeEdge= obj
                 * @returns NodeEdge
                 */
                static getSizePrefixedRootAsNodeEdge(bb, obj) {
                    bb.setPosition(bb.position() + flatbuffers_1.flatbuffers.SIZE_PREFIX_LENGTH);
                    return (obj || new NodeEdge()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                /**
                 * @returns number
                 */
                nodeIndex() {
                    let offset = this.bb.__offset(this.bb_pos, 4);
                    return offset ? this.bb.readUint32(this.bb_pos + offset) : 0;
                }
                /**
                 * @param number index
                 * @param onnxruntime.experimental.fbs.EdgeEnd= obj
                 * @returns onnxruntime.experimental.fbs.EdgeEnd
                 */
                inputEdges(index, obj) {
                    let offset = this.bb.__offset(this.bb_pos, 6);
                    return offset ? (obj || new onnxruntime.experimental.fbs.EdgeEnd())
                        .__init(this.bb.__vector(this.bb_pos + offset) + index * 12, this.bb) :
                        null;
                }
                /**
                 * @returns number
                 */
                inputEdgesLength() {
                    let offset = this.bb.__offset(this.bb_pos, 6);
                    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
                }
                /**
                 * @param number index
                 * @param onnxruntime.experimental.fbs.EdgeEnd= obj
                 * @returns onnxruntime.experimental.fbs.EdgeEnd
                 */
                outputEdges(index, obj) {
                    let offset = this.bb.__offset(this.bb_pos, 8);
                    return offset ? (obj || new onnxruntime.experimental.fbs.EdgeEnd())
                        .__init(this.bb.__vector(this.bb_pos + offset) + index * 12, this.bb) :
                        null;
                }
                /**
                 * @returns number
                 */
                outputEdgesLength() {
                    let offset = this.bb.__offset(this.bb_pos, 8);
                    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
                }
                /**
                 * @param flatbuffers.Builder builder
                 */
                static startNodeEdge(builder) {
                    builder.startObject(3);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number nodeIndex
                 */
                static addNodeIndex(builder, nodeIndex) {
                    builder.addFieldInt32(0, nodeIndex, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset inputEdgesOffset
                 */
                static addInputEdges(builder, inputEdgesOffset) {
                    builder.addFieldOffset(1, inputEdgesOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number numElems
                 */
                static startInputEdgesVector(builder, numElems) {
                    builder.startVector(12, numElems, 4);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset outputEdgesOffset
                 */
                static addOutputEdges(builder, outputEdgesOffset) {
                    builder.addFieldOffset(2, outputEdgesOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number numElems
                 */
                static startOutputEdgesVector(builder, numElems) {
                    builder.startVector(12, numElems, 4);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @returns flatbuffers.Offset
                 */
                static endNodeEdge(builder) {
                    let offset = builder.endObject();
                    return offset;
                }
                static createNodeEdge(builder, nodeIndex, inputEdgesOffset, outputEdgesOffset) {
                    NodeEdge.startNodeEdge(builder);
                    NodeEdge.addNodeIndex(builder, nodeIndex);
                    NodeEdge.addInputEdges(builder, inputEdgesOffset);
                    NodeEdge.addOutputEdges(builder, outputEdgesOffset);
                    return NodeEdge.endNodeEdge(builder);
                }
            }
            fbs.NodeEdge = NodeEdge;
        })(fbs = experimental.fbs || (experimental.fbs = {}));
    })(experimental = onnxruntime.experimental || (onnxruntime.experimental = {}));
})(onnxruntime = exports.onnxruntime || (exports.onnxruntime = {}));
/**
 * @constructor
 */
(function (onnxruntime) {
    var experimental;
    (function (experimental) {
        var fbs;
        (function (fbs) {
            class Node {
                constructor() {
                    this.bb = null;
                    this.bb_pos = 0;
                }
                /**
                 * @param number i
                 * @param flatbuffers.ByteBuffer bb
                 * @returns Node
                 */
                __init(i, bb) {
                    this.bb_pos = i;
                    this.bb = bb;
                    return this;
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param Node= obj
                 * @returns Node
                 */
                static getRootAsNode(bb, obj) {
                    return (obj || new Node()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param Node= obj
                 * @returns Node
                 */
                static getSizePrefixedRootAsNode(bb, obj) {
                    bb.setPosition(bb.position() + flatbuffers_1.flatbuffers.SIZE_PREFIX_LENGTH);
                    return (obj || new Node()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                name(optionalEncoding) {
                    let offset = this.bb.__offset(this.bb_pos, 4);
                    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
                }
                docString(optionalEncoding) {
                    let offset = this.bb.__offset(this.bb_pos, 6);
                    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
                }
                domain(optionalEncoding) {
                    let offset = this.bb.__offset(this.bb_pos, 8);
                    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
                }
                /**
                 * @returns number
                 */
                sinceVersion() {
                    let offset = this.bb.__offset(this.bb_pos, 10);
                    return offset ? this.bb.readInt32(this.bb_pos + offset) : 0;
                }
                /**
                 * @returns number
                 */
                index() {
                    let offset = this.bb.__offset(this.bb_pos, 12);
                    return offset ? this.bb.readUint32(this.bb_pos + offset) : 0;
                }
                opType(optionalEncoding) {
                    let offset = this.bb.__offset(this.bb_pos, 14);
                    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
                }
                /**
                 * @returns onnxruntime.experimental.fbs.NodeType
                 */
                type() {
                    let offset = this.bb.__offset(this.bb_pos, 16);
                    return offset ? /**  */ (this.bb.readInt32(this.bb_pos + offset)) :
                        onnxruntime.experimental.fbs.NodeType.Primitive;
                }
                executionProviderType(optionalEncoding) {
                    let offset = this.bb.__offset(this.bb_pos, 18);
                    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
                }
                inputs(index, optionalEncoding) {
                    let offset = this.bb.__offset(this.bb_pos, 20);
                    return offset ? this.bb.__string(this.bb.__vector(this.bb_pos + offset) + index * 4, optionalEncoding) : null;
                }
                /**
                 * @returns number
                 */
                inputsLength() {
                    let offset = this.bb.__offset(this.bb_pos, 20);
                    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
                }
                outputs(index, optionalEncoding) {
                    let offset = this.bb.__offset(this.bb_pos, 22);
                    return offset ? this.bb.__string(this.bb.__vector(this.bb_pos + offset) + index * 4, optionalEncoding) : null;
                }
                /**
                 * @returns number
                 */
                outputsLength() {
                    let offset = this.bb.__offset(this.bb_pos, 22);
                    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
                }
                /**
                 * @param number index
                 * @param onnxruntime.experimental.fbs.Attribute= obj
                 * @returns onnxruntime.experimental.fbs.Attribute
                 */
                attributes(index, obj) {
                    let offset = this.bb.__offset(this.bb_pos, 24);
                    return offset ? (obj || new onnxruntime.experimental.fbs.Attribute())
                        .__init(this.bb.__indirect(this.bb.__vector(this.bb_pos + offset) + index * 4), this.bb) :
                        null;
                }
                /**
                 * @returns number
                 */
                attributesLength() {
                    let offset = this.bb.__offset(this.bb_pos, 24);
                    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
                }
                /**
                 * @param number index
                 * @returns number
                 */
                inputArgCounts(index) {
                    let offset = this.bb.__offset(this.bb_pos, 26);
                    return offset ? this.bb.readInt32(this.bb.__vector(this.bb_pos + offset) + index * 4) : 0;
                }
                /**
                 * @returns number
                 */
                inputArgCountsLength() {
                    let offset = this.bb.__offset(this.bb_pos, 26);
                    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
                }
                /**
                 * @returns Int32Array
                 */
                inputArgCountsArray() {
                    let offset = this.bb.__offset(this.bb_pos, 26);
                    return offset ?
                        new Int32Array(this.bb.bytes().buffer, this.bb.bytes().byteOffset + this.bb.__vector(this.bb_pos + offset), this.bb.__vector_len(this.bb_pos + offset)) :
                        null;
                }
                implicitInputs(index, optionalEncoding) {
                    let offset = this.bb.__offset(this.bb_pos, 28);
                    return offset ? this.bb.__string(this.bb.__vector(this.bb_pos + offset) + index * 4, optionalEncoding) : null;
                }
                /**
                 * @returns number
                 */
                implicitInputsLength() {
                    let offset = this.bb.__offset(this.bb_pos, 28);
                    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
                }
                /**
                 * @param flatbuffers.Builder builder
                 */
                static startNode(builder) {
                    builder.startObject(13);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset nameOffset
                 */
                static addName(builder, nameOffset) {
                    builder.addFieldOffset(0, nameOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset docStringOffset
                 */
                static addDocString(builder, docStringOffset) {
                    builder.addFieldOffset(1, docStringOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset domainOffset
                 */
                static addDomain(builder, domainOffset) {
                    builder.addFieldOffset(2, domainOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number sinceVersion
                 */
                static addSinceVersion(builder, sinceVersion) {
                    builder.addFieldInt32(3, sinceVersion, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number index
                 */
                static addIndex(builder, index) {
                    builder.addFieldInt32(4, index, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset opTypeOffset
                 */
                static addOpType(builder, opTypeOffset) {
                    builder.addFieldOffset(5, opTypeOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param onnxruntime.experimental.fbs.NodeType type
                 */
                static addType(builder, type) {
                    builder.addFieldInt32(6, type, onnxruntime.experimental.fbs.NodeType.Primitive);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset executionProviderTypeOffset
                 */
                static addExecutionProviderType(builder, executionProviderTypeOffset) {
                    builder.addFieldOffset(7, executionProviderTypeOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset inputsOffset
                 */
                static addInputs(builder, inputsOffset) {
                    builder.addFieldOffset(8, inputsOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param Array.<flatbuffers.Offset> data
                 * @returns flatbuffers.Offset
                 */
                static createInputsVector(builder, data) {
                    builder.startVector(4, data.length, 4);
                    for (let i = data.length - 1; i >= 0; i--) {
                        builder.addOffset(data[i]);
                    }
                    return builder.endVector();
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number numElems
                 */
                static startInputsVector(builder, numElems) {
                    builder.startVector(4, numElems, 4);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset outputsOffset
                 */
                static addOutputs(builder, outputsOffset) {
                    builder.addFieldOffset(9, outputsOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param Array.<flatbuffers.Offset> data
                 * @returns flatbuffers.Offset
                 */
                static createOutputsVector(builder, data) {
                    builder.startVector(4, data.length, 4);
                    for (let i = data.length - 1; i >= 0; i--) {
                        builder.addOffset(data[i]);
                    }
                    return builder.endVector();
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number numElems
                 */
                static startOutputsVector(builder, numElems) {
                    builder.startVector(4, numElems, 4);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset attributesOffset
                 */
                static addAttributes(builder, attributesOffset) {
                    builder.addFieldOffset(10, attributesOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param Array.<flatbuffers.Offset> data
                 * @returns flatbuffers.Offset
                 */
                static createAttributesVector(builder, data) {
                    builder.startVector(4, data.length, 4);
                    for (let i = data.length - 1; i >= 0; i--) {
                        builder.addOffset(data[i]);
                    }
                    return builder.endVector();
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number numElems
                 */
                static startAttributesVector(builder, numElems) {
                    builder.startVector(4, numElems, 4);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset inputArgCountsOffset
                 */
                static addInputArgCounts(builder, inputArgCountsOffset) {
                    builder.addFieldOffset(11, inputArgCountsOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param Array.<number> data
                 * @returns flatbuffers.Offset
                 */
                static createInputArgCountsVector(builder, data) {
                    builder.startVector(4, data.length, 4);
                    for (let i = data.length - 1; i >= 0; i--) {
                        builder.addInt32(data[i]);
                    }
                    return builder.endVector();
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number numElems
                 */
                static startInputArgCountsVector(builder, numElems) {
                    builder.startVector(4, numElems, 4);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset implicitInputsOffset
                 */
                static addImplicitInputs(builder, implicitInputsOffset) {
                    builder.addFieldOffset(12, implicitInputsOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param Array.<flatbuffers.Offset> data
                 * @returns flatbuffers.Offset
                 */
                static createImplicitInputsVector(builder, data) {
                    builder.startVector(4, data.length, 4);
                    for (let i = data.length - 1; i >= 0; i--) {
                        builder.addOffset(data[i]);
                    }
                    return builder.endVector();
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number numElems
                 */
                static startImplicitInputsVector(builder, numElems) {
                    builder.startVector(4, numElems, 4);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @returns flatbuffers.Offset
                 */
                static endNode(builder) {
                    let offset = builder.endObject();
                    return offset;
                }
                static createNode(builder, nameOffset, docStringOffset, domainOffset, sinceVersion, index, opTypeOffset, type, executionProviderTypeOffset, inputsOffset, outputsOffset, attributesOffset, inputArgCountsOffset, implicitInputsOffset) {
                    Node.startNode(builder);
                    Node.addName(builder, nameOffset);
                    Node.addDocString(builder, docStringOffset);
                    Node.addDomain(builder, domainOffset);
                    Node.addSinceVersion(builder, sinceVersion);
                    Node.addIndex(builder, index);
                    Node.addOpType(builder, opTypeOffset);
                    Node.addType(builder, type);
                    Node.addExecutionProviderType(builder, executionProviderTypeOffset);
                    Node.addInputs(builder, inputsOffset);
                    Node.addOutputs(builder, outputsOffset);
                    Node.addAttributes(builder, attributesOffset);
                    Node.addInputArgCounts(builder, inputArgCountsOffset);
                    Node.addImplicitInputs(builder, implicitInputsOffset);
                    return Node.endNode(builder);
                }
            }
            fbs.Node = Node;
        })(fbs = experimental.fbs || (experimental.fbs = {}));
    })(experimental = onnxruntime.experimental || (onnxruntime.experimental = {}));
})(onnxruntime = exports.onnxruntime || (exports.onnxruntime = {}));
/**
 * @constructor
 */
(function (onnxruntime) {
    var experimental;
    (function (experimental) {
        var fbs;
        (function (fbs) {
            class ValueInfo {
                constructor() {
                    this.bb = null;
                    this.bb_pos = 0;
                }
                /**
                 * @param number i
                 * @param flatbuffers.ByteBuffer bb
                 * @returns ValueInfo
                 */
                __init(i, bb) {
                    this.bb_pos = i;
                    this.bb = bb;
                    return this;
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param ValueInfo= obj
                 * @returns ValueInfo
                 */
                static getRootAsValueInfo(bb, obj) {
                    return (obj || new ValueInfo()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param ValueInfo= obj
                 * @returns ValueInfo
                 */
                static getSizePrefixedRootAsValueInfo(bb, obj) {
                    bb.setPosition(bb.position() + flatbuffers_1.flatbuffers.SIZE_PREFIX_LENGTH);
                    return (obj || new ValueInfo()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                name(optionalEncoding) {
                    let offset = this.bb.__offset(this.bb_pos, 4);
                    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
                }
                docString(optionalEncoding) {
                    let offset = this.bb.__offset(this.bb_pos, 6);
                    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
                }
                /**
                 * @param onnxruntime.experimental.fbs.TypeInfo= obj
                 * @returns onnxruntime.experimental.fbs.TypeInfo|null
                 */
                type(obj) {
                    let offset = this.bb.__offset(this.bb_pos, 8);
                    return offset ? (obj || new onnxruntime.experimental.fbs.TypeInfo())
                        .__init(this.bb.__indirect(this.bb_pos + offset), this.bb) :
                        null;
                }
                /**
                 * @param flatbuffers.Builder builder
                 */
                static startValueInfo(builder) {
                    builder.startObject(3);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset nameOffset
                 */
                static addName(builder, nameOffset) {
                    builder.addFieldOffset(0, nameOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset docStringOffset
                 */
                static addDocString(builder, docStringOffset) {
                    builder.addFieldOffset(1, docStringOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset typeOffset
                 */
                static addType(builder, typeOffset) {
                    builder.addFieldOffset(2, typeOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @returns flatbuffers.Offset
                 */
                static endValueInfo(builder) {
                    let offset = builder.endObject();
                    return offset;
                }
                static createValueInfo(builder, nameOffset, docStringOffset, typeOffset) {
                    ValueInfo.startValueInfo(builder);
                    ValueInfo.addName(builder, nameOffset);
                    ValueInfo.addDocString(builder, docStringOffset);
                    ValueInfo.addType(builder, typeOffset);
                    return ValueInfo.endValueInfo(builder);
                }
            }
            fbs.ValueInfo = ValueInfo;
        })(fbs = experimental.fbs || (experimental.fbs = {}));
    })(experimental = onnxruntime.experimental || (onnxruntime.experimental = {}));
})(onnxruntime = exports.onnxruntime || (exports.onnxruntime = {}));
/**
 * @constructor
 */
(function (onnxruntime) {
    var experimental;
    (function (experimental) {
        var fbs;
        (function (fbs) {
            class TypeInfo {
                constructor() {
                    this.bb = null;
                    this.bb_pos = 0;
                }
                /**
                 * @param number i
                 * @param flatbuffers.ByteBuffer bb
                 * @returns TypeInfo
                 */
                __init(i, bb) {
                    this.bb_pos = i;
                    this.bb = bb;
                    return this;
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param TypeInfo= obj
                 * @returns TypeInfo
                 */
                static getRootAsTypeInfo(bb, obj) {
                    return (obj || new TypeInfo()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param TypeInfo= obj
                 * @returns TypeInfo
                 */
                static getSizePrefixedRootAsTypeInfo(bb, obj) {
                    bb.setPosition(bb.position() + flatbuffers_1.flatbuffers.SIZE_PREFIX_LENGTH);
                    return (obj || new TypeInfo()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                denotation(optionalEncoding) {
                    let offset = this.bb.__offset(this.bb_pos, 4);
                    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
                }
                /**
                 * @returns onnxruntime.experimental.fbs.TypeInfoValue
                 */
                valueType() {
                    let offset = this.bb.__offset(this.bb_pos, 6);
                    return offset ? /**  */ (this.bb.readUint8(this.bb_pos + offset)) :
                        onnxruntime.experimental.fbs.TypeInfoValue.NONE;
                }
                /**
                 * @param flatbuffers.Table obj
                 * @returns ?flatbuffers.Table
                 */
                value(obj) {
                    let offset = this.bb.__offset(this.bb_pos, 8);
                    return offset ? this.bb.__union(obj, this.bb_pos + offset) : null;
                }
                /**
                 * @param flatbuffers.Builder builder
                 */
                static startTypeInfo(builder) {
                    builder.startObject(3);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset denotationOffset
                 */
                static addDenotation(builder, denotationOffset) {
                    builder.addFieldOffset(0, denotationOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param onnxruntime.experimental.fbs.TypeInfoValue valueType
                 */
                static addValueType(builder, valueType) {
                    builder.addFieldInt8(1, valueType, onnxruntime.experimental.fbs.TypeInfoValue.NONE);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset valueOffset
                 */
                static addValue(builder, valueOffset) {
                    builder.addFieldOffset(2, valueOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @returns flatbuffers.Offset
                 */
                static endTypeInfo(builder) {
                    let offset = builder.endObject();
                    return offset;
                }
                static createTypeInfo(builder, denotationOffset, valueType, valueOffset) {
                    TypeInfo.startTypeInfo(builder);
                    TypeInfo.addDenotation(builder, denotationOffset);
                    TypeInfo.addValueType(builder, valueType);
                    TypeInfo.addValue(builder, valueOffset);
                    return TypeInfo.endTypeInfo(builder);
                }
            }
            fbs.TypeInfo = TypeInfo;
        })(fbs = experimental.fbs || (experimental.fbs = {}));
    })(experimental = onnxruntime.experimental || (onnxruntime.experimental = {}));
})(onnxruntime = exports.onnxruntime || (exports.onnxruntime = {}));
/**
 * @constructor
 */
(function (onnxruntime) {
    var experimental;
    (function (experimental) {
        var fbs;
        (function (fbs) {
            class OperatorSetId {
                constructor() {
                    this.bb = null;
                    this.bb_pos = 0;
                }
                /**
                 * @param number i
                 * @param flatbuffers.ByteBuffer bb
                 * @returns OperatorSetId
                 */
                __init(i, bb) {
                    this.bb_pos = i;
                    this.bb = bb;
                    return this;
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param OperatorSetId= obj
                 * @returns OperatorSetId
                 */
                static getRootAsOperatorSetId(bb, obj) {
                    return (obj || new OperatorSetId()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param OperatorSetId= obj
                 * @returns OperatorSetId
                 */
                static getSizePrefixedRootAsOperatorSetId(bb, obj) {
                    bb.setPosition(bb.position() + flatbuffers_1.flatbuffers.SIZE_PREFIX_LENGTH);
                    return (obj || new OperatorSetId()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                domain(optionalEncoding) {
                    let offset = this.bb.__offset(this.bb_pos, 4);
                    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
                }
                /**
                 * @returns flatbuffers.Long
                 */
                version() {
                    let offset = this.bb.__offset(this.bb_pos, 6);
                    return offset ? this.bb.readInt64(this.bb_pos + offset) : this.bb.createLong(0, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 */
                static startOperatorSetId(builder) {
                    builder.startObject(2);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset domainOffset
                 */
                static addDomain(builder, domainOffset) {
                    builder.addFieldOffset(0, domainOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Long version
                 */
                static addVersion(builder, version) {
                    builder.addFieldInt64(1, version, builder.createLong(0, 0));
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @returns flatbuffers.Offset
                 */
                static endOperatorSetId(builder) {
                    let offset = builder.endObject();
                    return offset;
                }
                static createOperatorSetId(builder, domainOffset, version) {
                    OperatorSetId.startOperatorSetId(builder);
                    OperatorSetId.addDomain(builder, domainOffset);
                    OperatorSetId.addVersion(builder, version);
                    return OperatorSetId.endOperatorSetId(builder);
                }
            }
            fbs.OperatorSetId = OperatorSetId;
        })(fbs = experimental.fbs || (experimental.fbs = {}));
    })(experimental = onnxruntime.experimental || (onnxruntime.experimental = {}));
})(onnxruntime = exports.onnxruntime || (exports.onnxruntime = {}));
/**
 * @constructor
 */
(function (onnxruntime) {
    var experimental;
    (function (experimental) {
        var fbs;
        (function (fbs) {
            class Tensor {
                constructor() {
                    this.bb = null;
                    this.bb_pos = 0;
                }
                /**
                 * @param number i
                 * @param flatbuffers.ByteBuffer bb
                 * @returns Tensor
                 */
                __init(i, bb) {
                    this.bb_pos = i;
                    this.bb = bb;
                    return this;
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param Tensor= obj
                 * @returns Tensor
                 */
                static getRootAsTensor(bb, obj) {
                    return (obj || new Tensor()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param Tensor= obj
                 * @returns Tensor
                 */
                static getSizePrefixedRootAsTensor(bb, obj) {
                    bb.setPosition(bb.position() + flatbuffers_1.flatbuffers.SIZE_PREFIX_LENGTH);
                    return (obj || new Tensor()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                name(optionalEncoding) {
                    let offset = this.bb.__offset(this.bb_pos, 4);
                    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
                }
                docString(optionalEncoding) {
                    let offset = this.bb.__offset(this.bb_pos, 6);
                    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
                }
                /**
                 * @param number index
                 * @returns flatbuffers.Long
                 */
                dims(index) {
                    let offset = this.bb.__offset(this.bb_pos, 8);
                    return offset ? this.bb.readInt64(this.bb.__vector(this.bb_pos + offset) + index * 8) :
                        this.bb.createLong(0, 0);
                }
                /**
                 * @returns number
                 */
                dimsLength() {
                    let offset = this.bb.__offset(this.bb_pos, 8);
                    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
                }
                /**
                 * @returns onnxruntime.experimental.fbs.TensorDataType
                 */
                dataType() {
                    let offset = this.bb.__offset(this.bb_pos, 10);
                    return offset ? /**  */ (this.bb.readInt32(this.bb_pos + offset)) :
                        onnxruntime.experimental.fbs.TensorDataType.UNDEFINED;
                }
                /**
                 * @param number index
                 * @returns number
                 */
                rawData(index) {
                    let offset = this.bb.__offset(this.bb_pos, 12);
                    return offset ? this.bb.readUint8(this.bb.__vector(this.bb_pos + offset) + index) : 0;
                }
                /**
                 * @returns number
                 */
                rawDataLength() {
                    let offset = this.bb.__offset(this.bb_pos, 12);
                    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
                }
                /**
                 * @returns Uint8Array
                 */
                rawDataArray() {
                    let offset = this.bb.__offset(this.bb_pos, 12);
                    return offset ?
                        new Uint8Array(this.bb.bytes().buffer, this.bb.bytes().byteOffset + this.bb.__vector(this.bb_pos + offset), this.bb.__vector_len(this.bb_pos + offset)) :
                        null;
                }
                stringData(index, optionalEncoding) {
                    let offset = this.bb.__offset(this.bb_pos, 14);
                    return offset ? this.bb.__string(this.bb.__vector(this.bb_pos + offset) + index * 4, optionalEncoding) : null;
                }
                /**
                 * @returns number
                 */
                stringDataLength() {
                    let offset = this.bb.__offset(this.bb_pos, 14);
                    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
                }
                /**
                 * @param flatbuffers.Builder builder
                 */
                static startTensor(builder) {
                    builder.startObject(6);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset nameOffset
                 */
                static addName(builder, nameOffset) {
                    builder.addFieldOffset(0, nameOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset docStringOffset
                 */
                static addDocString(builder, docStringOffset) {
                    builder.addFieldOffset(1, docStringOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset dimsOffset
                 */
                static addDims(builder, dimsOffset) {
                    builder.addFieldOffset(2, dimsOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param Array.<flatbuffers.Long> data
                 * @returns flatbuffers.Offset
                 */
                static createDimsVector(builder, data) {
                    builder.startVector(8, data.length, 8);
                    for (let i = data.length - 1; i >= 0; i--) {
                        builder.addInt64(data[i]);
                    }
                    return builder.endVector();
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number numElems
                 */
                static startDimsVector(builder, numElems) {
                    builder.startVector(8, numElems, 8);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param onnxruntime.experimental.fbs.TensorDataType dataType
                 */
                static addDataType(builder, dataType) {
                    builder.addFieldInt32(3, dataType, onnxruntime.experimental.fbs.TensorDataType.UNDEFINED);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset rawDataOffset
                 */
                static addRawData(builder, rawDataOffset) {
                    builder.addFieldOffset(4, rawDataOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param Array.<number> data
                 * @returns flatbuffers.Offset
                 */
                static createRawDataVector(builder, data) {
                    builder.startVector(1, data.length, 1);
                    for (let i = data.length - 1; i >= 0; i--) {
                        builder.addInt8(data[i]);
                    }
                    return builder.endVector();
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number numElems
                 */
                static startRawDataVector(builder, numElems) {
                    builder.startVector(1, numElems, 1);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset stringDataOffset
                 */
                static addStringData(builder, stringDataOffset) {
                    builder.addFieldOffset(5, stringDataOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param Array.<flatbuffers.Offset> data
                 * @returns flatbuffers.Offset
                 */
                static createStringDataVector(builder, data) {
                    builder.startVector(4, data.length, 4);
                    for (let i = data.length - 1; i >= 0; i--) {
                        builder.addOffset(data[i]);
                    }
                    return builder.endVector();
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number numElems
                 */
                static startStringDataVector(builder, numElems) {
                    builder.startVector(4, numElems, 4);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @returns flatbuffers.Offset
                 */
                static endTensor(builder) {
                    let offset = builder.endObject();
                    return offset;
                }
                static createTensor(builder, nameOffset, docStringOffset, dimsOffset, dataType, rawDataOffset, stringDataOffset) {
                    Tensor.startTensor(builder);
                    Tensor.addName(builder, nameOffset);
                    Tensor.addDocString(builder, docStringOffset);
                    Tensor.addDims(builder, dimsOffset);
                    Tensor.addDataType(builder, dataType);
                    Tensor.addRawData(builder, rawDataOffset);
                    Tensor.addStringData(builder, stringDataOffset);
                    return Tensor.endTensor(builder);
                }
            }
            fbs.Tensor = Tensor;
        })(fbs = experimental.fbs || (experimental.fbs = {}));
    })(experimental = onnxruntime.experimental || (onnxruntime.experimental = {}));
})(onnxruntime = exports.onnxruntime || (exports.onnxruntime = {}));
/**
 * @constructor
 */
(function (onnxruntime) {
    var experimental;
    (function (experimental) {
        var fbs;
        (function (fbs) {
            class SparseTensor {
                constructor() {
                    this.bb = null;
                    this.bb_pos = 0;
                }
                /**
                 * @param number i
                 * @param flatbuffers.ByteBuffer bb
                 * @returns SparseTensor
                 */
                __init(i, bb) {
                    this.bb_pos = i;
                    this.bb = bb;
                    return this;
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param SparseTensor= obj
                 * @returns SparseTensor
                 */
                static getRootAsSparseTensor(bb, obj) {
                    return (obj || new SparseTensor()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param SparseTensor= obj
                 * @returns SparseTensor
                 */
                static getSizePrefixedRootAsSparseTensor(bb, obj) {
                    bb.setPosition(bb.position() + flatbuffers_1.flatbuffers.SIZE_PREFIX_LENGTH);
                    return (obj || new SparseTensor()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                /**
                 * @param onnxruntime.experimental.fbs.Tensor= obj
                 * @returns onnxruntime.experimental.fbs.Tensor|null
                 */
                values(obj) {
                    let offset = this.bb.__offset(this.bb_pos, 4);
                    return offset ? (obj || new onnxruntime.experimental.fbs.Tensor())
                        .__init(this.bb.__indirect(this.bb_pos + offset), this.bb) :
                        null;
                }
                /**
                 * @param onnxruntime.experimental.fbs.Tensor= obj
                 * @returns onnxruntime.experimental.fbs.Tensor|null
                 */
                indices(obj) {
                    let offset = this.bb.__offset(this.bb_pos, 6);
                    return offset ? (obj || new onnxruntime.experimental.fbs.Tensor())
                        .__init(this.bb.__indirect(this.bb_pos + offset), this.bb) :
                        null;
                }
                /**
                 * @param number index
                 * @returns flatbuffers.Long
                 */
                dims(index) {
                    let offset = this.bb.__offset(this.bb_pos, 8);
                    return offset ? this.bb.readInt64(this.bb.__vector(this.bb_pos + offset) + index * 8) :
                        this.bb.createLong(0, 0);
                }
                /**
                 * @returns number
                 */
                dimsLength() {
                    let offset = this.bb.__offset(this.bb_pos, 8);
                    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
                }
                /**
                 * @param flatbuffers.Builder builder
                 */
                static startSparseTensor(builder) {
                    builder.startObject(3);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset valuesOffset
                 */
                static addValues(builder, valuesOffset) {
                    builder.addFieldOffset(0, valuesOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset indicesOffset
                 */
                static addIndices(builder, indicesOffset) {
                    builder.addFieldOffset(1, indicesOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset dimsOffset
                 */
                static addDims(builder, dimsOffset) {
                    builder.addFieldOffset(2, dimsOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param Array.<flatbuffers.Long> data
                 * @returns flatbuffers.Offset
                 */
                static createDimsVector(builder, data) {
                    builder.startVector(8, data.length, 8);
                    for (let i = data.length - 1; i >= 0; i--) {
                        builder.addInt64(data[i]);
                    }
                    return builder.endVector();
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number numElems
                 */
                static startDimsVector(builder, numElems) {
                    builder.startVector(8, numElems, 8);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @returns flatbuffers.Offset
                 */
                static endSparseTensor(builder) {
                    let offset = builder.endObject();
                    return offset;
                }
                static createSparseTensor(builder, valuesOffset, indicesOffset, dimsOffset) {
                    SparseTensor.startSparseTensor(builder);
                    SparseTensor.addValues(builder, valuesOffset);
                    SparseTensor.addIndices(builder, indicesOffset);
                    SparseTensor.addDims(builder, dimsOffset);
                    return SparseTensor.endSparseTensor(builder);
                }
            }
            fbs.SparseTensor = SparseTensor;
        })(fbs = experimental.fbs || (experimental.fbs = {}));
    })(experimental = onnxruntime.experimental || (onnxruntime.experimental = {}));
})(onnxruntime = exports.onnxruntime || (exports.onnxruntime = {}));
/**
 * @constructor
 */
(function (onnxruntime) {
    var experimental;
    (function (experimental) {
        var fbs;
        (function (fbs) {
            class Attribute {
                constructor() {
                    this.bb = null;
                    this.bb_pos = 0;
                }
                /**
                 * @param number i
                 * @param flatbuffers.ByteBuffer bb
                 * @returns Attribute
                 */
                __init(i, bb) {
                    this.bb_pos = i;
                    this.bb = bb;
                    return this;
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param Attribute= obj
                 * @returns Attribute
                 */
                static getRootAsAttribute(bb, obj) {
                    return (obj || new Attribute()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param Attribute= obj
                 * @returns Attribute
                 */
                static getSizePrefixedRootAsAttribute(bb, obj) {
                    bb.setPosition(bb.position() + flatbuffers_1.flatbuffers.SIZE_PREFIX_LENGTH);
                    return (obj || new Attribute()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                name(optionalEncoding) {
                    let offset = this.bb.__offset(this.bb_pos, 4);
                    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
                }
                docString(optionalEncoding) {
                    let offset = this.bb.__offset(this.bb_pos, 6);
                    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
                }
                /**
                 * @returns onnxruntime.experimental.fbs.AttributeType
                 */
                type() {
                    let offset = this.bb.__offset(this.bb_pos, 8);
                    return offset ? /**  */ (this.bb.readInt32(this.bb_pos + offset)) :
                        onnxruntime.experimental.fbs.AttributeType.UNDEFINED;
                }
                /**
                 * @returns number
                 */
                f() {
                    let offset = this.bb.__offset(this.bb_pos, 10);
                    return offset ? this.bb.readFloat32(this.bb_pos + offset) : 0.0;
                }
                /**
                 * @returns flatbuffers.Long
                 */
                i() {
                    let offset = this.bb.__offset(this.bb_pos, 12);
                    return offset ? this.bb.readInt64(this.bb_pos + offset) : this.bb.createLong(0, 0);
                }
                s(optionalEncoding) {
                    let offset = this.bb.__offset(this.bb_pos, 14);
                    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
                }
                /**
                 * @param onnxruntime.experimental.fbs.Tensor= obj
                 * @returns onnxruntime.experimental.fbs.Tensor|null
                 */
                t(obj) {
                    let offset = this.bb.__offset(this.bb_pos, 16);
                    return offset ? (obj || new onnxruntime.experimental.fbs.Tensor())
                        .__init(this.bb.__indirect(this.bb_pos + offset), this.bb) :
                        null;
                }
                /**
                 * @param onnxruntime.experimental.fbs.Graph= obj
                 * @returns onnxruntime.experimental.fbs.Graph|null
                 */
                g(obj) {
                    let offset = this.bb.__offset(this.bb_pos, 18);
                    return offset ? (obj || new onnxruntime.experimental.fbs.Graph())
                        .__init(this.bb.__indirect(this.bb_pos + offset), this.bb) :
                        null;
                }
                /**
                 * @param number index
                 * @returns number
                 */
                floats(index) {
                    let offset = this.bb.__offset(this.bb_pos, 20);
                    return offset ? this.bb.readFloat32(this.bb.__vector(this.bb_pos + offset) + index * 4) : 0;
                }
                /**
                 * @returns number
                 */
                floatsLength() {
                    let offset = this.bb.__offset(this.bb_pos, 20);
                    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
                }
                /**
                 * @returns Float32Array
                 */
                floatsArray() {
                    let offset = this.bb.__offset(this.bb_pos, 20);
                    return offset ?
                        new Float32Array(this.bb.bytes().buffer, this.bb.bytes().byteOffset + this.bb.__vector(this.bb_pos + offset), this.bb.__vector_len(this.bb_pos + offset)) :
                        null;
                }
                /**
                 * @param number index
                 * @returns flatbuffers.Long
                 */
                ints(index) {
                    let offset = this.bb.__offset(this.bb_pos, 22);
                    return offset ? this.bb.readInt64(this.bb.__vector(this.bb_pos + offset) + index * 8) :
                        this.bb.createLong(0, 0);
                }
                /**
                 * @returns number
                 */
                intsLength() {
                    let offset = this.bb.__offset(this.bb_pos, 22);
                    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
                }
                strings(index, optionalEncoding) {
                    let offset = this.bb.__offset(this.bb_pos, 24);
                    return offset ? this.bb.__string(this.bb.__vector(this.bb_pos + offset) + index * 4, optionalEncoding) : null;
                }
                /**
                 * @returns number
                 */
                stringsLength() {
                    let offset = this.bb.__offset(this.bb_pos, 24);
                    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
                }
                /**
                 * @param number index
                 * @param onnxruntime.experimental.fbs.Tensor= obj
                 * @returns onnxruntime.experimental.fbs.Tensor
                 */
                tensors(index, obj) {
                    let offset = this.bb.__offset(this.bb_pos, 26);
                    return offset ? (obj || new onnxruntime.experimental.fbs.Tensor())
                        .__init(this.bb.__indirect(this.bb.__vector(this.bb_pos + offset) + index * 4), this.bb) :
                        null;
                }
                /**
                 * @returns number
                 */
                tensorsLength() {
                    let offset = this.bb.__offset(this.bb_pos, 26);
                    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
                }
                /**
                 * @param number index
                 * @param onnxruntime.experimental.fbs.Graph= obj
                 * @returns onnxruntime.experimental.fbs.Graph
                 */
                graphs(index, obj) {
                    let offset = this.bb.__offset(this.bb_pos, 28);
                    return offset ? (obj || new onnxruntime.experimental.fbs.Graph())
                        .__init(this.bb.__indirect(this.bb.__vector(this.bb_pos + offset) + index * 4), this.bb) :
                        null;
                }
                /**
                 * @returns number
                 */
                graphsLength() {
                    let offset = this.bb.__offset(this.bb_pos, 28);
                    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
                }
                /**
                 * @param flatbuffers.Builder builder
                 */
                static startAttribute(builder) {
                    builder.startObject(13);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset nameOffset
                 */
                static addName(builder, nameOffset) {
                    builder.addFieldOffset(0, nameOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset docStringOffset
                 */
                static addDocString(builder, docStringOffset) {
                    builder.addFieldOffset(1, docStringOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param onnxruntime.experimental.fbs.AttributeType type
                 */
                static addType(builder, type) {
                    builder.addFieldInt32(2, type, onnxruntime.experimental.fbs.AttributeType.UNDEFINED);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number f
                 */
                static addF(builder, f) {
                    builder.addFieldFloat32(3, f, 0.0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Long i
                 */
                static addI(builder, i) {
                    builder.addFieldInt64(4, i, builder.createLong(0, 0));
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset sOffset
                 */
                static addS(builder, sOffset) {
                    builder.addFieldOffset(5, sOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset tOffset
                 */
                static addT(builder, tOffset) {
                    builder.addFieldOffset(6, tOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset gOffset
                 */
                static addG(builder, gOffset) {
                    builder.addFieldOffset(7, gOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset floatsOffset
                 */
                static addFloats(builder, floatsOffset) {
                    builder.addFieldOffset(8, floatsOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param Array.<number> data
                 * @returns flatbuffers.Offset
                 */
                static createFloatsVector(builder, data) {
                    builder.startVector(4, data.length, 4);
                    for (let i = data.length - 1; i >= 0; i--) {
                        builder.addFloat32(data[i]);
                    }
                    return builder.endVector();
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number numElems
                 */
                static startFloatsVector(builder, numElems) {
                    builder.startVector(4, numElems, 4);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset intsOffset
                 */
                static addInts(builder, intsOffset) {
                    builder.addFieldOffset(9, intsOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param Array.<flatbuffers.Long> data
                 * @returns flatbuffers.Offset
                 */
                static createIntsVector(builder, data) {
                    builder.startVector(8, data.length, 8);
                    for (let i = data.length - 1; i >= 0; i--) {
                        builder.addInt64(data[i]);
                    }
                    return builder.endVector();
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number numElems
                 */
                static startIntsVector(builder, numElems) {
                    builder.startVector(8, numElems, 8);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset stringsOffset
                 */
                static addStrings(builder, stringsOffset) {
                    builder.addFieldOffset(10, stringsOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param Array.<flatbuffers.Offset> data
                 * @returns flatbuffers.Offset
                 */
                static createStringsVector(builder, data) {
                    builder.startVector(4, data.length, 4);
                    for (let i = data.length - 1; i >= 0; i--) {
                        builder.addOffset(data[i]);
                    }
                    return builder.endVector();
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number numElems
                 */
                static startStringsVector(builder, numElems) {
                    builder.startVector(4, numElems, 4);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset tensorsOffset
                 */
                static addTensors(builder, tensorsOffset) {
                    builder.addFieldOffset(11, tensorsOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param Array.<flatbuffers.Offset> data
                 * @returns flatbuffers.Offset
                 */
                static createTensorsVector(builder, data) {
                    builder.startVector(4, data.length, 4);
                    for (let i = data.length - 1; i >= 0; i--) {
                        builder.addOffset(data[i]);
                    }
                    return builder.endVector();
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number numElems
                 */
                static startTensorsVector(builder, numElems) {
                    builder.startVector(4, numElems, 4);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset graphsOffset
                 */
                static addGraphs(builder, graphsOffset) {
                    builder.addFieldOffset(12, graphsOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param Array.<flatbuffers.Offset> data
                 * @returns flatbuffers.Offset
                 */
                static createGraphsVector(builder, data) {
                    builder.startVector(4, data.length, 4);
                    for (let i = data.length - 1; i >= 0; i--) {
                        builder.addOffset(data[i]);
                    }
                    return builder.endVector();
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number numElems
                 */
                static startGraphsVector(builder, numElems) {
                    builder.startVector(4, numElems, 4);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @returns flatbuffers.Offset
                 */
                static endAttribute(builder) {
                    let offset = builder.endObject();
                    return offset;
                }
                static createAttribute(builder, nameOffset, docStringOffset, type, f, i, sOffset, tOffset, gOffset, floatsOffset, intsOffset, stringsOffset, tensorsOffset, graphsOffset) {
                    Attribute.startAttribute(builder);
                    Attribute.addName(builder, nameOffset);
                    Attribute.addDocString(builder, docStringOffset);
                    Attribute.addType(builder, type);
                    Attribute.addF(builder, f);
                    Attribute.addI(builder, i);
                    Attribute.addS(builder, sOffset);
                    Attribute.addT(builder, tOffset);
                    Attribute.addG(builder, gOffset);
                    Attribute.addFloats(builder, floatsOffset);
                    Attribute.addInts(builder, intsOffset);
                    Attribute.addStrings(builder, stringsOffset);
                    Attribute.addTensors(builder, tensorsOffset);
                    Attribute.addGraphs(builder, graphsOffset);
                    return Attribute.endAttribute(builder);
                }
            }
            fbs.Attribute = Attribute;
        })(fbs = experimental.fbs || (experimental.fbs = {}));
    })(experimental = onnxruntime.experimental || (onnxruntime.experimental = {}));
})(onnxruntime = exports.onnxruntime || (exports.onnxruntime = {}));
/**
 * @constructor
 */
(function (onnxruntime) {
    var experimental;
    (function (experimental) {
        var fbs;
        (function (fbs) {
            class Graph {
                constructor() {
                    this.bb = null;
                    this.bb_pos = 0;
                }
                /**
                 * @param number i
                 * @param flatbuffers.ByteBuffer bb
                 * @returns Graph
                 */
                __init(i, bb) {
                    this.bb_pos = i;
                    this.bb = bb;
                    return this;
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param Graph= obj
                 * @returns Graph
                 */
                static getRootAsGraph(bb, obj) {
                    return (obj || new Graph()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param Graph= obj
                 * @returns Graph
                 */
                static getSizePrefixedRootAsGraph(bb, obj) {
                    bb.setPosition(bb.position() + flatbuffers_1.flatbuffers.SIZE_PREFIX_LENGTH);
                    return (obj || new Graph()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                /**
                 * @param number index
                 * @param onnxruntime.experimental.fbs.Tensor= obj
                 * @returns onnxruntime.experimental.fbs.Tensor
                 */
                initializers(index, obj) {
                    let offset = this.bb.__offset(this.bb_pos, 4);
                    return offset ? (obj || new onnxruntime.experimental.fbs.Tensor())
                        .__init(this.bb.__indirect(this.bb.__vector(this.bb_pos + offset) + index * 4), this.bb) :
                        null;
                }
                /**
                 * @returns number
                 */
                initializersLength() {
                    let offset = this.bb.__offset(this.bb_pos, 4);
                    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
                }
                /**
                 * @param number index
                 * @param onnxruntime.experimental.fbs.ValueInfo= obj
                 * @returns onnxruntime.experimental.fbs.ValueInfo
                 */
                nodeArgs(index, obj) {
                    let offset = this.bb.__offset(this.bb_pos, 6);
                    return offset ? (obj || new onnxruntime.experimental.fbs.ValueInfo())
                        .__init(this.bb.__indirect(this.bb.__vector(this.bb_pos + offset) + index * 4), this.bb) :
                        null;
                }
                /**
                 * @returns number
                 */
                nodeArgsLength() {
                    let offset = this.bb.__offset(this.bb_pos, 6);
                    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
                }
                /**
                 * @param number index
                 * @param onnxruntime.experimental.fbs.Node= obj
                 * @returns onnxruntime.experimental.fbs.Node
                 */
                nodes(index, obj) {
                    let offset = this.bb.__offset(this.bb_pos, 8);
                    return offset ? (obj || new onnxruntime.experimental.fbs.Node())
                        .__init(this.bb.__indirect(this.bb.__vector(this.bb_pos + offset) + index * 4), this.bb) :
                        null;
                }
                /**
                 * @returns number
                 */
                nodesLength() {
                    let offset = this.bb.__offset(this.bb_pos, 8);
                    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
                }
                /**
                 * @returns number
                 */
                maxNodeIndex() {
                    let offset = this.bb.__offset(this.bb_pos, 10);
                    return offset ? this.bb.readUint32(this.bb_pos + offset) : 0;
                }
                /**
                 * @param number index
                 * @param onnxruntime.experimental.fbs.NodeEdge= obj
                 * @returns onnxruntime.experimental.fbs.NodeEdge
                 */
                nodeEdges(index, obj) {
                    let offset = this.bb.__offset(this.bb_pos, 12);
                    return offset ? (obj || new onnxruntime.experimental.fbs.NodeEdge())
                        .__init(this.bb.__indirect(this.bb.__vector(this.bb_pos + offset) + index * 4), this.bb) :
                        null;
                }
                /**
                 * @returns number
                 */
                nodeEdgesLength() {
                    let offset = this.bb.__offset(this.bb_pos, 12);
                    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
                }
                inputs(index, optionalEncoding) {
                    let offset = this.bb.__offset(this.bb_pos, 14);
                    return offset ? this.bb.__string(this.bb.__vector(this.bb_pos + offset) + index * 4, optionalEncoding) : null;
                }
                /**
                 * @returns number
                 */
                inputsLength() {
                    let offset = this.bb.__offset(this.bb_pos, 14);
                    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
                }
                outputs(index, optionalEncoding) {
                    let offset = this.bb.__offset(this.bb_pos, 16);
                    return offset ? this.bb.__string(this.bb.__vector(this.bb_pos + offset) + index * 4, optionalEncoding) : null;
                }
                /**
                 * @returns number
                 */
                outputsLength() {
                    let offset = this.bb.__offset(this.bb_pos, 16);
                    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
                }
                /**
                 * @param number index
                 * @param onnxruntime.experimental.fbs.SparseTensor= obj
                 * @returns onnxruntime.experimental.fbs.SparseTensor
                 */
                sparseInitializers(index, obj) {
                    let offset = this.bb.__offset(this.bb_pos, 18);
                    return offset ? (obj || new onnxruntime.experimental.fbs.SparseTensor())
                        .__init(this.bb.__indirect(this.bb.__vector(this.bb_pos + offset) + index * 4), this.bb) :
                        null;
                }
                /**
                 * @returns number
                 */
                sparseInitializersLength() {
                    let offset = this.bb.__offset(this.bb_pos, 18);
                    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
                }
                /**
                 * @param flatbuffers.Builder builder
                 */
                static startGraph(builder) {
                    builder.startObject(8);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset initializersOffset
                 */
                static addInitializers(builder, initializersOffset) {
                    builder.addFieldOffset(0, initializersOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param Array.<flatbuffers.Offset> data
                 * @returns flatbuffers.Offset
                 */
                static createInitializersVector(builder, data) {
                    builder.startVector(4, data.length, 4);
                    for (let i = data.length - 1; i >= 0; i--) {
                        builder.addOffset(data[i]);
                    }
                    return builder.endVector();
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number numElems
                 */
                static startInitializersVector(builder, numElems) {
                    builder.startVector(4, numElems, 4);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset nodeArgsOffset
                 */
                static addNodeArgs(builder, nodeArgsOffset) {
                    builder.addFieldOffset(1, nodeArgsOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param Array.<flatbuffers.Offset> data
                 * @returns flatbuffers.Offset
                 */
                static createNodeArgsVector(builder, data) {
                    builder.startVector(4, data.length, 4);
                    for (let i = data.length - 1; i >= 0; i--) {
                        builder.addOffset(data[i]);
                    }
                    return builder.endVector();
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number numElems
                 */
                static startNodeArgsVector(builder, numElems) {
                    builder.startVector(4, numElems, 4);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset nodesOffset
                 */
                static addNodes(builder, nodesOffset) {
                    builder.addFieldOffset(2, nodesOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param Array.<flatbuffers.Offset> data
                 * @returns flatbuffers.Offset
                 */
                static createNodesVector(builder, data) {
                    builder.startVector(4, data.length, 4);
                    for (let i = data.length - 1; i >= 0; i--) {
                        builder.addOffset(data[i]);
                    }
                    return builder.endVector();
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number numElems
                 */
                static startNodesVector(builder, numElems) {
                    builder.startVector(4, numElems, 4);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number maxNodeIndex
                 */
                static addMaxNodeIndex(builder, maxNodeIndex) {
                    builder.addFieldInt32(3, maxNodeIndex, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset nodeEdgesOffset
                 */
                static addNodeEdges(builder, nodeEdgesOffset) {
                    builder.addFieldOffset(4, nodeEdgesOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param Array.<flatbuffers.Offset> data
                 * @returns flatbuffers.Offset
                 */
                static createNodeEdgesVector(builder, data) {
                    builder.startVector(4, data.length, 4);
                    for (let i = data.length - 1; i >= 0; i--) {
                        builder.addOffset(data[i]);
                    }
                    return builder.endVector();
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number numElems
                 */
                static startNodeEdgesVector(builder, numElems) {
                    builder.startVector(4, numElems, 4);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset inputsOffset
                 */
                static addInputs(builder, inputsOffset) {
                    builder.addFieldOffset(5, inputsOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param Array.<flatbuffers.Offset> data
                 * @returns flatbuffers.Offset
                 */
                static createInputsVector(builder, data) {
                    builder.startVector(4, data.length, 4);
                    for (let i = data.length - 1; i >= 0; i--) {
                        builder.addOffset(data[i]);
                    }
                    return builder.endVector();
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number numElems
                 */
                static startInputsVector(builder, numElems) {
                    builder.startVector(4, numElems, 4);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset outputsOffset
                 */
                static addOutputs(builder, outputsOffset) {
                    builder.addFieldOffset(6, outputsOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param Array.<flatbuffers.Offset> data
                 * @returns flatbuffers.Offset
                 */
                static createOutputsVector(builder, data) {
                    builder.startVector(4, data.length, 4);
                    for (let i = data.length - 1; i >= 0; i--) {
                        builder.addOffset(data[i]);
                    }
                    return builder.endVector();
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number numElems
                 */
                static startOutputsVector(builder, numElems) {
                    builder.startVector(4, numElems, 4);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset sparseInitializersOffset
                 */
                static addSparseInitializers(builder, sparseInitializersOffset) {
                    builder.addFieldOffset(7, sparseInitializersOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param Array.<flatbuffers.Offset> data
                 * @returns flatbuffers.Offset
                 */
                static createSparseInitializersVector(builder, data) {
                    builder.startVector(4, data.length, 4);
                    for (let i = data.length - 1; i >= 0; i--) {
                        builder.addOffset(data[i]);
                    }
                    return builder.endVector();
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number numElems
                 */
                static startSparseInitializersVector(builder, numElems) {
                    builder.startVector(4, numElems, 4);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @returns flatbuffers.Offset
                 */
                static endGraph(builder) {
                    let offset = builder.endObject();
                    return offset;
                }
                static createGraph(builder, initializersOffset, nodeArgsOffset, nodesOffset, maxNodeIndex, nodeEdgesOffset, inputsOffset, outputsOffset, sparseInitializersOffset) {
                    Graph.startGraph(builder);
                    Graph.addInitializers(builder, initializersOffset);
                    Graph.addNodeArgs(builder, nodeArgsOffset);
                    Graph.addNodes(builder, nodesOffset);
                    Graph.addMaxNodeIndex(builder, maxNodeIndex);
                    Graph.addNodeEdges(builder, nodeEdgesOffset);
                    Graph.addInputs(builder, inputsOffset);
                    Graph.addOutputs(builder, outputsOffset);
                    Graph.addSparseInitializers(builder, sparseInitializersOffset);
                    return Graph.endGraph(builder);
                }
            }
            fbs.Graph = Graph;
        })(fbs = experimental.fbs || (experimental.fbs = {}));
    })(experimental = onnxruntime.experimental || (onnxruntime.experimental = {}));
})(onnxruntime = exports.onnxruntime || (exports.onnxruntime = {}));
/**
 * @constructor
 */
(function (onnxruntime) {
    var experimental;
    (function (experimental) {
        var fbs;
        (function (fbs) {
            class Model {
                constructor() {
                    this.bb = null;
                    this.bb_pos = 0;
                }
                /**
                 * @param number i
                 * @param flatbuffers.ByteBuffer bb
                 * @returns Model
                 */
                __init(i, bb) {
                    this.bb_pos = i;
                    this.bb = bb;
                    return this;
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param Model= obj
                 * @returns Model
                 */
                static getRootAsModel(bb, obj) {
                    return (obj || new Model()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param Model= obj
                 * @returns Model
                 */
                static getSizePrefixedRootAsModel(bb, obj) {
                    bb.setPosition(bb.position() + flatbuffers_1.flatbuffers.SIZE_PREFIX_LENGTH);
                    return (obj || new Model()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                /**
                 * @returns flatbuffers.Long
                 */
                irVersion() {
                    let offset = this.bb.__offset(this.bb_pos, 4);
                    return offset ? this.bb.readInt64(this.bb_pos + offset) : this.bb.createLong(0, 0);
                }
                /**
                 * @param number index
                 * @param onnxruntime.experimental.fbs.OperatorSetId= obj
                 * @returns onnxruntime.experimental.fbs.OperatorSetId
                 */
                opsetImport(index, obj) {
                    let offset = this.bb.__offset(this.bb_pos, 6);
                    return offset ? (obj || new onnxruntime.experimental.fbs.OperatorSetId())
                        .__init(this.bb.__indirect(this.bb.__vector(this.bb_pos + offset) + index * 4), this.bb) :
                        null;
                }
                /**
                 * @returns number
                 */
                opsetImportLength() {
                    let offset = this.bb.__offset(this.bb_pos, 6);
                    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
                }
                producerName(optionalEncoding) {
                    let offset = this.bb.__offset(this.bb_pos, 8);
                    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
                }
                producerVersion(optionalEncoding) {
                    let offset = this.bb.__offset(this.bb_pos, 10);
                    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
                }
                domain(optionalEncoding) {
                    let offset = this.bb.__offset(this.bb_pos, 12);
                    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
                }
                /**
                 * @returns flatbuffers.Long
                 */
                modelVersion() {
                    let offset = this.bb.__offset(this.bb_pos, 14);
                    return offset ? this.bb.readInt64(this.bb_pos + offset) : this.bb.createLong(0, 0);
                }
                docString(optionalEncoding) {
                    let offset = this.bb.__offset(this.bb_pos, 16);
                    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
                }
                /**
                 * @param onnxruntime.experimental.fbs.Graph= obj
                 * @returns onnxruntime.experimental.fbs.Graph|null
                 */
                graph(obj) {
                    let offset = this.bb.__offset(this.bb_pos, 18);
                    return offset ? (obj || new onnxruntime.experimental.fbs.Graph())
                        .__init(this.bb.__indirect(this.bb_pos + offset), this.bb) :
                        null;
                }
                graphDocString(optionalEncoding) {
                    let offset = this.bb.__offset(this.bb_pos, 20);
                    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
                }
                /**
                 * @param flatbuffers.Builder builder
                 */
                static startModel(builder) {
                    builder.startObject(9);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Long irVersion
                 */
                static addIrVersion(builder, irVersion) {
                    builder.addFieldInt64(0, irVersion, builder.createLong(0, 0));
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset opsetImportOffset
                 */
                static addOpsetImport(builder, opsetImportOffset) {
                    builder.addFieldOffset(1, opsetImportOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param Array.<flatbuffers.Offset> data
                 * @returns flatbuffers.Offset
                 */
                static createOpsetImportVector(builder, data) {
                    builder.startVector(4, data.length, 4);
                    for (let i = data.length - 1; i >= 0; i--) {
                        builder.addOffset(data[i]);
                    }
                    return builder.endVector();
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number numElems
                 */
                static startOpsetImportVector(builder, numElems) {
                    builder.startVector(4, numElems, 4);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset producerNameOffset
                 */
                static addProducerName(builder, producerNameOffset) {
                    builder.addFieldOffset(2, producerNameOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset producerVersionOffset
                 */
                static addProducerVersion(builder, producerVersionOffset) {
                    builder.addFieldOffset(3, producerVersionOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset domainOffset
                 */
                static addDomain(builder, domainOffset) {
                    builder.addFieldOffset(4, domainOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Long modelVersion
                 */
                static addModelVersion(builder, modelVersion) {
                    builder.addFieldInt64(5, modelVersion, builder.createLong(0, 0));
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset docStringOffset
                 */
                static addDocString(builder, docStringOffset) {
                    builder.addFieldOffset(6, docStringOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset graphOffset
                 */
                static addGraph(builder, graphOffset) {
                    builder.addFieldOffset(7, graphOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset graphDocStringOffset
                 */
                static addGraphDocString(builder, graphDocStringOffset) {
                    builder.addFieldOffset(8, graphDocStringOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @returns flatbuffers.Offset
                 */
                static endModel(builder) {
                    let offset = builder.endObject();
                    return offset;
                }
                static createModel(builder, irVersion, opsetImportOffset, producerNameOffset, producerVersionOffset, domainOffset, modelVersion, docStringOffset, graphOffset, graphDocStringOffset) {
                    Model.startModel(builder);
                    Model.addIrVersion(builder, irVersion);
                    Model.addOpsetImport(builder, opsetImportOffset);
                    Model.addProducerName(builder, producerNameOffset);
                    Model.addProducerVersion(builder, producerVersionOffset);
                    Model.addDomain(builder, domainOffset);
                    Model.addModelVersion(builder, modelVersion);
                    Model.addDocString(builder, docStringOffset);
                    Model.addGraph(builder, graphOffset);
                    Model.addGraphDocString(builder, graphDocStringOffset);
                    return Model.endModel(builder);
                }
            }
            fbs.Model = Model;
        })(fbs = experimental.fbs || (experimental.fbs = {}));
    })(experimental = onnxruntime.experimental || (onnxruntime.experimental = {}));
})(onnxruntime = exports.onnxruntime || (exports.onnxruntime = {}));
/**
 * @constructor
 */
(function (onnxruntime) {
    var experimental;
    (function (experimental) {
        var fbs;
        (function (fbs) {
            class KernelCreateInfos {
                constructor() {
                    this.bb = null;
                    this.bb_pos = 0;
                }
                /**
                 * @param number i
                 * @param flatbuffers.ByteBuffer bb
                 * @returns KernelCreateInfos
                 */
                __init(i, bb) {
                    this.bb_pos = i;
                    this.bb = bb;
                    return this;
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param KernelCreateInfos= obj
                 * @returns KernelCreateInfos
                 */
                static getRootAsKernelCreateInfos(bb, obj) {
                    return (obj || new KernelCreateInfos()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param KernelCreateInfos= obj
                 * @returns KernelCreateInfos
                 */
                static getSizePrefixedRootAsKernelCreateInfos(bb, obj) {
                    bb.setPosition(bb.position() + flatbuffers_1.flatbuffers.SIZE_PREFIX_LENGTH);
                    return (obj || new KernelCreateInfos()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                /**
                 * @param number index
                 * @returns number
                 */
                nodeIndices(index) {
                    let offset = this.bb.__offset(this.bb_pos, 4);
                    return offset ? this.bb.readUint32(this.bb.__vector(this.bb_pos + offset) + index * 4) : 0;
                }
                /**
                 * @returns number
                 */
                nodeIndicesLength() {
                    let offset = this.bb.__offset(this.bb_pos, 4);
                    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
                }
                /**
                 * @returns Uint32Array
                 */
                nodeIndicesArray() {
                    let offset = this.bb.__offset(this.bb_pos, 4);
                    return offset ?
                        new Uint32Array(this.bb.bytes().buffer, this.bb.bytes().byteOffset + this.bb.__vector(this.bb_pos + offset), this.bb.__vector_len(this.bb_pos + offset)) :
                        null;
                }
                /**
                 * @param number index
                 * @returns flatbuffers.Long
                 */
                kernelDefHashes(index) {
                    let offset = this.bb.__offset(this.bb_pos, 6);
                    return offset ? this.bb.readUint64(this.bb.__vector(this.bb_pos + offset) + index * 8) :
                        this.bb.createLong(0, 0);
                }
                /**
                 * @returns number
                 */
                kernelDefHashesLength() {
                    let offset = this.bb.__offset(this.bb_pos, 6);
                    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
                }
                /**
                 * @param flatbuffers.Builder builder
                 */
                static startKernelCreateInfos(builder) {
                    builder.startObject(2);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset nodeIndicesOffset
                 */
                static addNodeIndices(builder, nodeIndicesOffset) {
                    builder.addFieldOffset(0, nodeIndicesOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param Array.<number> data
                 * @returns flatbuffers.Offset
                 */
                static createNodeIndicesVector(builder, data) {
                    builder.startVector(4, data.length, 4);
                    for (let i = data.length - 1; i >= 0; i--) {
                        builder.addInt32(data[i]);
                    }
                    return builder.endVector();
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number numElems
                 */
                static startNodeIndicesVector(builder, numElems) {
                    builder.startVector(4, numElems, 4);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset kernelDefHashesOffset
                 */
                static addKernelDefHashes(builder, kernelDefHashesOffset) {
                    builder.addFieldOffset(1, kernelDefHashesOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param Array.<flatbuffers.Long> data
                 * @returns flatbuffers.Offset
                 */
                static createKernelDefHashesVector(builder, data) {
                    builder.startVector(8, data.length, 8);
                    for (let i = data.length - 1; i >= 0; i--) {
                        builder.addInt64(data[i]);
                    }
                    return builder.endVector();
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number numElems
                 */
                static startKernelDefHashesVector(builder, numElems) {
                    builder.startVector(8, numElems, 8);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @returns flatbuffers.Offset
                 */
                static endKernelCreateInfos(builder) {
                    let offset = builder.endObject();
                    return offset;
                }
                static createKernelCreateInfos(builder, nodeIndicesOffset, kernelDefHashesOffset) {
                    KernelCreateInfos.startKernelCreateInfos(builder);
                    KernelCreateInfos.addNodeIndices(builder, nodeIndicesOffset);
                    KernelCreateInfos.addKernelDefHashes(builder, kernelDefHashesOffset);
                    return KernelCreateInfos.endKernelCreateInfos(builder);
                }
            }
            fbs.KernelCreateInfos = KernelCreateInfos;
        })(fbs = experimental.fbs || (experimental.fbs = {}));
    })(experimental = onnxruntime.experimental || (onnxruntime.experimental = {}));
})(onnxruntime = exports.onnxruntime || (exports.onnxruntime = {}));
/**
 * @constructor
 */
(function (onnxruntime) {
    var experimental;
    (function (experimental) {
        var fbs;
        (function (fbs) {
            class SubGraphSessionState {
                constructor() {
                    this.bb = null;
                    this.bb_pos = 0;
                }
                /**
                 * @param number i
                 * @param flatbuffers.ByteBuffer bb
                 * @returns SubGraphSessionState
                 */
                __init(i, bb) {
                    this.bb_pos = i;
                    this.bb = bb;
                    return this;
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param SubGraphSessionState= obj
                 * @returns SubGraphSessionState
                 */
                static getRootAsSubGraphSessionState(bb, obj) {
                    return (obj || new SubGraphSessionState()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param SubGraphSessionState= obj
                 * @returns SubGraphSessionState
                 */
                static getSizePrefixedRootAsSubGraphSessionState(bb, obj) {
                    bb.setPosition(bb.position() + flatbuffers_1.flatbuffers.SIZE_PREFIX_LENGTH);
                    return (obj || new SubGraphSessionState()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                graphId(optionalEncoding) {
                    let offset = this.bb.__offset(this.bb_pos, 4);
                    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
                }
                /**
                 * @param onnxruntime.experimental.fbs.SessionState= obj
                 * @returns onnxruntime.experimental.fbs.SessionState|null
                 */
                sessionState(obj) {
                    let offset = this.bb.__offset(this.bb_pos, 6);
                    return offset ? (obj || new onnxruntime.experimental.fbs.SessionState())
                        .__init(this.bb.__indirect(this.bb_pos + offset), this.bb) :
                        null;
                }
                /**
                 * @param flatbuffers.Builder builder
                 */
                static startSubGraphSessionState(builder) {
                    builder.startObject(2);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset graphIdOffset
                 */
                static addGraphId(builder, graphIdOffset) {
                    builder.addFieldOffset(0, graphIdOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset sessionStateOffset
                 */
                static addSessionState(builder, sessionStateOffset) {
                    builder.addFieldOffset(1, sessionStateOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @returns flatbuffers.Offset
                 */
                static endSubGraphSessionState(builder) {
                    let offset = builder.endObject();
                    builder.requiredField(offset, 4); // graph_id
                    return offset;
                }
                static createSubGraphSessionState(builder, graphIdOffset, sessionStateOffset) {
                    SubGraphSessionState.startSubGraphSessionState(builder);
                    SubGraphSessionState.addGraphId(builder, graphIdOffset);
                    SubGraphSessionState.addSessionState(builder, sessionStateOffset);
                    return SubGraphSessionState.endSubGraphSessionState(builder);
                }
            }
            fbs.SubGraphSessionState = SubGraphSessionState;
        })(fbs = experimental.fbs || (experimental.fbs = {}));
    })(experimental = onnxruntime.experimental || (onnxruntime.experimental = {}));
})(onnxruntime = exports.onnxruntime || (exports.onnxruntime = {}));
/**
 * @constructor
 */
(function (onnxruntime) {
    var experimental;
    (function (experimental) {
        var fbs;
        (function (fbs) {
            class SessionState {
                constructor() {
                    this.bb = null;
                    this.bb_pos = 0;
                }
                /**
                 * @param number i
                 * @param flatbuffers.ByteBuffer bb
                 * @returns SessionState
                 */
                __init(i, bb) {
                    this.bb_pos = i;
                    this.bb = bb;
                    return this;
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param SessionState= obj
                 * @returns SessionState
                 */
                static getRootAsSessionState(bb, obj) {
                    return (obj || new SessionState()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param SessionState= obj
                 * @returns SessionState
                 */
                static getSizePrefixedRootAsSessionState(bb, obj) {
                    bb.setPosition(bb.position() + flatbuffers_1.flatbuffers.SIZE_PREFIX_LENGTH);
                    return (obj || new SessionState()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                /**
                 * @param onnxruntime.experimental.fbs.KernelCreateInfos= obj
                 * @returns onnxruntime.experimental.fbs.KernelCreateInfos|null
                 */
                kernels(obj) {
                    let offset = this.bb.__offset(this.bb_pos, 4);
                    return offset ? (obj || new onnxruntime.experimental.fbs.KernelCreateInfos())
                        .__init(this.bb.__indirect(this.bb_pos + offset), this.bb) :
                        null;
                }
                /**
                 * @param number index
                 * @param onnxruntime.experimental.fbs.SubGraphSessionState= obj
                 * @returns onnxruntime.experimental.fbs.SubGraphSessionState
                 */
                subGraphSessionStates(index, obj) {
                    let offset = this.bb.__offset(this.bb_pos, 6);
                    return offset ? (obj || new onnxruntime.experimental.fbs.SubGraphSessionState())
                        .__init(this.bb.__indirect(this.bb.__vector(this.bb_pos + offset) + index * 4), this.bb) :
                        null;
                }
                /**
                 * @returns number
                 */
                subGraphSessionStatesLength() {
                    let offset = this.bb.__offset(this.bb_pos, 6);
                    return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
                }
                /**
                 * @param flatbuffers.Builder builder
                 */
                static startSessionState(builder) {
                    builder.startObject(2);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset kernelsOffset
                 */
                static addKernels(builder, kernelsOffset) {
                    builder.addFieldOffset(0, kernelsOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset subGraphSessionStatesOffset
                 */
                static addSubGraphSessionStates(builder, subGraphSessionStatesOffset) {
                    builder.addFieldOffset(1, subGraphSessionStatesOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param Array.<flatbuffers.Offset> data
                 * @returns flatbuffers.Offset
                 */
                static createSubGraphSessionStatesVector(builder, data) {
                    builder.startVector(4, data.length, 4);
                    for (let i = data.length - 1; i >= 0; i--) {
                        builder.addOffset(data[i]);
                    }
                    return builder.endVector();
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param number numElems
                 */
                static startSubGraphSessionStatesVector(builder, numElems) {
                    builder.startVector(4, numElems, 4);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @returns flatbuffers.Offset
                 */
                static endSessionState(builder) {
                    let offset = builder.endObject();
                    return offset;
                }
                static createSessionState(builder, kernelsOffset, subGraphSessionStatesOffset) {
                    SessionState.startSessionState(builder);
                    SessionState.addKernels(builder, kernelsOffset);
                    SessionState.addSubGraphSessionStates(builder, subGraphSessionStatesOffset);
                    return SessionState.endSessionState(builder);
                }
            }
            fbs.SessionState = SessionState;
        })(fbs = experimental.fbs || (experimental.fbs = {}));
    })(experimental = onnxruntime.experimental || (onnxruntime.experimental = {}));
})(onnxruntime = exports.onnxruntime || (exports.onnxruntime = {}));
/**
 * @constructor
 */
(function (onnxruntime) {
    var experimental;
    (function (experimental) {
        var fbs;
        (function (fbs) {
            class InferenceSession {
                constructor() {
                    this.bb = null;
                    this.bb_pos = 0;
                }
                /**
                 * @param number i
                 * @param flatbuffers.ByteBuffer bb
                 * @returns InferenceSession
                 */
                __init(i, bb) {
                    this.bb_pos = i;
                    this.bb = bb;
                    return this;
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param InferenceSession= obj
                 * @returns InferenceSession
                 */
                static getRootAsInferenceSession(bb, obj) {
                    return (obj || new InferenceSession()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @param InferenceSession= obj
                 * @returns InferenceSession
                 */
                static getSizePrefixedRootAsInferenceSession(bb, obj) {
                    bb.setPosition(bb.position() + flatbuffers_1.flatbuffers.SIZE_PREFIX_LENGTH);
                    return (obj || new InferenceSession()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
                }
                /**
                 * @param flatbuffers.ByteBuffer bb
                 * @returns boolean
                 */
                static bufferHasIdentifier(bb) {
                    return bb.__has_identifier('ORTM');
                }
                ortVersion(optionalEncoding) {
                    let offset = this.bb.__offset(this.bb_pos, 4);
                    return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
                }
                /**
                 * @param onnxruntime.experimental.fbs.Model= obj
                 * @returns onnxruntime.experimental.fbs.Model|null
                 */
                model(obj) {
                    let offset = this.bb.__offset(this.bb_pos, 6);
                    return offset ? (obj || new onnxruntime.experimental.fbs.Model())
                        .__init(this.bb.__indirect(this.bb_pos + offset), this.bb) :
                        null;
                }
                /**
                 * @param onnxruntime.experimental.fbs.SessionState= obj
                 * @returns onnxruntime.experimental.fbs.SessionState|null
                 */
                sessionState(obj) {
                    let offset = this.bb.__offset(this.bb_pos, 8);
                    return offset ? (obj || new onnxruntime.experimental.fbs.SessionState())
                        .__init(this.bb.__indirect(this.bb_pos + offset), this.bb) :
                        null;
                }
                /**
                 * @param flatbuffers.Builder builder
                 */
                static startInferenceSession(builder) {
                    builder.startObject(3);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset ortVersionOffset
                 */
                static addOrtVersion(builder, ortVersionOffset) {
                    builder.addFieldOffset(0, ortVersionOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset modelOffset
                 */
                static addModel(builder, modelOffset) {
                    builder.addFieldOffset(1, modelOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset sessionStateOffset
                 */
                static addSessionState(builder, sessionStateOffset) {
                    builder.addFieldOffset(2, sessionStateOffset, 0);
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @returns flatbuffers.Offset
                 */
                static endInferenceSession(builder) {
                    let offset = builder.endObject();
                    return offset;
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset offset
                 */
                static finishInferenceSessionBuffer(builder, offset) {
                    builder.finish(offset, 'ORTM');
                }
                /**
                 * @param flatbuffers.Builder builder
                 * @param flatbuffers.Offset offset
                 */
                static finishSizePrefixedInferenceSessionBuffer(builder, offset) {
                    builder.finish(offset, 'ORTM', true);
                }
                static createInferenceSession(builder, ortVersionOffset, modelOffset, sessionStateOffset) {
                    InferenceSession.startInferenceSession(builder);
                    InferenceSession.addOrtVersion(builder, ortVersionOffset);
                    InferenceSession.addModel(builder, modelOffset);
                    InferenceSession.addSessionState(builder, sessionStateOffset);
                    return InferenceSession.endInferenceSession(builder);
                }
            }
            fbs.InferenceSession = InferenceSession;
        })(fbs = experimental.fbs || (experimental.fbs = {}));
    })(experimental = onnxruntime.experimental || (onnxruntime.experimental = {}));
})(onnxruntime = exports.onnxruntime || (exports.onnxruntime = {}));
//# sourceMappingURL=ort-generated.js.map