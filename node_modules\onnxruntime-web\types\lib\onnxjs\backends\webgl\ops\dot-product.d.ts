import { Tensor } from '../../../tensor';
import { WebGLInferenceHandler } from '../inference-handler';
import { ProgramInfoLoader } from '../types';
import { InternalActivationAttributes } from './fuse-utils';
export declare const createDotProductProgramInfoLoader: (inferenceHandler: WebGLInferenceHandler, inputs: readonly Tensor[], outputShape: number[], attributes: InternalActivationAttributes) => ProgramInfoLoader;
