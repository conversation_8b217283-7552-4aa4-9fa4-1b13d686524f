{"license": "MIT", "browser": "dist/ort-web.min.js", "unpkg": "dist/ort.min.js", "name": "onnxruntime-web", "repository": {"url": "https://github.com/Microsoft/onnxruntime.git", "type": "git"}, "author": "fs-eire", "version": "1.14.0", "jsdelivr": "dist/ort.min.js", "dependencies": {"flatbuffers": "^1.12.0", "guid-typescript": "^1.0.9", "long": "^4.0.0", "onnx-proto": "^4.0.4", "onnxruntime-common": "~1.14.0", "platform": "^1.3.6"}, "scripts": {"prepare": "tsc", "build:doc": "node ./script/generate-operator-md", "pull:wasm": "node ./script/pull-prebuilt-wasm-artifacts", "test:e2e": "node ./test/e2e/run", "build": "node ./script/build", "test": "tsc --build ../scripts && node ../scripts/prepare-onnx-node-tests && node ./script/test-runner-cli", "prepack": "node ./script/build && node ./script/prepack"}, "keywords": ["ONNX", "ONNXRuntime", "ONNX Runtime"], "devDependencies": {"@chiragrupani/karma-chromium-edge-launcher": "^2.1.0", "@types/chai": "^4.2.16", "@types/emscripten": "^1.39.4", "@types/flatbuffers": "^1.10.0", "@types/fs-extra": "^9.0.10", "@types/karma": "^6.1.0", "@types/long": "^4.0.1", "@types/minimatch": "^3.0.4", "@types/minimist": "^1.2.2", "@types/mocha": "^8.2.2", "@types/npmlog": "^4.1.2", "@types/platform": "^1.3.3", "base64-js": "^1.5.1", "chai": "^4.3.4", "dir-compare": "^3.3.0", "electron": "^18.3.7", "fs-extra": "^9.1.0", "globby": "^11.0.3", "jszip": "^3.7.1", "karma": "^6.3.2", "karma-browserstack-launcher": "^1.6.0", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^3.1.0", "karma-edge-launcher": "^0.4.2", "karma-electron": "^7.0.0", "karma-firefox-launcher": "^2.1.0", "karma-mocha": "^2.0.1", "karma-mocha-reporter": "^2.2.5", "karma-safari-applescript-launcher": "^0.1.0", "karma-sourcemap-loader": "^0.3.8", "minimatch": "^3.0.4", "minimist": "^1.2.7", "mocha": "^10.2.0", "node-polyfill-webpack-plugin": "^1.1.0", "npmlog": "^4.1.2", "numpy-parser": "^1.2.3", "strip-json-comments": "^3.1.1", "terser": "^5.16.1", "ts-loader": "^9.4.2", "typescript": "^4.9.4", "webpack": "^5.75.0", "webpack-bundle-analyzer": "^4.7.0", "webpack-cli": "^5.0.1", "worker-loader": "^3.0.8"}, "main": "dist/ort-web.node.js", "types": "./types/lib/index.d.ts", "description": "A Javascript library for running ONNX models on browsers"}