{"version": 3, "file": "matmul-pack.js", "sourceRoot": "", "sources": ["matmul-pack.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAGlC,wCAAuD;AACvD,gDAAuC;AAEvC,oCAAsF;AACtF,oCAA0D;AAE1D,6CAAgF;AAChF,qCAA0C;AAE1C,MAAM,iCAAiC,GAAG,CAAC,OAAgB,EAAE,SAAiB,EAAE,EAAE,CAAC,CAAC;IAClF,IAAI,EAAE,iBAAiB;IACvB,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;IACrD,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,mBAAW,CAAC,MAAM,EAAE,mBAAW,CAAC,MAAM,EAAE,mBAAW,CAAC,MAAM,CAAC,CAAC,CAAC;QAC9D,CAAC,mBAAW,CAAC,MAAM,EAAE,mBAAW,CAAC,MAAM,CAAC;IAC9D,SAAS;CACV,CAAC,CAAC;AAEH,MAAM,6BAA6B,GAC/B,CAAC,gBAAuC,EAAE,QAAyB,EAAE,MAAgB,EACpF,oBAAkD,EAAe,EAAE;IAClE,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IAClC,MAAM,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC,8BAA8B,CAAC,CAAC,CAAC,EAAE,CAAC;IAClE,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC9B,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC9B,MAAM,WAAW,GAAG,oBAAa,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IAClE,MAAM,WAAW,GAAG,CAAC,gBAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAExE,IAAI,CAAC,WAAW,EAAE;QAChB,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;KAC3D;IACD,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC5C,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;IAChD,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;IAC5B,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;IAE5B,MAAM,IAAI,GAAG,IAAA,qBAAO,EAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;IACzE,MAAM,cAAc,GAAG,IAAA,yBAAiB,EAAC,WAAW,CAAC,MAAM,CAAC,CAAC;IAC7D,MAAM,OAAO,GAAG,WAAW,CAAC,MAAM,CAAC;IACnC,MAAM,aAAa,GAAG,IAAA,qBAAa,GAAE,CAAC;IACtC,MAAM,EAAC,kBAAkB,EAAE,eAAe,EAAC,GAAG,IAAA,iCAAoB,EAAC,oBAAoB,CAAC,CAAC;IAEzF,MAAM,uBAAuB,GACzB,OAAO,CAAC,CAAC,CAAC,GAAG,IAAA,yBAAgB,EAAC,cAAc,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAE3G,MAAM,iCAAiC,GACnC,WAAW,CAAC,CAAC,CAAC,GAAG,wBAAwB,CAAC,cAAc,EAAE,aAAa,EAAE,MAAM,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAEzG,MAAM,wBAAwB,GAAG,WAAW,CAAC,CAAC,CAAC,0BAA0B,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,GAAG,CAAC;IAClH,MAAM,wBAAwB,GAAG,WAAW,CAAC,CAAC,CAAC,0BAA0B,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,GAAG,CAAC;IAClH,MAAM,sBAAsB,GAAG,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,cAAc;gDACzB,aAAa,CAAC,OAAO,GAAG,CAAC,CAAC,QAAQ,aAAa,CAAC,OAAO,GAAG,CAAC,CAAC;eAC7F,aAAa,CAAC,OAAO,GAAG,CAAC,CAAC,QAAQ,aAAa,CAAC,OAAO,GAAG,CAAC,CAAC;OACpE,CAAC;IACF,MAAM,YAAY,GAAG;cACb,iCAAiC;cACjC,uBAAuB;cACvB,kBAAkB;;gBAEhB,sBAAsB;;;oCAGF,cAAc;2BACvB,wBAAwB;2BACxB,wBAAwB;;;;;gBAKnC,WAAW;gBACX,eAAe;gBACf,IAAI,CAAC,MAAM;cACb,CAAC;IACT,uCACK,QAAQ,KACX,MAAM,EAAE,EAAC,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,WAAW,EAAE,mBAAW,CAAC,MAAM,EAAC,EAClF,YAAY,EACZ,OAAO,EAAE,IAAI,IACb;AACJ,CAAC,CAAC;AAEC,MAAM,mCAAmC,GAC5C,CAAC,gBAAuC,EAAE,MAAgB,EACzD,oBAAkD,EAAqB,EAAE;IACxE,MAAM,QAAQ,GAAG,iCAAiC,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;IAC/G,uCACK,QAAQ,KACX,GAAG,EAAE,GAAG,EAAE,CAAC,6BAA6B,CAAC,gBAAgB,EAAE,QAAQ,EAAE,MAAM,EAAE,oBAAoB,CAAC,IAClG;AACJ,CAAC,CAAC;AARO,QAAA,mCAAmC,uCAQ1C;AAEN,SAAS,wBAAwB,CAC7B,cAAsB,EAAE,aAAgC,EAAE,MAAgB,EAAE,QAA2B;IACzG,IAAI,sBAAsB,GAAG,EAAE,CAAC;IAChC,IAAI,sBAAsB,GAAG,EAAE,CAAC;IAEhC,MAAM,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAChC,MAAM,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAEhC,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC;IAChC,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC;IAEhC,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC;IAChC,MAAM,SAAS,GAAG,OAAO,GAAG,OAAO,CAAC;IACpC,MAAM,SAAS,GAAG,OAAO,GAAG,OAAO,CAAC;IAEpC,sBAAsB,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,UAAU,aAAa,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;IAC1F,sBAAsB,CAAC,OAAO,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;IAC5C,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAClC,sBAAsB,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,UAAU,aAAa,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;IAC1F,sBAAsB,CAAC,OAAO,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;IAC5C,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAElC,MAAM,cAAc,GAAG,oBAAa,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAC1E,MAAM,cAAc,GAAG,oBAAa,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAE1E,MAAM,cAAc,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,UAAU,aAAa,CAAC,CAAC,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACzG,MAAM,cAAc,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,UAAU,aAAa,CAAC,CAAC,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACzG,MAAM,cAAc,GAAG,wBAAwB,aAAa,CAAC,OAAO,GAAG,CAAC,CAAC;WAChE,aAAa,CAAC,OAAO,GAAG,CAAC,CAAC,aAAa,aAAa,CAAC,OAAO,GAAG,CAAC,CAAC;WACjE,aAAa,CAAC,OAAO,GAAG,CAAC,CAAC,aAAa,CAAC;IAEjD,MAAM,2BAA2B,GAAG;;IAElC,cAAc;IACd,cAAc;IACd,cAAc;4BACU,sBAAsB;;;;;IAK9C,cAAc;IACd,cAAc;IACd,cAAc;4BACU,sBAAsB;;EAEhD,CAAC;IAED,OAAO,2BAA2B,CAAC;AACrC,CAAC;AAED,SAAS,IAAI,CAAC,aAAuB,EAAE,IAAY;IACjD,IAAI,GAAG,GAAG,EAAE,CAAC;IACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QACjC,GAAG,IAAI,MAAM,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC;KACnC;IACD,GAAG,IAAI,MAAM,aAAa,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI;QACpC,KAAK,CAAC;IACV,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,IAAI,CAAC,aAAuB,EAAE,IAAY;IACjD,IAAI,GAAG,GAAG,EAAE,CAAC;IACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QACjC,GAAG,IAAI,MAAM,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC;KACnC;IACD,GAAG,IAAI,OAAO;QACV,MAAM,aAAa,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC;IACpC,OAAO,GAAG,CAAC;AACb,CAAC"}