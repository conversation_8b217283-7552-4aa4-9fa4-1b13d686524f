{"version": 3, "file": "softmax.js", "sourceRoot": "", "sources": ["softmax.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAElC,gFAAqG;AAIrG,wCAAwC;AACxC,gDAAuC;AAEvC,oCAAkD;AAElD,2CAA2D;AAM3D,MAAM,gCAAgC,GAAG;IACvC,IAAI,EAAE,mBAAmB;IACzB,UAAU,EAAE,CAAC,GAAG,CAAC;IACjB,UAAU,EAAE,CAAC,mBAAW,CAAC,QAAQ,CAAC;CACnC,CAAC;AAEF,MAAM,kCAAkC,GAAG;IACzC,IAAI,EAAE,qBAAqB;IAC3B,UAAU,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC;IACxB,UAAU,EAAE,CAAC,mBAAW,CAAC,QAAQ,EAAE,mBAAW,CAAC,QAAQ,CAAC;CACzD,CAAC;AAEF,MAAM,sBAAsB,GAAG;IAC7B,IAAI,EAAE,SAAS;IACf,UAAU,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC;IAChC,UAAU,EAAE,CAAC,mBAAW,CAAC,QAAQ,EAAE,mBAAW,CAAC,QAAQ,EAAE,mBAAW,CAAC,QAAQ,CAAC;CAC/E,CAAC;AAEK,MAAM,OAAO,GAChB,CAAC,gBAAuC,EAAE,MAAgB,EAAE,UAA6B,EAAY,EAAE;IACrG,cAAc,CAAC,MAAM,CAAC,CAAC;IAEvB,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;IAC1C,MAAM,IAAI,GAAG,gBAAS,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC;IACzE,MAAM,eAAe,GAAG,gBAAS,CAAC,eAAe,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IACpE,MAAM,YAAY,GAAG,gBAAS,CAAC,iBAAiB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IAEnE,MAAM,MAAM,GAAG,cAAc,CAAC,gBAAgB,EAAE,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,YAAY,CAAC,CAAC;IACnG,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAXO,QAAA,OAAO,WAWd;AAEC,MAAM,sBAAsB,GAC/B,CAAC,IAAgB,EAAqB,EAAE,CAAC,IAAA,sDAA2B,EAAC,EAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,EAAC,CAAC,CAAC;AADvG,QAAA,sBAAsB,0BACiF;AAE7G,MAAM,yBAAyB,GAClC,CAAC,IAAgB,EAAqB,EAAE,CAAC,IAAA,sDAA2B,EAAC,EAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC;AADxG,QAAA,yBAAyB,6BAC+E;AAErH,0DAA0D;AAC1D,mFAAmF;AACnF,qGAAqG;AACrG,0GAA0G;AAC1G,gHAAgH;AAChH,oBAAoB;AACb,MAAM,UAAU,GACnB,CAAC,gBAAuC,EAAE,MAAgB,EAAE,UAA6B,EAAY,EAAE;IACrG,cAAc,CAAC,MAAM,CAAC,CAAC;IAEvB,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;IAC1C,MAAM,IAAI,GAAG,gBAAS,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC;IACzE,MAAM,IAAI,GAAG,UAAU,CAAC,MAAM,CAAC;IAE/B,MAAM,mBAAmB,GAAG,CAAC,IAAI,KAAK,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;IAC/D,MAAM,oBAAoB,GAAa,EAAE,CAAC;IAC1C,IAAI,IAAI,GAAa,EAAE,CAAC;IACxB,IAAI,gBAAgB,GAAa,EAAE,CAAC;IACpC,IAAI,kBAAuC,CAAC;IAE5C,IAAI,mBAAmB,EAAE;QACvB,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,EAAC,MAAM,EAAE,IAAI,EAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAEnD,4DAA4D;QAC5D,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;QAEtB,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAExD,kBAAkB,GAAG,IAAA,sDAA2B,EAAC,EAAC,IAAI,EAAC,CAAC,CAAC;QACzD,gBAAgB,GAAG,IAAA,qBAAS,EAAC,gBAAgB,EAAE,MAAM,EAAE,kBAAkB,CAAC,CAAC;KAC5E;IAED,MAAM,eAAe,GAAG,mBAAmB,CAAC,CAAC,CAAC,gBAAS,CAAC,eAAe,CAAC,oBAAoB,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;QAC3D,gBAAS,CAAC,eAAe,CAAC,UAAU,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC;IAC9F,MAAM,YAAY,GAAG,mBAAmB,CAAC,CAAC,CAAC,gBAAS,CAAC,iBAAiB,CAAC,oBAAoB,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;QAC7D,gBAAS,CAAC,iBAAiB,CAAC,UAAU,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC;IAE7F,MAAM,MAAM,GAAG,cAAc,CACzB,gBAAgB,EAAE,mBAAmB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,YAAY,CAAC,CAAC;IAElH,IAAI,mBAAmB,EAAE;QACvB,MAAM,cAAc,GAAG,IAAA,qBAAS,EAAC,gBAAgB,EAAE,MAAM,EAAE,kBAAmB,CAAC,CAAC;QAChF,OAAO,cAAc,CAAC;KACvB;SAAM;QACL,OAAO,MAAM,CAAC;KACf;AACH,CAAC,CAAC;AAzCO,QAAA,UAAU,cAyCjB;AAEN,MAAM,cAAc,GAChB,CAAC,gBAAuC,EAAE,MAAgB,EAAE,UAA6B,EAAE,eAAuB,EACjH,YAAoB,EAAY,EAAE;IACjC,MAAM,qBAAqB,GACvB,2BAA2B,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,eAAe,EAAE,YAAY,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC;IAC/G,MAAM,GAAG,GAAG,gBAAgB,CAAC,GAAG,iCACxB,gCAAgC,KAAE,SAAS,EAAE,UAAU,CAAC,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,qBAAqB,KACtG,MAAM,CAAC,CAAC;IAEZ,MAAM,uBAAuB,GAAG,4BAA4B,CACxD,gBAAgB,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,eAAe,EAAE,YAAY,EAAE,qBAAqB,CAAC,MAAM,CAAC,IAAI,EAC7F,CAAC,eAAe,CAAC,CAAC,CAAC;IACvB,MAAM,KAAK,GAAG,gBAAgB,CAAC,GAAG,iCAC1B,kCAAkC,KAAE,SAAS,EAAE,UAAU,CAAC,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,uBAAuB,KAC1G,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IAEtB,MAAM,kBAAkB,GAAG,wBAAwB,CAC/C,gBAAgB,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,eAAe,EAAE,YAAY,EAAE,qBAAqB,CAAC,MAAM,CAAC,IAAI,EAC7F,uBAAuB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACzC,MAAM,MAAM,GAAG,gBAAgB,CAAC,GAAG,iCAC3B,sBAAsB,KAAE,SAAS,EAAE,UAAU,CAAC,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,kBAAkB,KACzF,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;IAC7B,OAAO,CAAC,MAAM,CAAC,CAAC;AAClB,CAAC,CAAC;AAEN;;GAEG;AACH,MAAM,2BAA2B,GAC7B,CAAC,gBAAuC,EAAE,KAAa,EAAE,eAAuB,EAAE,YAAoB,EACrG,WAAqB,EAAe,EAAE;IACrC,MAAM,CAAC,YAAY,EAAE,aAAa,CAAC,GAC/B,gBAAgB,CAAC,8BAA8B,CAAC,KAAK,CAAC,IAAI,EAAE,mBAAW,CAAC,QAAQ,CAAC,CAAC;IACtF,MAAM,IAAI,GAAG,WAAW,CAAC,MAAM,CAAC;IAEhC,IAAI,eAAe,GAAG,CAAC,IAAI,YAAY,GAAG,CAAC,EAAE;QAC3C,MAAM,IAAI,KAAK,CAAC,4EAA4E,CAAC,CAAC;KAC/F;IAED,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;QAC5B,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;KAC7D;IAED,IAAI,WAAW,CAAC,CAAC,CAAC,KAAK,eAAe,EAAE;QACtC,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;KAC7E;IAED,MAAM,IAAI,GAAG,IAAA,qBAAO,EAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;IACzE,MAAM,YAAY,GAAG;0BACD,IAAI;sDACwB,YAAY;;sCAE5B,IAAI,CAAC,SAAS,gDAAgD,YAAY;UACtG,aAAa;yBACE,YAAY;;4CAEO,IAAI,CAAC,SAAS;cAC5C,YAAY,KAAK,aAAa;;;;;;QAMpC,CAAC;IACH,uCACK,gCAAgC,KACnC,MAAM,EAAE,EAAC,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,WAAW,EAAE,mBAAW,CAAC,QAAQ,EAAC,EAChF,YAAY,IACZ;AACJ,CAAC,CAAC;AAEN;;GAEG;AACH,MAAM,4BAA4B,GAC9B,CAAC,gBAAuC,EAAE,KAAa,EAAE,eAAuB,EAAE,YAAoB,EACrG,uBAA0C,EAAE,WAAqB,EAAe,EAAE;IACjF,MAAM,CAAC,YAAY,EAAE,aAAa,CAAC,GAC/B,gBAAgB,CAAC,8BAA8B,CAAC,KAAK,CAAC,IAAI,EAAE,mBAAW,CAAC,QAAQ,CAAC,CAAC;IACtF,MAAM,IAAI,GAAG,WAAW,CAAC,MAAM,CAAC;IAEhC,IAAI,eAAe,GAAG,CAAC,IAAI,YAAY,GAAG,CAAC,EAAE;QAC3C,MAAM,IAAI,KAAK,CAAC,4EAA4E,CAAC,CAAC;KAC/F;IAED,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;QAC5B,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;KAC7D;IAED,IAAI,WAAW,CAAC,CAAC,CAAC,KAAK,eAAe,EAAE;QACtC,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;KAC7E;IAED,IAAI,uBAAuB,CAAC,MAAM,KAAK,CAAC,EAAE;QACxC,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;KAC3E;IAED,IAAI,uBAAuB,CAAC,CAAC,CAAC,KAAK,eAAe,EAAE;QAClD,MAAM,IAAI,KAAK,CAAC,wEAAwE,CAAC,CAAC;KAC3F;IAED,MAAM,IAAI,GAAG,IAAA,qBAAO,EAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;IACzE,MAAM,YAAY,GAAG;0BACD,IAAI;sDACwB,YAAY;;;;yBAIzC,YAAY;;+CAEU,IAAI,CAAC,SAAS;cAC/C,YAAY,KAAK,aAAa;;;;QAIpC,CAAC;IACH,uCACK,kCAAkC,KACrC,MAAM,EAAE,EAAC,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,WAAW,EAAE,mBAAW,CAAC,QAAQ,EAAC,EAChF,YAAY,IACZ;AACJ,CAAC,CAAC;AAEN,MAAM,wBAAwB,GAC1B,CAAC,gBAAuC,EAAE,KAAa,EAAE,eAAuB,EAAE,YAAoB,EACrG,uBAA0C,EAAE,0BAA6C,EAAe,EAAE;IACzG,MAAM,CAAC,YAAY,EAAE,aAAa,CAAC,GAC/B,gBAAgB,CAAC,8BAA8B,CAAC,KAAK,CAAC,IAAI,EAAE,mBAAW,CAAC,QAAQ,CAAC,CAAC;IACtF,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC;IAE/B,IAAI,eAAe,GAAG,CAAC,IAAI,YAAY,GAAG,CAAC,EAAE;QAC3C,MAAM,IAAI,KAAK,CAAC,4EAA4E,CAAC,CAAC;KAC/F;IAED,IAAI,uBAAuB,CAAC,MAAM,KAAK,CAAC,IAAI,0BAA0B,CAAC,MAAM,KAAK,CAAC,EAAE;QACnF,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;KAC3E;IAED,IAAI,uBAAuB,CAAC,CAAC,CAAC,KAAK,eAAe,IAAI,0BAA0B,CAAC,CAAC,CAAC,KAAK,eAAe,EAAE;QACvG,MAAM,IAAI,KAAK,CAAC,wEAAwE,CAAC,CAAC;KAC3F;IAED,MAAM,YAAY,GAAG;0BACD,IAAI;;;+CAGiB,YAAY,KAAK,aAAa;;;;wCAIrC,YAAY;;;;;;;;;;;MAW9C,CAAC;IACD,uCACK,sBAAsB,KACzB,MAAM,EAAE,EAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,WAAW,EAAE,mBAAW,CAAC,QAAQ,EAAC,EAC/E,YAAY,IACZ;AACJ,CAAC,CAAC;AAEN,MAAM,cAAc,GAAG,CAAC,MAAgB,EAAQ,EAAE;IAChD,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QAClC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;KAC9C;IAED,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,EAAE;QAChE,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;KACvC;AACH,CAAC,CAAC"}