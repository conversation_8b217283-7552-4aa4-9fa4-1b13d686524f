import { AttributeWith<PERSON>ache<PERSON>ey } from '../../../attribute-with-cache-key';
import { OperatorImplementation, OperatorInitialization } from '../../../operators';
export interface BatchNormalizationAttributes extends AttributeWithCacheKey {
    epsilon: number;
    momentum: number;
    spatial: number;
}
export declare const batchNormalization: OperatorImplementation<BatchNormalizationAttributes>;
export declare const parseBatchNormalizationAttributes: OperatorInitialization<BatchNormalizationAttributes>;
