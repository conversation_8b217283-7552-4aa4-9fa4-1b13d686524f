import { OperatorImplementation, OperatorInitialization } from '../../../operators';
import { Tensor } from '../../../tensor';
import { WebGLInferenceHandler } from '../inference-handler';
export declare const unsqueeze: OperatorImplementation<number[]>;
export declare const unsqueezeV13: (inferenceHandler: WebGLInferenceHandler, inputs: Tensor[]) => Tensor[];
export declare const parseUnsqueezeAttributes: OperatorInitialization<number[]>;
