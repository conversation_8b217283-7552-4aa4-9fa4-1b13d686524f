import { AttributeWithCache<PERSON>ey } from '../../../attribute-with-cache-key';
import { OperatorImplementation, OperatorInitialization } from '../../../operators';
export interface SplitAttributes extends AttributeWithCacheKey {
    readonly axis: number;
    readonly split: number[];
    readonly numOutputs: number;
}
export declare const split: OperatorImplementation<SplitAttributes>;
export declare const parseSplitAttributes: OperatorInitialization<SplitAttributes>;
