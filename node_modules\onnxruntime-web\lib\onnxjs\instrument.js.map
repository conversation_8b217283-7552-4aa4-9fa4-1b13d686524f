{"version": 3, "file": "instrument.js", "sourceRoot": "", "sources": ["instrument.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAwFlC,MAAM,kBAAkB;IACtB,GAAG,CAAC,SAA0B,EAAE,QAAgB,EAAE,SAAkB;QAClE,aAAa;IACf,CAAC;CACF;AACD,MAAM,qBAAqB;IACzB,GAAG,CAAC,QAAyB,EAAE,OAAe,EAAE,QAAiB;QAC/D,sCAAsC;QACtC,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,UAAU,GAAG,QAAQ,GAAG,UAAU,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC;IACzG,CAAC;IAEO,KAAK,CAAC,QAAyB;QACrC,QAAQ,QAAQ,EAAE;YAChB,KAAK,SAAS;gBACZ,OAAO,qBAAqB,CAAC;YAC/B,KAAK,MAAM;gBACT,OAAO,kBAAkB,CAAC;YAC5B,KAAK,SAAS;gBACZ,OAAO,qBAAqB,CAAC;YAC/B,KAAK,OAAO;gBACV,OAAO,qBAAqB,CAAC;YAC/B,KAAK,OAAO;gBACV,OAAO,mBAAmB,CAAC;YAC7B;gBACE,MAAM,IAAI,KAAK,CAAC,yBAAyB,QAAQ,EAAE,CAAC,CAAC;SACxD;IACH,CAAC;CACF;AAED,MAAM,cAAc,GAAG;IACrB,OAAO,EAAE,IAAI;IACb,IAAI,EAAE,IAAI;IACV,OAAO,EAAE,IAAI;IACb,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;CACZ,CAAC;AAEF,MAAM,mBAAmB,GAA4D;IACnF,CAAC,MAAM,CAAC,EAAE,IAAI,kBAAkB,EAAE;IAClC,CAAC,SAAS,CAAC,EAAE,IAAI,qBAAqB,EAAE;CACzC,CAAC;AACF,MAAM,qBAAqB,GAAG;IAC5B,QAAQ,EAAE,SAAS;IACnB,eAAe,EAAE,SAAS;IAC1B,WAAW,EAAE,IAAI;IACjB,iBAAiB,EAAE,KAAK;CACzB,CAAC;AACF,IAAI,iBAAiB,GACyC,EAAC,CAAC,EAAE,CAAC,EAAE,qBAAgD,EAAC,CAAC;AAMvH,SAAS,GAAG,CACR,IAA4B,EAAE,IAAa,EAAE,IAAoB,EAAE,IAAa;IAClF,IAAI,IAAI,KAAK,SAAS,EAAE;QACtB,mDAAmD;QACnD,OAAO,uBAAuB,CAAC,IAAI,CAAC,CAAC;KACtC;SAAM,IAAI,IAAI,KAAK,SAAS,EAAE;QAC7B,0BAA0B;QAC1B,WAAW,CAAC,IAAuB,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;KAC/C;SAAM,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,SAAS,EAAE;QACzD,gCAAgC;QAChC,WAAW,CAAC,IAAuB,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;KAClD;SAAM,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,SAAS,EAAE;QACzD,mCAAmC;QACnC,WAAW,CAAC,IAAuB,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;KACrD;SAAM,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QAC/D,0CAA0C;QAC1C,WAAW,CAAC,IAAuB,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;KACxD;SAAM;QACL,MAAM,IAAI,SAAS,CAAC,gBAAgB,CAAC,CAAC;KACvC;AACH,CAAC;AAED,SAAS,uBAAuB,CAAC,QAAgB;IAC/C,OAAO;QACL,OAAO,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC;QACzC,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC;QACnC,OAAO,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC;QACzC,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC;QACrC,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC;KACtC,CAAC;AACJ,CAAC;AAED,0EAA0E;AAC1E,0EAA0E;AAC1E,+CAA+C;AAC/C,SAAS,WAAW,CAAC,QAAyB,EAAE,OAAe,EAAE,KAAa,EAAE,QAAiB;IAC/F,MAAM,MAAM,GAAG,iBAAiB,CAAC,QAAQ,IAAI,EAAE,CAAC,IAAI,iBAAiB,CAAC,EAAE,CAAC,CAAC;IAC1E,IAAI,cAAc,CAAC,QAAQ,CAAC,GAAG,cAAc,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE;QACrE,OAAO;KACR;IAED,IAAI,MAAM,CAAC,WAAW,EAAE;QACtB,OAAO,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,IAAI,OAAO,EAAE,CAAC;KACpD;IAED,IAAI,MAAM,CAAC,iBAAiB,EAAE;QAC5B,+CAA+C;KAChD;IAED,mBAAmB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;AACxE,CAAC;AAED,2DAA2D;AAC3D,WAAU,GAAG;IAGX,SAAgB,OAAO,CAAC,IAAY,EAAE,IAAa;QACjD,GAAG,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAC7B,CAAC;IAFe,WAAO,UAEtB,CAAA;IAGD,SAAgB,IAAI,CAAC,IAAY,EAAE,IAAa;QAC9C,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAC1B,CAAC;IAFe,QAAI,OAEnB,CAAA;IAGD,SAAgB,OAAO,CAAC,IAAY,EAAE,IAAa;QACjD,GAAG,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAC7B,CAAC;IAFe,WAAO,UAEtB,CAAA;IAGD,SAAgB,KAAK,CAAC,IAAY,EAAE,IAAa;QAC/C,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAC3B,CAAC;IAFe,SAAK,QAEpB,CAAA;IAGD,SAAgB,KAAK,CAAC,IAAY,EAAE,IAAa;QAC/C,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAC3B,CAAC;IAFe,SAAK,QAEpB,CAAA;IAED,SAAgB,KAAK,CAAC,MAAsB;QAC1C,iBAAiB,GAAG,EAAE,CAAC;QACvB,GAAG,CAAC,EAAE,EAAE,MAAM,IAAI,EAAE,CAAC,CAAC;IACxB,CAAC;IAHe,SAAK,QAGpB,CAAA;IACD,SAAgB,GAAG,CAAC,QAAgB,EAAE,MAAqB;QACzD,IAAI,QAAQ,KAAK,GAAG,EAAE;YACpB,KAAK,CAAC,MAAM,CAAC,CAAC;SACf;aAAM;YACL,MAAM,cAAc,GAAG,iBAAiB,CAAC,QAAQ,CAAC,IAAI,qBAAqB,CAAC;YAC5E,iBAAiB,CAAC,QAAQ,CAAC,GAAG;gBAC5B,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,cAAc,CAAC,QAAQ;gBACpD,eAAe,EAAE,MAAM,CAAC,eAAe,IAAI,cAAc,CAAC,eAAe;gBACzE,WAAW,EAAE,CAAC,MAAM,CAAC,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW;gBACjG,iBAAiB,EAAE,CAAC,MAAM,CAAC,iBAAiB,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;oBAClC,MAAM,CAAC,iBAAiB;aACvF,CAAC;SACH;QAED,8CAA8C;IAChD,CAAC;IAfe,OAAG,MAelB,CAAA;IAED,SAAgB,UAAU,CAAC,GAAQ;QACjC,MAAM,MAAM,GAAkB,EAAE,CAAC;QACjC,IAAI,GAAG,CAAC,QAAQ,EAAE;YAChB,MAAM,CAAC,eAAe,GAAG,GAAG,CAAC,QAA2B,CAAC;SAC1D;QACD,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;IAClB,CAAC;IANe,cAAU,aAMzB,CAAA;AACH,CAAC,EAvDS,GAAG,KAAH,GAAG,QAuDZ;AAED,iGAAiG;AACpF,QAAA,MAAM,GAAW,GAAG,CAAC;AAelC,OAAO;AACP,gDAAgD;AAEhD,MAAM,KAAK;IACT,YACW,QAAgC,EAAS,IAAY,EAAS,SAAiB,EAC9E,WAA6C,EAAS,KAAkB,EAAS,GAAkB;QADpG,aAAQ,GAAR,QAAQ,CAAwB;QAAS,SAAI,GAAJ,IAAI,CAAQ;QAAS,cAAS,GAAT,SAAS,CAAQ;QAC9E,gBAAW,GAAX,WAAW,CAAkC;QAAS,UAAK,GAAL,KAAK,CAAa;QAAS,QAAG,GAAH,GAAG,CAAe;IAAG,CAAC;IAEnH,GAAG;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IAED,KAAK,CAAC,UAAU;QACd,IAAI,IAAI,CAAC,GAAG,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;YACtD,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;SACzC;aAAM;YACL,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;YACpB,OAAO,IAAI,CAAC,GAAG,CAAC,sBAAsB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACpD;IACH,CAAC;CACF;AAED,MAAM,WAAW;IACf,YACW,QAAgC,EAAS,IAAY,EAAS,SAAiB,EAAS,OAAe;QAAvG,aAAQ,GAAR,QAAQ,CAAwB;QAAS,SAAI,GAAJ,IAAI,CAAQ;QAAS,cAAS,GAAT,SAAS,CAAQ;QAAS,YAAO,GAAP,OAAO,CAAQ;IAAG,CAAC;CACvH;AAED,MAAa,QAAQ;IACnB,MAAM,CAAC,MAAM,CAAC,MAAwB;QACpC,IAAI,MAAM,KAAK,SAAS,EAAE;YACxB,OAAO,IAAI,IAAI,EAAE,CAAC;SACnB;QACD,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,MAAM,CAAC,cAAc,EAAE,MAAM,CAAC,2BAA2B,CAAC,CAAC;IACrG,CAAC;IAED,YAAoB,eAAwB,EAAE,cAAuB,EAAE,2BAAoC;QA+HnG,aAAQ,GAAG,KAAK,CAAC;QASjB,kBAAa,GAAG,CAAC,CAAC;QAvIxB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,gBAAgB,GAAG,eAAe,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,eAAe,CAAC;QAChF,IAAI,CAAC,eAAe,GAAG,cAAc,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC;QAC1E,IAAI,CAAC,4BAA4B,GAAG,2BAA2B,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,2BAA2B,CAAC;IACrH,CAAC;IAED,kBAAkB;IAClB,KAAK;QACH,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,UAAU,GAAG,IAAA,WAAG,GAAE,CAAC;QACxB,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;IACzB,CAAC;IAED,iBAAiB;IACjB,IAAI;QACF,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,OAAO,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC,aAAa,EAAE,EAAE;YAC3E,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;SAC1D;IACH,CAAC;IAMD,KAAK,CAAI,QAAgC,EAAE,IAAY,EAAE,IAA0B,EAAE,GAAkB;QAErG,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAC1E,IAAI,SAAS,GAAG,KAAK,CAAC;QAEtB,MAAM,GAAG,GAAG,IAAI,EAAE,CAAC;QAEnB,8CAA8C;QAC9C,IAAI,GAAG,IAAI,OAAQ,GAAkB,CAAC,IAAI,KAAK,UAAU,EAAE;YACzD,SAAS,GAAG,IAAI,CAAC;YACjB,OAAO,IAAI,OAAO,CAAI,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACvC,GAAkB;qBACd,IAAI,CACD,KAAK,EAAC,KAAK,EAAC,EAAE;oBACZ,IAAI,KAAK,EAAE;wBACT,MAAM,KAAK,CAAC,GAAG,EAAE,CAAC;qBACnB;oBACD,OAAO,CAAC,KAAK,CAAC,CAAC;gBACjB,CAAC,EACD,KAAK,EAAC,MAAM,EAAC,EAAE;oBACb,IAAI,KAAK,EAAE;wBACT,MAAM,KAAK,CAAC,GAAG,EAAE,CAAC;qBACnB;oBACD,MAAM,CAAC,MAAM,CAAC,CAAC;gBACjB,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC;SACJ;QACD,IAAI,CAAC,SAAS,IAAI,KAAK,EAAE;YACvB,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;YAC7B,IAAI,QAAQ,IAAI,OAAO,QAAQ,CAAC,IAAI,KAAK,UAAU,EAAE;gBACnD,OAAO,IAAI,OAAO,CAAI,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBACxC,CAAC,QAAQ,CAAC,CAAC,IAAI,CACX,GAAG,EAAE;wBACH,OAAO,CAAC,GAAG,CAAC,CAAC;oBACf,CAAC,EACD,CAAC,MAAM,EAAE,EAAE;wBACT,MAAM,CAAC,MAAM,CAAC,CAAC;oBACjB,CAAC,CAAC,CAAC;gBACT,CAAC,CAAC,CAAC;aACJ;SACF;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,iBAAiB;IACjB,KAAK,CAAC,QAAgC,EAAE,IAAY,EAAE,GAAkB;QACtE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;SAChD;QACD,IAAI,GAAG,KAAK,SAAS,EAAE;YACrB,MAAM,SAAS,GAAG,IAAA,WAAG,GAAE,CAAC;YACxB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YACtB,OAAO,IAAI,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;SACnE;aAAM;YACL,MAAM,KAAK,GAAe,GAAG,CAAC,UAAU,EAAE,CAAC;YAC3C,OAAO,IAAI,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAC,CAAC,EAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;SACzE;IACH,CAAC;IAED,yBAAyB;IACjB,KAAK,CAAC,GAAG,CAAC,KAAY;QAC5B,MAAM,OAAO,GAAW,MAAM,KAAK,CAAC,UAAU,EAAE,CAAC;QACjD,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,EAAE;YACrD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,WAAW,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;YAC/F,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;SACrB;IACH,CAAC;IAEO,OAAO,CAAC,KAAY;QAC1B,MAAM,OAAO,GAAW,IAAA,WAAG,GAAE,CAAC;QAC9B,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,EAAE;YACrD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,WAAW,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;YAC/F,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;SACrB;IACH,CAAC;IAEO,WAAW,CAAC,KAAkB;QACpC,cAAM,CAAC,OAAO,CACV,YAAY,KAAK,CAAC,QAAQ,EAAE,EAC5B,GAAG,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,gBAAgB,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACnH,CAAC;IAEO,KAAK,CAAC,WAAmB;QAC/B,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,eAAe;YACtE,WAAW,GAAG,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,4BAA4B,EAAE;YACtE,qEAAqE;YAErE,KAAK,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,GAAG,eAAe,GAAG,IAAI,CAAC,eAAe;gBACvG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAC9C,IAAI,CAAC,aAAa,EAAE,EAAE;gBACzB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;aAC1D;YAED,IAAI,CAAC,UAAU,GAAG,IAAA,WAAG,GAAE,CAAC;SACzB;IACH,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;CAWF;AAjJD,4BAiJC;AAED;;GAEG;AACU,QAAA,GAAG,GAAG,CAAC,OAAO,WAAW,KAAK,WAAW,IAAI,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC"}