var _scriptDir,t=(_scriptDir="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0,"undefined"!=typeof __filename&&(_scriptDir=_scriptDir||__filename),function(t){function n(){return E.buffer!=D&&z(E.buffer),j}function e(){return E.buffer!=D&&z(E.buffer),F}function r(){return E.buffer!=D&&z(E.buffer),U}function i(){return E.buffer!=D&&z(E.buffer),Y}function a(){return E.buffer!=D&&z(E.buffer),I}var u,o,c;t=t||{},u||(u=void 0!==t?t:{}),u.ready=new Promise((function(t,n){o=t,c=n}));var f,s,l,p,h,m,d=Object.assign({},u),y="./this.program",b=(t,n)=>{throw n},g="object"==typeof window,_="function"==typeof importScripts,v="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,w=u.ENVIRONMENT_IS_PTHREAD||!1,T="";function O(t){return u.locateFile?u.locateFile(t,T):T+t}if(v){let t;T=_?require("path").dirname(T)+"/":__dirname+"/",m=()=>{h||(p=require("fs"),h=require("path"))},f=function(t,n){return m(),t=h.normalize(t),p.readFileSync(t,n?void 0:"utf8")},l=t=>((t=f(t,!0)).buffer||(t=new Uint8Array(t)),t),s=(t,n,e)=>{m(),t=h.normalize(t),p.readFile(t,(function(t,r){t?e(t):n(r.buffer)}))},1<process.argv.length&&(y=process.argv[1].replace(/\\/g,"/")),process.argv.slice(2),process.on("uncaughtException",(function(t){if(!(t instanceof ot))throw t})),process.on("unhandledRejection",(function(t){throw t})),b=(t,n)=>{if(J())throw process.exitCode=t,n;n instanceof ot||x("exiting due to exception: "+n),process.exit(t)},u.inspect=function(){return"[Emscripten Module object]"};try{t=require("worker_threads")}catch(t){throw console.error('The "worker_threads" module is not supported in this node.js build - perhaps a newer version is needed?'),t}global.Worker=t.Worker}else(g||_)&&(_?T=self.location.href:"undefined"!=typeof document&&document.currentScript&&(T=document.currentScript.src),_scriptDir&&(T=_scriptDir),T=0!==T.indexOf("blob:")?T.substr(0,T.replace(/[?#].*/,"").lastIndexOf("/")+1):"",v||(f=t=>{var n=new XMLHttpRequest;return n.open("GET",t,!1),n.send(null),n.responseText},_&&(l=t=>{var n=new XMLHttpRequest;return n.open("GET",t,!1),n.responseType="arraybuffer",n.send(null),new Uint8Array(n.response)}),s=(t,n,e)=>{var r=new XMLHttpRequest;r.open("GET",t,!0),r.responseType="arraybuffer",r.onload=()=>{200==r.status||0==r.status&&r.response?n(r.response):e()},r.onerror=e,r.send(null)}));v&&"undefined"==typeof performance&&(global.performance=require("perf_hooks").performance);var S=console.log.bind(console),A=console.warn.bind(console);v&&(m(),S=t=>p.writeSync(1,t+"\n"),A=t=>p.writeSync(2,t+"\n"));var M,C=u.print||S,x=u.printErr||A;Object.assign(u,d),d=null,u.thisProgram&&(y=u.thisProgram),u.quit&&(b=u.quit),u.wasmBinary&&(M=u.wasmBinary);var R=u.noExitRuntime||!1;"object"!=typeof WebAssembly&&rt("no native wasm support detected");var E,k,D,j,F,U,Y,I,W=!1,P="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function H(t,n,e){var r=(n>>>=0)+e;for(e=n;t[e]&&!(e>=r);)++e;if(16<e-n&&t.buffer&&P)return P.decode(t.buffer instanceof SharedArrayBuffer?t.slice(n,e):t.subarray(n,e));for(r="";n<e;){var i=t[n++];if(128&i){var a=63&t[n++];if(192==(224&i))r+=String.fromCharCode((31&i)<<6|a);else{var u=63&t[n++];65536>(i=224==(240&i)?(15&i)<<12|a<<6|u:(7&i)<<18|a<<12|u<<6|63&t[n++])?r+=String.fromCharCode(i):(i-=65536,r+=String.fromCharCode(55296|i>>10,56320|1023&i))}}else r+=String.fromCharCode(i)}return r}function q(t,n){return(t>>>=0)?H(e(),t,n):""}function B(t,n,e,r){if(!(0<r))return 0;var i=e>>>=0;r=e+r-1;for(var a=0;a<t.length;++a){var u=t.charCodeAt(a);if(55296<=u&&57343>=u&&(u=65536+((1023&u)<<10)|1023&t.charCodeAt(++a)),127>=u){if(e>=r)break;n[e++>>>0]=u}else{if(2047>=u){if(e+1>=r)break;n[e++>>>0]=192|u>>6}else{if(65535>=u){if(e+2>=r)break;n[e++>>>0]=224|u>>12}else{if(e+3>=r)break;n[e++>>>0]=240|u>>18,n[e++>>>0]=128|u>>12&63}n[e++>>>0]=128|u>>6&63}n[e++>>>0]=128|63&u}}return n[e>>>0]=0,e-i}function G(t){for(var n=0,e=0;e<t.length;++e){var r=t.charCodeAt(e);127>=r?n++:2047>=r?n+=2:55296<=r&&57343>=r?(n+=4,++e):n+=3}return n}function z(t){D=t,u.HEAP8=j=new Int8Array(t),u.HEAP16=new Int16Array(t),u.HEAP32=U=new Int32Array(t),u.HEAPU8=F=new Uint8Array(t),u.HEAPU16=new Uint16Array(t),u.HEAPU32=Y=new Uint32Array(t),u.HEAPF32=new Float32Array(t),u.HEAPF64=I=new Float64Array(t)}w&&(D=u.buffer);var N=u.INITIAL_MEMORY||16777216;if(w)E=u.wasmMemory,D=u.buffer;else if(u.wasmMemory)E=u.wasmMemory;else if(!((E=new WebAssembly.Memory({initial:N/65536,maximum:65536,shared:!0})).buffer instanceof SharedArrayBuffer))throw x("requested a shared WebAssembly.Memory but the returned buffer is not a SharedArrayBuffer, indicating that while the browser has SharedArrayBuffer it does not have WebAssembly threads support - you may need to set a flag"),v&&console.log("(on node you may need: --experimental-wasm-threads --experimental-wasm-bulk-memory and also use a recent version)"),Error("bad memory");E&&(D=E.buffer),N=D.byteLength,z(D);var V,L=[],X=[],Z=[],$=[];function J(){return R||!1}function Q(){var t=u.preRun.shift();L.unshift(t)}var K,tt=0,nt=null,et=null;function rt(t){throw w?postMessage({cmd:"onAbort",arg:t}):u.onAbort&&u.onAbort(t),x(t="Aborted("+t+")"),W=!0,t=new WebAssembly.RuntimeError(t+". Build with -sASSERTIONS for more info."),c(t),t}function it(){return K.startsWith("data:application/octet-stream;base64,")}function at(){var t=K;try{if(t==K&&M)return new Uint8Array(M);if(l)return l(t);throw"both async and sync fetching of the wasm failed"}catch(t){rt(t)}}K="ort-wasm-threaded.wasm",it()||(K=O(K));var ut={};function ot(t){this.name="ExitStatus",this.message="Program terminated with exit("+t+")",this.status=t}function ct(t){(t=pt.Vb[t])||rt(),pt.mc(t)}function ft(t){var n=pt.Cc();if(!n)return 6;pt.ac.push(n),pt.Vb[t.Ub]=n,n.Ub=t.Ub;var e={cmd:"run",start_routine:t.Ic,arg:t.zc,pthread_ptr:t.Ub};return n.$b=()=>{e.time=performance.now(),n.postMessage(e,t.Nc)},n.loaded&&(n.$b(),delete n.$b),0}function st(t){if(w)return Vt(1,1,t);J()||(pt.oc(),u.onExit&&u.onExit(t),W=!0),b(t,new ot(t))}function lt(t,n){if(!n&&w)throw dt(t),"unwind";J()||w||(bn(),ht(Z),yn(0),en[1].length&&rn(1,10),en[2].length&&rn(2,10),pt.oc()),st(t)}var pt={Yb:[],ac:[],qc:[],Vb:{},fc:function(){w&&pt.Ec()},Pc:function(){},Ec:function(){pt.receiveObjectTransfer=pt.Gc,pt.threadInitTLS=pt.pc,pt.setExitStatus=pt.nc,R=!1},nc:function(){},oc:function(){for(var t of Object.values(pt.Vb))pt.mc(t);for(t of pt.Yb)t.terminate();pt.Yb=[]},mc:function(t){var n=t.Ub;delete pt.Vb[n],pt.Yb.push(t),pt.ac.splice(pt.ac.indexOf(t),1),t.Ub=0,Tn(n)},Gc:function(){},pc:function(){pt.qc.forEach((t=>t()))},Fc:function(t,n){t.onmessage=e=>{var r=(e=e.data).cmd;if(t.Ub&&(pt.Bc=t.Ub),e.targetThread&&e.targetThread!=hn()){var i=pt.Vb[e.Qc];i?i.postMessage(e,e.transferList):x('Internal error! Worker sent a message "'+r+'" to target pthread '+e.targetThread+", but that thread no longer exists!")}else"processProxyingQueue"===r?Ht(e.queue):"spawnThread"===r?ft(e):"cleanupThread"===r?ct(e.thread):"killThread"===r?(e=e.thread,r=pt.Vb[e],delete pt.Vb[e],r.terminate(),Tn(e),pt.ac.splice(pt.ac.indexOf(r),1),r.Ub=0):"cancelThread"===r?pt.Vb[e.thread].postMessage({cmd:"cancel"}):"loaded"===r?(t.loaded=!0,n&&n(t),t.$b&&(t.$b(),delete t.$b)):"print"===r?C("Thread "+e.threadId+": "+e.text):"printErr"===r?x("Thread "+e.threadId+": "+e.text):"alert"===r?alert("Thread "+e.threadId+": "+e.text):"setimmediate"===e.target?t.postMessage(e):"onAbort"===r?u.onAbort&&u.onAbort(e.arg):r&&x("worker sent an unknown command "+r);pt.Bc=void 0},t.onerror=t=>{throw x("worker sent an error! "+t.filename+":"+t.lineno+": "+t.message),t},v&&(t.on("message",(function(n){t.onmessage({data:n})})),t.on("error",(function(n){t.onerror(n)})),t.on("detachedExit",(function(){}))),t.postMessage({cmd:"load",urlOrBlob:u.mainScriptUrlOrBlob||_scriptDir,wasmMemory:E,wasmModule:k})},yc:function(){var t=O("ort-wasm-threaded.worker.js");pt.Yb.push(new Worker(t))},Cc:function(){return 0==pt.Yb.length&&(pt.yc(),pt.Fc(pt.Yb[0])),pt.Yb.pop()}};function ht(t){for(;0<t.length;)t.shift()(u)}function mt(t){var n=Mn();return t=t(),Cn(n),t}function dt(t){if(w)return Vt(2,0,t);try{lt(t)}catch(t){t instanceof ot||"unwind"==t||b(1,t)}}u.PThread=pt,u.establishStackSpace=function(){var t=hn(),n=r()[t+44>>2>>>0];t=r()[t+48>>2>>>0],An(n,n-t),Cn(n)};var yt=[];function bt(t){var n=yt[t];return n||(t>=yt.length&&(yt.length=t+1),yt[t]=n=V.get(t)),n}u.invokeEntryPoint=function(t,n){t=bt(t)(n),J()?pt.nc(t):On(t)};var gt,_t,vt=[],wt=0,Tt=0;function Ot(t){this.Zb=t,this.Sb=t-24,this.xc=function(t){i()[this.Sb+4>>2>>>0]=t},this.bc=function(){return i()[this.Sb+4>>2>>>0]},this.wc=function(t){i()[this.Sb+8>>2>>>0]=t},this.Dc=function(){return i()[this.Sb+8>>2>>>0]},this.rc=function(){r()[this.Sb>>2>>>0]=0},this.hc=function(t){t=t?1:0,n()[this.Sb+12>>0>>>0]=t},this.uc=function(){return 0!=n()[this.Sb+12>>0>>>0]},this.ic=function(t){t=t?1:0,n()[this.Sb+13>>0>>>0]=t},this.kc=function(){return 0!=n()[this.Sb+13>>0>>>0]},this.fc=function(t,n){this.cc(0),this.xc(t),this.wc(n),this.rc(),this.hc(!1),this.ic(!1)},this.sc=function(){Atomics.add(r(),this.Sb>>2,1)},this.Hc=function(){return 1===Atomics.sub(r(),this.Sb>>2,1)},this.cc=function(t){i()[this.Sb+16>>2>>>0]=t},this.tc=function(){return i()[this.Sb+16>>2>>>0]},this.vc=function(){if(En(this.bc()))return i()[this.Zb>>2>>>0];var t=this.tc();return 0!==t?t:this.Zb}}function St(t){return dn(new Ot(t).Sb)}function At(t,n,e,r){return w?Vt(3,1,t,n,e,r):Mt(t,n,e,r)}function Mt(t,n,e,r){if("undefined"==typeof SharedArrayBuffer)return x("Current environment does not support SharedArrayBuffer, pthreads are not available!"),6;var i=[];return w&&0===i.length?At(t,n,e,r):(t={Ic:e,Ub:t,zc:r,Nc:i},w?(t.Oc="spawnThread",postMessage(t,i),0):ft(t))}function Ct(t,n,e){return w?Vt(4,1,t,n,e):0}function xt(t,n){if(w)return Vt(5,1,t,n)}function Rt(t,n){if(w)return Vt(6,1,t,n)}function Et(t,n,e){if(w)return Vt(7,1,t,n,e)}function kt(t,n,e){return w?Vt(8,1,t,n,e):0}function Dt(t,n){if(w)return Vt(9,1,t,n)}function jt(t,n,e){if(w)return Vt(10,1,t,n,e)}function Ft(t,n,e,r){if(w)return Vt(11,1,t,n,e,r)}function Ut(t,n,e,r){if(w)return Vt(12,1,t,n,e,r)}function Yt(t,n,e,r){if(w)return Vt(13,1,t,n,e,r)}function It(t){if(w)return Vt(14,1,t)}function Wt(t,n){if(w)return Vt(15,1,t,n)}function Pt(t,n,e){if(w)return Vt(16,1,t,n,e)}function Ht(t){Atomics.store(r(),t>>2,1),hn()&&wn(t),Atomics.compareExchange(r(),t>>2,1,0)}function qt(t){return i()[t>>>2]+4294967296*r()[t+4>>>2]}function Bt(t,n,e,r,i,a){return w?Vt(17,1,t,n,e,r,i,a):-52}function Gt(t,n,e,r,i,a){if(w)return Vt(18,1,t,n,e,r,i,a)}function zt(t){var e=G(t)+1,r=mn(e);return r&&B(t,n(),r,e),r}function Nt(t,n,e){function a(t){return(t=t.toTimeString().match(/\(([A-Za-z ]+)\)$/))?t[1]:"GMT"}if(w)return Vt(19,1,t,n,e);var u=(new Date).getFullYear(),o=new Date(u,0,1),c=new Date(u,6,1);u=o.getTimezoneOffset();var f=c.getTimezoneOffset(),s=Math.max(u,f);r()[t>>2>>>0]=60*s,r()[n>>2>>>0]=Number(u!=f),t=a(o),n=a(c),t=zt(t),n=zt(n),f<u?(i()[e>>2>>>0]=t,i()[e+4>>2>>>0]=n):(i()[e>>2>>>0]=n,i()[e+4>>2>>>0]=t)}function Vt(t,n){var e=arguments.length-2,r=arguments;return mt((()=>{for(var i=xn(8*e),u=i>>3,o=0;o<e;o++){var c=r[2+o];a()[u+o>>>0]=c}return vn(t,e,i,n)}))}u.executeNotifiedProxyingQueue=Ht,_t=v?()=>{var t=process.hrtime();return 1e3*t[0]+t[1]/1e6}:w?()=>performance.now()-u.__performance_now_clock_drift:()=>performance.now();var Lt,Xt=[],Zt={};function $t(){if(!Lt){var t,n={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:y||"./this.program"};for(t in Zt)void 0===Zt[t]?delete n[t]:n[t]=Zt[t];var e=[];for(t in n)e.push(t+"="+n[t]);Lt=e}return Lt}function Jt(t,e){if(w)return Vt(20,1,t,e);var r=0;return $t().forEach((function(a,u){var o=e+r;for(u=i()[t+4*u>>2>>>0]=o,o=0;o<a.length;++o)n()[u++>>0>>>0]=a.charCodeAt(o);n()[u>>0>>>0]=0,r+=a.length+1})),0}function Qt(t,n){if(w)return Vt(21,1,t,n);var e=$t();i()[t>>2>>>0]=e.length;var r=0;return e.forEach((function(t){r+=t.length+1})),i()[n>>2>>>0]=r,0}function Kt(t){return w?Vt(22,1,t):52}function tn(t,n,e,r){return w?Vt(23,1,t,n,e,r):52}function nn(t,n,e,r,i){return w?Vt(24,1,t,n,e,r,i):70}var en=[null,[],[]];function rn(t,n){var e=en[t];0===n||10===n?((1===t?C:x)(H(e,0)),e.length=0):e.push(n)}function an(t,n,r,a){if(w)return Vt(25,1,t,n,r,a);for(var u=0,o=0;o<r;o++){var c=i()[n>>2>>>0],f=i()[n+4>>2>>>0];n+=8;for(var s=0;s<f;s++)rn(t,e()[c+s>>>0]);u+=f}return i()[a>>2>>>0]=u,0}var un=0;function on(t){return 0==t%4&&(0!=t%100||0==t%400)}var cn=[31,29,31,30,31,30,31,31,30,31,30,31],fn=[31,28,31,30,31,30,31,31,30,31,30,31];function sn(t,e,i,a){function u(t,n,e){for(t="number"==typeof t?t.toString():t||"";t.length<n;)t=e[0]+t;return t}function o(t,n){return u(t,n,"0")}function c(t,n){function e(t){return 0>t?-1:0<t?1:0}var r;return 0===(r=e(t.getFullYear()-n.getFullYear()))&&0===(r=e(t.getMonth()-n.getMonth()))&&(r=e(t.getDate()-n.getDate())),r}function f(t){switch(t.getDay()){case 0:return new Date(t.getFullYear()-1,11,29);case 1:return t;case 2:return new Date(t.getFullYear(),0,3);case 3:return new Date(t.getFullYear(),0,2);case 4:return new Date(t.getFullYear(),0,1);case 5:return new Date(t.getFullYear()-1,11,31);case 6:return new Date(t.getFullYear()-1,11,30)}}function s(t){var n=t.Wb;for(t=new Date(new Date(t.Xb+1900,0,1).getTime());0<n;){var e=t.getMonth(),r=(on(t.getFullYear())?cn:fn)[e];if(!(n>r-t.getDate())){t.setDate(t.getDate()+n);break}n-=r-t.getDate()+1,t.setDate(1),11>e?t.setMonth(e+1):(t.setMonth(0),t.setFullYear(t.getFullYear()+1))}return e=new Date(t.getFullYear()+1,0,4),n=f(new Date(t.getFullYear(),0,4)),e=f(e),0>=c(n,t)?0>=c(e,t)?t.getFullYear()+1:t.getFullYear():t.getFullYear()-1}var l=r()[a+40>>2>>>0];for(var p in a={Lc:r()[a>>2>>>0],Kc:r()[a+4>>2>>>0],dc:r()[a+8>>2>>>0],jc:r()[a+12>>2>>>0],ec:r()[a+16>>2>>>0],Xb:r()[a+20>>2>>>0],Tb:r()[a+24>>2>>>0],Wb:r()[a+28>>2>>>0],Rc:r()[a+32>>2>>>0],Jc:r()[a+36>>2>>>0],Mc:l?q(l):""},i=q(i),l={"%c":"%a %b %d %H:%M:%S %Y","%D":"%m/%d/%y","%F":"%Y-%m-%d","%h":"%b","%r":"%I:%M:%S %p","%R":"%H:%M","%T":"%H:%M:%S","%x":"%m/%d/%y","%X":"%H:%M:%S","%Ec":"%c","%EC":"%C","%Ex":"%m/%d/%y","%EX":"%H:%M:%S","%Ey":"%y","%EY":"%Y","%Od":"%d","%Oe":"%e","%OH":"%H","%OI":"%I","%Om":"%m","%OM":"%M","%OS":"%S","%Ou":"%u","%OU":"%U","%OV":"%V","%Ow":"%w","%OW":"%W","%Oy":"%y"})i=i.replace(new RegExp(p,"g"),l[p]);var h="Sunday Monday Tuesday Wednesday Thursday Friday Saturday".split(" "),m="January February March April May June July August September October November December".split(" ");for(p in l={"%a":function(t){return h[t.Tb].substring(0,3)},"%A":function(t){return h[t.Tb]},"%b":function(t){return m[t.ec].substring(0,3)},"%B":function(t){return m[t.ec]},"%C":function(t){return o((t.Xb+1900)/100|0,2)},"%d":function(t){return o(t.jc,2)},"%e":function(t){return u(t.jc,2," ")},"%g":function(t){return s(t).toString().substring(2)},"%G":function(t){return s(t)},"%H":function(t){return o(t.dc,2)},"%I":function(t){return 0==(t=t.dc)?t=12:12<t&&(t-=12),o(t,2)},"%j":function(t){for(var n=0,e=0;e<=t.ec-1;n+=(on(t.Xb+1900)?cn:fn)[e++]);return o(t.jc+n,3)},"%m":function(t){return o(t.ec+1,2)},"%M":function(t){return o(t.Kc,2)},"%n":function(){return"\n"},"%p":function(t){return 0<=t.dc&&12>t.dc?"AM":"PM"},"%S":function(t){return o(t.Lc,2)},"%t":function(){return"\t"},"%u":function(t){return t.Tb||7},"%U":function(t){return o(Math.floor((t.Wb+7-t.Tb)/7),2)},"%V":function(t){var n=Math.floor((t.Wb+7-(t.Tb+6)%7)/7);if(2>=(t.Tb+371-t.Wb-2)%7&&n++,n)53==n&&(4==(e=(t.Tb+371-t.Wb)%7)||3==e&&on(t.Xb)||(n=1));else{n=52;var e=(t.Tb+7-t.Wb-1)%7;(4==e||5==e&&on(t.Xb%400-1))&&n++}return o(n,2)},"%w":function(t){return t.Tb},"%W":function(t){return o(Math.floor((t.Wb+7-(t.Tb+6)%7)/7),2)},"%y":function(t){return(t.Xb+1900).toString().substring(2)},"%Y":function(t){return t.Xb+1900},"%z":function(t){var n=0<=(t=t.Jc);return t=Math.abs(t)/60,(n?"+":"-")+String("0000"+(t/60*100+t%60)).slice(-4)},"%Z":function(t){return t.Mc},"%%":function(){return"%"}},i=i.replace(/%%/g,"\0\0"),l)i.includes(p)&&(i=i.replace(new RegExp(p,"g"),l[p](a)));return p=function(t){var n=Array(G(t)+1);return B(t,n,0,n.length),n}(i=i.replace(/\0\0/g,"%")),p.length>e?0:(function(t,e){n().set(t,e>>>0)}(p,t),p.length-1)}pt.fc();var ln=[null,st,dt,At,Ct,xt,Rt,Et,kt,Dt,jt,Ft,Ut,Yt,It,Wt,Pt,Bt,Gt,Nt,Jt,Qt,Kt,tn,nn,an],pn={b:function(t){return mn(t+24)+24},n:function(t){return(t=new Ot(t)).uc()||(t.hc(!0),wt--),t.ic(!1),vt.push(t),t.sc(),t.vc()},ma:function(t){throw x("Unexpected exception thrown, this is not properly supported - aborting"),W=!0,t},x:function(){Sn(0);var t=vt.pop();if(t.Hc()&&!t.kc()){var n=t.Dc();n&&bt(n)(t.Zb),St(t.Zb)}Tt=0},e:function(){var t=Tt;if(!t)return un=0;var n=new Ot(t);n.cc(t);var e=n.bc();if(!e)return un=0,t;for(var r=Array.prototype.slice.call(arguments),i=0;i<r.length;i++){var a=r[i];if(0===a||a===e)break;if(Rn(a,e,n.Sb+16))return un=a,t}return un=e,t},l:function(){var t=Tt;if(!t)return un=0;var n=new Ot(t);n.cc(t);var e=n.bc();if(!e)return un=0,t;for(var r=Array.prototype.slice.call(arguments),i=0;i<r.length;i++){var a=r[i];if(0===a||a===e)break;if(Rn(a,e,n.Sb+16))return un=a,t}return un=e,t},h:function(){var t=Tt;if(!t)return un=0;var n=new Ot(t);n.cc(t);var e=n.bc();if(!e)return un=0,t;for(var r=Array.prototype.slice.call(arguments),i=0;i<r.length;i++){var a=r[i];if(0===a||a===e)break;if(Rn(a,e,n.Sb+16))return un=a,t}return un=e,t},t:St,M:function(){var t=vt.pop();t||rt("no exception to throw");var n=t.Zb;throw t.kc()||(vt.push(t),t.ic(!0),t.hc(!1),wt++),Tt=n,n},c:function(t,n,e){throw new Ot(t).fc(n,e),Tt=t,wt++,t},pa:function(){return wt},Fa:function(t){gn(t,!_,1,!g),pt.pc()},T:function(t){w?postMessage({cmd:"cleanupThread",thread:t}):ct(t)},xa:Mt,j:function(t){throw Tt||(Tt=t),t},H:Ct,Ma:xt,ua:Rt,wa:Et,oa:kt,Ka:Dt,Ca:jt,Ja:Ft,V:Ut,va:Yt,sa:It,La:Wt,ta:Pt,Ta:function(){},X:function(){rt("To use dlopen, you need enable dynamic linking, see https://github.com/emscripten-core/emscripten/wiki/Linking")},Ua:function(){rt("To use dlopen, you need enable dynamic linking, see https://github.com/emscripten-core/emscripten/wiki/Linking")},W:function(){return Date.now()},ya:function(){return 2097152},Oa:function(){return!0},za:function(t,n,e,r){if(t==n)setTimeout((()=>Ht(r)));else if(w)postMessage({targetThread:t,cmd:"processProxyingQueue",queue:r});else{if(!(t=pt.Vb[t]))return;t.postMessage({cmd:"processProxyingQueue",queue:r})}return 1},Ea:function(){return-1},Pa:function(t,n){t=new Date(1e3*qt(t)),r()[n>>2>>>0]=t.getUTCSeconds(),r()[n+4>>2>>>0]=t.getUTCMinutes(),r()[n+8>>2>>>0]=t.getUTCHours(),r()[n+12>>2>>>0]=t.getUTCDate(),r()[n+16>>2>>>0]=t.getUTCMonth(),r()[n+20>>2>>>0]=t.getUTCFullYear()-1900,r()[n+24>>2>>>0]=t.getUTCDay(),t=(t.getTime()-Date.UTC(t.getUTCFullYear(),0,1,0,0,0,0))/864e5|0,r()[n+28>>2>>>0]=t},Qa:function(t,n){t=new Date(1e3*qt(t)),r()[n>>2>>>0]=t.getSeconds(),r()[n+4>>2>>>0]=t.getMinutes(),r()[n+8>>2>>>0]=t.getHours(),r()[n+12>>2>>>0]=t.getDate(),r()[n+16>>2>>>0]=t.getMonth(),r()[n+20>>2>>>0]=t.getFullYear()-1900,r()[n+24>>2>>>0]=t.getDay();var e=new Date(t.getFullYear(),0,1),i=(t.getTime()-e.getTime())/864e5|0;r()[n+28>>2>>>0]=i,r()[n+36>>2>>>0]=-60*t.getTimezoneOffset(),i=new Date(t.getFullYear(),6,1).getTimezoneOffset(),t=0|(i!=(e=e.getTimezoneOffset())&&t.getTimezoneOffset()==Math.min(e,i)),r()[n+32>>2>>>0]=t},Ra:function(t){var n=new Date(r()[t+20>>2>>>0]+1900,r()[t+16>>2>>>0],r()[t+12>>2>>>0],r()[t+8>>2>>>0],r()[t+4>>2>>>0],r()[t>>2>>>0],0),e=r()[t+32>>2>>>0],i=n.getTimezoneOffset(),a=new Date(n.getFullYear(),0,1),u=new Date(n.getFullYear(),6,1).getTimezoneOffset(),o=a.getTimezoneOffset(),c=Math.min(o,u);return 0>e?r()[t+32>>2>>>0]=Number(u!=o&&c==i):0<e!=(c==i)&&(u=Math.max(o,u),n.setTime(n.getTime()+6e4*((0<e?c:u)-i))),r()[t+24>>2>>>0]=n.getDay(),e=(n.getTime()-a.getTime())/864e5|0,r()[t+28>>2>>>0]=e,r()[t>>2>>>0]=n.getSeconds(),r()[t+4>>2>>>0]=n.getMinutes(),r()[t+8>>2>>>0]=n.getHours(),r()[t+12>>2>>>0]=n.getDate(),r()[t+16>>2>>>0]=n.getMonth(),n.getTime()/1e3|0},Aa:Bt,Ba:Gt,Sa:function t(n,e,r){t.Ac||(t.Ac=!0,Nt(n,e,r))},y:function(){rt("")},U:function(){if(!v&&!_){var t="Blocking on the main thread is very dangerous, see https://emscripten.org/docs/porting/pthreads.html#blocking-on-the-main-browser-thread";gt||(gt={}),gt[t]||(gt[t]=1,v&&(t="warning: "+t),x(t))}},ra:function(){return 4294901760},B:_t,Ia:function(t,n,r){e().copyWithin(t>>>0,n>>>0,n+r>>>0)},F:function(){return v?require("os").cpus().length:navigator.hardwareConcurrency},Da:function(t,n,e){Xt.length=n,e>>=3;for(var r=0;r<n;r++)Xt[r]=a()[e+r>>>0];return(0>t?ut[-t-1]:ln[t]).apply(null,Xt)},qa:function(t){var n=e().length;if((t>>>=0)<=n||4294901760<t)return!1;for(var r=1;4>=r;r*=2){var i=n*(1+.2/r);i=Math.min(i,t+100663296);var a=Math;i=Math.max(t,i),a=a.min.call(a,4294901760,i+(65536-i%65536)%65536);t:{try{E.grow(a-D.byteLength+65535>>>16),z(E.buffer);var u=1;break t}catch(t){}u=void 0}if(u)return!0}return!1},Na:function(){throw"unwind"},Ga:Jt,Ha:Qt,J:lt,I:Kt,S:tn,ga:nn,R:an,d:function(){return un},na:function t(e,r){t.lc||(t.lc=function(){if("object"==typeof crypto&&"function"==typeof crypto.getRandomValues){var t=new Uint8Array(1);return()=>(crypto.getRandomValues(t),t[0])}if(v)try{var n=require("crypto");return()=>n.randomBytes(1)[0]}catch(t){}return()=>rt("randomDevice")}());for(var i=0;i<r;i++)n()[e+i>>0>>>0]=t.lc();return 0},ia:function(t,n,e){var r=Mn();try{return bt(t)(n,e)}catch(t){if(Cn(r),t!==t+0)throw t;Sn(1,0)}},ja:function(t,n,e){var r=Mn();try{return bt(t)(n,e)}catch(t){if(Cn(r),t!==t+0)throw t;Sn(1,0)}},K:function(t){var n=Mn();try{return bt(t)()}catch(t){if(Cn(n),t!==t+0)throw t;Sn(1,0)}},f:function(t,n){var e=Mn();try{return bt(t)(n)}catch(t){if(Cn(e),t!==t+0)throw t;Sn(1,0)}},P:function(t,n,e){var r=Mn();try{return bt(t)(n,e)}catch(t){if(Cn(r),t!==t+0)throw t;Sn(1,0)}},Q:function(t,n,e){var r=Mn();try{return bt(t)(n,e)}catch(t){if(Cn(r),t!==t+0)throw t;Sn(1,0)}},k:function(t,n,e){var r=Mn();try{return bt(t)(n,e)}catch(t){if(Cn(r),t!==t+0)throw t;Sn(1,0)}},p:function(t,n,e,r){var i=Mn();try{return bt(t)(n,e,r)}catch(t){if(Cn(i),t!==t+0)throw t;Sn(1,0)}},q:function(t,n,e,r,i){var a=Mn();try{return bt(t)(n,e,r,i)}catch(t){if(Cn(a),t!==t+0)throw t;Sn(1,0)}},N:function(t,n,e,r,i,a){var u=Mn();try{return bt(t)(n,e,r,i,a)}catch(t){if(Cn(u),t!==t+0)throw t;Sn(1,0)}},s:function(t,n,e,r,i,a){var u=Mn();try{return bt(t)(n,e,r,i,a)}catch(t){if(Cn(u),t!==t+0)throw t;Sn(1,0)}},w:function(t,n,e,r,i,a,u){var o=Mn();try{return bt(t)(n,e,r,i,a,u)}catch(t){if(Cn(o),t!==t+0)throw t;Sn(1,0)}},L:function(t,n,e,r,i,a,u,o){var c=Mn();try{return bt(t)(n,e,r,i,a,u,o)}catch(t){if(Cn(c),t!==t+0)throw t;Sn(1,0)}},E:function(t,n,e,r,i,a,u,o,c,f,s,l){var p=Mn();try{return bt(t)(n,e,r,i,a,u,o,c,f,s,l)}catch(t){if(Cn(p),t!==t+0)throw t;Sn(1,0)}},aa:function(t,n,e,r,i,a,u,o){var c=Mn();try{return Pn(t,n,e,r,i,a,u,o)}catch(t){if(Cn(c),t!==t+0)throw t;Sn(1,0)}},_:function(t,n,e,r,i,a,u){var o=Mn();try{return Dn(t,n,e,r,i,a,u)}catch(t){if(Cn(o),t!==t+0)throw t;Sn(1,0)}},Z:function(t,n,e,r,i){var a=Mn();try{return Hn(t,n,e,r,i)}catch(t){if(Cn(a),t!==t+0)throw t;Sn(1,0)}},ca:function(t,n,e,r){var i=Mn();try{return In(t,n,e,r)}catch(t){if(Cn(i),t!==t+0)throw t;Sn(1,0)}},$:function(t){var n=Mn();try{return kn(t)}catch(t){if(Cn(n),t!==t+0)throw t;Sn(1,0)}},ba:function(t,n){var e=Mn();try{return Wn(t,n)}catch(t){if(Cn(e),t!==t+0)throw t;Sn(1,0)}},Y:function(t,n,e){var r=Mn();try{return jn(t,n,e)}catch(t){if(Cn(r),t!==t+0)throw t;Sn(1,0)}},g:function(t){var n=Mn();try{bt(t)()}catch(t){if(Cn(n),t!==t+0)throw t;Sn(1,0)}},r:function(t,n){var e=Mn();try{bt(t)(n)}catch(t){if(Cn(e),t!==t+0)throw t;Sn(1,0)}},i:function(t,n,e){var r=Mn();try{bt(t)(n,e)}catch(t){if(Cn(r),t!==t+0)throw t;Sn(1,0)}},ha:function(t,n,e,r){var i=Mn();try{bt(t)(n,e,r)}catch(t){if(Cn(i),t!==t+0)throw t;Sn(1,0)}},m:function(t,n,e,r){var i=Mn();try{bt(t)(n,e,r)}catch(t){if(Cn(i),t!==t+0)throw t;Sn(1,0)}},v:function(t,n,e,r,i){var a=Mn();try{bt(t)(n,e,r,i)}catch(t){if(Cn(a),t!==t+0)throw t;Sn(1,0)}},u:function(t,n,e,r,i,a){var u=Mn();try{bt(t)(n,e,r,i,a)}catch(t){if(Cn(u),t!==t+0)throw t;Sn(1,0)}},O:function(t,n,e,r,i,a,u){var o=Mn();try{bt(t)(n,e,r,i,a,u)}catch(t){if(Cn(o),t!==t+0)throw t;Sn(1,0)}},A:function(t,n,e,r,i,a,u,o){var c=Mn();try{bt(t)(n,e,r,i,a,u,o)}catch(t){if(Cn(c),t!==t+0)throw t;Sn(1,0)}},ka:function(t,n,e,r,i,a,u,o,c){var f=Mn();try{bt(t)(n,e,r,i,a,u,o,c)}catch(t){if(Cn(f),t!==t+0)throw t;Sn(1,0)}},C:function(t,n,e,r,i,a,u,o,c,f,s){var l=Mn();try{bt(t)(n,e,r,i,a,u,o,c,f,s)}catch(t){if(Cn(l),t!==t+0)throw t;Sn(1,0)}},D:function(t,n,e,r,i,a,u,o,c,f,s,l,p,h,m,d){var y=Mn();try{bt(t)(n,e,r,i,a,u,o,c,f,s,l,p,h,m,d)}catch(t){if(Cn(y),t!==t+0)throw t;Sn(1,0)}},fa:function(t,n,e,r,i,a,u,o){var c=Mn();try{Fn(t,n,e,r,i,a,u,o)}catch(t){if(Cn(c),t!==t+0)throw t;Sn(1,0)}},da:function(t,n,e,r,i,a,u,o,c,f,s,l){var p=Mn();try{Yn(t,n,e,r,i,a,u,o,c,f,s,l)}catch(t){if(Cn(p),t!==t+0)throw t;Sn(1,0)}},ea:function(t,n,e,r,i,a){var u=Mn();try{Un(t,n,e,r,i,a)}catch(t){if(Cn(u),t!==t+0)throw t;Sn(1,0)}},o:function(t){return t},a:E||u.wasmMemory,G:function(t){un=t},la:sn,z:function(t,n,e,r){return sn(t,n,e,r)}};!function(){function t(t,n){u.asm=t.exports,pt.qc.push(u.asm.sb),V=u.asm.ub,X.unshift(u.asm.Va),k=n,w||(tt--,u.monitorRunDependencies&&u.monitorRunDependencies(tt),0==tt&&(null!==nt&&(clearInterval(nt),nt=null),et&&(t=et,et=null,t())))}function n(n){t(n.instance,n.module)}function e(t){return function(){if(!M&&(g||_)){if("function"==typeof fetch&&!K.startsWith("file://"))return fetch(K,{credentials:"same-origin"}).then((function(t){if(!t.ok)throw"failed to load wasm binary file at '"+K+"'";return t.arrayBuffer()})).catch((function(){return at()}));if(s)return new Promise((function(t,n){s(K,(function(n){t(new Uint8Array(n))}),n)}))}return Promise.resolve().then((function(){return at()}))}().then((function(t){return WebAssembly.instantiate(t,r)})).then((function(t){return t})).then(t,(function(t){x("failed to asynchronously prepare wasm: "+t),rt(t)}))}var r={a:pn};if(w||(tt++,u.monitorRunDependencies&&u.monitorRunDependencies(tt)),u.instantiateWasm)try{return u.instantiateWasm(r,t)}catch(t){return x("Module.instantiateWasm callback failed with error: "+t),!1}(M||"function"!=typeof WebAssembly.instantiateStreaming||it()||K.startsWith("file://")||v||"function"!=typeof fetch?e(n):fetch(K,{credentials:"same-origin"}).then((function(t){return WebAssembly.instantiateStreaming(t,r).then(n,(function(t){return x("wasm streaming compile failed: "+t),x("falling back to ArrayBuffer instantiation"),e(n)}))}))).catch(c)}(),u.___wasm_call_ctors=function(){return(u.___wasm_call_ctors=u.asm.Va).apply(null,arguments)},u._OrtInit=function(){return(u._OrtInit=u.asm.Wa).apply(null,arguments)},u._OrtCreateSessionOptions=function(){return(u._OrtCreateSessionOptions=u.asm.Xa).apply(null,arguments)},u._OrtAppendExecutionProvider=function(){return(u._OrtAppendExecutionProvider=u.asm.Ya).apply(null,arguments)},u._OrtAddSessionConfigEntry=function(){return(u._OrtAddSessionConfigEntry=u.asm.Za).apply(null,arguments)},u._OrtReleaseSessionOptions=function(){return(u._OrtReleaseSessionOptions=u.asm._a).apply(null,arguments)},u._OrtCreateSession=function(){return(u._OrtCreateSession=u.asm.$a).apply(null,arguments)},u._OrtReleaseSession=function(){return(u._OrtReleaseSession=u.asm.ab).apply(null,arguments)},u._OrtGetInputCount=function(){return(u._OrtGetInputCount=u.asm.bb).apply(null,arguments)},u._OrtGetOutputCount=function(){return(u._OrtGetOutputCount=u.asm.cb).apply(null,arguments)},u._OrtGetInputName=function(){return(u._OrtGetInputName=u.asm.db).apply(null,arguments)},u._OrtGetOutputName=function(){return(u._OrtGetOutputName=u.asm.eb).apply(null,arguments)},u._OrtFree=function(){return(u._OrtFree=u.asm.fb).apply(null,arguments)},u._OrtCreateTensor=function(){return(u._OrtCreateTensor=u.asm.gb).apply(null,arguments)},u._OrtGetTensorData=function(){return(u._OrtGetTensorData=u.asm.hb).apply(null,arguments)},u._OrtReleaseTensor=function(){return(u._OrtReleaseTensor=u.asm.ib).apply(null,arguments)},u._OrtCreateRunOptions=function(){return(u._OrtCreateRunOptions=u.asm.jb).apply(null,arguments)},u._OrtAddRunConfigEntry=function(){return(u._OrtAddRunConfigEntry=u.asm.kb).apply(null,arguments)},u._OrtReleaseRunOptions=function(){return(u._OrtReleaseRunOptions=u.asm.lb).apply(null,arguments)},u._OrtRun=function(){return(u._OrtRun=u.asm.mb).apply(null,arguments)},u._OrtEndProfiling=function(){return(u._OrtEndProfiling=u.asm.nb).apply(null,arguments)};var hn=u._pthread_self=function(){return(hn=u._pthread_self=u.asm.ob).apply(null,arguments)},mn=u._malloc=function(){return(mn=u._malloc=u.asm.pb).apply(null,arguments)},dn=u._free=function(){return(dn=u._free=u.asm.qb).apply(null,arguments)},yn=u._fflush=function(){return(yn=u._fflush=u.asm.rb).apply(null,arguments)};u.__emscripten_tls_init=function(){return(u.__emscripten_tls_init=u.asm.sb).apply(null,arguments)};var bn=u.___funcs_on_exit=function(){return(bn=u.___funcs_on_exit=u.asm.tb).apply(null,arguments)},gn=u.__emscripten_thread_init=function(){return(gn=u.__emscripten_thread_init=u.asm.vb).apply(null,arguments)};u.__emscripten_thread_crashed=function(){return(u.__emscripten_thread_crashed=u.asm.wb).apply(null,arguments)};var _n,vn=u._emscripten_run_in_main_runtime_thread_js=function(){return(vn=u._emscripten_run_in_main_runtime_thread_js=u.asm.xb).apply(null,arguments)},wn=u.__emscripten_proxy_execute_task_queue=function(){return(wn=u.__emscripten_proxy_execute_task_queue=u.asm.yb).apply(null,arguments)},Tn=u.__emscripten_thread_free_data=function(){return(Tn=u.__emscripten_thread_free_data=u.asm.zb).apply(null,arguments)},On=u.__emscripten_thread_exit=function(){return(On=u.__emscripten_thread_exit=u.asm.Ab).apply(null,arguments)},Sn=u._setThrew=function(){return(Sn=u._setThrew=u.asm.Bb).apply(null,arguments)},An=u._emscripten_stack_set_limits=function(){return(An=u._emscripten_stack_set_limits=u.asm.Cb).apply(null,arguments)},Mn=u.stackSave=function(){return(Mn=u.stackSave=u.asm.Db).apply(null,arguments)},Cn=u.stackRestore=function(){return(Cn=u.stackRestore=u.asm.Eb).apply(null,arguments)},xn=u.stackAlloc=function(){return(xn=u.stackAlloc=u.asm.Fb).apply(null,arguments)},Rn=u.___cxa_can_catch=function(){return(Rn=u.___cxa_can_catch=u.asm.Gb).apply(null,arguments)},En=u.___cxa_is_pointer_type=function(){return(En=u.___cxa_is_pointer_type=u.asm.Hb).apply(null,arguments)},kn=u.dynCall_j=function(){return(kn=u.dynCall_j=u.asm.Ib).apply(null,arguments)},Dn=u.dynCall_iiiiij=function(){return(Dn=u.dynCall_iiiiij=u.asm.Jb).apply(null,arguments)},jn=u.dynCall_jii=function(){return(jn=u.dynCall_jii=u.asm.Kb).apply(null,arguments)},Fn=u.dynCall_viiiiij=function(){return(Fn=u.dynCall_viiiiij=u.asm.Lb).apply(null,arguments)},Un=u.dynCall_vjji=function(){return(Un=u.dynCall_vjji=u.asm.Mb).apply(null,arguments)},Yn=u.dynCall_viiijjjii=function(){return(Yn=u.dynCall_viiijjjii=u.asm.Nb).apply(null,arguments)},In=u.dynCall_iij=function(){return(In=u.dynCall_iij=u.asm.Ob).apply(null,arguments)},Wn=u.dynCall_ji=function(){return(Wn=u.dynCall_ji=u.asm.Pb).apply(null,arguments)},Pn=u.dynCall_iiiiiij=function(){return(Pn=u.dynCall_iiiiiij=u.asm.Qb).apply(null,arguments)},Hn=u.dynCall_iiij=function(){return(Hn=u.dynCall_iiij=u.asm.Rb).apply(null,arguments)};function qn(){function t(){if(!_n&&(_n=!0,u.calledRun=!0,!W)&&(w||ht(X),o(u),u.onRuntimeInitialized&&u.onRuntimeInitialized(),!w)){if(u.postRun)for("function"==typeof u.postRun&&(u.postRun=[u.postRun]);u.postRun.length;){var t=u.postRun.shift();$.unshift(t)}ht($)}}if(!(0<tt))if(w)o(u),w||ht(X),postMessage({cmd:"loaded"});else{if(u.preRun)for("function"==typeof u.preRun&&(u.preRun=[u.preRun]);u.preRun.length;)Q();ht(L),0<tt||(u.setStatus?(u.setStatus("Running..."),setTimeout((function(){setTimeout((function(){u.setStatus("")}),1),t()}),1)):t())}}if(u.UTF8ToString=q,u.stringToUTF8=function(t,n,r){return B(t,e(),n,r)},u.lengthBytesUTF8=G,u.keepRuntimeAlive=J,u.wasmMemory=E,u.stackSave=Mn,u.stackRestore=Cn,u.stackAlloc=xn,u.ExitStatus=ot,u.PThread=pt,et=function t(){_n||qn(),_n||(et=t)},u.preInit)for("function"==typeof u.preInit&&(u.preInit=[u.preInit]);0<u.preInit.length;)u.preInit.pop()();return qn(),t.ready});"object"==typeof exports&&"object"==typeof module?module.exports=t:"function"==typeof define&&define.amd?define([],(function(){return t})):"object"==typeof exports&&(exports.ortWasmThreaded=t);
