{"version": 3, "file": "session-options.js", "sourceRoot": "", "sources": ["session-options.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAIlC,mDAAoD;AACpD,iDAA+C;AAC/C,iDAA2C;AAE3C,MAAM,wBAAwB,GAAG,CAAC,sBAAsC,EAAU,EAAE;IAClF,QAAQ,sBAAsB,EAAE;QAC9B,KAAK,UAAU;YACb,OAAO,CAAC,CAAC;QACX,KAAK,OAAO;YACV,OAAO,CAAC,CAAC;QACX,KAAK,UAAU;YACb,OAAO,CAAC,CAAC;QACX,KAAK,KAAK;YACR,OAAO,EAAE,CAAC;QACZ;YACE,MAAM,IAAI,KAAK,CAAC,yCAAyC,sBAAsB,EAAE,CAAC,CAAC;KACtF;AACH,CAAC,CAAC;AAEF,MAAM,gBAAgB,GAAG,CAAC,aAAsC,EAAU,EAAE;IAC1E,QAAQ,aAAa,EAAE;QACrB,KAAK,YAAY;YACf,OAAO,CAAC,CAAC;QACX,KAAK,UAAU;YACb,OAAO,CAAC,CAAC;QACX;YACE,MAAM,IAAI,KAAK,CAAC,+BAA+B,aAAa,EAAE,CAAC,CAAC;KACnE;AACH,CAAC,CAAC;AAEF,MAAM,oBAAoB,GAAG,CAAC,OAAwC,EAAQ,EAAE;IAC9E,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;QAClB,OAAO,CAAC,KAAK,GAAG,EAAE,CAAC;KACpB;IACD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE;QAC1B,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC;KAC5B;IACD,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,OAAiC,CAAC;IAChE,IAAI,CAAC,OAAO,CAAC,4BAA4B,EAAE;QACzC,qCAAqC;QACrC,OAAO,CAAC,4BAA4B,GAAG,GAAG,CAAC;KAC5C;AACH,CAAC,CAAC;AAEF,MAAM,qBAAqB,GACvB,CAAC,oBAA4B,EAAE,kBAAuE,EACrG,MAAgB,EAAQ,EAAE;IACzB,KAAK,MAAM,EAAE,IAAI,kBAAkB,EAAE;QACnC,IAAI,MAAM,GAAG,OAAO,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC;QAEnD,gBAAgB;QAChB,QAAQ,MAAM,EAAE;YACd,KAAK,SAAS;gBACZ,MAAM,GAAG,SAAS,CAAC;gBACnB,MAAM;YACR,KAAK,MAAM,CAAC;YACZ,KAAK,KAAK;gBACR,SAAS;YACX;gBACE,MAAM,IAAI,KAAK,CAAC,qBAAqB,MAAM,EAAE,CAAC,CAAC;SAClD;QAED,MAAM,gBAAgB,GAAG,IAAA,8BAAe,EAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACzD,IAAI,IAAA,0BAAW,GAAE,CAAC,2BAA2B,CAAC,oBAAoB,EAAE,gBAAgB,CAAC,KAAK,CAAC,EAAE;YAC3F,MAAM,IAAI,KAAK,CAAC,oCAAoC,MAAM,EAAE,CAAC,CAAC;SAC/D;KACF;AACH,CAAC,CAAC;AAEC,MAAM,iBAAiB,GAAG,CAAC,OAAyC,EAAsB,EAAE;IACjG,MAAM,IAAI,GAAG,IAAA,0BAAW,GAAE,CAAC;IAC3B,IAAI,oBAAoB,GAAG,CAAC,CAAC;IAC7B,MAAM,MAAM,GAAa,EAAE,CAAC;IAE5B,MAAM,cAAc,GAAoC,OAAO,IAAI,EAAE,CAAC;IACtE,oBAAoB,CAAC,cAAc,CAAC,CAAC;IAErC,IAAI;QACF,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,sBAAsB,MAAK,SAAS,EAAE;YACjD,cAAc,CAAC,sBAAsB,GAAG,KAAK,CAAC;SAC/C;QACD,MAAM,sBAAsB,GAAG,wBAAwB,CAAC,cAAc,CAAC,sBAAuB,CAAC,CAAC;QAEhG,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,iBAAiB,MAAK,SAAS,EAAE;YAC5C,cAAc,CAAC,iBAAiB,GAAG,IAAI,CAAC;SACzC;QAED,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,gBAAgB,MAAK,SAAS,EAAE;YAC3C,cAAc,CAAC,gBAAgB,GAAG,IAAI,CAAC;SACxC;QAED,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa,MAAK,SAAS,EAAE;YACxC,cAAc,CAAC,aAAa,GAAG,YAAY,CAAC;SAC7C;QACD,MAAM,aAAa,GAAG,gBAAgB,CAAC,cAAc,CAAC,aAAc,CAAC,CAAC;QAEtE,IAAI,eAAe,GAAG,CAAC,CAAC;QACxB,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK,MAAK,SAAS,EAAE;YAChC,eAAe,GAAG,IAAA,8BAAe,EAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;SAC1D;QAED,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,gBAAgB,MAAK,SAAS,EAAE;YAC3C,cAAc,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAE,qBAAqB;SAC5D;aAAM,IACH,OAAO,OAAO,CAAC,gBAAgB,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,gBAAgB,CAAC;YAC3F,OAAO,CAAC,gBAAgB,GAAG,CAAC,IAAI,OAAO,CAAC,gBAAgB,GAAG,CAAC,EAAE;YAChE,MAAM,IAAI,KAAK,CAAC,qCAAqC,OAAO,CAAC,gBAAgB,EAAE,CAAC,CAAC;SAClF;QAED,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,iBAAiB,MAAK,SAAS,EAAE;YAC5C,cAAc,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAE,eAAe;SACvD;aAAM,IAAI,OAAO,OAAO,CAAC,iBAAiB,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE;YACxG,MAAM,IAAI,KAAK,CAAC,qCAAqC,OAAO,CAAC,iBAAiB,EAAE,CAAC,CAAC;SACnF;QAED,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,eAAe,MAAK,SAAS,EAAE;YAC1C,cAAc,CAAC,eAAe,GAAG,KAAK,CAAC;SACxC;QAED,oBAAoB,GAAG,IAAI,CAAC,wBAAwB,CAChD,sBAAsB,EAAE,CAAC,CAAC,cAAc,CAAC,iBAAkB,EAAE,CAAC,CAAC,cAAc,CAAC,gBAAiB,EAAE,aAAa,EAC9G,CAAC,CAAC,cAAc,CAAC,eAAgB,EAAE,CAAC,EAAE,eAAe,EAAE,cAAc,CAAC,gBAAiB,EACvF,cAAc,CAAC,iBAAkB,CAAC,CAAC;QACvC,IAAI,oBAAoB,KAAK,CAAC,EAAE;YAC9B,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;SAClD;QAED,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,kBAAkB,EAAE;YAC/B,qBAAqB,CAAC,oBAAoB,EAAE,OAAO,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;SACjF;QAED,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK,MAAK,SAAS,EAAE;YAChC,IAAA,mCAAmB,EAAC,OAAO,CAAC,KAAK,EAAE,EAAE,EAAE,IAAI,OAAO,EAA2B,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;gBAC5F,MAAM,aAAa,GAAG,IAAA,8BAAe,EAAC,GAAG,EAAE,MAAM,CAAC,CAAC;gBACnD,MAAM,eAAe,GAAG,IAAA,8BAAe,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC;gBAEvD,IAAI,IAAI,CAAC,yBAAyB,CAAC,oBAAoB,EAAE,aAAa,EAAE,eAAe,CAAC,KAAK,CAAC,EAAE;oBAC9F,MAAM,IAAI,KAAK,CAAC,qCAAqC,GAAG,MAAM,KAAK,EAAE,CAAC,CAAC;iBACxE;YACH,CAAC,CAAC,CAAC;SACJ;QAED,OAAO,CAAC,oBAAoB,EAAE,MAAM,CAAC,CAAC;KACvC;IAAC,OAAO,CAAC,EAAE;QACV,IAAI,oBAAoB,KAAK,CAAC,EAAE;YAC9B,IAAI,CAAC,yBAAyB,CAAC,oBAAoB,CAAC,CAAC;SACtD;QACD,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3B,MAAM,CAAC,CAAC;KACT;AACH,CAAC,CAAC;AAjFW,QAAA,iBAAiB,qBAiF5B"}