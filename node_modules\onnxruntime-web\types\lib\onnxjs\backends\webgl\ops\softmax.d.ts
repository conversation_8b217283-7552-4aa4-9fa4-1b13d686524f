import { Attribute<PERSON>ithCache<PERSON>ey } from '../../../attribute-with-cache-key';
import { OperatorImplementation, OperatorInitialization } from '../../../operators';
export interface SoftmaxAttributes extends AttributeWithCacheKey {
    readonly axis: number;
}
export declare const softmax: OperatorImplementation<SoftmaxAttributes>;
export declare const parseSoftmaxAttributes: OperatorInitialization<SoftmaxAttributes>;
export declare const parseSoftmaxAttributesV13: OperatorInitialization<SoftmaxAttributes>;
export declare const softmaxV13: OperatorImplementation<SoftmaxAttributes>;
