import { AttributeWithCache<PERSON>ey } from '../../../attribute-with-cache-key';
import { OperatorImplementation, OperatorInitialization } from '../../../operators';
export interface ImageScalerAttributes extends AttributeWithCacheKey {
    scale: number;
    bias: number[];
}
export declare const imageScaler: OperatorImplementation<ImageScalerAttributes>;
export declare const parseImageScalerAttributes: OperatorInitialization<ImageScalerAttributes>;
