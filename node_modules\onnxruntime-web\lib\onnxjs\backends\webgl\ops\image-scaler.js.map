{"version": 3, "file": "image-scaler.js", "sourceRoot": "", "sources": ["image-scaler.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAElC,gFAAqG;AAKrG,oCAAsF;AAO/E,MAAM,WAAW,GACpB,CAAC,gBAAuC,EAAE,MAAgB,EAAE,UAAiC,EAAY,EAAE;IACzG,cAAc,CAAC,MAAM,CAAC,CAAC;IACvB,MAAM,MAAM,GACR,gBAAgB,CAAC,GAAG,CAAC,kCAAkC,CAAC,gBAAgB,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM,CAAC,CAAC;IAC3G,OAAO,CAAC,MAAM,CAAC,CAAC;AAClB,CAAC,CAAC;AANO,QAAA,WAAW,eAMlB;AAEC,MAAM,0BAA0B,GACnC,CAAC,IAAgB,EAAyB,EAAE;IAC1C,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IAChD,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAC/C,OAAO,IAAA,sDAA2B,EAAC,EAAC,KAAK,EAAE,IAAI,EAAC,CAAC,CAAC;AACpD,CAAC,CAAC;AALO,QAAA,0BAA0B,8BAKjC;AAEN,MAAM,0BAA0B,GAAG;IACjC,IAAI,EAAE,aAAa;IACnB,UAAU,EAAE,CAAC,GAAG,CAAC;IACjB,UAAU,EAAE,CAAC,mBAAW,CAAC,QAAQ,CAAC;CACnC,CAAC;AAEF,MAAM,4BAA4B,GAC9B,CAAC,OAA8B,EAAE,QAAyB,EAAE,MAAgB,EAAE,UAAiC,EAC/F,EAAE;IACZ,MAAM,WAAW,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;IAC3C,MAAM,IAAI,GAAG,WAAW,CAAC,MAAM,CAAC;IAChC,MAAM,aAAa,GAAG,mBAAmB,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAClE,MAAM,YAAY,GAAG;QACvB,aAAa;kCACa,IAAI;;QAE9B,CAAC;IACC,uCACK,QAAQ,KACX,MAAM,EAAE,EAAC,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,WAAW,EAAE,mBAAW,CAAC,QAAQ,EAAC,EACpF,SAAS,EAAE;YACT,EAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,EAAC;YACzF,EAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,CAAC,KAAK,EAAC;SACvD,EACD,YAAY,IACZ;AACJ,CAAC,CAAC;AAEV,MAAM,kCAAkC,GACpC,CAAC,OAA8B,EAAE,MAAgB,EAAE,UAAiC,EAAqB,EAAE;IACzG,MAAM,QAAQ,mCAAO,0BAA0B,KAAE,SAAS,EAAE,UAAU,CAAC,QAAQ,GAAC,CAAC;IACjF,uCAAW,QAAQ,KAAE,GAAG,EAAE,GAAG,EAAE,CAAC,4BAA4B,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC,IAAE;AACvG,CAAC,CAAC;AAEN,MAAM,mBAAmB,GAAG,CAAC,WAAmB,EAAU,EAAE;IAC1D,MAAM,SAAS,GAAa,CAAC,4BAA4B,WAAW,mBAAmB,CAAC,CAAC;IACzF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,KAAK,CAAC,EAAE;YACX,SAAS,CAAC,IAAI,CACV,IAAI;gBACJ,kBAAkB,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;SACpD;aAAM,IAAI,CAAC,KAAK,WAAW,GAAG,CAAC,EAAE;YAChC,SAAS,CAAC,IAAI,CACV,IAAI;gBACJ,sBAAsB,CAAC,MAAM,CAAC,CAAC;SACpC;aAAM;YACL,SAAS,CAAC,IAAI,CACV,IAAI;gBACJ,uBAAuB,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;SACzD;KACF;IACD,SAAS,CAAC,IAAI,CACV,IAAI;QACJ,GAAG,CAAC,CAAC;IACT,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC9B,CAAC,CAAC;AAEF,MAAM,cAAc,GAAG,CAAC,MAAgB,EAAQ,EAAE;IAChD,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QAClC,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;KAClD;IACD,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QAC/B,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;KACzC;IACD,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,EAAE;QAChE,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;KACxC;AACH,CAAC,CAAC"}