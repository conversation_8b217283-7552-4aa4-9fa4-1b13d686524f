{"version": 3, "file": "glsl-preprocessor.js", "sourceRoot": "", "sources": ["glsl-preprocessor.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAElC,yDAAyG;AACzG,mEAAuD;AACvD,iEAAoD;AACpD,+CAA8E;AAI9E;;;;;;;GAOG;AACH,MAAa,gBAAgB;IAK3B,YACI,SAAuB,EAAE,WAAwB,EAAE,mBAAoC,EACvF,mBAAkC;QAL7B,SAAI,GAA8B,EAAE,CAAC;QACrC,kCAA6B,GAAgD,EAAE,CAAC;QAKvF,IAAI,CAAC,OAAO,GAAG,IAAI,8BAAW,CAAC,SAAS,EAAE,WAAW,EAAE,mBAAmB,EAAE,mBAAmB,CAAC,CAAC;QAEjG,qBAAqB;QACrB,MAAM,CAAC,IAAI,CAAC,mCAAY,CAAC,CAAC,OAAO,CAAC,CAAC,IAAY,EAAE,EAAE;YACjD,MAAM,GAAG,GAAG,IAAI,mCAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACjD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;QACxB,CAAC,CAAC,CAAC;QAEH,uCAAuC;QACvC,MAAM,GAAG,GAAG,IAAI,CAAC,6BAA6B,CAAC;QAC/C,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,IAAI,EAAE;YAC/B,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC/B,MAAM,aAAa,GAAG,GAAG,CAAC,YAAY,EAAE,CAAC;YACzC,KAAK,MAAM,OAAO,IAAI,aAAa,EAAE;gBACnC,MAAM,GAAG,GAAG,OAAO,GAAG,GAAG,GAAG,OAAO,CAAC;gBACpC,IAAI,WAA+B,CAAC;gBACpC,IAAI,GAAG,CAAC,GAAG,CAAC,EAAE;oBACZ,WAAW,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;oBACvB,WAAW,CAAC,WAAW,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC,WAAW,CAAC;iBAC9D;qBAAM;oBACL,WAAW,GAAG,IAAI,qCAAkB,CAAC,GAAG,EAAE,aAAa,CAAC,OAAO,CAAC,CAAC,WAAW,CAAC,CAAC;oBAC9E,GAAG,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC;iBACxB;gBACD,MAAM,YAAY,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC;gBACzD,IAAI,YAAY,EAAE;oBAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;wBAC5C,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE;4BACzB,MAAM,IAAI,GAAG,IAAI,qCAAkB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;4BACrD,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;4BAC5B,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;yBACjC;6BAAM;4BACL,WAAW,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;yBACjD;qBACF;iBACF;aACF;SACF;IACH,CAAC;IAED,UAAU;QACR,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;QAC7C,IAAI,MAAM,GAAG,WAAW,CAAC,YAAY,CAAC;QAEtC,yBAAyB;QACzB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE;YACrC,MAAM,GAAG,GAAG,MAAM;QAChB,IAAA,sCAAwB,EAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC;SAC7G;QACD,kBAAkB;QAClB,MAAM,GAAG,IAAA,sCAAc,EAAC,MAAM,CAAC,CAAC;QAEhC,6BAA6B;QAC7B,OAAO,GAAG,IAAA,mCAAqB,EAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC;MAC7D,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,UAAU,EAAE,WAAW,CAAC,SAAS,CAAC;MAC/D,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;MACvB,MAAM,EAAE,CAAC;IACb,CAAC;IAES,UAAU,CAAC,MAAc;QACjC,MAAM,gBAAgB,GAAG,IAAI,CAAC,iCAAiC,CAAC,MAAM,CAAC,CAAC;QAExE,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;YACjC,OAAO,EAAE,CAAC;SACX;QAED,IAAI,QAAQ,GAAG,EAAE,CAAC;QAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YAChD,IAAI,gBAAgB,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE;gBACnC,QAAQ,IAAI,gBAAgB,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC;aACpD;iBAAM;gBACL,MAAM,IAAI,KAAK,CAAC,8CAA8C,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;aAC3F;SACF;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IACO,iCAAiC,CAAC,MAAc;QACtD,MAAM,KAAK,GAAyB,EAAE,CAAC;QAEvC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE;YACxE,MAAM,OAAO,GAAG,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9C,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;gBAClC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC,eAAe,CAAC,CAAC,CAAC;aACjE;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,8CAA2B,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;IAC/D,CAAC;IAES,WAAW,CAAC,QAAmB,EAAE,SAA0B;QACnE,MAAM,YAAY,GAAa,EAAE,CAAC;QAClC,IAAI,QAAQ,EAAE;YACZ,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;gBAC9B,YAAY,CAAC,IAAI,CAAC,qBAAqB,OAAO,GAAG,CAAC,CAAC;aACpD;SACF;QACD,IAAI,SAAS,EAAE;YACb,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;gBAChC,YAAY,CAAC,IAAI,CACb,WAAW,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;aAC7G;SACF;QACD,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;CACF;AAhHD,4CAgHC"}