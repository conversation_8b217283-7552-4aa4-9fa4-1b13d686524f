{"version": 3, "file": "proxy-wrapper.js", "sourceRoot": "", "sources": ["proxy-wrapper.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;AAElC,2DAAyD;AAGzD,uDAAyC;AACzC,iDAAqD;AAErD,MAAM,OAAO,GAAG,GAAY,EAAE,CAAC,CAAC,CAAC,wBAAG,CAAC,IAAI,CAAC,KAAK,IAAI,OAAO,QAAQ,KAAK,WAAW,CAAC;AACnF,IAAI,WAA6B,CAAC;AAClC,IAAI,YAAY,GAAG,KAAK,CAAC;AACzB,IAAI,WAAW,GAAG,KAAK,CAAC;AACxB,IAAI,OAAO,GAAG,KAAK,CAAC;AAKpB,IAAI,iBAAmC,CAAC;AACxC,IAAI,gBAAkC,CAAC;AACvC,MAAM,8BAA8B,GAAmD,EAAE,CAAC;AAC1F,MAAM,8BAA8B,GAAyD,EAAE,CAAC;AAChG,MAAM,sBAAsB,GAAyD,EAAE,CAAC;AACxF,MAAM,uBAAuB,GAAkC,EAAE,CAAC;AAClE,MAAM,YAAY,GAAkD,EAAE,CAAC;AACvE,MAAM,qBAAqB,GAAkC,EAAE,CAAC;AAEhE,MAAM,YAAY,GAAG,GAAS,EAAE;IAC9B,IAAI,YAAY,IAAI,CAAC,WAAW,IAAI,OAAO,IAAI,CAAC,WAAW,EAAE;QAC3D,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;KACrC;AACH,CAAC,CAAC;AAEF,MAAM,oBAAoB,GAAG,CAAC,EAAgC,EAAQ,EAAE;IACtE,QAAQ,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE;QACpB,KAAK,WAAW;YACd,YAAY,GAAG,KAAK,CAAC;YACrB,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;gBACf,OAAO,GAAG,IAAI,CAAC;gBACf,iBAAiB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACnC;iBAAM;gBACL,WAAW,GAAG,IAAI,CAAC;gBACnB,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC;aACxB;YACD,MAAM;QACR,KAAK,UAAU;YACb,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;gBACf,gBAAgB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aAClC;iBAAM;gBACL,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC;aACvB;YACD,MAAM;QACR,KAAK,iBAAiB;YACpB,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;gBACf,8BAA8B,CAAC,KAAK,EAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACzD;iBAAM;gBACL,8BAA8B,CAAC,KAAK,EAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAI,CAAC,CAAC;aAC1D;YACD,MAAM;QACR,KAAK,iBAAiB;YACpB,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;gBACf,8BAA8B,CAAC,KAAK,EAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACzD;iBAAM;gBACL,8BAA8B,CAAC,KAAK,EAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAI,CAAC,CAAC;aAC1D;YACD,MAAM;QACR,KAAK,QAAQ;YACX,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;gBACf,sBAAsB,CAAC,KAAK,EAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACjD;iBAAM;gBACL,sBAAsB,CAAC,KAAK,EAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAI,CAAC,CAAC;aAClD;YACD,MAAM;QACR,KAAK,SAAS;YACZ,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;gBACf,uBAAuB,CAAC,KAAK,EAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aAClD;iBAAM;gBACL,uBAAuB,CAAC,KAAK,EAAG,CAAC,CAAC,CAAC,EAAE,CAAC;aACvC;YACD,MAAM;QACR,KAAK,KAAK;YACR,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;gBACf,YAAY,CAAC,KAAK,EAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACvC;iBAAM;gBACL,YAAY,CAAC,KAAK,EAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAI,CAAC,CAAC;aACxC;YACD,MAAM;QACR,KAAK,eAAe;YAClB,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;gBACf,qBAAqB,CAAC,KAAK,EAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aAChD;iBAAM;gBACL,qBAAqB,CAAC,KAAK,EAAG,CAAC,CAAC,CAAC,EAAE,CAAC;aACrC;YACD,MAAM;QACR,QAAQ;KACT;AACH,CAAC,CAAC;AAEF,MAAM,SAAS,GAAG,OAAO,QAAQ,KAAK,WAAW,CAAC,CAAC,CAAC,MAAC,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,aAAmC,0CAAE,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC;AAE7G,MAAM,QAAQ,GAAG,KAAK,IAAkB,EAAE;IAC/C,IAAI,CAAC,UAAU,CAAC,kBAAkB,IAAI,OAAO,EAAE,EAAE;QAC/C,IAAI,WAAW,EAAE;YACf,OAAO;SACR;QACD,IAAI,YAAY,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;SAC/D;QACD,IAAI,OAAO,EAAE;YACX,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;SAC5D;QAED,YAAY,GAAG,IAAI,CAAC;QAEpB,2BAA2B;QAC3B,IAAI,wBAAG,CAAC,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE;YACpC,IAAI,SAAS,IAAI,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACjD,wBAAG,CAAC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;aAC7E;SACF;QAED,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC3C,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,SAAS,EAAE,CAAC;YACzB,qGAAqG;YACrG,WAAW,GAAG,OAAO,CAAC,sDAAsD,CAAC,CAAC,OAAO,EAAY,CAAC;YAClG,WAAW,CAAC,SAAS,GAAG,oBAAoB,CAAC;YAC7C,iBAAiB,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YACtC,MAAM,OAAO,GAAmB,EAAC,IAAI,EAAE,WAAW,EAAE,EAAE,EAAG,wBAAG,CAAC,IAAI,EAAC,CAAC;YACnE,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;KAEJ;SAAM;QACL,OAAO,IAAA,oCAAqB,EAAC,wBAAG,CAAC,IAAI,CAAC,CAAC;KACxC;AACH,CAAC,CAAC;AAlCW,QAAA,QAAQ,YAkCnB;AAEK,MAAM,OAAO,GAAG,KAAK,EAAC,UAAkB,EAAE,YAAoB,EAAiB,EAAE;IACtF,IAAI,CAAC,UAAU,CAAC,kBAAkB,IAAI,OAAO,EAAE,EAAE;QAC/C,YAAY,EAAE,CAAC;QACf,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC3C,gBAAgB,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YACrC,MAAM,OAAO,GAAmB,EAAC,IAAI,EAAE,UAAU,EAAE,EAAE,EAAG,EAAC,UAAU,EAAE,YAAY,EAAC,EAAC,CAAC;YACpF,WAAY,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;KACJ;SAAM;QACL,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;KACxC;AACH,CAAC,CAAC;AAXW,QAAA,OAAO,WAWlB;AAEK,MAAM,qBAAqB,GAAG,KAAK,EAAC,KAAiB,EAAkC,EAAE;IAC9F,IAAI,CAAC,UAAU,CAAC,kBAAkB,IAAI,OAAO,EAAE,EAAE;QAC/C,YAAY,EAAE,CAAC;QACf,OAAO,IAAI,OAAO,CAAwB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC5D,8BAA8B,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;YACvD,MAAM,OAAO,GAAmB,EAAC,IAAI,EAAE,iBAAiB,EAAE,EAAE,EAAG,EAAC,KAAK,EAAC,EAAC,CAAC;YACxE,WAAY,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;KACJ;SAAM;QACL,OAAO,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;KAC1C;AACH,CAAC,CAAC;AAXW,QAAA,qBAAqB,yBAWhC;AAEK,MAAM,qBAAqB,GAAG,KAAK,EAAC,SAAgC,EAAE,OAAyC,EAC7E,EAAE;IACrC,IAAI,CAAC,UAAU,CAAC,kBAAkB,IAAI,OAAO,EAAE,EAAE;QAC/C,YAAY,EAAE,CAAC;QACf,OAAO,IAAI,OAAO,CAA8B,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAClE,8BAA8B,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;YACvD,MAAM,OAAO,GAAmB,EAAC,IAAI,EAAE,iBAAiB,EAAE,EAAE,EAAG,EAAC,SAAS,EAAE,OAAO,EAAC,EAAC,CAAC;YACrF,WAAY,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;KACJ;SAAM;QACL,OAAO,IAAI,CAAC,qBAAqB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;KACvD;AACH,CAAC,CAAC;AAZO,QAAA,qBAAqB,yBAY5B;AAEC,MAAM,aAAa,GACtB,KAAK,EAAC,KAAiB,EAAE,OAAyC,EAAwC,EAAE;IAC9G,IAAI,CAAC,UAAU,CAAC,kBAAkB,IAAI,OAAO,EAAE,EAAE;QAC/C,YAAY,EAAE,CAAC;QACf,OAAO,IAAI,OAAO,CAA8B,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAClE,sBAAsB,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;YAC/C,MAAM,OAAO,GAAmB,EAAC,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAG,EAAC,KAAK,EAAE,OAAO,EAAC,EAAC,CAAC;YACxE,WAAY,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;KACJ;SAAM;QACL,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;KAC3C;AACH,CAAC,CAAC;AAZW,QAAA,aAAa,iBAYxB;AAEK,MAAM,cAAc,GAAG,KAAK,EAAC,SAAiB,EAAiB,EAAE;IACtE,IAAI,CAAC,UAAU,CAAC,kBAAkB,IAAI,OAAO,EAAE,EAAE;QAC/C,YAAY,EAAE,CAAC;QACf,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC3C,uBAAuB,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;YAChD,MAAM,OAAO,GAAmB,EAAC,IAAI,EAAE,SAAS,EAAE,EAAE,EAAG,SAAS,EAAC,CAAC;YAClE,WAAY,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;KACJ;SAAM;QACL,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;KAChC;AACH,CAAC,CAAC;AAXW,QAAA,cAAc,kBAWzB;AAEK,MAAM,GAAG,GAAG,KAAK,EACpB,SAAiB,EAAE,YAAsB,EAAE,MAA4B,EAAE,aAAuB,EAChG,OAAoC,EAAiC,EAAE;IACzE,IAAI,CAAC,UAAU,CAAC,kBAAkB,IAAI,OAAO,EAAE,EAAE;QAC/C,YAAY,EAAE,CAAC;QACf,OAAO,IAAI,OAAO,CAAuB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC3D,YAAY,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;YACrC,MAAM,OAAO,GAAmB,EAAC,IAAI,EAAE,KAAK,EAAE,EAAE,EAAG,EAAC,SAAS,EAAE,YAAY,EAAE,MAAM,EAAE,aAAa,EAAE,OAAO,EAAC,EAAC,CAAC;YAC9G,WAAY,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC,CAAC;QAC7E,CAAC,CAAC,CAAC;KACJ;SAAM;QACL,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,YAAY,EAAE,MAAM,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;KAC1E;AACH,CAAC,CAAC;AAbW,QAAA,GAAG,OAad;AAEK,MAAM,YAAY,GAAG,KAAK,EAAC,SAAiB,EAAiB,EAAE;IACpE,IAAI,CAAC,UAAU,CAAC,kBAAkB,IAAI,OAAO,EAAE,EAAE;QAC/C,YAAY,EAAE,CAAC;QACf,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC3C,qBAAqB,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;YAC9C,MAAM,OAAO,GAAmB,EAAC,IAAI,EAAE,eAAe,EAAE,EAAE,EAAG,SAAS,EAAC,CAAC;YACxE,WAAY,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;KACJ;SAAM;QACL,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;KAC9B;AACH,CAAC,CAAC;AAXW,QAAA,YAAY,gBAWvB"}