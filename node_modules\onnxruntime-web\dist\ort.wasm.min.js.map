{"version": 3, "file": "ort.wasm.min.js", "mappings": ";;;;;CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAa,IAAID,IAEjBD,EAAU,IAAIC,GACf,CATD,CASGK,MAAM,I,4HCPT,MAAMC,EAAW,CAAC,EACZC,EAA2B,GAWpBC,EAAkB,CAACC,EAAMC,EAASC,KAC3C,IAAID,GAAmC,mBAAjBA,EAAQE,MAA+D,mBAAjCF,EAAQG,qBA6BpE,MAAM,IAAIC,UAAU,uBA7BpB,CACI,MAAMC,EAAiBT,EAASG,GAChC,QAAuBO,IAAnBD,EACAT,EAASG,GAAQ,CAAEC,UAASC,gBAE3B,IAAII,EAAeJ,SAAWA,EAE/B,OAEC,GAAII,EAAeJ,WAAaA,GAC7BI,EAAeL,UAAYA,EAC3B,MAAM,IAAIO,MAAM,4BAA4BR,qBAAwBE,IAE5E,CACA,GAAIA,GAAY,EAAG,CACf,MAAMO,EAAIX,EAAyBY,QAAQV,IAChC,IAAPS,GACAX,EAAyBa,OAAOF,EAAG,GAEvC,IAAK,IAAIA,EAAI,EAAGA,EAAIX,EAAyBc,OAAQH,IACjD,GAAIZ,EAASC,EAAyBW,IAAIP,UAAYA,EAElD,YADAJ,EAAyBa,OAAOF,EAAG,EAAGT,GAI9CF,EAAyBe,KAAKb,EAClC,CAEJ,CAC0C,ECtCjCc,EAAM,ICJZ,MACHC,cACIC,KAAKC,KAAO,CAAC,EACbD,KAAKE,MAAQ,CAAC,EACdF,KAAKG,iBAAmB,SAC5B,CAEIC,aAASC,GACT,QAAcd,IAAVc,EAAJ,CAGA,GAAqB,iBAAVA,IAA2F,IAArE,CAAC,UAAW,OAAQ,UAAW,QAAS,SAASX,QAAQW,GACtF,MAAM,IAAIb,MAAM,8BAA8Ba,KAElDL,KAAKG,iBAAmBE,CAJxB,CAKJ,CACID,eACA,OAAOJ,KAAKG,gBAChB,GClBEG,EAAoD,oBAAlBC,eAA+D,mBAAvBA,cAAcC,KACxFC,EAAsD,oBAAnBC,gBAAiE,mBAAxBA,eAAeF,KAE3FG,EAAwC,IAAIC,IAAI,CAClD,CAAC,UAAWC,cACZ,CAAC,QAASC,YACV,CAAC,OAAQC,WACT,CAAC,SAAUC,aACX,CAAC,QAASC,YACV,CAAC,QAASC,YACV,CAAC,OAAQJ,YACT,CAAC,UAAWK,cACZ,CAAC,SAAUC,eAGTC,EAAwC,IAAIT,IAAI,CAClD,CAACC,aAAc,WACf,CAACC,WAAY,SACb,CAACC,UAAW,QACZ,CAACC,YAAa,UACd,CAACC,WAAY,SACb,CAACC,WAAY,SACb,CAACC,aAAc,WACf,CAACC,YAAa,YAEdd,IACAK,EAAsCW,IAAI,QAASf,eACnDc,EAAsCC,IAAIf,cAAe,UAEzDE,IACAE,EAAsCW,IAAI,SAAUZ,gBACpDW,EAAsCC,IAAIZ,eAAgB,WAqBvD,MAAMa,EACTxB,YAAYyB,EAAMC,EAAMC,GACpB,IAAIC,EACAC,EACAC,EAEJ,GAAoB,iBAATL,EAMP,GAFAG,EAAOH,EACPK,EAAOH,EACM,WAATF,EAAmB,CAEnB,IAAKM,MAAMC,QAAQN,GACf,MAAM,IAAIpC,UAAU,kDAIxBuC,EAAOH,CACX,KACK,CAED,MAAMO,EAAwBrB,EAAsCsB,IAAIT,GACxE,QAA8BjC,IAA1ByC,EACA,MAAM,IAAI3C,UAAU,4BAA4BmC,MAEpD,GAAIM,MAAMC,QAAQN,GAKdG,EAAOI,EAAsBxB,KAAKiB,OAEjC,MAAIA,aAAgBO,GAIrB,MAAM,IAAI3C,UAAU,KAAKsC,mCAAsCK,KAH/DJ,EAAOH,CAIX,CACJ,MAOA,GADAI,EAAOJ,EACHK,MAAMC,QAAQP,GAAO,CAErB,GAAoB,IAAhBA,EAAK5B,OACL,MAAM,IAAIP,UAAU,uDAExB,MAAM6C,SAA0BV,EAAK,GACrC,GAAyB,WAArBU,EACAP,EAAO,SACPC,EAAOJ,MAEN,IAAyB,YAArBU,EAQL,MAAM,IAAI7C,UAAU,uCAAuC6C,MAP3DP,EAAO,OAIPC,EAAOd,WAAWN,KAAKgB,EAI3B,CACJ,KACK,CAED,MAAMW,EAAad,EAAsCY,IAAIT,EAAKzB,aAClE,QAAmBR,IAAf4C,EACA,MAAM,IAAI9C,UAAU,qCAAqCmC,EAAKzB,gBAElE4B,EAAOQ,EACPP,EAAOJ,CACX,CAGJ,QAAajC,IAATsC,EAEAA,EAAO,CAACD,EAAKhC,aAEZ,IAAKkC,MAAMC,QAAQF,GACpB,MAAM,IAAIxC,UAAU,0CAGxB,MAAM+C,EArGQ,CAACP,IACnB,IAAIO,EAAO,EACX,IAAK,IAAI3C,EAAI,EAAGA,EAAIoC,EAAKjC,OAAQH,IAAK,CAClC,MAAM4C,EAAMR,EAAKpC,GACjB,GAAmB,iBAAR4C,IAAqBC,OAAOC,cAAcF,GACjD,MAAM,IAAIhD,UAAU,QAAQI,+BAA+B4C,KAE/D,GAAIA,EAAM,EACN,MAAM,IAAIG,WAAW,QAAQ/C,2CAA2C4C,KAE5ED,GAAQC,CACZ,CACA,OAAOD,CAAI,EAyFMK,CAAcZ,GAC3B,GAAIO,IAASR,EAAKhC,OACd,MAAM,IAAIJ,MAAM,iBAAiB4C,iCAAoCR,EAAKhC,YAE9EI,KAAK6B,KAAOA,EACZ7B,KAAK2B,KAAOA,EACZ3B,KAAK4B,KAAOA,EACZ5B,KAAKoC,KAAOA,CAChB,CASAM,sBAAsBC,EAAQC,GAC1B,QAAerD,IAAXoD,EACA,MAAM,IAAInD,MAAM,gCAEpB,QAAuBD,IAAnBqD,EAAQC,aAA0CtD,IAAlBqD,EAAQE,MACxC,MAAM,IAAItD,MAAM,0CAEpB,MAAM,OAAEqD,EAAM,MAAEC,GAAUF,EACpBG,EAAOH,EAAQG,KACrB,IAAIC,EACAC,EAEAD,OADSzD,IAATwD,QAAoCxD,IAAdwD,EAAKG,KAChB,IAGAH,EAAKG,KAGhBD,OADS1D,IAATwD,QAAoCxD,IAAdwD,EAAKI,KAChB,EAGAJ,EAAKI,KAEpB,MAAMC,OAAuC7D,IAAzBqD,EAAQS,aAA6BT,EAAQS,aAAe,OAE1EC,OAAwC/D,IAAzBqD,EAAQW,mBACChE,IAAzBqD,EAAQW,aAA6BX,EAAQW,aAC9C,MACEC,EAASX,EAASC,EAClBW,EAA+B,SAAjBH,EAA0B,IAAIzC,aAAsB,EAAT2C,GAAc,IAAI3C,aAAsB,EAAT2C,GAE9F,IAAIE,EAAO,EAAGC,EAAgB,EAAGC,EAAgB,EAAGC,EAAgB,EAAGC,EAAgB,EACnFC,EAAiB,EAAGC,EAAiBR,EAAQS,EAA0B,EAATT,EAAYU,GAAkB,EAE5E,QAAhBd,IACAM,EAAO,EACPC,EAAgB,EAChBC,EAAgB,EAChBC,EAAgB,EAChBC,GAAiB,GAGA,SAAjBR,EACAY,EAA0B,EAATV,EAEK,QAAjBF,GACLS,EAAiB,EACjBE,EAAiBT,EACjBQ,EAA0B,EAATR,GAEK,QAAjBF,IACLW,EAAiB,EACjBD,EAAiBR,EACjBO,EAA0B,EAATP,GAErB,IAAK,IAAI/D,EAAI,EAAGA,EAAI+D,EAAQ/D,IAAKkE,GAAiBD,EAAMG,GAAiBH,EAAME,GAAiBF,EAAMI,GAAiBJ,EACnHD,EAAYM,MAAqBpB,EAAOgB,GAAiBV,GAAYD,EACrES,EAAYO,MAAqBrB,EAAOiB,GAAiBX,GAAYD,EACrES,EAAYQ,MAAqBtB,EAAOkB,GAAiBZ,GAAYD,GAC7C,IAApBkB,IAA4C,IAAnBJ,IACzBL,EAAYS,MAAqBvB,EAAOmB,GAAiBb,GAAYD,GAM7E,OAF+C,IAAIzB,EAAO,UAAWkC,EAA/B,SAAjBH,EAA6D,CAAC,EAAG,EAAGT,EAAQC,GAC1D,CAAC,EAAG,EAAGD,EAAQC,GAE1D,CACAJ,uBAAuByB,EAAOvB,GAE1B,MAAMwB,EAA+C,oBAAvB,kBAAsCD,aAAiBE,iBAC/EC,EAAwC,oBAAhB,WAA+BH,aAAiBI,UACxEC,EAAyC,oBAAlB,aAAiCL,aAAiBM,YACzEC,EAA4B,oBAAb,SAA6BP,aAAiBQ,QAA2B,iBAAVR,GACpF,IAAIvC,EACAgD,EAAe,CAAC,EAEpB,GAAIR,EAAgB,CAEhB,MAAMS,EAASC,SAASC,cAAc,UAChCC,EAAkBH,EAAOI,WAAW,MAC1C,GAAuB,MAAnBD,EAuCA,MAAM,IAAIxF,MAAM,6BAvCS,CACzB,IAAIqD,EAASsB,EAAMe,cACfpC,EAAQqB,EAAMgB,aAKlB,QAJgB5F,IAAZqD,QAAmDrD,IAA1BqD,EAAQwC,oBAAwD7F,IAAzBqD,EAAQyC,eACxExC,EAASD,EAAQwC,cACjBtC,EAAQF,EAAQyC,mBAEJ9F,IAAZqD,EAAuB,CAEvB,GADAgC,EAAehC,OACcrD,IAAzBqD,EAAQW,aACR,MAAM,IAAI/D,MAAM,+DAKpB,GAFIoF,EAAarB,aAAe,YAEThE,IAAnBqD,EAAQC,QAAwBD,EAAQC,SAAWA,EACnD,MAAM,IAAIrD,MAAM,mEAKpB,GAFIoF,EAAa/B,OAASA,OAEJtD,IAAlBqD,EAAQE,OAAuBF,EAAQE,QAAUA,EACjD,MAAM,IAAItD,MAAM,iEAGhBoF,EAAa9B,MAAQA,CAE7B,MAEI8B,EAAarB,aAAe,OAC5BqB,EAAa/B,OAASA,EACtB+B,EAAa9B,MAAQA,EAEzB+B,EAAO/B,MAAQA,EACf+B,EAAOhC,OAASA,EAChBmC,EAAgBM,UAAUnB,EAAO,EAAG,EAAGrB,EAAOD,GAC9CjB,EAAOoD,EAAgBO,aAAa,EAAG,EAAGzC,EAAOD,GAAQjB,IAC7D,CAIJ,KACK,KAAI0C,EA4CJ,IAAIE,EAAe,CAEpB,QAAgBjF,IAAZqD,EACA,MAAM,IAAIpD,MAAM,2DAEpB,QAA6BD,IAAzBqD,EAAQS,aACR,MAAM,IAAI7D,MAAM,6DAEpB,MAAMwF,EAAkBF,SAASC,cAAc,UAAUE,WAAW,MACpE,GAAuB,MAAnBD,EAAyB,CACzB,MAAMnC,EAASsB,EAAMtB,OACfC,EAAQqB,EAAMrB,MAGpB,GAFAkC,EAAgBM,UAAUnB,EAAO,EAAG,EAAGrB,EAAOD,GAC9CjB,EAAOoD,EAAgBO,aAAa,EAAG,EAAGzC,EAAOD,GAAQjB,UACzCrC,IAAZqD,EAAuB,CAEvB,QAAuBrD,IAAnBqD,EAAQC,QAAwBD,EAAQC,SAAWA,EACnD,MAAM,IAAIrD,MAAM,8DAMpB,GAHIoF,EAAa/B,OAASA,OAGJtD,IAAlBqD,EAAQE,OAAuBF,EAAQE,QAAUA,EACjD,MAAM,IAAItD,MAAM,4DAGhBoF,EAAa9B,MAAQA,CAE7B,MAEI8B,EAAa/B,OAASA,EACtB+B,EAAa9B,MAAQA,EAEzB,OAAOvB,EAAOiE,eAAe5D,EAAMgD,EACvC,CAEI,MAAM,IAAIpF,MAAM,4BAExB,CACK,GAAIkF,EACL,OAAO,IAAIe,SAAQ,CAACC,EAASC,KACzB,MAAMd,EAASC,SAASC,cAAc,UAChCa,EAAUf,EAAOI,WAAW,MAClC,IAAKd,IAAUyB,EACX,OAAOD,IAEX,MAAME,EAAW,IAAIC,MACrBD,EAASE,YAAc,YACvBF,EAASG,IAAM7B,EACf0B,EAASI,OAAS,KACdpB,EAAO/B,MAAQ+C,EAAS/C,MACxB+B,EAAOhC,OAASgD,EAAShD,OACzB+C,EAAQN,UAAUO,EAAU,EAAG,EAAGhB,EAAO/B,MAAO+B,EAAOhC,QACvD,MAAMqD,EAAMN,EAAQL,aAAa,EAAG,EAAGV,EAAO/B,MAAO+B,EAAOhC,QAC5D,QAAgBtD,IAAZqD,EAAuB,CAEvB,QAAuBrD,IAAnBqD,EAAQC,QAAwBD,EAAQC,SAAWgC,EAAOhC,OAC1D,MAAM,IAAIrD,MAAM,8DAMpB,GAHIoF,EAAa/B,OAASgC,EAAOhC,YAGXtD,IAAlBqD,EAAQE,OAAuBF,EAAQE,QAAU+B,EAAO/B,MACxD,MAAM,IAAItD,MAAM,4DAGhBoF,EAAa9B,MAAQ+B,EAAO/B,KAEpC,MAEI8B,EAAa/B,OAASgC,EAAOhC,OAC7B+B,EAAa9B,MAAQ+B,EAAO/B,MAEhC4C,EAAQnE,EAAOiE,eAAeU,EAAItE,KAAMgD,GAAc,CACzD,IAIL,MAAM,IAAIpF,MAAM,iEACpB,CA7HyB,CAErB,MAAM2G,EAAS,OACf,IAAItD,EACAC,EASJ,QARgBvD,IAAZqD,QAAkDrD,IAAzBqD,EAAQyC,mBAAwD9F,IAA1BqD,EAAQwC,eACvEvC,EAASD,EAAQwC,cACjBtC,EAAQF,EAAQyC,eAGhBxC,EAASsB,EAAMtB,OACfC,EAAQqB,EAAMrB,YAEFvD,IAAZqD,EAAuB,CAEvB,GADAgC,EAAehC,OACcrD,IAAzBqD,EAAQS,cAA8BT,EAAQS,eAAiB8C,EAC/D,MAAM,IAAI3G,MAAM,wDAGhBoF,EAAavB,aAAe,MAEpC,MAEIuB,EAAavB,aAAe,OAIhC,GAFAuB,EAAa/B,OAASA,EACtB+B,EAAa9B,MAAQA,OACLvD,IAAZqD,EAAuB,CACvB,MAAMwD,EAAatB,SAASC,cAAc,UAC1CqB,EAAWtD,MAAQA,EACnBsD,EAAWvD,OAASA,EACpB,MAAMmC,EAAkBoB,EAAWnB,WAAW,MAC9C,GAAuB,MAAnBD,EAKA,MAAM,IAAIxF,MAAM,6BAJhBwF,EAAgBqB,aAAalC,EAAO,EAAG,GACvCvC,EAAOoD,EAAgBO,aAAa,EAAG,EAAGzC,EAAOD,GAAQjB,IAKjE,MAEIA,EAAOuC,EAAMvC,IAErB,CAkFA,CACA,QAAarC,IAATqC,EACA,OAAOL,EAAOiE,eAAe5D,EAAMgD,GAGnC,MAAM,IAAIpF,MAAM,iEAExB,CACA8G,YAAY1D,GACR,IAAI2D,EAAIC,EACR,MAAMxB,EAAkBF,SAASC,cAAc,UAAUE,WAAW,MACpE,IAAId,EACJ,GAAuB,MAAnBa,EAoDA,MAAM,IAAIxF,MAAM,6BApDS,CAEzB,MAAMsD,EAAQ9C,KAAK6B,KAAK,GAClBgB,EAAS7C,KAAK6B,KAAK,GACnB4E,EAAWzG,KAAK6B,KAAK,GACrBuB,OAA0B7D,IAAZqD,QAA4CrD,IAAnBqD,EAAQuD,OAAuBvD,EAAQuD,OAAkB,MAChGnD,OAAuBzD,IAAZqD,QAAgGrD,KAA9C,QAAvBgH,EAAK3D,EAAQG,YAAyB,IAAPwD,OAAgB,EAASA,EAAGrD,MAAsBN,EAAQG,KAAKG,KAAc,IAClJD,OAAuB1D,IAAZqD,QAAgGrD,KAA9C,QAAvBiH,EAAK5D,EAAQG,YAAyB,IAAPyD,OAAgB,EAASA,EAAGrD,MAAsBP,EAAQG,KAAKI,KAAY,EAChJK,EAASX,EAASC,EACxB,QAAgBvD,IAAZqD,EAAuB,CACvB,QAAuBrD,IAAnBqD,EAAQC,QAAwBD,EAAQC,SAAWA,EACnD,MAAM,IAAIrD,MAAM,0DAEpB,QAAsBD,IAAlBqD,EAAQE,OAAuBF,EAAQE,QAAUA,EACjD,MAAM,IAAItD,MAAM,wDAEpB,QAAuBD,IAAnBqD,EAAQuD,QAAsC,IAAbM,GAAqC,SAAnB7D,EAAQuD,QAC7C,IAAbM,GAAsC,QAAnB7D,EAAQuD,QAAuC,QAAnBvD,EAAQuD,OACxD,MAAM,IAAI3G,MAAM,gDAExB,CAEA,MAAMkE,EAAO,EACb,IAAIC,EAAgB,EAAGC,EAAgB,EAAGC,EAAgB,EAAGC,EAAgB,EACzEC,EAAiB,EAAGC,EAAiBR,EAAQS,EAA0B,EAATT,EAAYU,GAAkB,EAE5E,SAAhBd,GACAW,EAAiB,EACjBC,EAAiBR,EACjBS,EAA0B,EAATT,EACjBU,EAA0B,EAATV,GAEI,QAAhBJ,GACLW,EAAiB,EACjBC,EAAiBR,EACjBS,EAA0B,EAATT,GAEI,QAAhBJ,IACLW,EAAiB,EACjBE,EAAiBT,EACjBQ,EAA0B,EAATR,GAErBW,EAAQa,EAAgB0B,gBAAgB5D,EAAOD,GAC/C,IAAK,IAAIpD,EAAI,EAAGA,EAAIoD,EAASC,EAAOa,GAAiBD,EAAME,GAAiBF,EAAMG,GAAiBH,EAAMI,GAAiBJ,EAAMjE,IAC5H0E,EAAMvC,KAAK+B,IAAkB3D,KAAK4B,KAAKmC,KAAoBd,GAAYD,EACvEmB,EAAMvC,KAAKgC,IAAkB5D,KAAK4B,KAAKoC,KAAoBf,GAAYD,EACvEmB,EAAMvC,KAAKiC,IAAkB7D,KAAK4B,KAAKqC,KAAoBhB,GAAYD,EACvEmB,EAAMvC,KAAKkC,IACa,IAApBI,EAAwB,KAAOlE,KAAK4B,KAAKsC,KAAoBjB,GAAYD,CAErF,CAIA,OAAOmB,CACX,CAGAwC,QAAQ9E,GACJ,OAAO,IAAIN,EAAOvB,KAAK2B,KAAM3B,KAAK4B,KAAMC,EAC5C,EC1dG,MAAM,EAASN,ECAf,MAAMqF,EACT7G,YAAY8G,GACR7G,KAAK6G,QAAUA,CACnB,CACAC,UAAUC,EAAOtF,EAAMC,GACnB,MAAMsF,EAAU,CAAC,EACjB,IAAIpE,EAAU,CAAC,EAEf,GAAqB,iBAAVmE,GAAgC,OAAVA,GAAkBA,aAAiB,GAAUjF,MAAMC,QAAQgF,GACxF,MAAM,IAAI1H,UAAU,iGAExB,IAAI4H,GAAiB,EAErB,GAAoB,iBAATxF,EAAmB,CAC1B,GAAa,OAATA,EACA,MAAM,IAAIpC,UAAU,2CAExB,GAAIoC,aAAgB,EAChB,MAAM,IAAIpC,UAAU,gCAExB,GAAIyC,MAAMC,QAAQN,GAAO,CACrB,GAAoB,IAAhBA,EAAK7B,OACL,MAAM,IAAIP,UAAU,uCAExB4H,GAAiB,EAEjB,IAAK,MAAMjI,KAAQyC,EAAM,CACrB,GAAoB,iBAATzC,EACP,MAAM,IAAIK,UAAU,kDAExB,IAAwC,IAApCW,KAAKkH,YAAYxH,QAAQV,GACzB,MAAM,IAAIwD,WAAW,2CAA2CxD,MAEpEgI,EAAQhI,GAAQ,IACpB,CACA,GAAoB,iBAAT0C,GAA8B,OAATA,EAC5BkB,EAAUlB,OAET,QAAoB,IAATA,EACZ,MAAM,IAAIrC,UAAU,+BAE5B,KACK,CAGD,IAAI8H,GAAY,EAChB,MAAMC,EAAWC,OAAOC,oBAAoB7F,GAC5C,IAAK,MAAMzC,KAAQgB,KAAKkH,YACpB,IAAgC,IAA5BE,EAAS1H,QAAQV,GAAc,CAC/B,MAAMuI,EAAI9F,EAAKzC,IACL,OAANuI,GAAcA,aAAa,KAC3BJ,GAAY,EACZF,GAAiB,EACjBD,EAAQhI,GAAQuI,EAExB,CAEJ,GAAIJ,GACA,GAAoB,iBAATzF,GAA8B,OAATA,EAC5BkB,EAAUlB,OAET,QAAoB,IAATA,EACZ,MAAM,IAAIrC,UAAU,qCAIxBuD,EAAUnB,CAElB,CACJ,MACK,QAAoB,IAATA,EACZ,MAAM,IAAIpC,UAAU,2DAGxB,IAAK,MAAML,KAAQgB,KAAKwH,WACpB,QAA2B,IAAhBT,EAAM/H,GACb,MAAM,IAAIQ,MAAM,UAAUR,6BAIlC,GAAIiI,EACA,IAAK,MAAMjI,KAAQgB,KAAKkH,YACpBF,EAAQhI,GAAQ,KAIxB,MAAMyI,QAAgBzH,KAAK6G,QAAQa,IAAIX,EAAOC,EAASpE,GACjD+E,EAAc,CAAC,EACrB,IAAK,MAAMC,KAAOH,EACVJ,OAAOQ,eAAeC,KAAKL,EAASG,KACpCD,EAAYC,GAAO,IAAI,EAAOH,EAAQG,GAAKjG,KAAM8F,EAAQG,GAAKhG,KAAM6F,EAAQG,GAAK/F,OAGzF,OAAO8F,CACX,CACAjF,oBAAoBlB,EAAMC,EAAMC,EAAMqG,GAElC,IAAIC,EACApF,EAAU,CAAC,EACf,GAAoB,iBAATpB,GAEP,GADAwG,EAAuBxG,EACH,iBAATC,GAA8B,OAATA,EAC5BmB,EAAUnB,OAET,QAAoB,IAATA,EACZ,MAAM,IAAIpC,UAAU,qCAGvB,GAAImC,aAAgBV,YAErB,GADAkH,EAAuBxG,EACH,iBAATC,GAA8B,OAATA,EAC5BmB,EAAUnB,OAET,QAAoB,IAATA,EACZ,MAAM,IAAIpC,UAAU,oCAGvB,MAAImC,aAAgByG,aACS,oBAAtBC,mBAAqC1G,aAAgB0G,mBAyC7D,MAAM,IAAI7I,UAAU,uDAzC6D,CACjF,MAAMsD,EAASnB,EACf,IAAI2G,EAAa,EACbC,EAAa5G,EAAK4G,WACtB,GAAoB,iBAAT3G,GAA8B,OAATA,EAC5BmB,EAAUnB,OAET,GAAoB,iBAATA,EAAmB,CAE/B,GADA0G,EAAa1G,GACRa,OAAOC,cAAc4F,GACtB,MAAM,IAAI3F,WAAW,oCAEzB,GAAI2F,EAAa,GAAKA,GAAcxF,EAAOyF,WACvC,MAAM,IAAI5F,WAAW,oCAAoCG,EAAOyF,gBAGpE,GADAA,EAAa5G,EAAK4G,WAAaD,EACX,iBAATzG,EAAmB,CAE1B,GADA0G,EAAa1G,GACRY,OAAOC,cAAc6F,GACtB,MAAM,IAAI5F,WAAW,oCAEzB,GAAI4F,GAAc,GAAKD,EAAaC,EAAazF,EAAOyF,WACpD,MAAM,IAAI5F,WAAW,oCAAoCG,EAAOyF,WAAaD,OAEjF,GAAoB,iBAATJ,GAA8B,OAATA,EAC5BnF,EAAUmF,OAET,QAAoB,IAATA,EACZ,MAAM,IAAI1I,UAAU,+BAE5B,MACK,QAAoB,IAATqC,EACZ,MAAM,IAAIrC,UAAU,iCAE5B,MACK,QAAoB,IAAToC,EACZ,MAAM,IAAIpC,UAAU,gCAExB2I,EAAuB,IAAIlH,WAAW6B,EAAQwF,EAAYC,EAC9D,CAGA,CAEA,MACMC,GADMzF,EAAQ0F,oBAAsB,IACjBC,KAAI9I,GAAkB,iBAANA,EAAiBA,EAAIA,EAAET,OAC1DC,OLlHgB6H,OAAOuB,IACjC,MAAMG,EAAuC,IAAxBH,EAAazI,OAAed,EAA2BuJ,EACtEI,EAAS,GACf,IAAK,MAAMC,KAAeF,EAAc,CACpC,MAAMG,EAAc9J,EAAS6J,GAC7B,GAAIC,EAAa,CACb,GAAIA,EAAYC,YACZ,OAAOD,EAAY1J,QAElB,GAAI0J,EAAYE,QACjB,SAEJ,MAAMC,IAAmBH,EAAYI,YACrC,IAMI,OALKD,IACDH,EAAYI,YAAcJ,EAAY1J,QAAQE,cAE5CwJ,EAAYI,YAClBJ,EAAYC,aAAc,EACnBD,EAAY1J,OAUvB,CARA,MAAO+J,GACEF,GACDL,EAAO5I,KAAK,CAAEb,KAAM0J,EAAaO,IAAKD,IAE1CL,EAAYE,SAAU,CAC1B,CACA,eACWF,EAAYI,WACvB,CACJ,CACJ,CACA,MAAM,IAAIvJ,MAAM,oCAAoCiJ,EAAOF,KAAIS,GAAK,IAAIA,EAAEhK,SAASgK,EAAEC,QAAOC,KAAK,QAAQ,EKkF/EC,CAAed,GAC/BxB,QAAgB5H,EAAQG,qBAAqB4I,EAAsBpF,GACzE,OAAO,IAAIgE,EAAiBC,EAChC,CACAuC,iBACIpJ,KAAK6G,QAAQuC,gBACjB,CACAC,eACIrJ,KAAK6G,QAAQwC,cACjB,CACI7B,iBACA,OAAOxH,KAAK6G,QAAQW,UACxB,CACIN,kBACA,OAAOlH,KAAK6G,QAAQK,WACxB,ECnLG,MAAM,EAAmBN,C,oBCJ5B0C,WAAWC,GAAsID,YAAnIA,WAAW,oBAAoBxE,UAAUA,SAAS0E,cAAc1E,SAAS0E,cAAcxD,SAAI,I,YAA2E,SAASuD,GAAG,SAASE,IAAI,OAAOC,EAAE/G,QAAQgH,GAAGC,EAAEF,EAAE/G,QAAQkH,CAAC,CAAC,SAASb,IAAI,OAAOU,EAAE/G,QAAQgH,GAAGC,EAAEF,EAAE/G,QAAQmH,CAAC,CAAC,SAASC,IAAI,OAAOL,EAAE/G,QAAQgH,GAAGC,EAAEF,EAAE/G,QAAQqH,CAAC,CAAC,SAASvK,IAAI,OAAOiK,EAAE/G,QAAQgH,GAAGC,EAAEF,EAAE/G,QAAQsH,CAAC,CAAC,SAASC,IAAI,OAAOR,EAAE/G,QAAQgH,GAAGC,EAAEF,EAAE/G,QAAQwH,CAAC,CAAC,IAAIC,EAAEC,EAAEC,EAAEf,EAAEA,GAAG,CAAC,EAAEa,IAAIA,OAAE,IAASb,EAAEA,EAAE,CAAC,GAAGa,EAAEG,MAAM,IAAI9E,SAAQ,SAAU8D,EAAEE,GAAGY,EAAEd,EAAEe,EAAEb,CAAE,IAAG,IAAIe,EAAEC,EAAEC,EAAEC,EAAEC,EAAEC,EAAEC,EAAEzD,OAAO0D,OAAO,CAAC,EAAEX,GAAGY,EAAE,iBAAiBC,EAAE,CAAC1B,EAAEE,KAAK,MAAMA,GAAGyB,EAAE,iBAAiBC,OAAOC,EAAE,mBAAmBC,cAAc9D,EAAE,iBAAiB+D,SAAS,iBAAiBA,QAAQC,UAAU,iBAAiBD,QAAQC,SAASC,KAAKC,EAAErB,EAAEsB,yBAAwB,EAAGC,EAAE,GAAG,SAASC,EAAErC,GAAG,OAAOa,EAAEyB,WAAWzB,EAAEyB,WAAWtC,EAAEoC,GAAGA,EAAEpC,CAAC,CAAC,GAAGhC,EAAE,CAAC,IAAIgC,EAAEoC,EAAEP,EAAE,eAAwBO,GAAG,IAAIG,KAAcjB,EAAE,KAAKD,IAAID,EAAE,EAAQ,KAAMC,EAAE,EAAQ,KAAO,EAAGJ,EAAE,SAASjB,EAAEE,GAAG,OAAOoB,IAAItB,EAAEqB,EAAEmB,UAAUxC,GAAGoB,EAAEqB,aAAazC,EAAEE,OAAE,EAAO,OAAO,EAAEiB,EAAEnB,KAAKA,EAAEiB,EAAEjB,GAAE,IAAK5G,SAAS4G,EAAE,IAAIzI,WAAWyI,IAAIA,GAAGkB,EAAE,CAAClB,EAAEE,EAAET,KAAK6B,IAAItB,EAAEqB,EAAEmB,UAAUxC,GAAGoB,EAAEsB,SAAS1C,GAAE,SAAUA,EAAEQ,GAAGR,EAAEP,EAAEO,GAAGE,EAAEM,EAAEpH,OAAQ,GAAC,EAAG,EAAE2I,QAAQY,KAAKtM,SAASoL,EAAEM,QAAQY,KAAK,GAAGC,QAAQ,MAAM,MAAMb,QAAQY,KAAKE,MAAM,GAAGd,QAAQe,GAAG,qBAAoB,SAAU9C,GAAG,KAAKA,aAAa+C,IAAI,MAAM/C,CAAE,IAAG+B,QAAQe,GAAG,sBAAqB,SAAU9C,GAAG,MAAMA,CAAE,IAAG0B,EAAE,CAAC1B,EAAEE,KAAK,GAAG8C,IAAI,MAAMjB,QAAQkB,SAASjD,EAAEE,EAAEA,aAAa6C,IAAIG,EAAE,6BAA6BhD,GAAG6B,QAAQoB,KAAKnD,EAAC,EAAGa,EAAEuC,QAAQ,WAAW,MAAM,4BAA4B,EAAE,IAAIpD,EAAE,EAAQ,IAA2J,CAAzI,MAAMA,GAAG,MAAMqD,QAAQC,MAAM,2GAA2GtD,CAAC,CAAC,EAAA2B,EAAO4B,OAAOvD,EAAEuD,MAAM,MAAM5B,GAAGE,KAAKA,EAAEO,EAAE/M,KAAKmO,SAASC,KAAK,oBAAoBlI,UAAUA,SAAS0E,gBAAgBmC,EAAE7G,SAAS0E,cAAcxD,KAAKsD,aAAaqC,EAAErC,YAAYqC,EAAE,IAAIA,EAAEjM,QAAQ,SAASiM,EAAEsB,OAAO,EAAEtB,EAAEQ,QAAQ,SAAS,IAAIe,YAAY,KAAK,GAAG,GAAG3F,IAAIiD,EAAEjB,IAAI,IAAIE,EAAE,IAAI0D,eAAe,OAAO1D,EAAE2D,KAAK,MAAM7D,GAAE,GAAIE,EAAE4D,KAAK,MAAM5D,EAAE6D,cAAclC,IAAIV,EAAEnB,IAAI,IAAIE,EAAE,IAAI0D,eAAe,OAAO1D,EAAE2D,KAAK,MAAM7D,GAAE,GAAIE,EAAE8D,aAAa,cAAc9D,EAAE4D,KAAK,MAAM,IAAIvM,WAAW2I,EAAE+D,SAAQ,GAAI/C,EAAE,CAAClB,EAAEE,EAAET,KAAK,IAAIe,EAAE,IAAIoD,eAAepD,EAAEqD,KAAK,MAAM7D,GAAE,GAAIQ,EAAEwD,aAAa,cAAcxD,EAAE9D,OAAO,KAAK,KAAK8D,EAAE0D,QAAQ,GAAG1D,EAAE0D,QAAQ1D,EAAEyD,SAAS/D,EAAEM,EAAEyD,UAAUxE,GAAE,EAAGe,EAAE2D,QAAQ1E,EAAEe,EAAEsD,KAAK,KAAI,IAAK9F,GAAG,oBAAoBoG,cAAc,EAAAzC,EAAOyC,YAAY,oBAAmC,IAAIC,EAAEhB,QAAQiB,IAAIC,KAAKlB,SAASmB,EAAEnB,QAAQoB,KAAKF,KAAKlB,SAASrF,IAAIsD,IAAI+C,EAAErE,GAAGoB,EAAEsD,UAAU,EAAE1E,EAAE,MAAMwE,EAAExE,GAAGoB,EAAEsD,UAAU,EAAE1E,EAAE,OAAO,IAAI2E,EAAEC,EAAE/D,EAAEgE,OAAOR,EAAEnB,EAAErC,EAAEiE,UAAUN,EAAE1G,OAAO0D,OAAOX,EAAEU,GAAGA,EAAE,KAAKV,EAAEkE,cAActD,EAAEZ,EAAEkE,aAAalE,EAAEmE,OAAOtD,EAAEb,EAAEmE,MAAMnE,EAAEoE,aAAaN,EAAE9D,EAAEoE,YAAY,IAAIC,EAAErE,EAAEsE,gBAAe,EAAG,iBAAiBC,aAAaC,GAAG,mCAAmC,IAAIlF,EAAEmF,EAAElF,EAAEE,EAAEC,EAAEE,EAAEC,EAAEE,EAAE2E,GAAE,EAAGC,EAAE,oBAAoBC,YAAY,IAAIA,YAAY,aAAQ,EAAO,SAASC,EAAE1F,EAAEE,EAAET,GAAG,IAAIe,GAAGN,KAAK,GAAGT,EAAE,IAAIA,EAAES,EAAEF,EAAEP,MAAMA,GAAGe,MAAMf,EAAE,GAAG,GAAGA,EAAES,GAAGF,EAAE5G,QAAQoM,EAAE,OAAOA,EAAEG,OAAO3F,EAAE5G,kBAAkBuF,kBAAkBqB,EAAE6C,MAAM3C,EAAET,GAAGO,EAAE4F,SAAS1F,EAAET,IAAI,IAAIe,EAAE,GAAGN,EAAET,GAAG,CAAC,IAAIvJ,EAAE8J,EAAEE,KAAK,GAAG,IAAIhK,EAAE,CAAC,IAAIyK,EAAE,GAAGX,EAAEE,KAAK,GAAG,MAAM,IAAIhK,GAAGsK,GAAGpF,OAAOyK,cAAc,GAAG3P,IAAI,EAAEyK,OAAO,CAAC,IAAIE,EAAE,GAAGb,EAAEE,KAAK,OAAOhK,EAAE,MAAM,IAAIA,IAAI,GAAGA,IAAI,GAAGyK,GAAG,EAAEE,GAAG,EAAE3K,IAAI,GAAGyK,GAAG,GAAGE,GAAG,EAAE,GAAGb,EAAEE,MAAMM,GAAGpF,OAAOyK,aAAa3P,IAAIA,GAAG,MAAMsK,GAAGpF,OAAOyK,aAAa,MAAM3P,GAAG,GAAG,MAAM,KAAKA,GAAG,CAAC,MAAMsK,GAAGpF,OAAOyK,aAAa3P,EAAE,CAAC,OAAOsK,CAAC,CAAC,SAASsF,EAAE9F,EAAEE,GAAG,OAAOF,KAAK,GAAG0F,EAAEjG,IAAIO,EAAEE,GAAG,EAAE,CAAC,SAAS6F,EAAE/F,EAAEE,EAAET,EAAEe,GAAG,KAAK,EAAEA,GAAG,OAAO,EAAE,IAAItK,EAAEuJ,KAAK,EAAEe,EAAEf,EAAEe,EAAE,EAAE,IAAI,IAAIG,EAAE,EAAEA,EAAEX,EAAE3J,SAASsK,EAAE,CAAC,IAAIE,EAAEb,EAAEgG,WAAWrF,GAAG,GAAG,OAAOE,GAAG,OAAOA,IAAIA,EAAE,QAAQ,KAAKA,IAAI,IAAI,KAAKb,EAAEgG,aAAarF,IAAI,KAAKE,EAAE,CAAC,GAAGpB,GAAGe,EAAE,MAAMN,EAAET,MAAM,GAAGoB,CAAC,KAAK,CAAC,GAAG,MAAMA,EAAE,CAAC,GAAGpB,EAAE,GAAGe,EAAE,MAAMN,EAAET,MAAM,GAAG,IAAIoB,GAAG,CAAC,KAAK,CAAC,GAAG,OAAOA,EAAE,CAAC,GAAGpB,EAAE,GAAGe,EAAE,MAAMN,EAAET,MAAM,GAAG,IAAIoB,GAAG,EAAE,KAAK,CAAC,GAAGpB,EAAE,GAAGe,EAAE,MAAMN,EAAET,MAAM,GAAG,IAAIoB,GAAG,GAAGX,EAAET,MAAM,GAAG,IAAIoB,GAAG,GAAG,EAAE,CAACX,EAAET,MAAM,GAAG,IAAIoB,GAAG,EAAE,EAAE,CAACX,EAAET,MAAM,GAAG,IAAI,GAAGoB,CAAC,CAAC,CAAC,OAAOX,EAAET,IAAI,GAAG,EAAEA,EAAEvJ,CAAC,CAAC,SAAS+P,EAAEjG,GAAG,IAAI,IAAIE,EAAE,EAAET,EAAE,EAAEA,EAAEO,EAAE3J,SAASoJ,EAAE,CAAC,IAAIe,EAAER,EAAEgG,WAAWvG,GAAG,KAAKe,EAAEN,IAAI,MAAMM,EAAEN,GAAG,EAAE,OAAOM,GAAG,OAAOA,GAAGN,GAAG,IAAIT,GAAGS,GAAG,CAAC,CAAC,OAAOA,CAAC,CAAC,SAASG,EAAEL,GAAGI,EAAEJ,EAAEa,EAAEqF,MAAM5F,EAAE,IAAI9I,UAAUwI,GAAGa,EAAEsF,OAAO,IAAIzO,WAAWsI,GAAGa,EAAEuF,OAAO3F,EAAE,IAAI9I,WAAWqI,GAAGa,EAAEwF,OAAO9F,EAAE,IAAIhJ,WAAWyI,GAAGa,EAAEyF,QAAQ,IAAI7O,YAAYuI,GAAGa,EAAE0F,QAAQ7F,EAAE,IAAI7I,YAAYmI,GAAGa,EAAE2F,QAAQ,IAAIlP,aAAa0I,GAAGa,EAAE4F,QAAQ7F,EAAE,IAAIhJ,aAAaoI,EAAE,CAACkC,IAAI9B,EAAES,EAAEzH,QAAQ,IAAIsN,EAAE7F,EAAE8F,gBAAgB,SAAS,GAAGzE,EAAE/B,EAAEU,EAAE+F,WAAWxG,EAAES,EAAEzH,YAAY,GAAGyH,EAAE+F,WAAWzG,EAAEU,EAAE+F,gBAAgB,MAAMzG,EAAE,IAAIiF,YAAYyB,OAAO,CAACC,QAAQJ,EAAE,MAAMK,QAAQ,MAAMC,QAAO,KAAM5N,kBAAkBuF,mBAAmB,MAAMuE,EAAE,+NAA+NlF,GAAGqF,QAAQiB,IAAI,qHAAqHrO,MAAM,cAAckK,IAAIC,EAAED,EAAE/G,QAAQsN,EAAEtG,EAAEvB,WAAWwB,EAAED,GAAG,IAAI6G,EAAEC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAG,SAASrE,IAAI,OAAOkC,IAAG,CAAE,CAAC,SAASoC,IAAI,IAAItH,EAAEa,EAAE0G,OAAOC,QAAQN,EAAEO,QAAQzH,EAAE,CAAC,IAAI0H,GAAEC,GAAG,EAAEC,GAAG,KAAKC,GAAG,KAAK,SAASxC,GAAGrF,GAAG,MAAMkC,EAAE4F,YAAY,CAACC,IAAI,UAAUC,IAAIhI,IAAIa,EAAEoH,SAASpH,EAAEoH,QAAQjI,GAAGkD,EAAElD,EAAE,WAAWA,EAAE,KAAKuF,GAAE,EAAGvF,EAAE,IAAIoF,YAAY8C,aAAalI,EAAE,4CAA4Ce,EAAEf,GAAGA,CAAC,CAAC,SAASmI,KAAK,OAAOT,GAAEU,WAAW,wCAAwC,CAAC,SAASC,KAAK,IAAIrI,EAAE0H,GAAE,IAAI,GAAG1H,GAAG0H,IAAG/C,EAAE,OAAO,IAAIpN,WAAWoN,GAAG,GAAGxD,EAAE,OAAOA,EAAEnB,GAAG,KAAK,iDAAgE,CAAd,MAAMA,GAAGqF,GAAGrF,EAAE,CAAC,CAAC0H,GAAE,yBAAyBS,OAAOT,GAAErF,EAAEqF,KAAI,IAAIY,GAAG,CAAC,EAAE,SAASvF,GAAG/C,GAAGvJ,KAAKhB,KAAK,aAAagB,KAAK8R,QAAQ,gCAAgCvI,EAAE,IAAIvJ,KAAKyN,OAAOlE,CAAC,CAAC,SAASwI,GAAGxI,IAAIA,EAAEyI,GAAGC,GAAG1I,KAAKqF,KAAKoD,GAAGE,GAAG3I,EAAE,CAAC,SAAS4I,GAAG5I,GAAG,IAAIE,EAAEuI,GAAGI,KAAK,IAAI3I,EAAE,OAAO,EAAEuI,GAAGK,GAAGxS,KAAK4J,GAAGuI,GAAGC,GAAG1I,EAAE+I,IAAI7I,EAAEA,EAAE6I,GAAG/I,EAAE+I,GAAG,IAAItJ,EAAE,CAACsI,IAAI,MAAMiB,cAAchJ,EAAEiJ,GAAGjB,IAAIhI,EAAEkJ,GAAGC,YAAYnJ,EAAE+I,IAAI,OAAO7I,EAAEkJ,GAAG,KAAK3J,EAAE4J,KAAKjF,YAAYkF,MAAMpJ,EAAE4H,YAAYrI,EAAEO,EAAEuJ,GAAE,EAAGrJ,EAAEsJ,SAAStJ,EAAEkJ,YAAYlJ,EAAEkJ,IAAI,CAAC,CAAC,SAASK,GAAGzJ,GAAG,GAAGkC,EAAE,OAAOwH,GAAG,EAAE,EAAE1J,GAAGgD,MAAMyF,GAAGkB,KAAK9I,EAAE+I,QAAQ/I,EAAE+I,OAAO5J,GAAGuF,GAAE,GAAI7D,EAAE1B,EAAE,IAAI+C,GAAG/C,GAAG,CAAC,SAAS6J,GAAG7J,EAAEE,GAAG,IAAIA,GAAGgC,EAAE,MAAM4H,GAAG9J,GAAG,SAASgD,KAAKd,IAAI6H,KAAKC,GAAG5C,GAAG6C,GAAG,GAAGC,GAAG,GAAG7T,QAAQ8T,GAAG,EAAE,IAAID,GAAG,GAAG7T,QAAQ8T,GAAG,EAAE,IAAI1B,GAAGkB,MAAMF,GAAGzJ,EAAE,CAAC,IAAIyI,GAAG,CAAC2B,GAAG,GAAGtB,GAAG,GAAGuB,GAAG,GAAG3B,GAAG,CAAC,EAAE4B,GAAG,WAAWpI,GAAGuG,GAAG8B,IAAI,EAAEC,GAAG,WAAW,EAAED,GAAG,WAAW9B,GAAGgC,sBAAsBhC,GAAGiC,GAAGjC,GAAGkC,cAAclC,GAAGmC,GAAGnC,GAAGoC,cAAcpC,GAAGqC,GAAG5F,GAAE,CAAE,EAAE4F,GAAG,WAAW,EAAEnB,GAAG,WAAW,IAAI,IAAI3J,KAAKlC,OAAOiN,OAAOtC,GAAGC,IAAID,GAAGE,GAAG3I,GAAG,IAAIA,KAAKyI,GAAG2B,GAAGpK,EAAEgL,YAAYvC,GAAG2B,GAAG,EAAE,EAAEzB,GAAG,SAAS3I,GAAG,IAAIE,EAAEF,EAAE+I,UAAUN,GAAGC,GAAGxI,GAAGuI,GAAG2B,GAAG9T,KAAK0J,GAAGyI,GAAGK,GAAG1S,OAAOqS,GAAGK,GAAG3S,QAAQ6J,GAAG,GAAGA,EAAE+I,GAAG,EAAEkC,GAAG/K,EAAE,EAAEwK,GAAG,WAAW,EAAEE,GAAG,WAAWnC,GAAG4B,GAAGa,SAASlL,GAAGA,KAAK,EAAEmL,GAAG,SAASnL,EAAEE,GAAGF,EAAEoL,UAAU3L,IAAI,IAAIe,GAAGf,EAAEA,EAAEpH,MAAM0P,IAAI,GAAG/H,EAAE+I,KAAKN,GAAG4C,GAAGrL,EAAE+I,IAAItJ,EAAE6L,cAAc7L,EAAE6L,cAAcC,KAAK,CAAC,IAAIrV,EAAEuS,GAAGC,GAAGjJ,EAAE+L,IAAItV,EAAEA,EAAE4R,YAAYrI,EAAEA,EAAEgM,cAAcvI,EAAE,0CAA0C1C,EAAE,uBAAuBf,EAAE6L,aAAa,sCAAsC,KAAK,yBAAyB9K,EAAEkL,GAAGjM,EAAEkM,OAAO,gBAAgBnL,EAAEoI,GAAGnJ,GAAG,kBAAkBe,EAAEgI,GAAG/I,EAAEmM,QAAQ,eAAepL,GAAGf,EAAEA,EAAEmM,OAAOpL,EAAEiI,GAAGC,GAAGjJ,UAAUgJ,GAAGC,GAAGjJ,GAAGe,EAAEwK,YAAYC,GAAGxL,GAAGgJ,GAAGK,GAAG1S,OAAOqS,GAAGK,GAAG3S,QAAQqK,GAAG,GAAGA,EAAEuI,GAAG,GAAG,iBAAiBvI,EAAEiI,GAAGC,GAAGjJ,EAAEmM,QAAQ9D,YAAY,CAACC,IAAI,WAAW,WAAWvH,GAAGR,EAAEwJ,QAAO,EAAGtJ,GAAGA,EAAEF,GAAGA,EAAEoJ,KAAKpJ,EAAEoJ,YAAYpJ,EAAEoJ,KAAK,UAAU5I,EAAEoE,EAAE,UAAUnF,EAAEoM,SAAS,KAAKpM,EAAEqM,MAAM,aAAatL,EAAE0C,EAAE,UAAUzD,EAAEoM,SAAS,KAAKpM,EAAEqM,MAAM,UAAUtL,EAAEuL,MAAM,UAAUtM,EAAEoM,SAAS,KAAKpM,EAAEqM,MAAM,iBAAiBrM,EAAEuM,OAAOhM,EAAE8H,YAAYrI,GAAG,YAAYe,EAAEK,EAAEoH,SAASpH,EAAEoH,QAAQxI,EAAEuI,KAAKxH,GAAG0C,EAAE,kCAAkC1C,GAAGiI,GAAG4C,QAAG,CAAK,EAAGrL,EAAEmE,QAAQnE,IAAI,MAAMkD,EAAE,yBAAyBlD,EAAEiM,SAAS,IAAIjM,EAAEkM,OAAO,KAAKlM,EAAEuI,SAASvI,GAAGhC,IAAIgC,EAAE8C,GAAG,WAAU,SAAU5C,GAAGF,EAAEoL,UAAU,CAAC/S,KAAK6H,GAAI,IAAGF,EAAE8C,GAAG,SAAQ,SAAU5C,GAAGF,EAAEmE,QAAQjE,EAAG,IAAGF,EAAE8C,GAAG,gBAAe,WAAa,KAAI9C,EAAE8H,YAAY,CAACC,IAAI,OAAOoE,UAAUtL,EAAEuL,qBAAqBrM,WAAW6G,WAAWzG,EAAEkM,WAAW/G,GAAG,EAAEgH,GAAG,WAAW,IAAItM,EAAEqC,EAAE,+BAA+BoG,GAAG2B,GAAG9T,KAAK,IAAIiN,OAAOvD,GAAG,EAAE6I,GAAG,WAAW,OAAO,GAAGJ,GAAG2B,GAAG/T,SAASoS,GAAG6D,KAAK7D,GAAG0C,GAAG1C,GAAG2B,GAAG,KAAK3B,GAAG2B,GAAGmC,KAAK,GAAG,SAASvC,GAAGhK,GAAG,KAAK,EAAEA,EAAE3J,QAAQ2J,EAAEwH,OAAFxH,CAAUa,EAAE,CAAC,SAAS2L,GAAGxM,GAAG,IAAIE,EAAEuM,KAAK,OAAOzM,EAAEA,IAAI0M,GAAGxM,GAAGF,CAAC,CAAC,SAAS8J,GAAG9J,GAAG,GAAGkC,EAAE,OAAOwH,GAAG,EAAE,EAAE1J,GAAG,IAAI6J,GAAG7J,EAAgD,CAA7C,MAAMA,GAAGA,aAAa+C,IAAI,UAAU/C,GAAG0B,EAAE,EAAE1B,EAAE,CAAC,CAACa,EAAE8L,QAAQlE,GAAG5H,EAAE+L,oBAAoB,WAAW,IAAI5M,EAAEuL,KAAKrL,EAAEM,IAAIR,EAAE,IAAI,IAAI,GAAGA,EAAEQ,IAAIR,EAAE,IAAI,IAAI,GAAG6M,GAAG3M,EAAEA,EAAEF,GAAG0M,GAAGxM,EAAE,EAAE,IAAI4M,GAAG,GAAG,SAASC,GAAG/M,GAAG,IAAIE,EAAE4M,GAAG9M,GAAG,OAAOE,IAAIF,GAAG8M,GAAGzW,SAASyW,GAAGzW,OAAO2J,EAAE,GAAG8M,GAAG9M,GAAGE,EAAE+G,EAAEvO,IAAIsH,IAAIE,CAAC,CAACW,EAAEmM,iBAAiB,SAAShN,EAAEE,GAAGF,EAAE+M,GAAG/M,EAAH+M,CAAM7M,GAAG8C,IAAIyF,GAAGqC,GAAG9K,GAAGiN,GAAGjN,EAAE,EAAE,IAAIkN,GAAGC,GAAGC,GAAG,GAAGC,GAAG,EAAEC,GAAG,EAAE,SAASC,GAAGvN,GAAGvJ,KAAK+W,GAAGxN,EAAEvJ,KAAKgX,GAAGzN,EAAE,GAAGvJ,KAAKiX,GAAG,SAAS1N,GAAG9J,IAAIO,KAAKgX,GAAG,GAAG,IAAI,GAAGzN,CAAC,EAAEvJ,KAAKkX,GAAG,WAAW,OAAOzX,IAAIO,KAAKgX,GAAG,GAAG,IAAI,EAAE,EAAEhX,KAAKmX,GAAG,SAAS5N,GAAG9J,IAAIO,KAAKgX,GAAG,GAAG,IAAI,GAAGzN,CAAC,EAAEvJ,KAAKoX,GAAG,WAAW,OAAO3X,IAAIO,KAAKgX,GAAG,GAAG,IAAI,EAAE,EAAEhX,KAAKqX,GAAG,WAAWtN,IAAI/J,KAAKgX,IAAI,IAAI,GAAG,CAAC,EAAEhX,KAAKsX,GAAG,SAAS/N,GAAGA,EAAEA,EAAE,EAAE,EAAEE,IAAIzJ,KAAKgX,GAAG,IAAI,IAAI,GAAGzN,CAAC,EAAEvJ,KAAKuX,GAAG,WAAW,OAAO,GAAG9N,IAAIzJ,KAAKgX,GAAG,IAAI,IAAI,EAAE,EAAEhX,KAAKwX,GAAG,SAASjO,GAAGA,EAAEA,EAAE,EAAE,EAAEE,IAAIzJ,KAAKgX,GAAG,IAAI,IAAI,GAAGzN,CAAC,EAAEvJ,KAAKyX,GAAG,WAAW,OAAO,GAAGhO,IAAIzJ,KAAKgX,GAAG,IAAI,IAAI,EAAE,EAAEhX,KAAK6T,GAAG,SAAStK,EAAEE,GAAGzJ,KAAK0X,GAAG,GAAG1X,KAAKiX,GAAG1N,GAAGvJ,KAAKmX,GAAG1N,GAAGzJ,KAAKqX,KAAKrX,KAAKsX,IAAG,GAAItX,KAAKwX,IAAG,EAAG,EAAExX,KAAK2X,GAAG,WAAWC,QAAQC,IAAI9N,IAAI/J,KAAKgX,IAAI,EAAE,EAAE,EAAEhX,KAAK8X,GAAG,WAAW,OAAO,IAAIF,QAAQG,IAAIhO,IAAI/J,KAAKgX,IAAI,EAAE,EAAE,EAAEhX,KAAK0X,GAAG,SAASnO,GAAG9J,IAAIO,KAAKgX,GAAG,IAAI,IAAI,GAAGzN,CAAC,EAAEvJ,KAAKgY,GAAG,WAAW,OAAOvY,IAAIO,KAAKgX,GAAG,IAAI,IAAI,EAAE,EAAEhX,KAAKiY,GAAG,WAAW,GAAGC,GAAGlY,KAAKkX,MAAM,OAAOzX,IAAIO,KAAK+W,IAAI,IAAI,GAAG,IAAIxN,EAAEvJ,KAAKgY,KAAK,OAAO,IAAIzO,EAAEA,EAAEvJ,KAAK+W,EAAE,CAAC,CAAC,SAASoB,GAAG5O,GAAG,OAAO6O,GAAG,IAAItB,GAAGvN,GAAGyN,GAAG,CAAC,SAASqB,GAAG9O,EAAEE,EAAET,EAAEe,GAAG,OAAO0B,EAAEwH,GAAG,EAAE,EAAE1J,EAAEE,EAAET,EAAEe,GAAGuO,GAAG/O,EAAEE,EAAET,EAAEe,EAAE,CAAC,SAASuO,GAAG/O,EAAEE,EAAET,EAAEe,GAAG,GAAG,oBAAoB7B,kBAAkB,OAAOuE,EAAE,uFAAuF,EAAE,IAAIhN,EAAE,GAAG,OAAOgM,GAAG,IAAIhM,EAAEG,OAAOyY,GAAG9O,EAAEE,EAAET,EAAEe,IAAIR,EAAE,CAACiJ,GAAGxJ,EAAEsJ,GAAG/I,EAAEkJ,GAAG1I,EAAE+I,GAAGrT,GAAGgM,GAAGlC,EAAEgP,GAAG,cAAclH,YAAY9H,EAAE9J,GAAG,GAAG0S,GAAG5I,GAAG,CAAC,SAASiP,GAAGjP,EAAEE,EAAET,GAAG,OAAOyC,EAAEwH,GAAG,EAAE,EAAE1J,EAAEE,EAAET,GAAG,CAAC,CAAC,SAASyP,GAAGlP,EAAEE,GAAG,GAAGgC,EAAE,OAAOwH,GAAG,EAAE,EAAE1J,EAAEE,EAAE,CAAC,SAASiP,GAAGnP,EAAEE,GAAG,GAAGgC,EAAE,OAAOwH,GAAG,EAAE,EAAE1J,EAAEE,EAAE,CAAC,SAASkP,GAAGpP,EAAEE,EAAET,GAAG,GAAGyC,EAAE,OAAOwH,GAAG,EAAE,EAAE1J,EAAEE,EAAET,EAAE,CAAC,SAAS4P,GAAGrP,EAAEE,EAAET,GAAG,OAAOyC,EAAEwH,GAAG,EAAE,EAAE1J,EAAEE,EAAET,GAAG,CAAC,CAAC,SAAS6P,GAAGtP,EAAEE,GAAG,GAAGgC,EAAE,OAAOwH,GAAG,EAAE,EAAE1J,EAAEE,EAAE,CAAC,SAASqP,GAAGvP,EAAEE,EAAET,GAAG,GAAGyC,EAAE,OAAOwH,GAAG,GAAG,EAAE1J,EAAEE,EAAET,EAAE,CAAC,SAAS+P,GAAGxP,EAAEE,EAAET,EAAEe,GAAG,GAAG0B,EAAE,OAAOwH,GAAG,GAAG,EAAE1J,EAAEE,EAAET,EAAEe,EAAE,CAAC,SAASiP,GAAGzP,EAAEE,EAAET,EAAEe,GAAG,GAAG0B,EAAE,OAAOwH,GAAG,GAAG,EAAE1J,EAAEE,EAAET,EAAEe,EAAE,CAAC,SAASkP,GAAG1P,EAAEE,EAAET,EAAEe,GAAG,GAAG0B,EAAE,OAAOwH,GAAG,GAAG,EAAE1J,EAAEE,EAAET,EAAEe,EAAE,CAAC,SAASmP,GAAG3P,GAAG,GAAGkC,EAAE,OAAOwH,GAAG,GAAG,EAAE1J,EAAE,CAAC,SAAS4P,GAAG5P,EAAEE,GAAG,GAAGgC,EAAE,OAAOwH,GAAG,GAAG,EAAE1J,EAAEE,EAAE,CAAC,SAAS2P,GAAG7P,EAAEE,EAAET,GAAG,GAAGyC,EAAE,OAAOwH,GAAG,GAAG,EAAE1J,EAAEE,EAAET,EAAE,CAAC,SAASiM,GAAG1L,GAAGqO,QAAQyB,MAAMtP,IAAIR,GAAG,EAAE,GAAGuL,MAAMwE,GAAG/P,GAAGqO,QAAQ2B,gBAAgBxP,IAAIR,GAAG,EAAE,EAAE,EAAE,CAAC,SAASiQ,GAAGjQ,GAAG,OAAO9J,IAAI8J,IAAI,GAAG,WAAWQ,IAAIR,EAAE,IAAI,EAAE,CAAC,SAASkQ,GAAGlQ,EAAEE,EAAET,EAAEe,EAAEtK,EAAEyK,GAAG,OAAOuB,EAAEwH,GAAG,GAAG,EAAE1J,EAAEE,EAAET,EAAEe,EAAEtK,EAAEyK,IAAI,EAAE,CAAC,SAASwP,GAAGnQ,EAAEE,EAAET,EAAEe,EAAEtK,EAAEyK,GAAG,GAAGuB,EAAE,OAAOwH,GAAG,GAAG,EAAE1J,EAAEE,EAAET,EAAEe,EAAEtK,EAAEyK,EAAE,CAAC,SAASyP,GAAGpQ,GAAG,IAAIP,EAAEwG,EAAEjG,GAAG,EAAEQ,EAAE6P,GAAG5Q,GAAG,OAAOe,GAAGuF,EAAE/F,EAAEE,IAAIM,EAAEf,GAAGe,CAAC,CAAC,SAAS8P,GAAGtQ,EAAEE,EAAET,GAAG,SAASkB,EAAEX,GAAG,OAAOA,EAAEA,EAAEuQ,eAAeC,MAAM,sBAAsBxQ,EAAE,GAAG,KAAK,CAAC,GAAGkC,EAAE,OAAOwH,GAAG,GAAG,EAAE1J,EAAEE,EAAET,GAAG,IAAIoB,GAAE,IAAK4P,MAAMC,cAAc5P,EAAE,IAAI2P,KAAK5P,EAAE,EAAE,GAAGE,EAAE,IAAI0P,KAAK5P,EAAE,EAAE,GAAGA,EAAEC,EAAE6P,oBAAoB,IAAI1P,EAAEF,EAAE4P,oBAAoBzP,EAAE0P,KAAKC,IAAIhQ,EAAEI,GAAGT,IAAIR,GAAG,IAAI,GAAG,GAAGkB,EAAEV,IAAIN,GAAG,IAAI,GAAGnH,OAAO8H,GAAGI,GAAGjB,EAAEW,EAAEG,GAAGZ,EAAES,EAAEI,GAAGf,EAAEoQ,GAAGpQ,GAAGE,EAAEkQ,GAAGlQ,GAAGe,EAAEJ,GAAG3K,IAAIuJ,GAAG,IAAI,GAAGO,EAAE9J,IAAIuJ,EAAE,GAAG,IAAI,GAAGS,IAAIhK,IAAIuJ,GAAG,IAAI,GAAGS,EAAEhK,IAAIuJ,EAAE,GAAG,IAAI,GAAGO,EAAE,CAAC,SAAS0J,GAAG1J,EAAEE,GAAG,IAAIT,EAAEqR,UAAUza,OAAO,EAAEmK,EAAEsQ,UAAU,OAAOtE,IAAG,KAAM,IAAI,IAAItW,EAAE6a,GAAG,EAAEtR,GAAGoB,EAAE3K,GAAG,EAAE4K,EAAE,EAAEA,EAAErB,EAAEqB,IAAI,CAAC,IAAIC,EAAEP,EAAE,EAAEM,GAAGH,IAAIE,EAAEC,IAAI,GAAGC,CAAC,CAAC,OAAOiQ,GAAGhR,EAAEP,EAAEvJ,EAAEgK,EAAG,GAAE,CAACW,EAAEoQ,6BAA6BvF,GAAGyB,GAAGnP,EAAE,KAAK,IAAIgC,EAAE+B,QAAQmP,SAAS,OAAO,IAAIlR,EAAE,GAAGA,EAAE,GAAG,KAAKkC,EAAE,IAAIkC,YAAYkF,MAAMzI,EAAEsQ,8BAA8B,IAAI/M,YAAYkF,MAAM,IAAI8H,GAAGC,GAAG,GAAGC,GAAG,CAAC,EAAE,SAASC,KAAK,IAAIH,GAAG,CAAC,IAAIpR,EAAEE,EAAE,CAACsR,KAAK,WAAWC,QAAQ,WAAWC,KAAK,IAAIC,IAAI,IAAIC,KAAK,iBAAiBC,MAAM,iBAAiBC,WAAWA,UAAUC,WAAWD,UAAUC,UAAU,IAAI,KAAKnP,QAAQ,IAAI,KAAK,SAASf,EAAEJ,GAAG,kBAAkB,IAAIzB,KAAKsR,QAAG,IAASA,GAAGtR,UAAUE,EAAEF,GAAGE,EAAEF,GAAGsR,GAAGtR,GAAG,IAAIP,EAAE,GAAG,IAAIO,KAAKE,EAAET,EAAEnJ,KAAK0J,EAAE,IAAIE,EAAEF,IAAIoR,GAAG3R,CAAC,CAAC,OAAO2R,EAAE,CAAC,SAASY,GAAGhS,EAAEP,GAAG,GAAGyC,EAAE,OAAOwH,GAAG,GAAG,EAAE1J,EAAEP,GAAG,IAAIe,EAAE,EAAE,OAAO+Q,KAAKrG,SAAQ,SAAUvK,EAAEE,GAAG,IAAIC,EAAErB,EAAEe,EAAE,IAAIK,EAAE3K,IAAI8J,EAAE,EAAEa,GAAG,IAAI,GAAGC,EAAEA,EAAE,EAAEA,EAAEH,EAAEtK,SAASyK,EAAEZ,IAAIW,KAAK,IAAI,GAAGF,EAAEqF,WAAWlF,GAAGZ,IAAIW,GAAG,IAAI,GAAG,EAAEL,GAAGG,EAAEtK,OAAO,CAAE,IAAG,CAAC,CAAC,SAAS4b,GAAGjS,EAAEE,GAAG,GAAGgC,EAAE,OAAOwH,GAAG,GAAG,EAAE1J,EAAEE,GAAG,IAAIT,EAAE8R,KAAKrb,IAAI8J,GAAG,IAAI,GAAGP,EAAEpJ,OAAO,IAAImK,EAAE,EAAE,OAAOf,EAAEyL,SAAQ,SAAUlL,GAAGQ,GAAGR,EAAE3J,OAAO,CAAE,IAAGH,IAAIgK,GAAG,IAAI,GAAGM,EAAE,CAAC,CAAC,SAAS0R,GAAGlS,GAAG,OAAOkC,EAAEwH,GAAG,GAAG,EAAE1J,GAAG,EAAE,CAAC,SAASmS,GAAGnS,EAAEE,EAAET,EAAEe,GAAG,OAAO0B,EAAEwH,GAAG,GAAG,EAAE1J,EAAEE,EAAET,EAAEe,GAAG,EAAE,CAAC,SAAS4R,GAAGpS,EAAEE,EAAET,EAAEe,EAAEtK,GAAG,OAAOgM,EAAEwH,GAAG,GAAG,EAAE1J,EAAEE,EAAET,EAAEe,EAAEtK,GAAG,EAAE,CAAC,IAAIgU,GAAG,CAAC,KAAK,GAAG,IAAI,SAASC,GAAGnK,EAAEE,GAAG,IAAIT,EAAEyK,GAAGlK,GAAG,IAAIE,GAAG,KAAKA,IAAI,IAAIF,EAAE4E,EAAE1B,GAAGwC,EAAEjG,EAAE,IAAIA,EAAEpJ,OAAO,GAAGoJ,EAAEnJ,KAAK4J,EAAE,CAAC,SAASmS,GAAGrS,EAAEE,EAAEM,EAAEG,GAAG,GAAGuB,EAAE,OAAOwH,GAAG,GAAG,EAAE1J,EAAEE,EAAEM,EAAEG,GAAG,IAAI,IAAIE,EAAE,EAAEC,EAAE,EAAEA,EAAEN,EAAEM,IAAI,CAAC,IAAIC,EAAE7K,IAAIgK,GAAG,IAAI,GAAGe,EAAE/K,IAAIgK,EAAE,GAAG,IAAI,GAAGA,GAAG,EAAE,IAAI,IAAIgB,EAAE,EAAEA,EAAED,EAAEC,IAAIiJ,GAAGnK,EAAEP,IAAIsB,EAAEG,IAAI,IAAIL,GAAGI,CAAC,CAAC,OAAO/K,IAAIyK,GAAG,IAAI,GAAGE,EAAE,CAAC,CAAC,IAAIyR,GAAG,EAAE,SAASxP,GAAG9C,GAAG,OAAO,GAAGA,EAAE,IAAI,GAAGA,EAAE,KAAK,GAAGA,EAAE,IAAI,CAAC,IAAIuS,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAIC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,SAASC,GAAGzS,EAAEP,EAAEvJ,EAAEyK,GAAG,SAASE,EAAEb,EAAEE,EAAET,GAAG,IAAIO,EAAE,iBAAiBA,EAAEA,EAAE0S,WAAW1S,GAAG,GAAGA,EAAE3J,OAAO6J,GAAGF,EAAEP,EAAE,GAAGO,EAAE,OAAOA,CAAC,CAAC,SAASc,EAAEd,EAAEE,GAAG,OAAOW,EAAEb,EAAEE,EAAE,IAAI,CAAC,SAASa,EAAEf,EAAEE,GAAG,SAAST,EAAEO,GAAG,OAAO,EAAEA,GAAG,EAAE,EAAEA,EAAE,EAAE,CAAC,CAAC,IAAIQ,EAAE,OAAO,KAAKA,EAAEf,EAAEO,EAAE0Q,cAAcxQ,EAAEwQ,iBAAiB,KAAKlQ,EAAEf,EAAEO,EAAE2S,WAAWzS,EAAEyS,eAAenS,EAAEf,EAAEO,EAAE4S,UAAU1S,EAAE0S,YAAYpS,CAAC,CAAC,SAASS,EAAEjB,GAAG,OAAOA,EAAE6S,UAAU,KAAK,EAAE,OAAO,IAAIpC,KAAKzQ,EAAE0Q,cAAc,EAAE,GAAG,IAAI,KAAK,EAAE,OAAO1Q,EAAE,KAAK,EAAE,OAAO,IAAIyQ,KAAKzQ,EAAE0Q,cAAc,EAAE,GAAG,KAAK,EAAE,OAAO,IAAID,KAAKzQ,EAAE0Q,cAAc,EAAE,GAAG,KAAK,EAAE,OAAO,IAAID,KAAKzQ,EAAE0Q,cAAc,EAAE,GAAG,KAAK,EAAE,OAAO,IAAID,KAAKzQ,EAAE0Q,cAAc,EAAE,GAAG,IAAI,KAAK,EAAE,OAAO,IAAID,KAAKzQ,EAAE0Q,cAAc,EAAE,GAAG,IAAI,CAAC,SAASxP,EAAElB,GAAG,IAAIE,EAAEF,EAAE8S,GAAG,IAAI9S,EAAE,IAAIyQ,KAAK,IAAIA,KAAKzQ,EAAE+S,GAAG,KAAK,EAAE,GAAGC,WAAW,EAAE9S,GAAG,CAAC,IAAIT,EAAEO,EAAE2S,WAAWnS,GAAGsC,GAAG9C,EAAE0Q,eAAe6B,GAAGC,IAAI/S,GAAG,KAAKS,EAAEM,EAAER,EAAE4S,WAAW,CAAC5S,EAAEiT,QAAQjT,EAAE4S,UAAU1S,GAAG,KAAK,CAACA,GAAGM,EAAER,EAAE4S,UAAU,EAAE5S,EAAEiT,QAAQ,GAAG,GAAGxT,EAAEO,EAAEkT,SAASzT,EAAE,IAAIO,EAAEkT,SAAS,GAAGlT,EAAEmT,YAAYnT,EAAE0Q,cAAc,GAAG,CAAC,OAAOjR,EAAE,IAAIgR,KAAKzQ,EAAE0Q,cAAc,EAAE,EAAE,GAAGxQ,EAAEe,EAAE,IAAIwP,KAAKzQ,EAAE0Q,cAAc,EAAE,IAAIjR,EAAEwB,EAAExB,GAAG,GAAGsB,EAAEb,EAAEF,GAAG,GAAGe,EAAEtB,EAAEO,GAAGA,EAAE0Q,cAAc,EAAE1Q,EAAE0Q,cAAc1Q,EAAE0Q,cAAc,CAAC,CAAC,IAAIvP,EAAEX,IAAIG,EAAE,IAAI,IAAI,GAAG,IAAI,IAAIS,KAAKT,EAAE,CAACyS,GAAG5S,IAAIG,GAAG,IAAI,GAAG0S,GAAG7S,IAAIG,EAAE,GAAG,IAAI,GAAG2S,GAAG9S,IAAIG,EAAE,GAAG,IAAI,GAAG4S,GAAG/S,IAAIG,EAAE,IAAI,IAAI,GAAG6S,GAAGhT,IAAIG,EAAE,IAAI,IAAI,GAAGoS,GAAGvS,IAAIG,EAAE,IAAI,IAAI,GAAG8S,GAAGjT,IAAIG,EAAE,IAAI,IAAI,GAAGmS,GAAGtS,IAAIG,EAAE,IAAI,IAAI,GAAG+S,GAAGlT,IAAIG,EAAE,IAAI,IAAI,GAAGgT,GAAGnT,IAAIG,EAAE,IAAI,IAAI,GAAGiT,GAAGzS,EAAE2E,EAAE3E,GAAG,IAAIjL,EAAE4P,EAAE5P,GAAGiL,EAAE,CAAC,KAAK,uBAAuB,KAAK,WAAW,KAAK,WAAW,KAAK,KAAK,KAAK,cAAc,KAAK,QAAQ,KAAK,WAAW,KAAK,WAAW,KAAK,WAAW,MAAM,KAAK,MAAM,KAAK,MAAM,WAAW,MAAM,WAAW,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,MAAMjL,EAAEA,EAAE0M,QAAQ,IAAIiR,OAAOzS,EAAE,KAAKD,EAAEC,IAAI,IAAIC,EAAE,2DAA2DyS,MAAM,KAAKxS,EAAE,wFAAwFwS,MAAM,KAAK,IAAI1S,KAAKD,EAAE,CAAC,KAAK,SAASnB,GAAG,OAAOqB,EAAErB,EAAEyT,IAAIM,UAAU,EAAE,EAAE,EAAE,KAAK,SAAS/T,GAAG,OAAOqB,EAAErB,EAAEyT,GAAG,EAAE,KAAK,SAASzT,GAAG,OAAOsB,EAAEtB,EAAEwT,IAAIO,UAAU,EAAE,EAAE,EAAE,KAAK,SAAS/T,GAAG,OAAOsB,EAAEtB,EAAEwT,GAAG,EAAE,KAAK,SAASxT,GAAG,OAAOc,GAAGd,EAAE+S,GAAG,MAAM,IAAI,EAAE,EAAE,EAAE,KAAK,SAAS/S,GAAG,OAAOc,EAAEd,EAAEuT,GAAG,EAAE,EAAE,KAAK,SAASvT,GAAG,OAAOa,EAAEb,EAAEuT,GAAG,EAAE,IAAI,EAAE,KAAK,SAASvT,GAAG,OAAOkB,EAAElB,GAAG0S,WAAWqB,UAAU,EAAE,EAAE,KAAK,SAAS/T,GAAG,OAAOkB,EAAElB,EAAE,EAAE,KAAK,SAASA,GAAG,OAAOc,EAAEd,EAAEsT,GAAG,EAAE,EAAE,KAAK,SAAStT,GAAG,OAAO,IAAIA,EAAEA,EAAEsT,IAAItT,EAAE,GAAG,GAAGA,IAAIA,GAAG,IAAIc,EAAEd,EAAE,EAAE,EAAE,KAAK,SAASA,GAAG,IAAI,IAAIE,EAAE,EAAET,EAAE,EAAEA,GAAGO,EAAEwT,GAAG,EAAEtT,IAAI4C,GAAG9C,EAAE+S,GAAG,MAAMR,GAAGC,IAAI/S,MAAM,OAAOqB,EAAEd,EAAEuT,GAAGrT,EAAE,EAAE,EAAE,KAAK,SAASF,GAAG,OAAOc,EAAEd,EAAEwT,GAAG,EAAE,EAAE,EAAE,KAAK,SAASxT,GAAG,OAAOc,EAAEd,EAAEqT,GAAG,EAAE,EAAE,KAAK,WAAW,MAAM,IAAI,EAAE,KAAK,SAASrT,GAAG,OAAO,GAAGA,EAAEsT,IAAI,GAAGtT,EAAEsT,GAAG,KAAK,IAAI,EAAE,KAAK,SAAStT,GAAG,OAAOc,EAAEd,EAAEoT,GAAG,EAAE,EAAE,KAAK,WAAW,MAAM,IAAI,EAAE,KAAK,SAASpT,GAAG,OAAOA,EAAEyT,IAAI,CAAC,EAAE,KAAK,SAASzT,GAAG,OAAOc,EAAE8P,KAAKoD,OAAOhU,EAAE8S,GAAG,EAAE9S,EAAEyT,IAAI,GAAG,EAAE,EAAE,KAAK,SAASzT,GAAG,IAAIE,EAAE0Q,KAAKoD,OAAOhU,EAAE8S,GAAG,GAAG9S,EAAEyT,GAAG,GAAG,GAAG,GAAG,GAAG,IAAIzT,EAAEyT,GAAG,IAAIzT,EAAE8S,GAAG,GAAG,GAAG5S,IAAIA,EAAE,IAAIA,IAAI,IAAIT,GAAGO,EAAEyT,GAAG,IAAIzT,EAAE8S,IAAI,IAAI,GAAGrT,GAAGqD,GAAG9C,EAAE+S,MAAM7S,EAAE,QAAQ,CAACA,EAAE,GAAG,IAAIT,GAAGO,EAAEyT,GAAG,EAAEzT,EAAE8S,GAAG,GAAG,GAAG,GAAGrT,GAAG,GAAGA,GAAGqD,GAAG9C,EAAE+S,GAAG,IAAI,KAAK7S,GAAG,CAAC,OAAOY,EAAEZ,EAAE,EAAE,EAAE,KAAK,SAASF,GAAG,OAAOA,EAAEyT,EAAE,EAAE,KAAK,SAASzT,GAAG,OAAOc,EAAE8P,KAAKoD,OAAOhU,EAAE8S,GAAG,GAAG9S,EAAEyT,GAAG,GAAG,GAAG,GAAG,EAAE,EAAE,KAAK,SAASzT,GAAG,OAAOA,EAAE+S,GAAG,MAAML,WAAWqB,UAAU,EAAE,EAAE,KAAK,SAAS/T,GAAG,OAAOA,EAAE+S,GAAG,IAAI,EAAE,KAAK,SAAS/S,GAAG,IAAIE,EAAE,IAAIF,EAAEA,EAAE2T,IAAI,OAAO3T,EAAE4Q,KAAKqD,IAAIjU,GAAG,IAAIE,EAAE,IAAI,KAAK9E,OAAO,QAAQ4E,EAAE,GAAG,IAAIA,EAAE,KAAK6C,OAAO,EAAE,EAAE,KAAK,SAAS7C,GAAG,OAAOA,EAAE4T,EAAE,EAAE,KAAK,WAAW,MAAM,GAAG,GAAG1d,EAAEA,EAAE0M,QAAQ,MAAM,QAAQzB,EAAEjL,EAAEge,SAAS9S,KAAKlL,EAAEA,EAAE0M,QAAQ,IAAIiR,OAAOzS,EAAE,KAAKD,EAAEC,GAAGT,KAAK,OAAOS,EAAE,SAASpB,GAAG,IAAIE,EAAE3H,MAAM0N,EAAEjG,GAAG,GAAG,OAAO+F,EAAE/F,EAAEE,EAAE,EAAEA,EAAE7J,QAAQ6J,CAAC,CAA1D,CAA4DhK,EAAEA,EAAE0M,QAAQ,QAAQ,MAAMxB,EAAE/K,OAAOoJ,EAAE,GAAG,SAASO,EAAEP,GAAGS,IAAInI,IAAIiI,EAAEP,IAAI,EAAE,CAA9B,CAAgC2B,EAAEpB,GAAGoB,EAAE/K,OAAO,EAAE,CAACoS,GAAG6B,KAAK,IAAI6J,GAAG,CAAC,KAAK1K,GAAGK,GAAGgF,GAAGG,GAAGC,GAAGC,GAAGC,GAAGC,GAAGC,GAAGC,GAAGC,GAAGC,GAAGC,GAAGC,GAAGC,GAAGC,GAAGK,GAAGC,GAAGG,GAAG0B,GAAGC,GAAGC,GAAGC,GAAGC,GAAGC,IAAI+B,GAAG,CAAC1S,EAAE,SAAS1B,GAAG,OAAOqQ,GAAGrQ,EAAE,IAAI,EAAE,EAAEE,EAAE,SAASF,GAAG,OAAOA,EAAE,IAAIuN,GAAGvN,IAAIgO,OAAOhO,EAAE+N,IAAG,GAAIV,MAAMrN,EAAEiO,IAAG,GAAIb,GAAG9W,KAAK0J,GAAGA,EAAEoO,KAAKpO,EAAE0O,IAAI,EAAE2F,GAAG,SAASrU,GAAG,MAAMkD,EAAE,0EAA0EqC,GAAE,EAAGvF,CAAC,EAAEkD,EAAE,WAAWoR,GAAG,GAAG,IAAItU,EAAEoN,GAAGb,MAAM,GAAGvM,EAAEuO,OAAOvO,EAAEkO,KAAK,CAAC,IAAIhO,EAAEF,EAAE6N,KAAK3N,GAAG6M,GAAG7M,EAAH6M,CAAM/M,EAAEwN,IAAIoB,GAAG5O,EAAEwN,GAAG,CAACF,GAAG,CAAC,EAAE7N,EAAE,WAAW,IAAIO,EAAEsN,GAAG,IAAItN,EAAE,OAAOsS,GAAG,EAAE,IAAIpS,EAAE,IAAIqN,GAAGvN,GAAGE,EAAEiO,GAAGnO,GAAG,IAAIP,EAAES,EAAEyN,KAAK,IAAIlO,EAAE,OAAO6S,GAAG,EAAEtS,EAAE,IAAI,IAAIQ,EAAEjI,MAAMgc,UAAU1R,MAAMtE,KAAKuS,WAAW5a,EAAE,EAAEA,EAAEsK,EAAEnK,OAAOH,IAAI,CAAC,IAAIyK,EAAEH,EAAEtK,GAAG,GAAG,IAAIyK,GAAGA,IAAIlB,EAAE,MAAM,GAAG+U,GAAG7T,EAAElB,EAAES,EAAEuN,GAAG,IAAI,OAAO6E,GAAG3R,EAAEX,CAAC,CAAC,OAAOsS,GAAG7S,EAAEO,CAAC,EAAEmB,EAAE,WAAW,IAAInB,EAAEsN,GAAG,IAAItN,EAAE,OAAOsS,GAAG,EAAE,IAAIpS,EAAE,IAAIqN,GAAGvN,GAAGE,EAAEiO,GAAGnO,GAAG,IAAIP,EAAES,EAAEyN,KAAK,IAAIlO,EAAE,OAAO6S,GAAG,EAAEtS,EAAE,IAAI,IAAIQ,EAAEjI,MAAMgc,UAAU1R,MAAMtE,KAAKuS,WAAW5a,EAAE,EAAEA,EAAEsK,EAAEnK,OAAOH,IAAI,CAAC,IAAIyK,EAAEH,EAAEtK,GAAG,GAAG,IAAIyK,GAAGA,IAAIlB,EAAE,MAAM,GAAG+U,GAAG7T,EAAElB,EAAES,EAAEuN,GAAG,IAAI,OAAO6E,GAAG3R,EAAEX,CAAC,CAAC,OAAOsS,GAAG7S,EAAEO,CAAC,EAAEqB,EAAE,WAAW,IAAIrB,EAAEsN,GAAG,IAAItN,EAAE,OAAOsS,GAAG,EAAE,IAAIpS,EAAE,IAAIqN,GAAGvN,GAAGE,EAAEiO,GAAGnO,GAAG,IAAIP,EAAES,EAAEyN,KAAK,IAAIlO,EAAE,OAAO6S,GAAG,EAAEtS,EAAE,IAAI,IAAIQ,EAAEjI,MAAMgc,UAAU1R,MAAMtE,KAAKuS,WAAW5a,EAAE,EAAEA,EAAEsK,EAAEnK,OAAOH,IAAI,CAAC,IAAIyK,EAAEH,EAAEtK,GAAG,GAAG,IAAIyK,GAAGA,IAAIlB,EAAE,MAAM,GAAG+U,GAAG7T,EAAElB,EAAES,EAAEuN,GAAG,IAAI,OAAO6E,GAAG3R,EAAEX,CAAC,CAAC,OAAOsS,GAAG7S,EAAEO,CAAC,EAAEA,EAAE4O,GAAGjK,EAAE,WAAW,IAAI3E,EAAEoN,GAAGb,MAAMvM,GAAGqF,GAAG,yBAAyB,IAAInF,EAAEF,EAAEwN,GAAG,MAAMxN,EAAEkO,OAAOd,GAAG9W,KAAK0J,GAAGA,EAAEiO,IAAG,GAAIjO,EAAE+N,IAAG,GAAIV,MAAMC,GAAGpN,EAAEA,CAAC,EAAEa,EAAE,SAASf,EAAEE,EAAET,GAAG,MAAM,IAAI8N,GAAGvN,GAAGsK,GAAGpK,EAAET,GAAG6N,GAAGtN,EAAEqN,KAAKrN,CAAC,EAAEyU,GAAG,WAAW,OAAOpH,EAAE,EAAEqH,GAAG,SAAS1U,GAAG2U,GAAG3U,GAAG6B,EAAE,GAAGF,GAAG8G,GAAGmC,IAAI,EAAExI,EAAE,SAASpC,GAAGkC,EAAE4F,YAAY,CAACC,IAAI,gBAAgB6D,OAAO5L,IAAIwI,GAAGxI,EAAE,EAAE4U,GAAG7F,GAAGzO,EAAE,SAASN,GAAG,MAAMsN,KAAKA,GAAGtN,GAAGA,CAAC,EAAE0F,EAAEuJ,GAAG4F,GAAG3F,GAAG4F,GAAG3F,GAAG4F,GAAG3F,GAAG4F,GAAG3F,GAAG4F,GAAG3F,GAAG4F,GAAG3F,GAAG4F,GAAG3F,GAAGvI,EAAEwI,GAAG2F,GAAG1F,GAAG2F,GAAG1F,GAAG2F,GAAG1F,GAAG2F,GAAG1F,GAAG2F,GAAG,WAAW,EAAErO,EAAE,WAAW9B,GAAG,iHAAiH,EAAEoQ,GAAG,WAAWpQ,GAAG,iHAAiH,EAAEE,EAAE,WAAW,OAAOkL,KAAKnH,KAAK,EAAEoM,GAAG,WAAW,OAAO,OAAO,EAAEC,GAAG,WAAW,OAAM,CAAE,EAAEC,GAAG,SAAS5V,EAAEE,EAAET,EAAEe,GAAG,GAAGR,GAAGE,EAAE2V,YAAW,IAAKnK,GAAGlL,UAAU,GAAG0B,EAAE4F,YAAY,CAACwD,aAAatL,EAAE+H,IAAI,uBAAuB4D,MAAMnL,QAAQ,CAAC,KAAKR,EAAEyI,GAAGC,GAAG1I,IAAI,OAAOA,EAAE8H,YAAY,CAACC,IAAI,uBAAuB4D,MAAMnL,GAAG,CAAC,OAAO,CAAC,EAAEsV,GAAG,WAAW,OAAO,CAAC,EAAEC,GAAG,SAAS/V,EAAEE,GAAGF,EAAE,IAAIyQ,KAAK,IAAIR,GAAGjQ,IAAIQ,IAAIN,GAAG,IAAI,GAAGF,EAAEgW,gBAAgBxV,IAAIN,EAAE,GAAG,IAAI,GAAGF,EAAEiW,gBAAgBzV,IAAIN,EAAE,GAAG,IAAI,GAAGF,EAAEkW,cAAc1V,IAAIN,EAAE,IAAI,IAAI,GAAGF,EAAEmW,aAAa3V,IAAIN,EAAE,IAAI,IAAI,GAAGF,EAAEoW,cAAc5V,IAAIN,EAAE,IAAI,IAAI,GAAGF,EAAEqW,iBAAiB,KAAK7V,IAAIN,EAAE,IAAI,IAAI,GAAGF,EAAEsW,YAAYtW,GAAGA,EAAEgT,UAAUvC,KAAK8F,IAAIvW,EAAEqW,iBAAiB,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,MAAM,EAAE7V,IAAIN,EAAE,IAAI,IAAI,GAAGF,CAAC,EAAEwW,GAAG,SAASxW,EAAEE,GAAGF,EAAE,IAAIyQ,KAAK,IAAIR,GAAGjQ,IAAIQ,IAAIN,GAAG,IAAI,GAAGF,EAAEyW,aAAajW,IAAIN,EAAE,GAAG,IAAI,GAAGF,EAAE0W,aAAalW,IAAIN,EAAE,GAAG,IAAI,GAAGF,EAAE2W,WAAWnW,IAAIN,EAAE,IAAI,IAAI,GAAGF,EAAE4S,UAAUpS,IAAIN,EAAE,IAAI,IAAI,GAAGF,EAAE2S,WAAWnS,IAAIN,EAAE,IAAI,IAAI,GAAGF,EAAE0Q,cAAc,KAAKlQ,IAAIN,EAAE,IAAI,IAAI,GAAGF,EAAE6S,SAAS,IAAIpT,EAAE,IAAIgR,KAAKzQ,EAAE0Q,cAAc,EAAE,GAAGxa,GAAG8J,EAAEgT,UAAUvT,EAAEuT,WAAW,MAAM,EAAExS,IAAIN,EAAE,IAAI,IAAI,GAAGhK,EAAEsK,IAAIN,EAAE,IAAI,IAAI,IAAI,GAAGF,EAAE2Q,oBAAoBza,EAAE,IAAIua,KAAKzQ,EAAE0Q,cAAc,EAAE,GAAGC,oBAAoB3Q,EAAE,GAAG9J,IAAIuJ,EAAEA,EAAEkR,sBAAsB3Q,EAAE2Q,qBAAqBC,KAAKgG,IAAInX,EAAEvJ,IAAIsK,IAAIN,EAAE,IAAI,IAAI,GAAGF,CAAC,EAAE6W,GAAG,SAAS7W,GAAG,IAAIE,EAAE,IAAIuQ,KAAKjQ,IAAIR,EAAE,IAAI,IAAI,GAAG,KAAKQ,IAAIR,EAAE,IAAI,IAAI,GAAGQ,IAAIR,EAAE,IAAI,IAAI,GAAGQ,IAAIR,EAAE,GAAG,IAAI,GAAGQ,IAAIR,EAAE,GAAG,IAAI,GAAGQ,IAAIR,GAAG,IAAI,GAAG,GAAGP,EAAEe,IAAIR,EAAE,IAAI,IAAI,GAAG9J,EAAEgK,EAAEyQ,oBAAoBhQ,EAAE,IAAI8P,KAAKvQ,EAAEwQ,cAAc,EAAE,GAAG7P,EAAE,IAAI4P,KAAKvQ,EAAEwQ,cAAc,EAAE,GAAGC,oBAAoB7P,EAAEH,EAAEgQ,oBAAoB5P,EAAE6P,KAAKgG,IAAI9V,EAAED,GAAG,OAAO,EAAEpB,EAAEe,IAAIR,EAAE,IAAI,IAAI,GAAGjH,OAAO8H,GAAGC,GAAGC,GAAG7K,GAAG,EAAEuJ,IAAIsB,GAAG7K,KAAK2K,EAAE+P,KAAKC,IAAI/P,EAAED,GAAGX,EAAE4W,QAAQ5W,EAAE8S,UAAU,MAAM,EAAEvT,EAAEsB,EAAEF,GAAG3K,KAAKsK,IAAIR,EAAE,IAAI,IAAI,GAAGE,EAAE2S,SAASpT,GAAGS,EAAE8S,UAAUrS,EAAEqS,WAAW,MAAM,EAAExS,IAAIR,EAAE,IAAI,IAAI,GAAGP,EAAEe,IAAIR,GAAG,IAAI,GAAGE,EAAEuW,aAAajW,IAAIR,EAAE,GAAG,IAAI,GAAGE,EAAEwW,aAAalW,IAAIR,EAAE,GAAG,IAAI,GAAGE,EAAEyW,WAAWnW,IAAIR,EAAE,IAAI,IAAI,GAAGE,EAAE0S,UAAUpS,IAAIR,EAAE,IAAI,IAAI,GAAGE,EAAEyS,WAAWzS,EAAE8S,UAAU,IAAI,CAAC,EAAE+D,GAAG7G,GAAG8G,GAAG7G,GAAG8G,GAAG,SAASjX,EAAEE,EAAET,EAAEe,GAAGR,EAAEkX,KAAKlX,EAAEkX,IAAG,EAAG5G,GAAGpQ,EAAET,EAAEe,GAAG,EAAEiB,EAAE,WAAW4D,GAAG,GAAG,EAAE5E,EAAE,WAAW,IAAIzC,IAAI6D,EAAE,CAAC,IAAI7B,EAAE,2IAA2IkN,KAAKA,GAAG,CAAC,GAAGA,GAAGlN,KAAKkN,GAAGlN,GAAG,EAAEhC,IAAIgC,EAAE,YAAYA,GAAGkD,EAAElD,GAAG,CAAC,EAAEmX,GAAG,WAAW,OAAO,UAAU,EAAEpR,EAAEoH,GAAGiK,GAAG,SAASpX,EAAEE,EAAEM,GAAGf,IAAI4X,WAAWrX,IAAI,EAAEE,IAAI,EAAEA,EAAEM,IAAI,EAAE,EAAED,EAAE,WAAW,OAAOvC,EAAE,cAAqB3H,OAAOyb,UAAUwF,mBAAmB,EAAEC,GAAG,SAASvX,EAAEE,EAAET,GAAG4R,GAAGhb,OAAO6J,EAAET,IAAI,EAAE,IAAI,IAAIe,EAAE,EAAEA,EAAEN,EAAEM,IAAI6Q,GAAG7Q,GAAGG,IAAIlB,EAAEe,IAAI,GAAG,OAAO,EAAER,EAAEsI,IAAItI,EAAE,GAAGmU,GAAGnU,IAAIwX,MAAM,KAAKnG,GAAG,EAAEoG,GAAG,SAASzX,GAAG,IAAIE,EAAET,IAAIpJ,OAAO,IAAI2J,KAAK,IAAIE,GAAG,WAAWF,EAAE,OAAM,EAAG,IAAI,IAAIQ,EAAE,EAAE,GAAGA,EAAEA,GAAG,EAAE,CAAC,IAAItK,EAAEgK,GAAG,EAAE,GAAGM,GAAGtK,EAAE0a,KAAKgG,IAAI1gB,EAAE8J,EAAE,WAAW,IAAIW,EAAEiQ,KAAK1a,EAAE0a,KAAKC,IAAI7Q,EAAE9J,GAAGyK,EAAEA,EAAEiW,IAAIrY,KAAKoC,EAAE,WAAWzK,GAAG,MAAMA,EAAE,OAAO,OAAO8J,EAAE,CAAC,IAAIG,EAAEuX,KAAK/W,EAAEP,EAAEvB,WAAW,QAAQ,IAAIwB,EAAEF,EAAE/G,QAAQ,IAAIyH,EAAE,EAAE,MAAMb,CAAW,CAAT,MAAMA,GAAG,CAACa,OAAE,CAAM,CAAC,GAAGA,EAAE,OAAM,CAAE,CAAC,OAAM,CAAE,EAAE8W,GAAG,WAAW,KAAK,QAAQ,EAAEC,GAAG5F,GAAG6F,GAAG5F,GAAGjP,EAAE6G,GAAGjJ,EAAEsR,GAAG7N,EAAE8N,GAAG2F,GAAG1F,GAAGlN,EAAEmN,GAAG9Q,EAAE,WAAW,OAAO+Q,EAAE,EAAEyF,GAAG,SAAS/X,EAAEP,EAAEe,GAAGR,EAAEgY,KAAKhY,EAAEgY,GAAG,WAAW,GAAG,iBAAiBC,QAAQ,mBAAmBA,OAAOC,gBAAgB,CAAC,IAAIlY,EAAE,IAAIzI,WAAW,GAAG,MAAM,KAAK0gB,OAAOC,gBAAgBlY,GAAGA,EAAE,GAAG,CAAC,GAAGhC,EAAE,IAAI,IAAIkC,EAAE,EAAQ,wGAAU,MAAM,IAAIA,EAAEiY,YAAY,GAAG,EAAY,CAAT,MAAMnY,GAAG,CAAC,MAAM,IAAIqF,GAAG,eAAe,CAA1P,IAA+P,IAAI,IAAInP,EAAE,EAAEA,EAAEsK,EAAEtK,IAAIgK,IAAIT,EAAEvJ,GAAG,IAAI,GAAG8J,EAAEgY,KAAK,OAAO,CAAC,EAAEI,GAAG,SAASpY,EAAEE,EAAET,GAAG,IAAIe,EAAEiM,KAAK,IAAI,OAAOM,GAAG/M,EAAH+M,CAAM7M,EAAET,EAA4C,CAAzC,MAAMO,GAAG,GAAG0M,GAAGlM,GAAGR,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAE+D,GAAG,SAASrY,EAAEE,EAAET,GAAG,IAAIe,EAAEiM,KAAK,IAAI,OAAOM,GAAG/M,EAAH+M,CAAM7M,EAAET,EAA4C,CAAzC,MAAMO,GAAG,GAAG0M,GAAGlM,GAAGR,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAE5M,EAAE,SAAS1H,GAAG,IAAIE,EAAEuM,KAAK,IAAI,OAAOM,GAAG/M,EAAH+M,EAAiD,CAAzC,MAAM/M,GAAG,GAAG0M,GAAGxM,GAAGF,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAErT,EAAE,SAASjB,EAAEE,GAAG,IAAIT,EAAEgN,KAAK,IAAI,OAAOM,GAAG/M,EAAH+M,CAAM7M,EAA4C,CAAzC,MAAMF,GAAG,GAAG0M,GAAGjN,GAAGO,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAE9O,EAAE,SAASxF,EAAEE,EAAET,GAAG,IAAIe,EAAEiM,KAAK,IAAI,OAAOM,GAAG/M,EAAH+M,CAAM7M,EAAET,EAA4C,CAAzC,MAAMO,GAAG,GAAG0M,GAAGlM,GAAGR,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAEhN,EAAE,SAAStH,EAAEE,EAAET,GAAG,IAAIe,EAAEiM,KAAK,IAAI,OAAOM,GAAG/M,EAAH+M,CAAM7M,EAAET,EAA4C,CAAzC,MAAMO,GAAG,GAAG0M,GAAGlM,GAAGR,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAEhP,EAAE,SAAStF,EAAEE,EAAET,GAAG,IAAIe,EAAEiM,KAAK,IAAI,OAAOM,GAAG/M,EAAH+M,CAAM7M,EAAET,EAA4C,CAAzC,MAAMO,GAAG,GAAG0M,GAAGlM,GAAGR,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAElT,EAAE,SAASpB,EAAEE,EAAET,EAAEe,GAAG,IAAItK,EAAEuW,KAAK,IAAI,OAAOM,GAAG/M,EAAH+M,CAAM7M,EAAET,EAAEe,EAA4C,CAAzC,MAAMR,GAAG,GAAG0M,GAAGxW,GAAG8J,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAExO,EAAE,SAAS9F,EAAEE,EAAET,EAAEe,EAAEtK,GAAG,IAAIyK,EAAE8L,KAAK,IAAI,OAAOM,GAAG/M,EAAH+M,CAAM7M,EAAET,EAAEe,EAAEtK,EAA4C,CAAzC,MAAM8J,GAAG,GAAG0M,GAAG/L,GAAGX,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAE5N,EAAE,SAAS1G,EAAEE,EAAET,EAAEe,EAAEtK,EAAEyK,GAAG,IAAIE,EAAE4L,KAAK,IAAI,OAAOM,GAAG/M,EAAH+M,CAAM7M,EAAET,EAAEe,EAAEtK,EAAEyK,EAA4C,CAAzC,MAAMX,GAAG,GAAG0M,GAAG7L,GAAGb,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAEpT,EAAE,SAASlB,EAAEE,EAAET,EAAEe,EAAEtK,EAAEyK,GAAG,IAAIE,EAAE4L,KAAK,IAAI,OAAOM,GAAG/M,EAAH+M,CAAM7M,EAAET,EAAEe,EAAEtK,EAAEyK,EAA4C,CAAzC,MAAMX,GAAG,GAAG0M,GAAG7L,GAAGb,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAEpS,EAAE,SAASlC,EAAEE,EAAET,EAAEe,EAAEtK,EAAEyK,EAAEE,GAAG,IAAIC,EAAE2L,KAAK,IAAI,OAAOM,GAAG/M,EAAH+M,CAAM7M,EAAET,EAAEe,EAAEtK,EAAEyK,EAAEE,EAA4C,CAAzC,MAAMb,GAAG,GAAG0M,GAAG5L,GAAGd,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAEpN,EAAE,SAASlH,EAAEE,EAAET,EAAEe,EAAEtK,EAAEyK,EAAEE,EAAEC,GAAG,IAAIC,EAAE0L,KAAK,IAAI,OAAOM,GAAG/M,EAAH+M,CAAM7M,EAAET,EAAEe,EAAEtK,EAAEyK,EAAEE,EAAEC,EAA4C,CAAzC,MAAMd,GAAG,GAAG0M,GAAG3L,GAAGf,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAEnU,EAAE,SAASH,EAAEE,EAAET,EAAEe,EAAEtK,EAAEyK,EAAEE,EAAEC,EAAEC,EAAEE,EAAEC,EAAEC,GAAG,IAAIC,EAAEqL,KAAK,IAAI,OAAOM,GAAG/M,EAAH+M,CAAM7M,EAAET,EAAEe,EAAEtK,EAAEyK,EAAEE,EAAEC,EAAEC,EAAEE,EAAEC,EAAEC,EAA4C,CAAzC,MAAMnB,GAAG,GAAG0M,GAAGtL,GAAGpB,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAEgE,GAAG,SAAStY,EAAEE,EAAET,EAAEe,EAAEtK,EAAEyK,EAAEE,EAAEC,GAAG,IAAIC,EAAE0L,KAAK,IAAI,OAAO8L,GAAGvY,EAAEE,EAAET,EAAEe,EAAEtK,EAAEyK,EAAEE,EAAEC,EAA4C,CAAzC,MAAMd,GAAG,GAAG0M,GAAG3L,GAAGf,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAEzS,EAAE,SAAS7B,EAAEE,EAAET,EAAEe,EAAEtK,EAAEyK,EAAEE,GAAG,IAAIC,EAAE2L,KAAK,IAAI,OAAO+L,GAAGxY,EAAEE,EAAET,EAAEe,EAAEtK,EAAEyK,EAAEE,EAA4C,CAAzC,MAAMb,GAAG,GAAG0M,GAAG5L,GAAGd,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAElN,EAAE,SAASpH,EAAEE,EAAET,EAAEe,EAAEtK,GAAG,IAAIyK,EAAE8L,KAAK,IAAI,OAAOgM,GAAGzY,EAAEE,EAAET,EAAEe,EAAEtK,EAA4C,CAAzC,MAAM8J,GAAG,GAAG0M,GAAG/L,GAAGX,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAEoE,GAAG,SAAS1Y,EAAEE,EAAET,EAAEe,GAAG,IAAItK,EAAEuW,KAAK,IAAI,OAAOkM,GAAG3Y,EAAEE,EAAET,EAAEe,EAA4C,CAAzC,MAAMR,GAAG,GAAG0M,GAAGxW,GAAG8J,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAEjN,EAAE,SAASrH,GAAG,IAAIE,EAAEuM,KAAK,IAAI,OAAOmM,GAAG5Y,EAA4C,CAAzC,MAAMA,GAAG,GAAG0M,GAAGxM,GAAGF,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAEuE,GAAG,SAAS7Y,EAAEE,GAAG,IAAIT,EAAEgN,KAAK,IAAI,OAAOqM,GAAG9Y,EAAEE,EAA4C,CAAzC,MAAMF,GAAG,GAAG0M,GAAGjN,GAAGO,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAE5T,EAAE,SAASV,EAAEE,EAAET,GAAG,IAAIe,EAAEiM,KAAK,IAAI,OAAOsM,GAAG/Y,EAAEE,EAAET,EAA4C,CAAzC,MAAMO,GAAG,GAAG0M,GAAGlM,GAAGR,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAE3S,EAAE,SAAS3B,GAAG,IAAIE,EAAEuM,KAAK,IAAIM,GAAG/M,EAAH+M,EAAiD,CAAzC,MAAM/M,GAAG,GAAG0M,GAAGxM,GAAGF,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAE9T,EAAE,SAASR,EAAEE,GAAG,IAAIT,EAAEgN,KAAK,IAAIM,GAAG/M,EAAH+M,CAAM7M,EAA4C,CAAzC,MAAMF,GAAG,GAAG0M,GAAGjN,GAAGO,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAEpe,EAAE,SAAS8J,EAAEE,EAAET,GAAG,IAAIe,EAAEiM,KAAK,IAAIM,GAAG/M,EAAH+M,CAAM7M,EAAET,EAA4C,CAAzC,MAAMO,GAAG,GAAG0M,GAAGlM,GAAGR,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAE0E,GAAG,SAAShZ,EAAEE,EAAET,EAAEe,GAAG,IAAItK,EAAEuW,KAAK,IAAIM,GAAG/M,EAAH+M,CAAM7M,EAAET,EAAEe,EAA4C,CAAzC,MAAMR,GAAG,GAAG0M,GAAGxW,GAAG8J,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAEhT,EAAE,SAAStB,EAAEE,EAAET,EAAEe,GAAG,IAAItK,EAAEuW,KAAK,IAAIM,GAAG/M,EAAH+M,CAAM7M,EAAET,EAAEe,EAA4C,CAAzC,MAAMR,GAAG,GAAG0M,GAAGxW,GAAG8J,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAEtW,EAAE,SAASgC,EAAEE,EAAET,EAAEe,EAAEtK,GAAG,IAAIyK,EAAE8L,KAAK,IAAIM,GAAG/M,EAAH+M,CAAM7M,EAAET,EAAEe,EAAEtK,EAA4C,CAAzC,MAAM8J,GAAG,GAAG0M,GAAG/L,GAAGX,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAEzT,EAAE,SAASb,EAAEE,EAAET,EAAEe,EAAEtK,EAAEyK,GAAG,IAAIE,EAAE4L,KAAK,IAAIM,GAAG/M,EAAH+M,CAAM7M,EAAET,EAAEe,EAAEtK,EAAEyK,EAA4C,CAAzC,MAAMX,GAAG,GAAG0M,GAAG7L,GAAGb,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAEjS,EAAE,SAASrC,EAAEE,EAAET,EAAEe,EAAEtK,EAAEyK,EAAEE,GAAG,IAAIC,EAAE2L,KAAK,IAAIM,GAAG/M,EAAH+M,CAAM7M,EAAET,EAAEe,EAAEtK,EAAEyK,EAAEE,EAA4C,CAAzC,MAAMb,GAAG,GAAG0M,GAAG5L,GAAGd,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAE9P,EAAE,SAASxE,EAAEE,EAAET,EAAEe,EAAEtK,EAAEyK,EAAEE,EAAEC,GAAG,IAAIC,EAAE0L,KAAK,IAAIM,GAAG/M,EAAH+M,CAAM7M,EAAET,EAAEe,EAAEtK,EAAEyK,EAAEE,EAAEC,EAA4C,CAAzC,MAAMd,GAAG,GAAG0M,GAAG3L,GAAGf,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAE2E,GAAG,SAASjZ,EAAEE,EAAET,EAAEe,EAAEtK,EAAEyK,EAAEE,EAAEC,EAAEC,GAAG,IAAIE,EAAEwL,KAAK,IAAIM,GAAG/M,EAAH+M,CAAM7M,EAAET,EAAEe,EAAEtK,EAAEyK,EAAEE,EAAEC,EAAEC,EAA4C,CAAzC,MAAMf,GAAG,GAAG0M,GAAGzL,GAAGjB,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAE1P,EAAE,SAAS5E,EAAEE,EAAET,EAAEe,EAAEtK,EAAEyK,EAAEE,EAAEC,EAAEC,EAAEE,EAAEC,GAAG,IAAIC,EAAEsL,KAAK,IAAIM,GAAG/M,EAAH+M,CAAM7M,EAAET,EAAEe,EAAEtK,EAAEyK,EAAEE,EAAEC,EAAEC,EAAEE,EAAEC,EAA4C,CAAzC,MAAMlB,GAAG,GAAG0M,GAAGvL,GAAGnB,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAElU,EAAE,SAASJ,EAAEE,EAAET,EAAEe,EAAEtK,EAAEyK,EAAEE,EAAEC,EAAEC,EAAEE,EAAEC,EAAEC,EAAEC,EAAEC,EAAEC,EAAEC,GAAG,IAAIE,EAAEgL,KAAK,IAAIM,GAAG/M,EAAH+M,CAAM7M,EAAET,EAAEe,EAAEtK,EAAEyK,EAAEE,EAAEC,EAAEC,EAAEE,EAAEC,EAAEC,EAAEC,EAAEC,EAAEC,EAAEC,EAA4C,CAAzC,MAAMvB,GAAG,GAAG0M,GAAGjL,GAAGzB,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAE4E,GAAG,SAASlZ,EAAEE,EAAET,EAAEe,EAAEtK,EAAEyK,EAAEE,EAAEC,GAAG,IAAIC,EAAE0L,KAAK,IAAI0M,GAAGnZ,EAAEE,EAAET,EAAEe,EAAEtK,EAAEyK,EAAEE,EAAEC,EAA4C,CAAzC,MAAMd,GAAG,GAAG0M,GAAG3L,GAAGf,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAE8E,GAAG,SAASpZ,EAAEE,EAAET,EAAEe,EAAEtK,EAAEyK,EAAEE,EAAEC,EAAEC,EAAEE,EAAEC,EAAEC,GAAG,IAAIC,EAAEqL,KAAK,IAAI4M,GAAGrZ,EAAEE,EAAET,EAAEe,EAAEtK,EAAEyK,EAAEE,EAAEC,EAAEC,EAAEE,EAAEC,EAAEC,EAA4C,CAAzC,MAAMnB,GAAG,GAAG0M,GAAGtL,GAAGpB,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAEgF,GAAG,SAAStZ,EAAEE,EAAET,EAAEe,EAAEtK,EAAEyK,GAAG,IAAIE,EAAE4L,KAAK,IAAI8M,GAAGvZ,EAAEE,EAAET,EAAEe,EAAEtK,EAAEyK,EAA4C,CAAzC,MAAMX,GAAG,GAAG0M,GAAG7L,GAAGb,IAAIA,EAAE,EAAE,MAAMA,EAAEsU,GAAG,EAAE,EAAE,CAAC,EAAExT,EAAE,SAASd,GAAG,OAAOA,CAAC,EAAEW,EAAER,GAAGU,EAAE+F,WAAWX,EAAE,SAASjG,GAAGsS,GAAGtS,CAAC,EAAEwZ,GAAG/G,GAAGpS,EAAE,SAASL,EAAEE,EAAET,EAAEe,GAAG,OAAOiS,GAAGzS,EAAEE,EAAET,EAAEe,EAAE,IAAI,WAAW,SAASR,EAAEA,EAAEE,GAAGW,EAAE4Y,IAAIzZ,EAAE/K,QAAQwT,GAAG4B,GAAG/T,KAAKuK,EAAE4Y,IAAIC,IAAIzS,EAAEpG,EAAE4Y,IAAIE,GAAGxS,EAAEM,QAAQ5G,EAAE4Y,IAAIG,IAAItU,EAAEpF,EAAEgC,IAAIyF,KAAK9G,EAAEgZ,wBAAwBhZ,EAAEgZ,uBAAuBlS,IAAI,GAAGA,KAAK,OAAOC,KAAKkS,cAAclS,IAAIA,GAAG,MAAMC,KAAK7H,EAAE6H,GAAGA,GAAG,KAAK7H,MAAM,CAAC,SAASE,EAAEA,GAAGF,EAAEE,EAAE6Z,SAAS7Z,EAAEhL,OAAO,CAAC,SAASuK,EAAEO,GAAG,OAAO,WAAW,IAAI2E,IAAIhD,GAAGE,GAAG,CAAC,GAAG,mBAAmBmY,QAAQtS,GAAEU,WAAW,WAAW,OAAO4R,MAAMtS,GAAE,CAACuS,YAAY,gBAAgBC,MAAK,SAAUla,GAAG,IAAIA,EAAEma,GAAG,KAAK,uCAAuCzS,GAAE,IAAI,OAAO1H,EAAEoa,aAAc,IAAGC,OAAM,WAAY,OAAOhS,IAAK,IAAG,GAAGnH,EAAE,OAAO,IAAIhF,SAAQ,SAAU8D,EAAEE,GAAGgB,EAAEwG,IAAE,SAAUxH,GAAGF,EAAE,IAAIzI,WAAW2I,GAAI,GAAEA,EAAG,GAAE,CAAC,OAAOhE,QAAQC,UAAU+d,MAAK,WAAY,OAAO7R,IAAK,GAAE,CAAjZ,GAAqZ6R,MAAK,SAAUla,GAAG,OAAOoF,YAAYkV,YAAYta,EAAEQ,EAAG,IAAG0Z,MAAK,SAAUla,GAAG,OAAOA,CAAE,IAAGka,KAAKla,GAAE,SAAUA,GAAGkD,EAAE,0CAA0ClD,GAAGqF,GAAGrF,EAAG,GAAE,CAAC,IAAIQ,EAAE,CAACG,EAAEyT,IAAI,GAAGlS,IAAIyF,KAAK9G,EAAEgZ,wBAAwBhZ,EAAEgZ,uBAAuBlS,KAAK9G,EAAE0Z,gBAAgB,IAAI,OAAO1Z,EAAE0Z,gBAAgB/Z,EAAER,EAAgF,CAA7E,MAAMA,GAAG,OAAOkD,EAAE,sDAAsDlD,IAAG,CAAE,EAAE2E,GAAG,mBAAmBS,YAAYoV,sBAAsBrS,MAAMT,GAAEU,WAAW,YAAYpK,GAAG,mBAAmBgc,MAAMva,EAAES,GAAG8Z,MAAMtS,GAAE,CAACuS,YAAY,gBAAgBC,MAAK,SAAUla,GAAG,OAAOoF,YAAYoV,qBAAqBxa,EAAEQ,GAAG0Z,KAAKha,GAAE,SAAUF,GAAG,OAAOkD,EAAE,kCAAkClD,GAAGkD,EAAE,6CAA6CzD,EAAES,EAAG,GAAG,KAAIma,MAAMtZ,EAAE,CAAl5C,GAAs5CF,EAAE4Z,mBAAmB,WAAW,OAAO5Z,EAAE4Z,mBAAmB5Z,EAAE4Y,IAAIG,IAAIpC,MAAM,KAAK1G,UAAU,EAAEjQ,EAAE6Z,SAAS,WAAW,OAAO7Z,EAAE6Z,SAAS7Z,EAAE4Y,IAAIkB,IAAInD,MAAM,KAAK1G,UAAU,EAAEjQ,EAAE+Z,yBAAyB,WAAW,OAAO/Z,EAAE+Z,yBAAyB/Z,EAAE4Y,IAAIoB,IAAIrD,MAAM,KAAK1G,UAAU,EAAEjQ,EAAEia,4BAA4B,WAAW,OAAOja,EAAEia,4BAA4Bja,EAAE4Y,IAAIsB,IAAIvD,MAAM,KAAK1G,UAAU,EAAEjQ,EAAEma,0BAA0B,WAAW,OAAOna,EAAEma,0BAA0Bna,EAAE4Y,IAAIwB,IAAIzD,MAAM,KAAK1G,UAAU,EAAEjQ,EAAEqa,0BAA0B,WAAW,OAAOra,EAAEqa,0BAA0Bra,EAAE4Y,IAAIzc,IAAIwa,MAAM,KAAK1G,UAAU,EAAEjQ,EAAEsa,kBAAkB,WAAW,OAAOta,EAAEsa,kBAAkBta,EAAE4Y,IAAI2B,IAAI5D,MAAM,KAAK1G,UAAU,EAAEjQ,EAAEwa,mBAAmB,WAAW,OAAOxa,EAAEwa,mBAAmBxa,EAAE4Y,IAAI6B,IAAI9D,MAAM,KAAK1G,UAAU,EAAEjQ,EAAE0a,kBAAkB,WAAW,OAAO1a,EAAE0a,kBAAkB1a,EAAE4Y,IAAI+B,IAAIhE,MAAM,KAAK1G,UAAU,EAAEjQ,EAAE4a,mBAAmB,WAAW,OAAO5a,EAAE4a,mBAAmB5a,EAAE4Y,IAAIiC,IAAIlE,MAAM,KAAK1G,UAAU,EAAEjQ,EAAE8a,iBAAiB,WAAW,OAAO9a,EAAE8a,iBAAiB9a,EAAE4Y,IAAImC,IAAIpE,MAAM,KAAK1G,UAAU,EAAEjQ,EAAEgb,kBAAkB,WAAW,OAAOhb,EAAEgb,kBAAkBhb,EAAE4Y,IAAIqC,IAAItE,MAAM,KAAK1G,UAAU,EAAEjQ,EAAEkb,SAAS,WAAW,OAAOlb,EAAEkb,SAASlb,EAAE4Y,IAAIuC,IAAIxE,MAAM,KAAK1G,UAAU,EAAEjQ,EAAEob,iBAAiB,WAAW,OAAOpb,EAAEob,iBAAiBpb,EAAE4Y,IAAIyC,IAAI1E,MAAM,KAAK1G,UAAU,EAAEjQ,EAAEsb,kBAAkB,WAAW,OAAOtb,EAAEsb,kBAAkBtb,EAAE4Y,IAAI2C,IAAI5E,MAAM,KAAK1G,UAAU,EAAEjQ,EAAEwb,kBAAkB,WAAW,OAAOxb,EAAEwb,kBAAkBxb,EAAE4Y,IAAI6C,IAAI9E,MAAM,KAAK1G,UAAU,EAAEjQ,EAAE0b,qBAAqB,WAAW,OAAO1b,EAAE0b,qBAAqB1b,EAAE4Y,IAAI+C,IAAIhF,MAAM,KAAK1G,UAAU,EAAEjQ,EAAE4b,sBAAsB,WAAW,OAAO5b,EAAE4b,sBAAsB5b,EAAE4Y,IAAIiD,IAAIlF,MAAM,KAAK1G,UAAU,EAAEjQ,EAAE8b,sBAAsB,WAAW,OAAO9b,EAAE8b,sBAAsB9b,EAAE4Y,IAAImD,IAAIpF,MAAM,KAAK1G,UAAU,EAAEjQ,EAAEgc,QAAQ,WAAW,OAAOhc,EAAEgc,QAAQhc,EAAE4Y,IAAIqD,IAAItF,MAAM,KAAK1G,UAAU,EAAEjQ,EAAEkc,iBAAiB,WAAW,OAAOlc,EAAEkc,iBAAiBlc,EAAE4Y,IAAIuD,IAAIxF,MAAM,KAAK1G,UAAU,EAAE,IAAIvF,GAAG1K,EAAEoc,cAAc,WAAW,OAAO1R,GAAG1K,EAAEoc,cAAcpc,EAAE4Y,IAAIyD,IAAI1F,MAAM,KAAK1G,UAAU,EAAET,GAAGxP,EAAEsc,QAAQ,WAAW,OAAO9M,GAAGxP,EAAEsc,QAAQtc,EAAE4Y,IAAI2D,IAAI5F,MAAM,KAAK1G,UAAU,EAAEjC,GAAGhO,EAAEwc,MAAM,WAAW,OAAOxO,GAAGhO,EAAEwc,MAAMxc,EAAE4Y,IAAI6D,IAAI9F,MAAM,KAAK1G,UAAU,EAAE7G,GAAGpJ,EAAE0c,QAAQ,WAAW,OAAOtT,GAAGpJ,EAAE0c,QAAQ1c,EAAE4Y,IAAI+D,IAAIhG,MAAM,KAAK1G,UAAU,EAAEjQ,EAAE4c,sBAAsB,WAAW,OAAO5c,EAAE4c,sBAAsB5c,EAAE4Y,IAAIC,IAAIlC,MAAM,KAAK1G,UAAU,EAAE,IAAI/G,GAAGlJ,EAAE6c,iBAAiB,WAAW,OAAO3T,GAAGlJ,EAAE6c,iBAAiB7c,EAAE4Y,IAAIkE,IAAInG,MAAM,KAAK1G,UAAU,EAAE6D,GAAG9T,EAAE+c,yBAAyB,WAAW,OAAOjJ,GAAG9T,EAAE+c,yBAAyB/c,EAAE4Y,IAAIoE,IAAIrG,MAAM,KAAK1G,UAAU,EAAEjQ,EAAEid,4BAA4B,WAAW,OAAOjd,EAAEid,4BAA4Bjd,EAAE4Y,IAAIsE,IAAIvG,MAAM,KAAK1G,UAAU,EAAE,IAAIkN,GAAGhN,GAAGnQ,EAAEod,0CAA0C,WAAW,OAAOjN,GAAGnQ,EAAEod,0CAA0Cpd,EAAE4Y,IAAIyE,IAAI1G,MAAM,KAAK1G,UAAU,EAAEf,GAAGlP,EAAEsd,sCAAsC,WAAW,OAAOpO,GAAGlP,EAAEsd,sCAAsCtd,EAAE4Y,IAAI2E,IAAI5G,MAAM,KAAK1G,UAAU,EAAE7F,GAAGpK,EAAEwd,8BAA8B,WAAW,OAAOpT,GAAGpK,EAAEwd,8BAA8Bxd,EAAE4Y,IAAI6E,IAAI9G,MAAM,KAAK1G,UAAU,EAAE7D,GAAGpM,EAAE0d,yBAAyB,WAAW,OAAOtR,GAAGpM,EAAE0d,yBAAyB1d,EAAE4Y,IAAI+E,IAAIhH,MAAM,KAAK1G,UAAU,EAAEwD,GAAGzT,EAAE4d,UAAU,WAAW,OAAOnK,GAAGzT,EAAE4d,UAAU5d,EAAE4Y,IAAIiF,IAAIlH,MAAM,KAAK1G,UAAU,EAAEjE,GAAGhM,EAAE8d,6BAA6B,WAAW,OAAO9R,GAAGhM,EAAE8d,6BAA6B9d,EAAE4Y,IAAImF,IAAIpH,MAAM,KAAK1G,UAAU,EAAErE,GAAG5L,EAAEge,UAAU,WAAW,OAAOpS,GAAG5L,EAAEge,UAAUhe,EAAE4Y,IAAIqF,IAAItH,MAAM,KAAK1G,UAAU,EAAEpE,GAAG7L,EAAEke,aAAa,WAAW,OAAOrS,GAAG7L,EAAEke,aAAale,EAAE4Y,IAAIuF,IAAIxH,MAAM,KAAK1G,UAAU,EAAEC,GAAGlQ,EAAEoe,WAAW,WAAW,OAAOlO,GAAGlQ,EAAEoe,WAAWpe,EAAE4Y,IAAIyF,IAAI1H,MAAM,KAAK1G,UAAU,EAAE0D,GAAG3T,EAAEse,iBAAiB,WAAW,OAAO3K,GAAG3T,EAAEse,iBAAiBte,EAAE4Y,IAAI2F,IAAI5H,MAAM,KAAK1G,UAAU,EAAEnC,GAAG9N,EAAEwe,uBAAuB,WAAW,OAAO1Q,GAAG9N,EAAEwe,uBAAuBxe,EAAE4Y,IAAI6F,IAAI9H,MAAM,KAAK1G,UAAU,EAAE8H,GAAG/X,EAAE0e,UAAU,WAAW,OAAO3G,GAAG/X,EAAE0e,UAAU1e,EAAE4Y,IAAI+F,IAAIhI,MAAM,KAAK1G,UAAU,EAAE0H,GAAG3X,EAAE4e,eAAe,WAAW,OAAOjH,GAAG3X,EAAE4e,eAAe5e,EAAE4Y,IAAIiG,IAAIlI,MAAM,KAAK1G,UAAU,EAAEiI,GAAGlY,EAAE8e,YAAY,WAAW,OAAO5G,GAAGlY,EAAE8e,YAAY9e,EAAE4Y,IAAImG,IAAIpI,MAAM,KAAK1G,UAAU,EAAEqI,GAAGtY,EAAEgf,gBAAgB,WAAW,OAAO1G,GAAGtY,EAAEgf,gBAAgBhf,EAAE4Y,IAAIqG,IAAItI,MAAM,KAAK1G,UAAU,EAAEyI,GAAG1Y,EAAEkf,aAAa,WAAW,OAAOxG,GAAG1Y,EAAEkf,aAAalf,EAAE4Y,IAAIuG,IAAIxI,MAAM,KAAK1G,UAAU,EAAEuI,GAAGxY,EAAEof,kBAAkB,WAAW,OAAO5G,GAAGxY,EAAEof,kBAAkBpf,EAAE4Y,IAAIyG,IAAI1I,MAAM,KAAK1G,UAAU,EAAE6H,GAAG9X,EAAEsf,YAAY,WAAW,OAAOxH,GAAG9X,EAAEsf,YAAYtf,EAAE4Y,IAAI2G,IAAI5I,MAAM,KAAK1G,UAAU,EAAEgI,GAAGjY,EAAEwf,WAAW,WAAW,OAAOvH,GAAGjY,EAAEwf,WAAWxf,EAAE4Y,IAAI6G,IAAI9I,MAAM,KAAK1G,UAAU,EAAEyH,GAAG1X,EAAE0f,gBAAgB,WAAW,OAAOhI,GAAG1X,EAAE0f,gBAAgB1f,EAAE4Y,IAAI+G,IAAIhJ,MAAM,KAAK1G,UAAU,EAAE2H,GAAG5X,EAAE4f,aAAa,WAAW,OAAOhI,GAAG5X,EAAE4f,aAAa5f,EAAE4Y,IAAIiH,IAAIlJ,MAAM,KAAK1G,UAAU,EAAE,SAAS6P,KAAK,SAAS3gB,IAAI,IAAIge,KAAKA,IAAG,EAAGnd,EAAE+f,WAAU,GAAIrb,KAAKrD,GAAG8H,GAAG7C,GAAGrG,EAAED,GAAGA,EAAEggB,sBAAsBhgB,EAAEggB,wBAAwB3e,GAAG,CAAC,GAAGrB,EAAEigB,QAAQ,IAAI,mBAAmBjgB,EAAEigB,UAAUjgB,EAAEigB,QAAQ,CAACjgB,EAAEigB,UAAUjgB,EAAEigB,QAAQzqB,QAAQ,CAAC,IAAI2J,EAAEa,EAAEigB,QAAQtZ,QAAQH,EAAEI,QAAQzH,EAAE,CAACgK,GAAG3C,EAAE,CAAC,CAAC,KAAK,EAAEM,IAAI,GAAGzF,EAAEpB,EAAED,GAAGqB,GAAG8H,GAAG7C,GAAGW,YAAY,CAACC,IAAI,eAAe,CAAC,GAAGlH,EAAE0G,OAAO,IAAI,mBAAmB1G,EAAE0G,SAAS1G,EAAE0G,OAAO,CAAC1G,EAAE0G,SAAS1G,EAAE0G,OAAOlR,QAAQiR,IAAI0C,GAAG9C,GAAG,EAAES,KAAK9G,EAAEkgB,WAAWlgB,EAAEkgB,UAAU,cAAclL,YAAW,WAAYA,YAAW,WAAYhV,EAAEkgB,UAAU,GAAI,GAAE,GAAG/gB,GAAI,GAAE,IAAIA,IAAI,CAAC,CAAC,GAAGa,EAAEmgB,aAAalb,EAAEjF,EAAEogB,aAAa,SAASjhB,EAAEE,EAAEM,GAAG,OAAOuF,EAAE/F,EAAEP,IAAIS,EAAEM,EAAE,EAAEK,EAAEqgB,gBAAgBjb,EAAEpF,EAAEsgB,iBAAiBne,EAAEnC,EAAE+F,WAAWzG,EAAEU,EAAEge,UAAUpS,GAAG5L,EAAEke,aAAarS,GAAG7L,EAAEoe,WAAWlO,GAAGlQ,EAAEugB,WAAWre,GAAGlC,EAAE8L,QAAQlE,GAAGZ,GAAG,SAAS7H,IAAIge,IAAI2C,KAAK3C,KAAKnW,GAAG7H,EAAE,EAAEa,EAAEwgB,QAAQ,IAAI,mBAAmBxgB,EAAEwgB,UAAUxgB,EAAEwgB,QAAQ,CAACxgB,EAAEwgB,UAAU,EAAExgB,EAAEwgB,QAAQhrB,QAAQwK,EAAEwgB,QAAQ9U,KAAV1L,GAAkB,OAAO8f,KAAK3gB,EAAEgB,KAAK,GAAqD9L,EAAOD,QAAQ+K,C,oBCE5q/BD,WADFuhB,GAEqCvhB,YADnCA,WAAiC,oBAAbxE,UAA4BA,SAAS0E,cAAgB1E,SAAS0E,cAAcxD,SAAMzG,I,YAEnG,SACAsrB,GAIT,IAAI/f,EAA2D+W,EAAGO,EAHhEyI,EAAUA,GAAW,CAAC,EAGlB/f,IAAIA,OAAqB,IAAZ+f,EAA0BA,EAAU,CAAC,GAAa/f,EAAEP,MAAM,IAAI9E,SAAQ,SAASyE,EAAEe,GAAG4W,EAAG3X,EAAEkY,EAAGnX,CAAC,IAAG,IAA6OsX,EAAGxY,EAAExC,EAAEujB,EAAG9f,EAAE2W,EAArPM,EAAG5a,OAAO0D,OAAO,CAAC,EAAED,GAAG6X,EAAG,iBAAiBE,EAAG,CAAC3Y,EAAEe,KAAK,MAAMA,CAAC,EAAGwX,EAAG,iBAAiBtX,OAAON,EAAE,mBAAmBQ,cAAcV,EAAE,iBAAiBW,SAAS,iBAAiBA,QAAQC,UAAU,iBAAiBD,QAAQC,SAASC,KAAK6D,EAAE,GAC1V1E,GAAE0E,EAAExE,EAAE,eAAwBwE,GAAG,IAAIvD,KAAc6V,EAAG,KAAK3W,IAAI8f,EAAG,EAAQ,KAAM9f,EAAE,EAAQ,KAAO,EAAGuX,EAAG,SAASrY,EAAEe,GAAyB,OAAtB0W,IAAKzX,EAAEc,EAAEe,UAAU7B,GAAU4gB,EAAG9e,aAAa9B,EAAEe,OAAE,EAAO,OAAO,EAAE1D,EAAE2C,KAAIA,EAAEqY,EAAGrY,GAAE,IAAMvH,SAASuH,EAAE,IAAIpJ,WAAWoJ,IAAWA,GAAGH,EAAE,CAACG,EAAEe,EAAEX,KAAKqX,IAAKzX,EAAEc,EAAEe,UAAU7B,GAAG4gB,EAAG7e,SAAS/B,GAAE,SAASlB,EAAEwB,GAAGxB,EAAEsB,EAAEtB,GAAGiC,EAAET,EAAE7H,OAAO,GAAC,EAAG,EAAE2I,QAAQY,KAAKtM,SAAS+iB,EAAGrX,QAAQY,KAAK,GAAGC,QAAQ,MAAM,MAAMb,QAAQY,KAAKE,MAAM,GAAGd,QAAQe,GAAG,qBAAoB,SAASnC,GAAG,KAAKA,aAAa0X,GAAI,MAAM1X,CAAE,IAAGoB,QAAQe,GAAG,sBACpf,SAASnC,GAAG,MAAMA,CAAE,IAAG2Y,EAAG,CAAC3Y,EAAEe,KAAK,GAAGyD,GAAe,EAAE8T,EAAG,MAAMlX,QAAQkB,SAAStC,EAAEe,EAAEA,aAAa2W,GAAIhY,EAAE,6BAA6BqB,GAAGK,QAAQoB,KAAKxC,EAAC,EAAGY,EAAE6B,QAAQ,WAAW,MAAM,4BAA4B,IAAU8V,GAAI5X,KAAEA,EAAEwE,EAAEzQ,KAAKmO,SAASC,KAAK,oBAAoBlI,UAAUA,SAAS0E,gBAAgB6F,EAAEvK,SAAS0E,cAAcxD,KAAKsD,aAAa+F,EAAE/F,YAAmC+F,EAAvB,IAAIA,EAAE3P,QAAQ,SAAW2P,EAAEpC,OAAO,EAAEoC,EAAElD,QAAQ,SAAS,IAAIe,YAAY,KAAK,GAAK,GAAGqV,EAAGrY,IAAI,IAAIe,EAAE,IAAIkC,eAC3c,OAD0dlC,EAAEmC,KAAK,MAAMlD,GAAE,GAAIe,EAAEoC,KAAK,MAC7epC,EAAEqC,cAAczC,IAAItD,EAAE2C,IAAI,IAAIe,EAAE,IAAIkC,eAA4E,OAA7DlC,EAAEmC,KAAK,MAAMlD,GAAE,GAAIe,EAAEsC,aAAa,cAActC,EAAEoC,KAAK,MAAa,IAAIvM,WAAWmK,EAAEuC,SAAQ,GAAIzD,EAAE,CAACG,EAAEe,EAAEX,KAAK,IAAItB,EAAE,IAAImE,eAAenE,EAAEoE,KAAK,MAAMlD,GAAE,GAAIlB,EAAEuE,aAAa,cAAcvE,EAAE/C,OAAO,KAAK,KAAK+C,EAAEyE,QAAQ,GAAGzE,EAAEyE,QAAQzE,EAAEwE,SAASvC,EAAEjC,EAAEwE,UAAUlD,GAAE,EAAGtB,EAAE0E,QAAQpD,EAAEtB,EAAEqE,KAAK,KAAI,GAAG,IAC9UU,EADkVgV,EAAGjY,EAAEsD,OAAOxB,QAAQiB,IAAIC,KAAKlB,SAAShD,EAAEkB,EAAEuD,UAAUzB,QAAQoB,KAAKF,KAAKlB,SAASvF,OAAO0D,OAAOD,EAAEmX,GAAIA,EAAG,KAAKnX,EAAEwD,cAAcqU,EAAG7X,EAAEwD,aAAaxD,EAAEyD,OAAOsU,EAAG/X,EAAEyD,MAC3ezD,EAAE0D,aAAaT,EAAEjD,EAAE0D,YAAY,IAAIE,EAAc5D,EAAE4D,gBAAe,EAAG,iBAAiBC,aAAaW,EAAE,mCAAmC,IAAIsO,EAGLgB,EAAG3P,EAAEO,EAAErF,EAAEoC,EAHD5C,GAAE,EAAG2X,EAAG,oBAAoBtS,YAAY,IAAIA,YAAY,aAAQ,EACrN,SAASuP,EAAGrU,EAAEe,EAAEX,GAAU,IAAItB,GAAXiC,KAAK,GAAUX,EAAE,IAAIA,EAAEW,EAAEf,EAAEI,MAAMA,GAAGtB,MAAMsB,EAAE,GAAG,GAAGA,EAAEW,GAAGf,EAAEvH,QAAQ2e,EAAG,OAAOA,EAAGpS,OAAOhF,EAAEiF,SAASlE,EAAEX,IAAI,IAAItB,EAAE,GAAGiC,EAAEX,GAAG,CAAC,IAAIE,EAAEN,EAAEe,KAAK,GAAK,IAAFT,EAAM,CAAC,IAAII,EAAS,GAAPV,EAAEe,KAAQ,GAAG,MAAQ,IAAFT,GAAOxB,GAAGrE,OAAOyK,cAAgB,GAAF5E,IAAO,EAAEI,OAAO,CAAC,IAAIiE,EAAS,GAAP3E,EAAEe,KAAwE,OAAhET,EAAE,MAAQ,IAAFA,IAAU,GAAFA,IAAO,GAAGI,GAAG,EAAEiE,GAAK,EAAFrE,IAAM,GAAGI,GAAG,GAAGiE,GAAG,EAAS,GAAP3E,EAAEe,MAAgBjC,GAAGrE,OAAOyK,aAAa5E,IAAIA,GAAG,MAAMxB,GAAGrE,OAAOyK,aAAa,MAAM5E,GAAG,GAAG,MAAQ,KAAFA,GAAQ,CAAC,MAAMxB,GAAGrE,OAAOyK,aAAa5E,EAAE,CAAC,OAAOxB,CAAC,CAAC,SAASgV,EAAG9T,EAAEe,GAAG,OAAOf,KAAK,GAAGqU,EAAG/O,EAAEtF,EAAEe,GAAG,EAAE,CAC3e,SAAS+V,EAAG9W,EAAEe,EAAEX,EAAEtB,GAAU,KAAK,EAAEA,GAAG,OAAO,EAAE,IAAIwB,EAA9BF,KAAK,EAA6BtB,EAAEsB,EAAEtB,EAAE,EAAE,IAAI,IAAI4B,EAAE,EAAEA,EAAEV,EAAEtK,SAASgL,EAAE,CAAC,IAAIiE,EAAE3E,EAAEqF,WAAW3E,GAAgF,GAA1E,OAAOiE,GAAG,OAAOA,IAA2BA,EAAE,QAAU,KAAFA,IAAS,IAAM,KAA3C3E,EAAEqF,aAAa3E,IAAoC,KAAKiE,EAAE,CAAC,GAAGvE,GAAGtB,EAAE,MAAMiC,EAAEX,MAAM,GAAGuE,CAAC,KAAK,CAAC,GAAG,MAAMA,EAAE,CAAC,GAAGvE,EAAE,GAAGtB,EAAE,MAAMiC,EAAEX,MAAM,GAAG,IAAIuE,GAAG,CAAC,KAAK,CAAC,GAAG,OAAOA,EAAE,CAAC,GAAGvE,EAAE,GAAGtB,EAAE,MAAMiC,EAAEX,MAAM,GAAG,IAAIuE,GAAG,EAAE,KAAK,CAAC,GAAGvE,EAAE,GAAGtB,EAAE,MAAMiC,EAAEX,MAAM,GAAG,IAAIuE,GAAG,GAAG5D,EAAEX,MAAM,GAAG,IAAIuE,GAAG,GAAG,EAAE,CAAC5D,EAAEX,MAAM,GAAG,IAAIuE,GAAG,EAAE,EAAE,CAAC5D,EAAEX,MAAM,GAAG,IAAM,GAAFuE,CAAI,CAAC,CAAY,OAAX5D,EAAEX,IAAI,GAAG,EAASA,EAAEE,CAAC,CACnd,SAASkW,EAAGxW,GAAG,IAAI,IAAIe,EAAE,EAAEX,EAAE,EAAEA,EAAEJ,EAAEtK,SAAS0K,EAAE,CAAC,IAAItB,EAAEkB,EAAEqF,WAAWjF,GAAG,KAAKtB,EAAEiC,IAAI,MAAMjC,EAAEiC,GAAG,EAAE,OAAOjC,GAAG,OAAOA,GAAGiC,GAAG,IAAIX,GAAGW,GAAG,CAAC,CAAC,OAAOA,CAAC,CAAgB,SAAS6T,IAAK,IAAI5U,EAAE0T,EAAGjb,OAAOic,EAAG1U,EAAEY,EAAE2E,MAAMR,EAAE,IAAIlO,UAAUmJ,GAAGY,EAAE4E,OAAO,IAAIzO,WAAWiJ,GAAGY,EAAE6E,OAAOxF,EAAE,IAAIjJ,WAAWgJ,GAAGY,EAAE8E,OAAOJ,EAAE,IAAI1O,WAAWoJ,GAAGY,EAAE+E,QAAQ,IAAI7O,YAAYkJ,GAAGY,EAAEgF,QAAQvD,EAAE,IAAInL,YAAY8I,GAAGY,EAAEiF,QAAQ,IAAIlP,aAAaqJ,GAAGY,EAAEkF,QAAQ,IAAI7O,aAAa+I,EAAE,CAAC,IAAImU,EAAGM,EAAG,GAAGL,EAAG,GAAGH,EAAG,GAAGc,EAAG,GAAGuD,EAAG,EACrc,SAASrD,IAAK,IAAIjV,EAAEY,EAAEgG,OAAOC,QAAQ4N,EAAG3N,QAAQ9G,EAAE,CAAC,IAAuQ+F,EAAnQgB,EAAE,EAAEqP,EAAG,KAAK7P,EAAE,KAAK,SAASnB,EAAEpF,GAA6I,MAAvIY,EAAE0G,SAAQ1G,EAAE0G,QAAQtH,GAAsBN,EAAnBM,EAAE,WAAWA,EAAE,KAASP,GAAE,EAAGO,EAAE,IAAIyE,YAAY8C,aAAavH,EAAE,4CAA4CkY,EAAGlY,GAASA,CAAE,CAAC,SAASqW,IAAK,OAAOtQ,EAAE0B,WAAW,wCAAwC,CAAyB,GAAlB1B,EAAE,iBAAoBsQ,IAAK,CAAC,IAAI9B,EAAGxO,EAAEA,EAAEnF,EAAEe,WAAWf,EAAEe,WAAW4S,EAAGpP,GAAGA,EAAEoP,CAAE,CACvY,SAASqC,IAAK,IAAI5W,EAAE+F,EAAE,IAAI,GAAG/F,GAAG+F,GAAGlC,EAAE,OAAO,IAAIjN,WAAWiN,GAAG,GAAGxG,EAAE,OAAOA,EAAE2C,GAAG,KAAK,iDAAgE,CAAb,MAAMe,GAAGqE,EAAErE,EAAE,CAAC,CACuP,SAAS2W,EAAG1X,GAAGlK,KAAKhB,KAAK,aAAagB,KAAK8R,QAAQ,gCAAgC5H,EAAE,IAAIlK,KAAKyN,OAAOvD,CAAC,CAClf,SAAS0B,EAAE1B,GAAG,KAAK,EAAEA,EAAEtK,QAAQsK,EAAE6G,OAAF7G,CAAUY,EAAE,CAAC,IAAIiE,EAAE,GAAG8B,EAAE,EAAEpC,GAAE,EAC3D,SAASb,GAAE1D,GAAGlK,KAAKqoB,GAAGne,EAAElK,KAAK6nB,GAAG3d,EAAE,GAAGlK,KAAKsS,GAAG,SAASrH,GAAGsB,EAAEvM,KAAK6nB,GAAG,GAAG,IAAI,GAAG5c,CAAC,EAAEjL,KAAKuoB,GAAG,WAAW,OAAOhc,EAAEvM,KAAK6nB,GAAG,GAAG,IAAI,EAAE,EAAE7nB,KAAKgX,GAAG,SAAS/L,GAAGsB,EAAEvM,KAAK6nB,GAAG,GAAG,IAAI,GAAG5c,CAAC,EAAEjL,KAAKqc,GAAG,WAAW,OAAO9P,EAAEvM,KAAK6nB,GAAG,GAAG,IAAI,EAAE,EAAE7nB,KAAKgd,GAAG,WAAW7S,EAAEnK,KAAK6nB,IAAI,IAAI,GAAG,CAAC,EAAE7nB,KAAK+oB,GAAG,SAAS9d,GAAGgE,EAAEjP,KAAK6nB,GAAG,IAAI,IAAI,GAAG5c,EAAE,EAAE,CAAC,EAAEjL,KAAK6pB,GAAG,WAAW,OAAO,GAAG5a,EAAEjP,KAAK6nB,GAAG,IAAI,IAAI,EAAE,EAAE7nB,KAAKipB,GAAG,SAAShe,GAAGgE,EAAEjP,KAAK6nB,GAAG,IAAI,IAAI,GAAG5c,EAAE,EAAE,CAAC,EAAEjL,KAAKqpB,GAAG,WAAW,OAAO,GAAGpa,EAAEjP,KAAK6nB,GAAG,IAAI,IAAI,EAAE,EAAE7nB,KAAKiqB,GAAG,SAAShf,EAAEX,GAAGtK,KAAKyoB,GAAG,GAAGzoB,KAAKsS,GAAGrH,GAAGjL,KAAKgX,GAAG1M,GAC3ftK,KAAKgd,KAAKhd,KAAK+oB,IAAG,GAAI/oB,KAAKipB,IAAG,EAAG,EAAEjpB,KAAKypB,GAAG,WAAWtf,EAAEnK,KAAK6nB,IAAI,IAAI,IAAI,CAAC,EAAE7nB,KAAKsc,GAAG,WAAW,IAAIrR,EAAEd,EAAEnK,KAAK6nB,IAAI,IAAI,GAAyB,OAAtB1d,EAAEnK,KAAK6nB,IAAI,IAAI,GAAG5c,EAAE,EAAS,IAAIA,CAAC,EAAEjL,KAAKyoB,GAAG,SAASxd,GAAGsB,EAAEvM,KAAK6nB,GAAG,IAAI,IAAI,GAAG5c,CAAC,EAAEjL,KAAK2pB,GAAG,WAAW,OAAOpd,EAAEvM,KAAK6nB,GAAG,IAAI,IAAI,EAAE,EAAE7nB,KAAK+pB,GAAG,WAAW,GAAG9L,GAAGje,KAAKuoB,MAAM,OAAOhc,EAAEvM,KAAKqoB,IAAI,IAAI,GAAG,IAAIpd,EAAEjL,KAAK2pB,KAAK,OAAO,IAAI1e,EAAEA,EAAEjL,KAAKqoB,EAAE,CAAC,CAAC,SAASlH,GAAGjX,GAAG,OAAOkX,GAAG,IAAKxT,GAAE1D,GAAI2d,GAAG,CAAC,IAAIlc,GAAE,GAAG,SAAS3B,GAAEE,GAAG,IAAIe,EAAEU,GAAEzB,GAAqD,OAAlDe,IAAIf,GAAGyB,GAAE/L,SAAS+L,GAAE/L,OAAOsK,EAAE,GAAGyB,GAAEzB,GAAGe,EAAEoT,EAAGpc,IAAIiI,IAAWe,CAAC,CAChe,SAAS0V,GAAGzW,GAAG,IAAIe,EAAEyV,EAAGxW,GAAG,EAAEI,EAAEoU,GAAGzT,GAAkB,OAAfX,GAAG0W,EAAG9W,EAAE+E,EAAE3E,EAAEW,GAAUX,CAAC,CAAia,IAAI8T,GAAG,CAAC,EACre,SAAS8C,KAAK,IAAIhC,GAAG,CAAC,IAAuNjU,EAAnNf,EAAE,CAAC6Q,KAAK,WAAWC,QAAQ,WAAWC,KAAK,IAAIC,IAAI,IAAIC,KAAK,iBAAiBC,MAAM,iBAAiBC,WAAWA,UAAUC,WAAWD,UAAUC,UAAU,IAAI,KAAKnP,QAAQ,IAAI,KAAK,SAASf,EAAEuX,GAAI,kBAAoB,IAAI1X,KAAKmT,QAAG,IAASA,GAAGnT,UAAUf,EAAEe,GAAGf,EAAEe,GAAGmT,GAAGnT,GAAG,IAAIX,EAAE,GAAG,IAAIW,KAAKf,EAAEI,EAAEzK,KAAKoL,EAAE,IAAIf,EAAEe,IAAIiU,GAAG5U,CAAC,CAAC,OAAO4U,EAAE,CAAC,IAAIA,GAAGI,GAAG,CAAC,KAAK,GAAG,IAAI,SAASS,GAAG7V,EAAEe,GAAG,IAAIX,EAAEgV,GAAGpV,GAAG,IAAIe,GAAG,KAAKA,IAAI,IAAIf,EAAE6Y,EAAGnZ,GAAG2U,EAAGjU,EAAE,IAAIA,EAAE1K,OAAO,GAAG0K,EAAEzK,KAAKoL,EAAE,CAAC,IAAIuF,GAAE,EACpH,SAASgQ,GAAGtW,GAAG,OAAO,GAAIA,EAAE,IAAI,GAAIA,EAAE,KAAK,GAAIA,EAAE,IAAI,CAAC,IAAI6U,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAIC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAEhe,SAASkF,GAAGha,EAAEe,EAAEX,EAAEtB,GAAG,SAASwB,EAAEU,EAAEd,EAAEqB,GAAG,IAAIP,EAAE,iBAAiBA,EAAEA,EAAE+Q,WAAW/Q,GAAG,GAAGA,EAAEtL,OAAOwK,GAAGc,EAAEO,EAAE,GAAGP,EAAE,OAAOA,CAAC,CAAC,SAASN,EAAEM,EAAEd,GAAG,OAAOI,EAAEU,EAAEd,EAAE,IAAI,CAAC,SAASyE,EAAE3D,EAAEd,GAAG,SAASqB,EAAEyC,GAAG,OAAO,EAAEA,GAAG,EAAE,EAAEA,EAAE,EAAE,CAAC,CAAC,IAAIpE,EAAmH,OAAjH,KAAKA,EAAE2B,EAAEP,EAAE+O,cAAc7P,EAAE6P,iBAAiB,KAAKnQ,EAAE2B,EAAEP,EAAEgR,WAAW9R,EAAE8R,eAAepS,EAAE2B,EAAEP,EAAEiR,UAAU/R,EAAE+R,YAAmBrS,CAAC,CAAC,SAASY,EAAEQ,GAAG,OAAOA,EAAEkR,UAAU,KAAK,EAAE,OAAO,IAAIpC,KAAK9O,EAAE+O,cAAc,EAAE,GAAG,IAAI,KAAK,EAAE,OAAO/O,EAAE,KAAK,EAAE,OAAO,IAAI8O,KAAK9O,EAAE+O,cAAc,EAAE,GAAG,KAAK,EAAE,OAAO,IAAID,KAAK9O,EAAE+O,cAC7e,EAAE,GAAG,KAAK,EAAE,OAAO,IAAID,KAAK9O,EAAE+O,cAAc,EAAE,GAAG,KAAK,EAAE,OAAO,IAAID,KAAK9O,EAAE+O,cAAc,EAAE,GAAG,IAAI,KAAK,EAAE,OAAO,IAAID,KAAK9O,EAAE+O,cAAc,EAAE,GAAG,IAAI,CAAC,SAASxQ,EAAEyB,GAAG,IAAId,EAAEc,EAAE+c,GAAG,IAAI/c,EAAE,IAAI8O,KAAK,IAAKA,KAAK9O,EAAEid,GAAG,KAAK,EAAE,GAAI5L,WAAW,EAAEnS,GAAG,CAAC,IAAIqB,EAAEP,EAAEgR,WAAWpS,GAAG0W,GAAGtV,EAAE+O,eAAe8E,GAAGC,IAAIvT,GAAG,KAAGrB,EAAEN,EAAEoB,EAAEiR,WAAoH,CAACjR,EAAEsR,QAAQtR,EAAEiR,UAAU/R,GAAG,KAAK,CAAzIA,GAAGN,EAAEoB,EAAEiR,UAAU,EAAEjR,EAAEsR,QAAQ,GAAG,GAAG/Q,EAAEP,EAAEuR,SAAShR,EAAE,IAAIP,EAAEuR,SAAS,GAAGvR,EAAEwR,YAAYxR,EAAE+O,cAAc,GAAwC,CACza,OAD0axO,EAAE,IAAIuO,KAAK9O,EAAE+O,cAAc,EAAE,EAAE,GAAG7P,EAAEM,EAAE,IAAIsP,KAAK9O,EAAE+O,cACxe,EAAE,IAAIxO,EAAEf,EAAEe,GAAU,GAAGoD,EAAEzE,EAAEc,GAAG,GAAG2D,EAAEpD,EAAEP,GAAGA,EAAE+O,cAAc,EAAE/O,EAAE+O,cAAc/O,EAAE+O,cAAc,CAAC,CAAC,IAAI1Q,EAAEY,EAAEnB,EAAE,IAAI,IAAI,GACyE,IAAI,IAAIyD,KAD9EzD,EAAE,CAAC2J,GAAGxI,EAAEnB,GAAG,IAAI,GAAG+N,GAAG5M,EAAEnB,EAAE,GAAG,IAAI,GAAG2f,GAAGxe,EAAEnB,EAAE,GAAG,IAAI,GAAGmgB,GAAGhf,EAAEnB,EAAE,IAAI,IAAI,GAAG6f,GAAG1e,EAAEnB,EAAE,IAAI,IAAI,GAAGmf,GAAGhe,EAAEnB,EAAE,IAAI,IAAI,GAAG+e,GAAG5d,EAAEnB,EAAE,IAAI,IAAI,GAAGif,GAAG9d,EAAEnB,EAAE,IAAI,IAAI,GAAGkO,GAAG/M,EAAEnB,EAAE,IAAI,IAAI,GAAG2K,GAAGxJ,EAAEnB,EAAE,IAAI,IAAI,GAAGqJ,GAAG9I,EAAEyU,EAAGzU,GAAG,IAAIe,EAAE0T,EAAG1T,GAAGf,EAAE,CAAC,KAAK,uBAAuB,KAAK,WAAW,KAAK,WAAW,KAAK,KAAK,KAAK,cAAc,KAAK,QAAQ,KAAK,WAAW,KAAK,WAAW,KAAK,WAAW,MAAM,KAAK,MAAM,KAAK,MAAM,WAC/e,MAAM,WAAW,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,MAAqBe,EAAEA,EAAE6B,QAAQ,IAAIiR,OAAO3Q,EAAE,KAAKlD,EAAEkD,IAAI,IAAI/C,EAAE,2DAA2D2T,MAAM,KAAKlP,EAAE,wFAAwFkP,MAAM,KAG1F,IAAI5Q,KAH2FlD,EAAE,CAAC,KAAK,SAAS2B,GAAG,OAAOxB,EAAEwB,EAAE6c,IAAIzK,UAAU,EAAE,EAAE,EAAE,KAAK,SAASpS,GAAG,OAAOxB,EAAEwB,EAAE6c,GAAG,EAAE,KAAK,SAAS7c,GAAG,OAAOiD,EAAEjD,EAAE2d,IAAIvL,UAAU,EACzhB,EAAE,EAAE,KAAK,SAASpS,GAAG,OAAOiD,EAAEjD,EAAE2d,GAAG,EAAE,KAAK,SAAS3d,GAAG,OAAON,GAAGM,EAAEid,GAAG,MAAM,IAAI,EAAE,EAAE,EAAE,KAAK,SAASjd,GAAG,OAAON,EAAEM,EAAEie,GAAG,EAAE,EAAE,KAAK,SAASje,GAAG,OAAOV,EAAEU,EAAEie,GAAG,EAAE,IAAI,EAAE,KAAK,SAASje,GAAG,OAAOzB,EAAEyB,GAAG+Q,WAAWqB,UAAU,EAAE,EAAE,KAAK,SAASpS,GAAG,OAAOzB,EAAEyB,EAAE,EAAE,KAAK,SAASA,GAAG,OAAON,EAAEM,EAAEyd,GAAG,EAAE,EAAE,KAAK,SAASzd,GAAkC,OAAxB,IAAPA,EAAEA,EAAEyd,IAAQzd,EAAE,GAAG,GAAGA,IAAIA,GAAG,IAAWN,EAAEM,EAAE,EAAE,EAAE,KAAK,SAASA,GAAG,IAAI,IAAId,EAAE,EAAEqB,EAAE,EAAEA,GAAGP,EAAE2d,GAAG,EAAEze,IAAIoW,GAAGtV,EAAEid,GAAG,MAAMpJ,GAAGC,IAAIvT,MAAM,OAAOb,EAAEM,EAAEie,GAAG/e,EAAE,EAAE,EAAE,KAAK,SAASc,GAAG,OAAON,EAAEM,EAAE2d,GAAG,EAAE,EAAE,EAAE,KAAK,SAAS3d,GAAG,OAAON,EAAEM,EAAE6L,GACpf,EAAE,EAAE,KAAK,WAAW,MAAM,IAAI,EAAE,KAAK,SAAS7L,GAAG,OAAO,GAAGA,EAAEyd,IAAI,GAAGzd,EAAEyd,GAAG,KAAK,IAAI,EAAE,KAAK,SAASzd,GAAG,OAAON,EAAEM,EAAEyH,GAAG,EAAE,EAAE,KAAK,WAAW,MAAM,IAAI,EAAE,KAAK,SAASzH,GAAG,OAAOA,EAAE6c,IAAI,CAAC,EAAE,KAAK,SAAS7c,GAAG,OAAON,EAAEuP,KAAKoD,OAAOrS,EAAE+c,GAAG,EAAE/c,EAAE6c,IAAI,GAAG,EAAE,EAAE,KAAK,SAAS7c,GAAG,IAAId,EAAE+P,KAAKoD,OAAOrS,EAAE+c,GAAG,GAAG/c,EAAE6c,GAAG,GAAG,GAAG,GAA+B,GAA5B,IAAI7c,EAAE6c,GAAG,IAAI7c,EAAE+c,GAAG,GAAG,GAAG7d,IAAOA,EAAE,IAAIA,IAAwB,IAApBqB,GAAGP,EAAE6c,GAAG,IAAI7c,EAAE+c,IAAI,IAAQ,GAAGxc,GAAG+U,GAAGtV,EAAEid,MAAM/d,EAAE,QAAQ,CAACA,EAAE,GAAG,IAAIqB,GAAGP,EAAE6c,GAAG,EAAE7c,EAAE+c,GAAG,GAAG,GAAG,GAAGxc,GAAG,GAAGA,GAAG+U,GAAGtV,EAAEid,GAAG,IAAI,KAAK/d,GAAG,CAAC,OAAOQ,EAAER,EAAE,EAAE,EAAE,KAAK,SAASc,GAAG,OAAOA,EAAE6c,EAAE,EAAE,KAAK,SAAS7c,GAAG,OAAON,EAAEuP,KAAKoD,OAAOrS,EAAE+c,GAC1hB,GAAG/c,EAAE6c,GAAG,GAAG,GAAG,GAAG,EAAE,EAAE,KAAK,SAAS7c,GAAG,OAAOA,EAAEid,GAAG,MAAMlM,WAAWqB,UAAU,EAAE,EAAE,KAAK,SAASpS,GAAG,OAAOA,EAAEid,GAAG,IAAI,EAAE,KAAK,SAASjd,GAAU,IAAId,EAAE,IAAbc,EAAEA,EAAEyI,IAA+B,OAAjBzI,EAAEiP,KAAKqD,IAAItS,GAAG,IAAUd,EAAE,IAAI,KAAKzF,OAAO,QAAQuG,EAAE,GAAG,IAAIA,EAAE,KAAKkB,OAAO,EAAE,EAAE,KAAK,SAASlB,GAAG,OAAOA,EAAEmH,EAAE,EAAE,KAAK,WAAW,MAAM,GAAG,GAAG/H,EAAEA,EAAE6B,QAAQ,MAAM,QAAqB5C,EAAEe,EAAEmT,SAAShR,KAAKnC,EAAEA,EAAE6B,QAAQ,IAAIiR,OAAO3Q,EAAE,KAAKlD,EAAEkD,GAAGzD,KAAsC,OAARyD,EAPxZ,SAAYvC,GAAG,IAAIe,EAAEnJ,MAAM4e,EAAGxW,GAAG,GAAsB,OAAnB8W,EAAG9W,EAAEe,EAAE,EAAEA,EAAErL,QAAeqL,CAAC,CAO2VkY,CAA3B7Y,EAAEA,EAAE6B,QAAQ,QAAQ,MAAgBM,EAAE7M,OAAOqL,EAAS,GAAEgE,EAAE3N,IAAImL,EAAEvC,IAAI,GAAUuC,EAAE7M,OAAO,EAAC,CACvd,IAAIqpB,GAAG,CAAC/e,EAAE,SAASA,GAAG,OAAOwU,GAAGxU,EAAE,IAAI,EAAE,EAAEW,EAAE,SAASX,GAA+D,OAA5DA,EAAE,IAAI0D,GAAE1D,IAAK2f,OAAO3f,EAAE6e,IAAG,GAAIlY,KAAK3G,EAAE+e,IAAG,GAAIla,EAAElP,KAAKqK,GAAGA,EAAEuf,KAAYvf,EAAE6f,IAAI,EAAEpI,GAAG,SAASzX,GAAoF,MAAjFN,EAAE,0EAA0ED,GAAE,EAASO,CAAE,EAAEuB,EAAE,WAAWiF,GAAE,GAAG,IAAIxG,EAAE6E,EAAE+G,MAAM,GAAG5L,EAAEoS,OAAOpS,EAAEmf,KAAK,CAAC,IAAIpe,EAAEf,EAAEmS,KAAKpR,GAAGjB,GAAEiB,EAAFjB,CAAKE,EAAEme,IAAIlH,GAAGjX,EAAEme,GAAG,CAAC5Z,GAAE,CAAC,EAAE3D,EAAE,WAAW,IAAIZ,EAAEuE,GAAE,IAAIvE,EAAE,OAAOsG,GAAE,EAAE,IAAIvF,EAAE,IAAI2C,GAAE1D,GAAGe,EAAEwd,GAAGve,GAAG,IAAII,EAAEW,EAAEsd,KAAK,IAAIje,EAAE,OAAOkG,GAAE,EAAEtG,EAAE,IAAI,IAAIlB,EAAElH,MAAMgc,UAAU1R,MAAMtE,KAAKuS,WAAW7P,EAAE,EAAEA,EAAExB,EAAEpJ,OAAO4K,IAAI,CAAC,IAAII,EAAE5B,EAAEwB,GACnf,GAAG,IAAII,GAAGA,IAAIN,EAAE,MAAM,GAAG8Z,GAAGxZ,EAAEN,EAAEW,EAAE4c,GAAG,IAAI,OAAOrX,GAAE5F,EAAEV,CAAC,CAAK,OAAJsG,GAAElG,EAASJ,CAAC,EAAE2E,EAAE,WAAW,IAAI3E,EAAEuE,GAAE,IAAIvE,EAAE,OAAOsG,GAAE,EAAE,IAAIvF,EAAE,IAAI2C,GAAE1D,GAAGe,EAAEwd,GAAGve,GAAG,IAAII,EAAEW,EAAEsd,KAAK,IAAIje,EAAE,OAAOkG,GAAE,EAAEtG,EAAE,IAAI,IAAIlB,EAAElH,MAAMgc,UAAU1R,MAAMtE,KAAKuS,WAAW7P,EAAE,EAAEA,EAAExB,EAAEpJ,OAAO4K,IAAI,CAAC,IAAII,EAAE5B,EAAEwB,GAAG,GAAG,IAAII,GAAGA,IAAIN,EAAE,MAAM,GAAG8Z,GAAGxZ,EAAEN,EAAEW,EAAE4c,GAAG,IAAI,OAAOrX,GAAE5F,EAAEV,CAAC,CAAK,OAAJsG,GAAElG,EAASJ,CAAC,EAAEgB,EAAE,WAAW,IAAIhB,EAAEuE,GAAE,IAAIvE,EAAE,OAAOsG,GAAE,EAAE,IAAIvF,EAAE,IAAI2C,GAAE1D,GAAGe,EAAEwd,GAAGve,GAAG,IAAII,EAAEW,EAAEsd,KAAK,IAAIje,EAAE,OAAOkG,GAAE,EAAEtG,EAAE,IAAI,IAAIlB,EAAElH,MAAMgc,UAAU1R,MAAMtE,KAAKuS,WAAW7P,EAAE,EAAEA,EAAExB,EAAEpJ,OAAO4K,IAAI,CAAC,IAAII,EAAE5B,EAAEwB,GAAG,GAAG,IAAII,GAAGA,IAAIN,EAAE,MAChf,GAAG8Z,GAAGxZ,EAAEN,EAAEW,EAAE4c,GAAG,IAAI,OAAOrX,GAAE5F,EAAEV,CAAC,CAAK,OAAJsG,GAAElG,EAASJ,CAAC,EAAEO,EAAE0W,GAAG1Q,EAAE,WAAW,IAAIvG,EAAE6E,EAAE+G,MAAM5L,GAAGoF,EAAE,yBAAyB,IAAIrE,EAAEf,EAAEme,GAAiD,MAA9Cne,EAAEmf,OAAOta,EAAElP,KAAKqK,GAAGA,EAAE+e,IAAG,GAAI/e,EAAE6e,IAAG,GAAIlY,KAAKpC,GAAExD,EAAQA,CAAE,EAAEA,EAAE,SAASf,EAAEe,EAAEX,GAA8B,MAA3B,IAAKsD,GAAE1D,GAAI+f,GAAGhf,EAAEX,GAAGmE,GAAEvE,EAAE2G,IAAU3G,CAAE,EAAE6Y,GAAG,WAAW,OAAOlS,CAAC,EAAEpR,EAAE,SAASyK,GAAY,MAATuE,KAAIA,GAAEvE,GAASA,CAAE,EAAE+E,EAAE,WAAW,OAAO,CAAC,EAAEsR,GAAG,WAAW,EAAEvC,GAAG,WAAW,EAAE0C,GAAG,WAAW,EAAE8B,GAAG,WAAW,OAAO,CAAC,EAAErD,GAAG,WAAW,EAAEd,GAAG,WAAW,EAAEY,GAAG,WAAW,EAAExQ,EAAE,WAAW,EAAEuS,GAAG,WAAW,EAAEM,GAAG,WAAW,EAAEhB,GAAG,WAAW,EAAE/B,GAAG,WAAW,EAC3f6C,GAAG,WAAW,EAAE1C,GAAG,WAAWpP,EAAE,iHAAiH,EAAEqR,GAAG,WAAWrR,EAAE,iHAAiH,EAAE1B,EAAE,WAAW,OAAOoM,KAAKnH,KAAK,EAAE4L,GAAG,WAAW,OAAM,CAAE,EAAEqC,GAAG,SAAS5W,EAAEe,GAAGf,EAAE,IAAI8P,KAAK,KAAKzN,EAAErC,IAAI,GAAG,WAAWC,EAAED,EAAE,IAAI,KAAKC,EAAEc,GAAG,IAAI,GAAGf,EAAEqV,gBAAgBpV,EAAEc,EAAE,GAAG,IAAI,GAAGf,EAAEsV,gBAAgBrV,EAAEc,EAAE,GAAG,IAAI,GAAGf,EAAEuV,cAActV,EAAEc,EAAE,IAAI,IACpf,GAAGf,EAAEwV,aAAavV,EAAEc,EAAE,IAAI,IAAI,GAAGf,EAAEyV,cAAcxV,EAAEc,EAAE,IAAI,IAAI,GAAGf,EAAE0V,iBAAiB,KAAKzV,EAAEc,EAAE,IAAI,IAAI,GAAGf,EAAE2V,YAAY1V,EAAEc,EAAE,IAAI,IAAI,IAAIf,EAAEqS,UAAUvC,KAAK8F,IAAI5V,EAAE0V,iBAAiB,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,MAAM,CAAC,EAAEP,GAAG,SAASnV,EAAEe,GAAGf,EAAE,IAAI8P,KAAK,KAAKzN,EAAErC,IAAI,GAAG,WAAWC,EAAED,EAAE,IAAI,KAAKC,EAAEc,GAAG,IAAI,GAAGf,EAAE8V,aAAa7V,EAAEc,EAAE,GAAG,IAAI,GAAGf,EAAE+V,aAAa9V,EAAEc,EAAE,GAAG,IAAI,GAAGf,EAAEgW,WAAW/V,EAAEc,EAAE,IAAI,IAAI,GAAGf,EAAEiS,UAAUhS,EAAEc,EAAE,IAAI,IAAI,GAAGf,EAAEgS,WAAW/R,EAAEc,EAAE,IAAI,IAAI,GAAGf,EAAE+P,cAAc,KAAK9P,EAAEc,EAAE,IAAI,IAAI,GAAGf,EAAEkS,SAAS,IAAI9R,EAAE,IAAI0P,KAAK9P,EAAE+P,cAAc,EAAE,GAAG9P,EAAEc,EACpf,IAAI,IAAI,IAAIf,EAAEqS,UAAUjS,EAAEiS,WAAW,MAAM,EAAEpS,EAAEc,EAAE,IAAI,IAAI,IAAK,GAAGf,EAAEgQ,oBAAqB,IAAIlR,EAAE,IAAKgR,KAAK9P,EAAE+P,cAAc,EAAE,GAAIC,oBAAoB5P,EAAEA,EAAE4P,oBAAoB/P,EAAEc,EAAE,IAAI,IAAI,GAAgD,GAA5CjC,GAAGsB,GAAGJ,EAAEgQ,qBAAqBC,KAAKgG,IAAI7V,EAAEtB,GAAK,EAAEiV,GAAG,SAAS/T,GAAG,IAAIe,EAAE,IAAI+O,KAAK7P,EAAED,EAAE,IAAI,IAAI,GAAG,KAAKC,EAAED,EAAE,IAAI,IAAI,GAAGC,EAAED,EAAE,IAAI,IAAI,GAAGC,EAAED,EAAE,GAAG,IAAI,GAAGC,EAAED,EAAE,GAAG,IAAI,GAAGC,EAAED,GAAG,IAAI,GAAG,GAAGI,EAAEH,EAAED,EAAE,IAAI,IAAI,GAAGlB,EAAEiC,EAAEiP,oBAAoB1P,EAAE,IAAIwP,KAAK/O,EAAEgP,cAAc,EAAE,GAAGrP,EAAE,IAAKoP,KAAK/O,EAAEgP,cAAc,EAAE,GAAIC,oBAAoBrL,EAAErE,EAAE0P,oBACvexP,EAAEyP,KAAKgG,IAAItR,EAAEjE,GAAsU,OAAnU,EAAEN,EAAEH,EAAED,EAAE,IAAI,IAAI,GAAG5H,OAAOsI,GAAGiE,GAAGnE,GAAG1B,GAAG,EAAEsB,IAAII,GAAG1B,KAAK4B,EAAEuP,KAAKC,IAAIvL,EAAEjE,GAAGK,EAAEoV,QAAQpV,EAAEsR,UAAU,MAAM,EAAEjS,EAAEI,EAAEE,GAAG5B,KAAKmB,EAAED,EAAE,IAAI,IAAI,GAAGe,EAAEmR,SAASjS,EAAED,EAAE,IAAI,IAAI,IAAIe,EAAEsR,UAAU/R,EAAE+R,WAAW,MAAM,EAAEpS,EAAED,GAAG,IAAI,GAAGe,EAAE+U,aAAa7V,EAAED,EAAE,GAAG,IAAI,GAAGe,EAAEgV,aAAa9V,EAAED,EAAE,GAAG,IAAI,GAAGe,EAAEiV,WAAW/V,EAAED,EAAE,IAAI,IAAI,GAAGe,EAAEkR,UAAUhS,EAAED,EAAE,IAAI,IAAI,GAAGe,EAAEiR,WAAkBjR,EAAEsR,UAAU,IAAI,CAAC,EAAEqC,GAAG,WAAW,OAAO,EAAE,EAAEE,GAAG,WAAW,EAAEqC,GAjBwB,SAAStC,EAAG3U,EAAEe,EAAEX,GAAGuU,EAAG5M,KAAK4M,EAAG5M,IAAG,EAAlZ,SAAY/H,EAAEe,EAAEX,GAAG,SAAStB,EAAES,GAAG,OAAOA,EAAEA,EAAEqQ,eAAeC,MAAM,sBAAsBtQ,EAAE,GAAG,KAAK,CAAC,IAAIe,GAAE,IAAKwP,MAAMC,cAAcrP,EAAE,IAAIoP,KAAKxP,EAAE,EAAE,GAAGqE,EAAE,IAAImL,KAAKxP,EAAE,EAAE,GAAGA,EAAEI,EAAEsP,oBAAoB,IAAIxP,EAAEmE,EAAEqL,oBAAoB/P,EAAED,GAAG,IAAI,GAAG,GAAGiQ,KAAKC,IAAI5P,EAAEE,GAAGP,EAAEc,GAAG,IAAI,GAAG3I,OAAOkI,GAAGE,GAAGR,EAAElB,EAAE4B,GAAGK,EAAEjC,EAAE6F,GAAG3E,EAAEyW,GAAGzW,GAAGe,EAAE0V,GAAG1V,GAAGP,EAAEF,GAAG+B,EAAEjC,GAAG,IAAI,GAAGJ,EAAEqC,EAAEjC,EAAE,GAAG,IAAI,GAAGW,IAAIsB,EAAEjC,GAAG,IAAI,GAAGW,EAAEsB,EAAEjC,EAAE,GAAG,IAAI,GAAGJ,EAAE,CAAqCsU,CAAGtU,EAAEe,EAAEX,GAAG,EAiBhEgF,EAAE,WAAWA,EAAE,GAAG,EAAEsO,GAAG,WAAW,OAAO,UAAU,EAAEzT,EAAEQ,EAAE,KAAK,IAAIT,EAAEoB,QAAQmP,SAAS,OAAO,IACxfvQ,EAAE,GAAGA,EAAE,GAAG,KAAK,IAAIyD,YAAYkF,MAAMsL,GAAG,SAASjU,EAAEe,EAAEX,GAAGkF,EAAEoR,WAAW1W,IAAI,EAAEe,IAAI,EAAEA,EAAEX,IAAI,EAAE,EAAEkF,EAAE,SAAStF,GAAG,IAAIe,EAAEuE,EAAE5P,OAAc,GAAG,YAAVsK,KAAK,GAAkB,OAAM,EAAG,IAAI,IAAII,EAAE,EAAE,GAAGA,EAAEA,GAAG,EAAE,CAAC,IAAItB,EAAEiC,GAAG,EAAE,GAAGX,GAAGtB,EAAEmR,KAAKgG,IAAInX,EAAEkB,EAAE,WAAW,IAAIM,EAAE2P,KAAKnR,EAAEmR,KAAKC,IAAIlQ,EAAElB,GAAGwB,EAAEA,EAAE2V,IAAIrY,KAAK0C,EAAE,WAAWxB,GAAG,MAAMA,EAAE,OAAO,OAAOkB,EAAE,CAAC,IAAI0T,EAAGqD,KAAKzW,EAAEoU,EAAGxW,WAAW,QAAQ,IAAI0W,IAAK,IAAIlU,EAAE,EAAE,MAAMV,CAAW,CAAT,MAAM2E,GAAG,CAACjE,OAAE,CAAM,CAAC,GAAGA,EAAE,OAAM,CAAE,CAAC,OAAM,CAAE,EAAE+T,GAAG,SAASzU,EAAEe,GAAG,IAAIX,EAAE,EACrX,OADuX4W,KAAKzM,SAAQ,SAASzL,EAAEwB,GAAG,IAAII,EAAEK,EAAEX,EAAsB,IAApBE,EAAE+B,EAAErC,EAAE,EAAEM,GAAG,IAAI,GAAGI,EAAMA,EAAE,EAAEA,EAAE5B,EAAEpJ,SAASgL,EAAEqE,EAAEzE,KAC9f,IAAI,GAAGxB,EAAEuG,WAAW3E,GAAGqE,EAAEzE,GAAG,IAAI,GAAG,EAAEF,GAAGtB,EAAEpJ,OAAO,CAAC,IAAU,CAAC,EAAE0e,GAAG,SAASpU,EAAEe,GAAG,IAAIX,EAAE4W,KAAK3U,EAAErC,GAAG,IAAI,GAAGI,EAAE1K,OAAO,IAAIoJ,EAAE,EAAsD,OAApDsB,EAAEmK,SAAQ,SAASjK,GAAGxB,GAAGwB,EAAE5K,OAAO,CAAC,IAAG2M,EAAEtB,GAAG,IAAI,GAAGjC,EAAS,CAAC,EAAEoZ,GAAG,SAASlY,GAAGwE,GAAe,EAAE8T,IAAK8B,KAAK1Y,EAAEuS,GAAIqG,GAAG,GAAGlF,GAAG,GAAG1f,QAAQmgB,GAAG,EAAE,IAAIT,GAAG,GAAG1f,QAAQmgB,GAAG,EAAE,KAAUrR,GAAe,EAAE8T,IAAQ1X,EAAEqI,QAAOrI,EAAEqI,OAAOjJ,GAAGP,GAAE,GAAGkZ,EAAG3Y,EAAE,IAAI0X,EAAG1X,GAAG,EAAER,EAAE,WAAW,OAAO,EAAE,EAAEmH,EAAE,WAAW,OAAO,EAAE,EAAEoR,GAAG,WAAW,OAAO,EAAE,EAAElT,EAAE,SAAS7E,EAAEe,EAAEX,EAAEtB,GAAG,IAAI,IAAIwB,EAAE,EAAEI,EAAE,EAAEA,EAAEN,EAAEM,IAAI,CAAC,IAAIiE,EAAEtC,EAAEtB,GAAG,IAAI,GAAGP,EAAE6B,EAAEtB,EAAE,GAClf,IAAI,GAAGA,GAAG,EAAE,IAAI,IAAIxB,EAAE,EAAEA,EAAEiB,EAAEjB,IAAIsW,GAAG7V,EAAEsF,EAAEX,EAAEpF,IAAI,IAAIe,GAAGE,CAAC,CAAe,OAAd6B,EAAEvD,GAAG,IAAI,GAAGwB,EAAS,CAAC,EAAEF,EAAE,WAAW,OAAOkG,EAAC,EAAEoR,GAlB+J,SAAS9S,EAAE5E,EAAEe,GAAG6D,EAAEya,KAAKza,EAAEya,GAA7R,WAAc,GAAG,iBAAiB/H,QAAQ,mBAAmBA,OAAOC,gBAAgB,CAAC,IAAIvX,EAAE,IAAIpJ,WAAW,GAAG,MAAM,KAAK0gB,OAAOC,gBAAgBvX,GAAUA,EAAE,GAAG,CAAC,GAAGS,EAAE,IAAI,IAAIM,EAAE,EAAQ,wGAAU,MAAM,IAAIA,EAAEyW,YAAY,GAAG,EAAY,CAAT,MAAMpX,GAAG,CAAC,MAAM,IAAIgF,EAAE,eAAe,CAA6B8Q,IAAM,IAAI,IAAI9V,EAAE,EAAEA,EAAEW,EAAEX,IAAI2E,EAAE/E,EAAEI,GAAG,IAAI,GAAGwE,EAAEya,KAAK,OAAO,CAAC,EAkB7O1G,GAgBqE,SAAY3Y,EAAEe,EAAEX,GAAG,IAAItB,EAAEiB,KAAI,IAAI,OAAOD,GAAEE,EAAFF,CAAKiB,EAAEX,EAA0C,CAAvC,MAAME,GAAQ,GAALmG,GAAE3H,GAAMwB,IAAIA,EAAE,EAAE,MAAMA,EAAEkG,GAAE,EAAE,EAAE,CAAC,EAhBzJ+R,GAgB5B,SAAYvY,EAAEe,EAAEX,GAAG,IAAItB,EAAEiB,KAAI,IAAI,OAAOD,GAAEE,EAAFF,CAAKiB,EAAEX,EAA0C,CAAvC,MAAME,GAAQ,GAALmG,GAAE3H,GAAMwB,IAAIA,EAAE,EAAE,MAAMA,EAAEkG,GAAE,EAAE,EAAE,CAAC,EAhBxDnE,EAgBtH,SAAYrC,GAAG,IAAIe,EAAEhB,KAAI,IAAI,OAAOD,GAAEE,EAAFF,EAA8C,CAAvC,MAAMM,GAAQ,GAALqG,GAAE1F,GAAMX,IAAIA,EAAE,EAAE,MAAMA,EAAEoG,GAAE,EAAE,EAAE,CAAC,EAhBwC1H,EAW0F,SAAYkB,EAAEe,GAAG,IAAIX,EAAEL,KAAI,IAAI,OAAOD,GAAEE,EAAFF,CAAKiB,EAA0C,CAAvC,MAAMjC,GAAQ,GAAL2H,GAAErG,GAAMtB,IAAIA,EAAE,EAAE,MAAMA,EAAE0H,GAAE,EAAE,EAAE,CAAC,EAX3KT,EAcmE,SAAY/F,EAAEe,EAAEX,GAAG,IAAItB,EAAEiB,KAAI,IAAI,OAAOD,GAAEE,EAAFF,CAAKiB,EAAEX,EAA0C,CAAvC,MAAME,GAAQ,GAALmG,GAAE3H,GAAMwB,IAAIA,EAAE,EAAE,MAAMA,EAAEkG,GAAE,EAAE,EAAE,CAAC,EAdxJ9E,EAc7B,SAAY1B,EAAEe,EAAEX,GAAG,IAAItB,EAAEiB,KAAI,IAAI,OAAOD,GAAEE,EAAFF,CAAKiB,EAAEX,EAA0C,CAAvC,MAAME,GAAQ,GAALmG,GAAE3H,GAAMwB,IAAIA,EAAE,EAAE,MAAMA,EAAEkG,GAAE,EAAE,EAAE,CAAC,EAdxD7G,EAY1I,SAAYK,EAAEe,EAAEX,GAAG,IAAItB,EAAEiB,KAAI,IAAI,OAAOD,GAAEE,EAAFF,CAAKiB,EAAEX,EAA0C,CAAvC,MAAME,GAAQ,GAALmG,GAAE3H,GAAMwB,IAAIA,EAAE,EAAE,MAAMA,EAAEkG,GAAE,EAAE,EAAE,CAAC,EAZqDrG,EAYpD,SAAYH,EAAEe,EAAEX,EAAEtB,GAAG,IAAIwB,EAAEP,KAAI,IAAI,OAAOD,GAAEE,EAAFF,CAAKiB,EAAEX,EAAEtB,EAA0C,CAAvC,MAAM4B,GAAQ,GAAL+F,GAAEnG,GAAMI,IAAIA,EAAE,EAAE,MAAMA,EAAE8F,GAAE,EAAE,EAAE,CAAC,EAZrC/F,EAapJ,SAAYT,EAAEe,EAAEX,EAAEtB,EAAEwB,GAAG,IAAII,EAAEX,KAAI,IAAI,OAAOD,GAAEE,EAAFF,CAAKiB,EAAEX,EAAEtB,EAAEwB,EAA0C,CAAvC,MAAMqE,GAAQ,GAAL8B,GAAE/F,GAAMiE,IAAIA,EAAE,EAAE,MAAMA,EAAE6B,GAAE,EAAE,EAAE,CAAC,EAbuDxC,EAciP,SAAYhE,EAAEe,EAAEX,EAAEtB,EAAEwB,EAAEI,GAAG,IAAIiE,EAAE5E,KAAI,IAAI,OAAOD,GAAEE,EAAFF,CAAKiB,EAAEX,EAAEtB,EAAEwB,EAAEI,EAA0C,CAAvC,MAAMF,GAAQ,GAALiG,GAAE9B,GAAMnE,IAAIA,EAAE,EAAE,MAAMA,EAAEgG,GAAE,EAAE,EAAE,CAAC,EAdlV3G,EAYoN,SAAYG,EAAEe,EAAEX,EAAEtB,EAAEwB,EAAEI,GAAG,IAAIiE,EAAE5E,KAAI,IAAI,OAAOD,GAAEE,EAAFF,CAAKiB,EAAEX,EAAEtB,EAAEwB,EAAEI,EAA0C,CAAvC,MAAMF,GAAQ,GAALiG,GAAE9B,GAAMnE,IAAIA,EAAE,EAAE,MAAMA,EAAEgG,GAAE,EAAE,EAAE,CAAC,EAZrTnJ,EAYuB,SAAY2C,EAAEe,EAAEX,EAAEtB,EAAEwB,EAAEI,EAAEiE,GAAG,IAAInE,EAAET,KAAI,IAAI,OAAOD,GAAEE,EAAFF,CAAKiB,EAAEX,EAAEtB,EAAEwB,EAAEI,EAAEiE,EAA0C,CAAvC,MAAMpF,GAAQ,GAALkH,GAAEjG,GAAMjB,IAAIA,EAAE,EAAE,MAAMA,EAAEiH,GAAE,EAAE,EAAE,CAAC,EAZ5HO,EAexK,SAAY/G,EAAEe,EAAEX,EAAEtB,EAAEwB,EAAEI,EAAEiE,EAAEnE,GAAG,IAAIjB,EAAEQ,KAAI,IAAI,OAAOD,GAAEE,EAAFF,CAAKiB,EAAEX,EAAEtB,EAAEwB,EAAEI,EAAEiE,EAAEnE,EAA0C,CAAvC,MAAMnB,GAAQ,GAALoH,GAAElH,GAAMF,IAAIA,EAAE,EAAE,MAAMA,EAAEmH,GAAE,EAAE,EAAE,CAAC,EAf+D/G,EAe9D,SAAYO,EAAEe,EAAEX,EAAEtB,EAAEwB,EAAEI,EAAEiE,EAAEnE,EAAEjB,EAAEF,EAAEkD,EAAE/C,GAAG,IAAIyE,EAAElE,KAAI,IAAI,OAAOD,GAAEE,EAAFF,CAAKiB,EAAEX,EAAEtB,EAAEwB,EAAEI,EAAEiE,EAAEnE,EAAEjB,EAAEF,EAAEkD,EAAE/C,EAA0C,CAAvC,MAAMwB,GAAQ,GAALyF,GAAExC,GAAMjD,IAAIA,EAAE,EAAE,MAAMA,EAAEwF,GAAE,EAAE,EAAE,CAAC,EAf3DA,EAkBlL,SAAYxG,EAAEe,EAAEX,EAAEtB,EAAEwB,EAAEI,EAAEiE,EAAEnE,GAAG,IAAIjB,EAAEQ,KAAI,IAAI,OAAO+S,GAAG9S,EAAEe,EAAEX,EAAEtB,EAAEwB,EAAEI,EAAEiE,EAAEnE,EAA0C,CAAvC,MAAMnB,GAAQ,GAALoH,GAAElH,GAAMF,IAAIA,EAAE,EAAE,MAAMA,EAAEmH,GAAE,EAAE,EAAE,CAAC,EAlByEF,EAkBW,SAAYtG,EAAEe,EAAEX,EAAEtB,EAAEwB,EAAEI,EAAEiE,GAAG,IAAInE,EAAET,KAAI,IAAI,OAAOsf,GAAGrf,EAAEe,EAAEX,EAAEtB,EAAEwB,EAAEI,EAAEiE,EAA0C,CAAvC,MAAMpF,GAAQ,GAALkH,GAAEjG,GAAMjB,IAAIA,EAAE,EAAE,MAAMA,EAAEiH,GAAE,EAAE,EAAE,CAAC,EAlBhH1G,EAkBiH,SAAYE,EAAEe,EAAEX,EAAEtB,EAAEwB,GAAG,IAAII,EAAEX,KAAI,IAAI,OAAOqI,GAAGpI,EAAEe,EAAEX,EAAEtB,EAAEwB,EAA0C,CAAvC,MAAMqE,GAAQ,GAAL8B,GAAE/F,GAAMiE,IAAIA,EAAE,EAAE,MAAMA,EAAE6B,GAAE,EAAE,EAAE,CAAC,EAlB9MC,EAiBuB,SAAYzG,EAAEe,EAAEX,EAAEtB,GAAG,IAAIwB,EAAEP,KAAI,IAAI,OAAOggB,GAAG/f,EAAEe,EAAEX,EAAEtB,EAA0C,CAAvC,MAAM4B,GAAQ,GAAL+F,GAAEnG,GAAMI,IAAIA,EAAE,EAAE,MAAMA,EAAE8F,GAAE,EAAE,EAAE,CAAC,EAjBhH5B,EAkBvF,SAAY5E,GAAG,IAAIe,EAAEhB,KAAI,IAAI,OAAOof,GAAGnf,EAA0C,CAAvC,MAAMI,GAAQ,GAALqG,GAAE1F,GAAMX,IAAIA,EAAE,EAAE,MAAMA,EAAEoG,GAAE,EAAE,EAAE,CAAC,EAlBUzG,EAiB4G,SAAYC,EAAEe,GAAG,IAAIX,EAAEL,KAAI,IAAI,OAAO+M,GAAG9M,EAAEe,EAA0C,CAAvC,MAAMjC,GAAQ,GAAL2H,GAAErG,GAAMtB,IAAIA,EAAE,EAAE,MAAMA,EAAE0H,GAAE,EAAE,EAAE,CAAC,EAjB7L/E,EAkBgM,SAAYzB,EAAEe,EAAEX,GAAG,IAAItB,EAAEiB,KAAI,IAAI,OAAOwf,GAAGvf,EAAEe,EAAEX,EAA0C,CAAvC,MAAME,GAAQ,GAALmG,GAAE3H,GAAMwB,IAAIA,EAAE,EAAE,MAAMA,EAAEkG,GAAE,EAAE,EAAE,CAAC,EAlBrRlG,EAYgF,SAAYN,GAAG,IAAIe,EAAEhB,KAAI,IAAID,GAAEE,EAAFF,EAA8C,CAAvC,MAAMM,GAAQ,GAALqG,GAAE1F,GAAMX,IAAIA,EAAE,EAAE,MAAMA,EAAEoG,GAAE,EAAE,EAAE,CAAC,EAZvJrB,EAWkF,SAAYnF,EAAEe,GAAG,IAAIX,EAAEL,KAAI,IAAID,GAAEE,EAAFF,CAAKiB,EAA0C,CAAvC,MAAMjC,GAAQ,GAAL2H,GAAErG,GAAMtB,IAAIA,EAAE,EAAE,MAAMA,EAAE0H,GAAE,EAAE,EAAE,CAAC,EAX5J9F,EAW6J,SAAYV,EAAEe,EAAEX,GAAG,IAAItB,EAAEiB,KAAI,IAAID,GAAEE,EAAFF,CAAKiB,EAAEX,EAA0C,CAAvC,MAAME,GAAQ,GAALmG,GAAE3H,GAAMwB,IAAIA,EAAE,EAAE,MAAMA,EAAEkG,GAAE,EAAE,EAAE,CAAC,EAX3OiS,GAgBsC,SAAYzY,EAAEe,EAAEX,EAAEtB,GAAG,IAAIwB,EAAEP,KAAI,IAAID,GAAEE,EAAFF,CAAKiB,EAAEX,EAAEtB,EAA0C,CAAvC,MAAM4B,GAAQ,GAAL+F,GAAEnG,GAAMI,IAAIA,EAAE,EAAE,MAAMA,EAAE8F,GAAE,EAAE,EAAE,CAAC,EAhBvHhG,EAavI,SAAYR,EAAEe,EAAEX,EAAEtB,GAAG,IAAIwB,EAAEP,KAAI,IAAID,GAAEE,EAAFF,CAAKiB,EAAEX,EAAEtB,EAA0C,CAAvC,MAAM4B,GAAQ,GAAL+F,GAAEnG,GAAMI,IAAIA,EAAE,EAAE,MAAMA,EAAE8F,GAAE,EAAE,EAAE,CAAC,EAbqDnH,EAa4C,SAAYW,EAAEe,EAAEX,EAAEtB,EAAEwB,GAAG,IAAII,EAAEX,KAAI,IAAID,GAAEE,EAAFF,CAAKiB,EAAEX,EAAEtB,EAAEwB,EAA0C,CAAvC,MAAMqE,GAAQ,GAAL8B,GAAE/F,GAAMiE,IAAIA,EAAE,EAAE,MAAMA,EAAE6B,GAAE,EAAE,EAAE,CAAC,EAblItG,EAazD,SAAYF,EAAEe,EAAEX,EAAEtB,EAAEwB,EAAEI,GAAG,IAAIiE,EAAE5E,KAAI,IAAID,GAAEE,EAAFF,CAAKiB,EAAEX,EAAEtB,EAAEwB,EAAEI,EAA0C,CAAvC,MAAMF,GAAQ,GAALiG,GAAE9B,GAAMnE,IAAIA,EAAE,EAAE,MAAMA,EAAEgG,GAAE,EAAE,EAAE,CAAC,EAbjCjE,EAa8H,SAAYvC,EAAEe,EAAEX,EAAEtB,EAAEwB,EAAEI,EAAEiE,GAAG,IAAInE,EAAET,KAAI,IAAID,GAAEE,EAAFF,CAAKiB,EAAEX,EAAEtB,EAAEwB,EAAEI,EAAEiE,EAA0C,CAAvC,MAAMpF,GAAQ,GAALkH,GAAEjG,GAAMjB,IAAIA,EAAE,EAAE,MAAMA,EAAEiH,GAAE,EAAE,EAAE,CAAC,EAb5N9G,EAc9P,SAAYM,EAAEe,EAAEX,EAAEtB,EAAEwB,EAAEI,EAAEiE,EAAEnE,GAAG,IAAIjB,EAAEQ,KAAI,IAAID,GAAEE,EAAFF,CAAKiB,EAAEX,EAAEtB,EAAEwB,EAAEI,EAAEiE,EAAEnE,EAA0C,CAAvC,MAAMnB,GAAQ,GAALoH,GAAElH,GAAMF,IAAIA,EAAE,EAAE,MAAMA,EAAEmH,GAAE,EAAE,EAAE,CAAC,EAd4J2Q,GAc2B,SAAYnX,EAAEe,EAAEX,EAAEtB,EAAEwB,EAAEI,EAAEiE,EAAEnE,EAAEjB,GAAG,IAAIF,EAAEU,KAAI,IAAID,GAAEE,EAAFF,CAAKiB,EAAEX,EAAEtB,EAAEwB,EAAEI,EAAEiE,EAAEnE,EAAEjB,EAA0C,CAAvC,MAAMgD,GAAQ,GAALkE,GAAEpH,GAAMkD,IAAIA,EAAE,EAAE,MAAMA,EAAEiE,GAAE,EAAE,EAAE,CAAC,EAdhI3C,EAe3B,SAAY7D,EAAEe,EAAEX,EAAEtB,EAAEwB,EAAEI,EAAEiE,EAAEnE,EAAEjB,EAAEF,EAAEkD,GAAG,IAAI/C,EAAEO,KAAI,IAAID,GAAEE,EAAFF,CAAKiB,EAAEX,EAAEtB,EAAEwB,EAAEI,EAAEiE,EAAEnE,EAAEjB,EAAEF,EAAEkD,EAA0C,CAAvC,MAAM0B,GAAQ,GAALwC,GAAEjH,GAAMyE,IAAIA,EAAE,EAAE,MAAMA,EAAEuC,GAAE,EAAE,EAAE,CAAC,EAfnFvC,EAeoF,SAAYjE,EAAEe,EAAEX,EAAEtB,EAAEwB,EAAEI,EAAEiE,EAAEnE,EAAEjB,EAAEF,EAAEkD,EAAE/C,EAAEyE,EAAEjD,EAAEd,EAAEqB,GAAG,IAAI3B,EAAEG,KAAI,IAAID,GAAEE,EAAFF,CAAKiB,EAAEX,EAAEtB,EAAEwB,EAAEI,EAAEiE,EAAEnE,EAAEjB,EAAEF,EAAEkD,EAAE/C,EAAEyE,EAAEjD,EAAEd,EAAEqB,EAA0C,CAAvC,MAAMyC,GAAQ,GAALyC,GAAE7G,GAAMoE,IAAIA,EAAE,EAAE,MAAMA,EAAEwC,GAAE,EAAE,EAAE,CAAC,EAftNmR,GAgB+E,SAAY3X,EAAEe,EAAEX,EAAEtB,EAAEwB,EAAEI,EAAEiE,EAAEnE,GAAG,IAAIjB,EAAEQ,KAAI,IAAI0f,GAAGzf,EAAEe,EAAEX,EAAEtB,EAAEwB,EAAEI,EAAEiE,EAAEnE,EAA0C,CAAvC,MAAMnB,GAAQ,GAALoH,GAAElH,GAAMF,IAAIA,EAAE,EAAE,MAAMA,EAAEmH,GAAE,EAAE,EAAE,CAAC,EAhBhLtF,EAiBzL,SAAYlB,EAAEe,EAAEX,EAAEtB,EAAEwB,EAAEI,EAAEiE,EAAEnE,EAAEjB,EAAEF,EAAEkD,EAAE/C,GAAG,IAAIyE,EAAElE,KAAI,IAAI8f,GAAG7f,EAAEe,EAAEX,EAAEtB,EAAEwB,EAAEI,EAAEiE,EAAEnE,EAAEjB,EAAEF,EAAEkD,EAAE/C,EAA0C,CAAvC,MAAMwB,GAAQ,GAALyF,GAAExC,GAAMjD,IAAIA,EAAE,EAAE,MAAMA,EAAEwF,GAAE,EAAE,EAAE,CAAC,EAjBuEE,EAiB9R,SAAY1G,EAAEe,EAAEX,EAAEtB,EAAEwB,EAAEI,GAAG,IAAIiE,EAAE5E,KAAI,IAAI4f,GAAG3f,EAAEe,EAAEX,EAAEtB,EAAEwB,EAAEI,EAA0C,CAAvC,MAAMF,GAAQ,GAALiG,GAAE9B,GAAMnE,IAAIA,EAAE,EAAE,MAAMA,EAAEgG,GAAE,EAAE,EAAE,CAAC,EAjBoMjH,EAAE,SAASS,GAAG,OAAOA,CAAC,EAAEJ,EAAE,SAASI,GAAGsG,GAAEtG,CAAC,EAAEqY,GAAG2B,GAAGlZ,EAAE,SAASd,EAAEe,EAAEX,EAAEtB,GAAG,OAAOkb,GAAGha,EAAEe,EAAEX,EAAEtB,EAAE,IAC1X,WAAY,SAASkB,EAAEM,GAAGM,EAAEkY,IAAIxY,EAAEhM,QAAQof,EAAG9S,EAAEkY,IAAIxE,GAAGM,IAAKT,EAAGvT,EAAEkY,IAAI6C,GAAGvH,EAAGtN,QAAQlG,EAAEkY,IAAInE,IAAI5N,IAAInG,EAAEsY,wBAAwBtY,EAAEsY,uBAAuBnS,GAAG,GAAGA,IAAI,OAAOqP,IAAK+C,cAAc/C,GAAIA,EAAG,MAAM7P,IAAIjG,EAAEiG,EAAEA,EAAE,KAAKjG,KAAK,CAAC,SAASS,EAAET,GAAGN,EAAEM,EAAE8Y,SAAS,CAAC,SAAShZ,EAAEE,GAAG,OAzBnQ,WAAc,IAAIuD,IAAI0U,GAAI5X,GAAG,CAAC,GAAG,mBAAmB0Y,QAAQtT,EAAE0B,WAAW,WAAW,OAAO4R,MAAMtT,EAAE,CAACuT,YAAY,gBAAgBC,MAAK,SAASvZ,GAAG,IAAIA,EAAEwZ,GAAG,KAAK,uCAAuCzT,EAAE,IAAI,OAAO/F,EAAEyZ,aAAa,IAAGC,OAAM,WAAW,OAAO9C,GAAI,IAAG,GAAG/W,EAAE,OAAO,IAAItE,SAAQ,SAASyE,EAAEe,GAAGlB,EAAEkG,GAAE,SAAS3F,GAAGJ,EAAE,IAAIpJ,WAAWwJ,GAAG,GAAEW,EAAE,GAAE,CAAC,OAAOxF,QAAQC,UAAU+d,MAAK,WAAW,OAAO3C,GAAI,GAAE,CAyBjIzB,GAAKoE,MAAK,SAAS7Y,GAAG,OAAO+D,YAAYkV,YAAYjZ,EAAE5B,EAAE,IAAGya,MAAK,SAAS7Y,GAAG,OAAOA,CAAC,IAAG6Y,KAAKjZ,GAAE,SAASI,GAAGhB,EAAE,0CAA0CgB,GAAG0E,EAAE1E,EAAE,GAAE,CAAC,IAAI5B,EAAE,CAACkB,EAAE+e,IAA8D,GAA1DhY,IAAInG,EAAEsY,wBAAwBtY,EAAEsY,uBAAuBnS,GAAMnG,EAAEgZ,gBAAgB,IAAI,OAAOhZ,EAAEgZ,gBAAgB9a,EACpiBkB,EAAgF,CAA7E,MAAMM,GAAG,OAAOZ,EAAE,sDAAsDY,IAAG,CAAE,EAAoBuD,GAAG,mBAAmBY,YAAYoV,sBAAsBxD,KAAMtQ,EAAE0B,WAAW,YAAYhH,GAAG,mBAAmB4Y,MAAMjZ,EAAEW,GAAGsY,MAAMtT,EAAE,CAACuT,YAAY,gBAAgBC,MAAK,SAASjZ,GAAG,OAAOmE,YAAYoV,qBAAqBvZ,EAAExB,GAAGya,KAAKxY,GAAE,SAASL,GAAyF,OAAtFhB,EAAE,kCAAkCgB,GAAGhB,EAAE,6CAAoDU,EAAEW,EAAE,GAAE,KAAO2Y,MAAMxB,EAAa,CAD/c,GAEAtX,EAAEkZ,mBAAmB,WAAW,OAAOlZ,EAAEkZ,mBAAmBlZ,EAAEkY,IAAInE,IAAIkC,MAAM,KAAK1G,UAAU,EAAEvP,EAAEmZ,SAAS,WAAW,OAAOnZ,EAAEmZ,SAASnZ,EAAEkY,IAAI5E,IAAI2C,MAAM,KAAK1G,UAAU,EAAEvP,EAAEqZ,yBAAyB,WAAW,OAAOrZ,EAAEqZ,yBAAyBrZ,EAAEkY,IAAI9B,IAAIH,MAAM,KAAK1G,UAAU,EAAEvP,EAAEuZ,4BAA4B,WAAW,OAAOvZ,EAAEuZ,4BAA4BvZ,EAAEkY,IAAI9D,IAAI6B,MAAM,KAAK1G,UAAU,EAAEvP,EAAEyZ,0BAA0B,WAAW,OAAOzZ,EAAEyZ,0BAA0BzZ,EAAEkY,IAAI1D,IAAIyB,MAAM,KAAK1G,UAAU,EACvevP,EAAE2Z,0BAA0B,WAAW,OAAO3Z,EAAE2Z,0BAA0B3Z,EAAEkY,IAAIjD,IAAIgB,MAAM,KAAK1G,UAAU,EAAEvP,EAAE4Z,kBAAkB,WAAW,OAAO5Z,EAAE4Z,kBAAkB5Z,EAAEkY,IAAI5C,IAAIW,MAAM,KAAK1G,UAAU,EAAEvP,EAAE8Z,mBAAmB,WAAW,OAAO9Z,EAAE8Z,mBAAmB9Z,EAAEkY,IAAIxC,IAAIO,MAAM,KAAK1G,UAAU,EAAEvP,EAAEga,kBAAkB,WAAW,OAAOha,EAAEga,kBAAkBha,EAAEkY,IAAIjE,IAAIgC,MAAM,KAAK1G,UAAU,EAAEvP,EAAEka,mBAAmB,WAAW,OAAOla,EAAEka,mBAAmBla,EAAEkY,IAAIhE,IAAI+B,MAAM,KAAK1G,UAAU,EACzdvP,EAAEoa,iBAAiB,WAAW,OAAOpa,EAAEoa,iBAAiBpa,EAAEkY,IAAIG,IAAIpC,MAAM,KAAK1G,UAAU,EAAEvP,EAAEsa,kBAAkB,WAAW,OAAOta,EAAEsa,kBAAkBta,EAAEkY,IAAIkB,IAAInD,MAAM,KAAK1G,UAAU,EAAEvP,EAAEwa,SAAS,WAAW,OAAOxa,EAAEwa,SAASxa,EAAEkY,IAAIoB,IAAIrD,MAAM,KAAK1G,UAAU,EAAEvP,EAAE0a,iBAAiB,WAAW,OAAO1a,EAAE0a,iBAAiB1a,EAAEkY,IAAIsB,IAAIvD,MAAM,KAAK1G,UAAU,EAAEvP,EAAE4a,kBAAkB,WAAW,OAAO5a,EAAE4a,kBAAkB5a,EAAEkY,IAAIwB,IAAIzD,MAAM,KAAK1G,UAAU,EAC/avP,EAAE8a,kBAAkB,WAAW,OAAO9a,EAAE8a,kBAAkB9a,EAAEkY,IAAIzc,IAAIwa,MAAM,KAAK1G,UAAU,EAAEvP,EAAEgb,qBAAqB,WAAW,OAAOhb,EAAEgb,qBAAqBhb,EAAEkY,IAAI2B,IAAI5D,MAAM,KAAK1G,UAAU,EAAEvP,EAAEkb,sBAAsB,WAAW,OAAOlb,EAAEkb,sBAAsBlb,EAAEkY,IAAI6B,IAAI9D,MAAM,KAAK1G,UAAU,EAAEvP,EAAEob,sBAAsB,WAAW,OAAOpb,EAAEob,sBAAsBpb,EAAEkY,IAAI+B,IAAIhE,MAAM,KAAK1G,UAAU,EAAEvP,EAAEsb,QAAQ,WAAW,OAAOtb,EAAEsb,QAAQtb,EAAEkY,IAAIiC,IAAIlE,MAAM,KAAK1G,UAAU,EACvcvP,EAAEwb,iBAAiB,WAAW,OAAOxb,EAAEwb,iBAAiBxb,EAAEkY,IAAImC,IAAIpE,MAAM,KAAK1G,UAAU,EACvF,IAW6IpI,GAXzIyM,GAAG5T,EAAE4b,QAAQ,WAAW,OAAOhI,GAAG5T,EAAE4b,QAAQ5b,EAAEkY,IAAIqC,IAAItE,MAAM,KAAK1G,UAAU,EAAE+G,GAAGtW,EAAE8b,MAAM,WAAW,OAAOxF,GAAGtW,EAAE8b,MAAM9b,EAAEkY,IAAIuC,IAAIxE,MAAM,KAAK1G,UAAU,EAAEmK,GAAG1Z,EAAEgc,QAAQ,WAAW,OAAOtC,GAAG1Z,EAAEgc,QAAQhc,EAAEkY,IAAIyC,IAAI1E,MAAM,KAAK1G,UAAU,EAAEiK,GAAGxZ,EAAEmc,iBAAiB,WAAW,OAAO3C,GAAGxZ,EAAEmc,iBAAiBnc,EAAEkY,IAAI2C,IAAI5E,MAAM,KAAK1G,UAAU,EAAE3J,GAAE5F,EAAEkd,UAAU,WAAW,OAAOtX,GAAE5F,EAAEkd,UAAUld,EAAEkY,IAAI+C,IAAIhF,MAAM,KAAK1G,UAAU,EAAEpQ,GAAEa,EAAEsd,UAAU,WAAW,OAAOne,GAAEa,EAAEsd,UAAUtd,EAAEkY,IAAIiD,IAAIlF,MAAM,KAAK1G,UAAU,EAAE1J,GAAE7F,EAAEwd,aACxe,WAAW,OAAO3X,GAAE7F,EAAEwd,aAAaxd,EAAEkY,IAAImD,IAAIpF,MAAM,KAAK1G,UAAU,EAAE8O,GAAGre,EAAE0d,WAAW,WAAW,OAAOW,GAAGre,EAAE0d,WAAW1d,EAAEkY,IAAIqD,IAAItF,MAAM,KAAK1G,UAAU,EAAE+J,GAAGtZ,EAAE4d,iBAAiB,WAAW,OAAOtE,GAAGtZ,EAAE4d,iBAAiB5d,EAAEkY,IAAIuD,IAAIxF,MAAM,KAAK1G,UAAU,EAAE4D,GAAGnT,EAAE8d,uBAAuB,WAAW,OAAO3K,GAAGnT,EAAE8d,uBAAuB9d,EAAEkY,IAAIyD,IAAI1F,MAAM,KAAK1G,UAAU,EAAEgP,GAAGve,EAAEge,UAAU,WAAW,OAAOO,GAAGve,EAAEge,UAAUhe,EAAEkY,IAAI2D,IAAI5F,MAAM,KAAK1G,UAAU,EAAEkP,GAAGze,EAAEke,eAAe,WAAW,OAAOO,GAAGze,EAAEke,eAAele,EAAEkY,IAAI6D,IAAI9F,MAAM,KAC5f1G,UAAU,EAAEoP,GAAG3e,EAAEoe,YAAY,WAAW,OAAOO,GAAG3e,EAAEoe,YAAYpe,EAAEkY,IAAI+D,IAAIhG,MAAM,KAAK1G,UAAU,EAAEsP,GAAG7e,EAAEse,gBAAgB,WAAW,OAAOO,GAAG7e,EAAEse,gBAAgBte,EAAEkY,IAAIC,IAAIlC,MAAM,KAAK1G,UAAU,EAAEwP,GAAG/e,EAAEwe,aAAa,WAAW,OAAOO,GAAG/e,EAAEwe,aAAaxe,EAAEkY,IAAIkE,IAAInG,MAAM,KAAK1G,UAAU,EAAE0P,GAAGjf,EAAE0e,kBAAkB,WAAW,OAAOO,GAAGjf,EAAE0e,kBAAkB1e,EAAEkY,IAAIE,IAAInC,MAAM,KAAK1G,UAAU,EAAE4P,GAAGnf,EAAE4e,YAAY,WAAW,OAAOO,GAAGnf,EAAE4e,YAAY5e,EAAEkY,IAAIoE,IAAIrG,MAAM,KAAK1G,UAAU,EAAErD,GAAGlM,EAAE8e,WAAW,WAAW,OAAO5S,GAAGlM,EAAE8e,WAClf9e,EAAEkY,IAAIsE,IAAIvG,MAAM,KAAK1G,UAAU,EAAE2C,GAAGlS,EAAEgf,gBAAgB,WAAW,OAAO9M,GAAGlS,EAAEgf,gBAAgBhf,EAAEkY,IAAIyE,IAAI1G,MAAM,KAAK1G,UAAU,EAAE/H,GAAGxH,EAAEkf,aAAa,WAAW,OAAO1X,GAAGxH,EAAEkf,aAAalf,EAAEkY,IAAI2E,IAAI5G,MAAM,KAAK1G,UAAU,EASnN,SAASiC,KAAK,SAASpS,IAAI,IAAI+H,KAAKA,IAAG,EAAGnH,EAAEqf,WAAU,GAAIxgB,GAAG,CAAgE,GAA/DiC,EAAE0S,GAAIuD,EAAG/W,GAAMA,EAAEsf,sBAAqBtf,EAAEsf,uBAA0Btf,EAAEuf,QAAQ,IAAI,mBAAmBvf,EAAEuf,UAAUvf,EAAEuf,QAAQ,CAACvf,EAAEuf,UAAUvf,EAAEuf,QAAQzqB,QAAQ,CAAC,IAAIqL,EAAEH,EAAEuf,QAAQtZ,QAAQkO,EAAGjO,QAAQ/F,EAAE,CAACW,EAAEqT,EAAG,CAAC,CAAC,KAAK,EAAEhO,GAAG,CAAC,GAAGnG,EAAEgG,OAAO,IAAI,mBAAmBhG,EAAEgG,SAAShG,EAAEgG,OAAO,CAAChG,EAAEgG,SAAShG,EAAEgG,OAAOlR,QAAQuf,IAAKvT,EAAE+S,GAAI,EAAE1N,IAAInG,EAAEwf,WAAWxf,EAAEwf,UAAU,cAAclL,YAAW,WAAWA,YAAW,WAAWtU,EAAEwf,UAAU,GAAG,GAAE,GAAGpgB,GAAG,GAAE,IAAIA,IAAI,CAAC,CACze,GAFAY,EAAEyf,aAAavM,EAAGlT,EAAE0f,aAAa,SAAStgB,EAAEe,EAAEX,GAAG,OAAO0W,EAAG9W,EAAEsF,EAAEvE,EAAEX,EAAE,EAAEQ,EAAE2f,gBAAgB/J,EAAG5V,EAAEsd,UAAUne,GAAEa,EAAEwd,aAAa3X,GAAE7F,EAAE0d,WAAWW,GAAU1Y,EAAE,SAAS4L,IAAKpK,IAAIqK,KAAKrK,KAAKxB,EAAE4L,EAAG,EAEhLvR,EAAE8f,QAAQ,IAAI,mBAAmB9f,EAAE8f,UAAU9f,EAAE8f,QAAQ,CAAC9f,EAAE8f,UAAU,EAAE9f,EAAE8f,QAAQhrB,QAAQkL,EAAE8f,QAAQ9U,KAAVhL,GAGzF,OAH2GwR,KAGpGuO,EAAQtgB,KAEjB,GAGE9L,EAAOD,QAAUqsB,C,8CCrEnB,eACA,SAEA,SACA,SAQa,KAAkB,KAa7B,IAZoC,iBAAzB,EAAA/qB,IAAIG,KAAK8qB,aAA4B,EAAAjrB,IAAIG,KAAK8qB,YAAc,KACrE,EAAAjrB,IAAIG,KAAK8qB,YAAc,GAGI,kBAAlB,EAAAjrB,IAAIG,KAAK+qB,OAClB,EAAAlrB,IAAIG,KAAK+qB,MAAO,GAGY,kBAAnB,EAAAlrB,IAAIG,KAAKgrB,QAClB,EAAAnrB,IAAIG,KAAKgrB,OAAQ,GAGgB,iBAAxB,EAAAnrB,IAAIG,KAAKirB,aAA4B5oB,OAAO6oB,UAAU,EAAArrB,IAAIG,KAAKirB,aAAe,EAAAprB,IAAIG,KAAKirB,YAAc,EAAG,CACjH,MAAME,EAA0C,oBAAd/P,WAA4B,IAAAgQ,QAAOzrB,OAASyb,UAAUwF,oBACxF,EAAA/gB,IAAIG,KAAKirB,WAAa/Q,KAAKgG,IAAI,EAAGhG,KAAKmR,MAAMF,GAAsB,GAAK,G,GAsB/D,KAAc,IAlB3B,MACEtkB,cAEE,gBAGM,IAAAykB,WACR,CAGAzkB,2BAA2B0kB,EAAiC5oB,GAE1D,MAAMiE,EAAU,IAAI,EAAA4kB,qCAEpB,aADM5kB,EAAQ6kB,UAAUF,EAAc5oB,GAC/B6C,QAAQC,QAAQmB,EACzB,E,ugBCzCF,YACA,eAM8B,CAC5B,MAAM8kB,EAAc,WACpB,IAAA5sB,iBAAgB,MAAO4sB,EAAa,KACpC,IAAA5sB,iBAAgB,OAAQ4sB,EAAa,KACrC,IAAA5sB,iBAAgB,UAAW4sB,EAAa,E,0GCZ7B,EAAAC,oBACT,CAAChpB,EAAkCipB,EAAgBC,EAClDjlB,KACC,GAAsB,iBAAXjE,GAAmC,OAAZA,EAAkB,CAClD,GAAIkpB,EAAKC,IAAInpB,GACX,MAAM,IAAIpD,MAAM,iCAEhBssB,EAAKjU,IAAIjV,E,CAIbyE,OAAO2kB,QAAQppB,GAAS6R,SAAQ,EAAE7M,EAAKvH,MACrC,MAAMrB,EAAO,EAAW6sB,EAASjkB,EAAMA,EACvC,GAAqB,iBAAVvH,GACT,IAAAurB,qBAAoBvrB,EAAkCrB,EAAO,IAAK8sB,EAAMjlB,QACnE,GAAqB,iBAAVxG,GAAuC,iBAAVA,EAC7CwG,EAAQ7H,EAAMqB,EAAM4b,gBACf,IAAqB,kBAAV5b,EAGhB,MAAM,IAAIb,MAAM,0CAA0Ca,GAF1DwG,EAAQ7H,EAAM,EAAU,IAAM,I,IAIhC,C,g2BC1BR,eAGA,YACA,SAEMitB,EAAU,MAAiB,EAAAnsB,IAAIG,KAAKgrB,OAA6B,oBAAbnmB,SAC1D,IAAIonB,EAQAC,EACAC,EARAC,GAAe,EACfzjB,GAAc,EACdC,GAAU,EAOd,MAAMyjB,EAAiF,GACjFC,EAAuF,GACvFC,EAA+E,GAC/EC,EAAyD,GACzDC,EAA8D,GAC9DC,EAAuD,GAEvDC,EAAe,KACnB,GAAIP,IAAiBzjB,GAAeC,IAAYqjB,EAC9C,MAAM,IAAI1sB,MAAM,mB,EAIdqtB,EAAwBC,IAC5B,OAAQA,EAAGlrB,KAAKD,MACd,IAAK,YACH0qB,GAAe,EACXS,EAAGlrB,KAAKqH,KACVJ,GAAU,EACVsjB,EAAkB,GAAGW,EAAGlrB,KAAKqH,OAE7BL,GAAc,EACdujB,EAAkB,MAEpB,MACF,IAAK,WACCW,EAAGlrB,KAAKqH,IACVmjB,EAAiB,GAAGU,EAAGlrB,KAAKqH,KAE5BmjB,EAAiB,KAEnB,MACF,IAAK,kBACCU,EAAGlrB,KAAKqH,IACVqjB,EAA+Bvb,QAAS,GAAG+b,EAAGlrB,KAAKqH,KAEnDqjB,EAA+Bvb,QAAS,GAAG+b,EAAGlrB,KAAKmrB,KAErD,MACF,IAAK,kBACCD,EAAGlrB,KAAKqH,IACVsjB,EAA+Bxb,QAAS,GAAG+b,EAAGlrB,KAAKqH,KAEnDsjB,EAA+Bxb,QAAS,GAAG+b,EAAGlrB,KAAKmrB,KAErD,MACF,IAAK,SACCD,EAAGlrB,KAAKqH,IACVujB,EAAuBzb,QAAS,GAAG+b,EAAGlrB,KAAKqH,KAE3CujB,EAAuBzb,QAAS,GAAG+b,EAAGlrB,KAAKmrB,KAE7C,MACF,IAAK,UACCD,EAAGlrB,KAAKqH,IACVwjB,EAAwB1b,QAAS,GAAG+b,EAAGlrB,KAAKqH,KAE5CwjB,EAAwB1b,QAAS,KAEnC,MACF,IAAK,MACC+b,EAAGlrB,KAAKqH,IACVyjB,EAAa3b,QAAS,GAAG+b,EAAGlrB,KAAKqH,KAEjCyjB,EAAa3b,QAAS,GAAG+b,EAAGlrB,KAAKmrB,KAEnC,MACF,IAAK,gBACCD,EAAGlrB,KAAKqH,IACV0jB,EAAsB5b,QAAS,GAAG+b,EAAGlrB,KAAKqH,KAE1C0jB,EAAsB5b,QAAS,K,EAOjCic,EAAgC,oBAAbloB,SAAyE,QAA7C,EAAQ,OAARA,eAAQ,IAARA,cAAQ,EAARA,SAAU0E,qBAAmC,eAAExD,SAAMzG,EAE7F,EAAAgsB,SAAWzkB,UACtB,GAAsCmlB,IAAW,CAC/C,GAAIrjB,EACF,OAEF,GAAIyjB,EACF,MAAM,IAAI7sB,MAAM,4CAElB,GAAIqJ,EACF,MAAM,IAAIrJ,MAAM,yCAYlB,OATA6sB,GAAe,OAGY9sB,IAAvB,EAAAO,IAAIG,KAAKgtB,WACPD,GAA4C,IAA/BA,EAAUttB,QAAQ,WACjC,EAAAI,IAAIG,KAAKgtB,UAAYD,EAAU/f,OAAO,GAAI,EAAYC,YAAY,KAAO,IAItE,IAAIzH,SAAc,CAACC,EAASC,KACjCumB,SAAAA,EAAa3X,YAEb2X,EAAc,WACdA,EAAYvX,UAAYkY,EACxBV,EAAoB,CAACzmB,EAASC,GAC9B,MAAMmM,EAA0B,CAACnQ,KAAM,YAAaurB,GAAK,EAAAptB,IAAIG,MAC7DisB,EAAY7a,YAAYS,EAAQ,G,CAIlC,OAAO,IAAAqb,uBAAsB,EAAArtB,IAAIG,K,EAIxB,EAAAmtB,QAAUtmB,MAAMokB,EAAoBmC,KAC/C,GAAsCpB,IAEpC,OADAW,IACO,IAAInnB,SAAc,CAACC,EAASC,KACjCymB,EAAmB,CAAC1mB,EAASC,GAC7B,MAAMmM,EAA0B,CAACnQ,KAAM,WAAYurB,GAAK,CAAChC,aAAYmC,iBACrEnB,EAAa7a,YAAYS,EAAQ,IAGnCwb,EAAKF,QAAQlC,EAAYmC,E,EAIhB,EAAAE,sBAAwBzmB,MAAM0mB,GACHvB,KACpCW,IACO,IAAInnB,SAA+B,CAACC,EAASC,KAClD2mB,EAA+BzsB,KAAK,CAAC6F,EAASC,IAC9C,MAAMmM,EAA0B,CAACnQ,KAAM,kBAAmBurB,GAAK,CAACM,UAChEtB,EAAa7a,YAAYS,EAAS,CAAC0b,EAAM7qB,QAAQ,KAG5C2qB,EAAKC,sBAAsBC,GAIzB,EAAAC,sBAAwB3mB,MAAM4mB,EAAkC9qB,IAEjCqpB,KACpCW,IACO,IAAInnB,SAAqC,CAACC,EAASC,KACxD4mB,EAA+B1sB,KAAK,CAAC6F,EAASC,IAC9C,MAAMmM,EAA0B,CAACnQ,KAAM,kBAAmBurB,GAAK,CAACQ,YAAW9qB,YAC3EspB,EAAa7a,YAAYS,EAAQ,KAG5Bwb,EAAKG,sBAAsBC,EAAW9qB,GAIxC,EAAA+qB,cACT7mB,MAAM0mB,EAAmB5qB,IACWqpB,KACpCW,IACO,IAAInnB,SAAqC,CAACC,EAASC,KACxD6mB,EAAuB3sB,KAAK,CAAC6F,EAASC,IACtC,MAAMmM,EAA0B,CAACnQ,KAAM,SAAUurB,GAAK,CAACM,QAAO5qB,YAC9DspB,EAAa7a,YAAYS,EAAS,CAAC0b,EAAM7qB,QAAQ,KAG5C2qB,EAAKK,cAAcH,EAAO5qB,GAIxB,EAAAgrB,eAAiB9mB,MAAM+mB,IAClC,GAAsC5B,IAEpC,OADAW,IACO,IAAInnB,SAAc,CAACC,EAASC,KACjC8mB,EAAwB5sB,KAAK,CAAC6F,EAASC,IACvC,MAAMmM,EAA0B,CAACnQ,KAAM,UAAWurB,GAAKW,GACvD3B,EAAa7a,YAAYS,EAAQ,IAGnCwb,EAAKM,eAAeC,E,EAIX,EAAAnmB,IAAMZ,MACf+mB,EAAmBC,EAAwBC,EAA8BC,EACzEprB,IACoCqpB,KACpCW,IACO,IAAInnB,SAA8B,CAACC,EAASC,KACjD+mB,EAAa7sB,KAAK,CAAC6F,EAASC,IAC5B,MAAMmM,EAA0B,CAACnQ,KAAM,MAAOurB,GAAK,CAACW,YAAWC,eAAcC,SAAQC,gBAAeprB,YACpGspB,EAAa7a,YAAYS,EAASwb,EAAKW,2BAA2BF,GAAQ,KAGrET,EAAK5lB,IAAImmB,EAAWC,EAAcC,EAAQC,EAAeprB,GAIvD,EAAAyG,aAAevC,MAAM+mB,IAChC,GAAsC5B,IAEpC,OADAW,IACO,IAAInnB,SAAc,CAACC,EAASC,KACjCgnB,EAAsB9sB,KAAK,CAAC6F,EAASC,IACrC,MAAMmM,EAA0B,CAACnQ,KAAM,gBAAiBurB,GAAKW,GAC7D3B,EAAa7a,YAAYS,EAAQ,IAGnCwb,EAAKjkB,aAAawkB,E,sGC9NtB,eACA,SACA,SAEa,EAAAK,cAAiBtrB,IAC5B,MAAM3C,GAAO,IAAAkuB,eACb,IAAIC,EAAmB,EACvB,MAAMC,EAAmB,GAEnBC,EAA0C1rB,GAAW,CAAC,EAE5D,IACE,QAAkCrD,KAA9BqD,aAAO,EAAPA,EAAS2rB,kBACXD,EAAWC,iBAAmB,OACzB,GACiC,iBAA7B3rB,EAAQ2rB,mBAAkCjsB,OAAO6oB,UAAUvoB,EAAQ2rB,mBAC1E3rB,EAAQ2rB,iBAAmB,GAAK3rB,EAAQ2rB,iBAAmB,EAC7D,MAAM,IAAI/uB,MAAM,qCAAqCoD,EAAQ2rB,oBAG/D,QAAmChvB,KAA/BqD,aAAO,EAAPA,EAAS4rB,mBACXF,EAAWE,kBAAoB,OAC1B,GAAyC,iBAA9B5rB,EAAQ4rB,oBAAmClsB,OAAO6oB,UAAUvoB,EAAQ4rB,mBACpF,MAAM,IAAIhvB,MAAM,qCAAqCoD,EAAQ4rB,0BAGpCjvB,KAAvBqD,aAAO,EAAPA,EAAS2R,aACX+Z,EAAW/Z,WAAY,GAGzB,IAAIka,EAAgB,EAOpB,QANqBlvB,KAAjBqD,aAAO,EAAPA,EAAS8rB,OACXD,GAAgB,IAAAE,iBAAgB/rB,EAAQ8rB,IAAKL,IAG/CD,EAAmBnuB,EAAK6lB,qBACpBwI,EAAWC,iBAAmBD,EAAWE,oBAAsBF,EAAW/Z,UAAYka,GACjE,IAArBL,EACF,MAAM,IAAI5uB,MAAM,4BAclB,YAXuBD,KAAnBqD,aAAO,EAAPA,EAASgsB,SACX,IAAAhD,qBAAoBhpB,EAAQgsB,MAAO,GAAI,IAAIC,SAAoC,CAACjnB,EAAKvH,KACnF,MAAMyuB,GAAgB,IAAAH,iBAAgB/mB,EAAKymB,GACrCU,GAAkB,IAAAJ,iBAAgBtuB,EAAOguB,GAE/C,GAAqF,IAAjFpuB,EAAK+lB,sBAAsBoI,EAAkBU,EAAeC,GAC9D,MAAM,IAAIvvB,MAAM,iCAAiCoI,OAASvH,I,IAKzD,CAAC+tB,EAAkBC,E,CAC1B,MAAOrlB,GAKP,MAJyB,IAArBolB,GACFnuB,EAAKimB,sBAAsBkI,GAE7BC,EAAO5Z,QAAQxU,EAAK2mB,OACd5d,C,8HC5DV,eACA,SACA,SAGA,SAEA,IAAIgmB,EAqBJ,6CAMEloB,4BAA4BmoB,GAG1B,MAAMzhB,QAAiB+V,MAAM0L,GACvBtL,QAAoBnW,EAASmW,cACnC,OAAO,IAAA4J,uBAAsB,IAAIzsB,WAAW6iB,GAC9C,CAEA7c,gBAAgB0kB,EAAiC5oB,GAM/C,GALKosB,UACG,IAAA5B,SAAQ,EAAAttB,IAAIG,KAAKirB,WAlCT,CAAC9qB,IACnB,OAAQA,GACN,IAAK,UACH,OAAO,EACT,IAAK,OACH,OAAO,EACT,IAAK,UACH,OAAO,EACT,IAAK,QACH,OAAO,EACT,IAAK,QACH,OAAO,EACT,QACE,MAAM,IAAIZ,MAAM,8BAA8BY,K,EAqBV8uB,CAAY,EAAApvB,IAAIM,WACpD4uB,GAAU,GAGgB,iBAAjBxD,EACT,GAAqB,oBAAVjI,MAAuB,CAEhC,MAAMiK,QAAc,IAAA2B,WAAU,EAAAljB,SAAV,CAAoBuf,IACvCxrB,KAAK6tB,UAAW7tB,KAAKwH,WAAYxH,KAAKkH,mBAAqB,IAAAymB,eAAcH,EAAO5qB,E,KAC5E,CAGL,MAAMwsB,QAAyCpvB,KAAKutB,sBAAsB/B,IAEzExrB,KAAK6tB,UAAW7tB,KAAKwH,WAAYxH,KAAKkH,mBAAqB,IAAAumB,uBAAsB2B,EAAWxsB,E,MAG9F5C,KAAK6tB,UAAW7tB,KAAKwH,WAAYxH,KAAKkH,mBAAqB,IAAAymB,eAAcnC,EAAc5oB,EAE5F,CAEAkE,gBACE,OAAO,IAAA8mB,gBAAe5tB,KAAK6tB,UAC7B,CAEA/mB,UAAUC,EAAiCC,EAAqCpE,GAE9E,MAAMysB,EAAuB,GACvBvB,EAAyB,GAC/BzmB,OAAO2kB,QAAQjlB,GAAO0N,SAAQ6a,IAC5B,MAAMtwB,EAAOswB,EAAI,GACXC,EAASD,EAAI,GACbE,EAAQxvB,KAAKwH,WAAW9H,QAAQV,GACtC,IAAe,IAAXwwB,EACF,MAAM,IAAIhwB,MAAM,kBAAkBR,MAEpCqwB,EAAWxvB,KAAK0vB,GAChBzB,EAAajuB,KAAK2vB,EAAM,IAG1B,MAAMxB,EAA0B,GAChC3mB,OAAO2kB,QAAQhlB,GAASyN,SAAQ6a,IAC9B,MAAMtwB,EAAOswB,EAAI,GAEXE,EAAQxvB,KAAKkH,YAAYxH,QAAQV,GACvC,IAAe,IAAXwwB,EACF,MAAM,IAAIhwB,MAAM,mBAAmBR,MAErCgvB,EAAcnuB,KAAK2vB,EAAM,IAG3B,MAAMC,QACI,IAAA/nB,KAAI1H,KAAK6tB,UAAWC,EAAcuB,EAAW9mB,KAAIgB,GAAK,CAACA,EAAE5H,KAAM4H,EAAE1H,KAAM0H,EAAE3H,QAAQosB,EAAeprB,GAEpG8sB,EAAoC,CAAC,EAC3C,IAAK,IAAIjwB,EAAI,EAAGA,EAAIgwB,EAAQ7vB,OAAQH,IAClCiwB,EAAO1vB,KAAKkH,YAAY8mB,EAAcvuB,KAAO,IAAI,EAAA8B,OAAOkuB,EAAQhwB,GAAG,GAAIgwB,EAAQhwB,GAAG,GAAIgwB,EAAQhwB,GAAG,IAEnG,OAAOiwB,CACT,CAEAtmB,iBAEA,CAEAC,gBACO,IAAAA,cAAarJ,KAAK6tB,UACzB,E,yGC7GF,eACA,SACA,SAmEa,EAAA8B,kBAAqB/sB,IAChC,MAAM3C,GAAO,IAAAkuB,eACb,IAAIyB,EAAuB,EAC3B,MAAMvB,EAAmB,GAEnBwB,EAAkDjtB,GAAW,CAAC,EA5CzC,CAACA,IACvBA,EAAQgsB,QACXhsB,EAAQgsB,MAAQ,CAAC,GAEdhsB,EAAQgsB,MAAMkB,UACjBltB,EAAQgsB,MAAMkB,QAAU,CAAC,GAE3B,MAAMA,EAAUltB,EAAQgsB,MAAMkB,QACzBA,EAAQC,+BAEXD,EAAQC,6BAA+B,I,EAmCzCC,CAAqBH,GAErB,SAC0CtwB,KAApCqD,aAAO,EAAPA,EAASqtB,0BACXJ,EAAeI,uBAAyB,OAE1C,MAAMA,EA7EuB,CAACA,IAChC,OAAQA,GACN,IAAK,WACH,OAAO,EACT,IAAK,QACH,OAAO,EACT,IAAK,WACH,OAAO,EACT,IAAK,MACH,OAAO,GACT,QACE,MAAM,IAAIzwB,MAAM,yCAAyCywB,K,EAkE5BC,CAAyBL,EAAeI,6BAEpC1wB,KAA/BqD,aAAO,EAAPA,EAASutB,qBACXN,EAAeM,mBAAoB,QAGH5wB,KAA9BqD,aAAO,EAAPA,EAASwtB,oBACXP,EAAeO,kBAAmB,QAGL7wB,KAA3BqD,aAAO,EAAPA,EAASytB,iBACXR,EAAeQ,cAAgB,cAEjC,MAAMA,EA3Ee,CAACA,IACxB,OAAQA,GACN,IAAK,aACH,OAAO,EACT,IAAK,WACH,OAAO,EACT,QACE,MAAM,IAAI7wB,MAAM,+BAA+B6wB,K,EAoE3BC,CAAiBT,EAAeQ,eAEtD,IAAIE,EAAkB,EAKtB,QAJuBhxB,KAAnBqD,aAAO,EAAPA,EAAS4tB,SACXD,GAAkB,IAAA5B,iBAAgB/rB,EAAQ4tB,MAAOnC,SAGjB9uB,KAA9BqD,aAAO,EAAPA,EAAS2rB,kBACXsB,EAAetB,iBAAmB,OAC7B,GACiC,iBAA7B3rB,EAAQ2rB,mBAAkCjsB,OAAO6oB,UAAUvoB,EAAQ2rB,mBAC1E3rB,EAAQ2rB,iBAAmB,GAAK3rB,EAAQ2rB,iBAAmB,EAC7D,MAAM,IAAI/uB,MAAM,qCAAqCoD,EAAQ2rB,oBAG/D,QAAmChvB,KAA/BqD,aAAO,EAAPA,EAAS4rB,mBACXqB,EAAerB,kBAAoB,OAC9B,GAAyC,iBAA9B5rB,EAAQ4rB,oBAAmClsB,OAAO6oB,UAAUvoB,EAAQ4rB,mBACpF,MAAM,IAAIhvB,MAAM,qCAAqCoD,EAAQ4rB,qBAW/D,QARiCjvB,KAA7BqD,aAAO,EAAPA,EAAS6tB,mBACXZ,EAAeY,iBAAkB,GAGnCb,EAAuB3vB,EAAKkkB,yBACxB8L,IAA0BJ,EAAeM,oBAAsBN,EAAeO,iBAAmBC,IAC/FR,EAAeY,gBAAkB,EAAGF,EAAiBV,EAAetB,iBACtEsB,EAAerB,mBACU,IAAzBoB,EACF,MAAM,IAAIpwB,MAAM,gCAkBlB,OAfIoD,aAAO,EAAPA,EAAS0F,qBAlFb,EAACsnB,EAA8BtnB,EAC9B+lB,KACC,IAAK,MAAMqC,KAAMpoB,EAAoB,CACnC,IAAIqoB,EAAuB,iBAAPD,EAAkBA,EAAKA,EAAG1xB,KAG9C,OAAQ2xB,GACN,IAAK,UACHA,EAAS,UACT,MACF,IAAK,OACL,IAAK,MACH,SACF,QACE,MAAM,IAAInxB,MAAM,qBAAqBmxB,KAGzC,MAAMC,GAAmB,IAAAjC,iBAAgBgC,EAAQtC,GACjD,GAA0F,KAAtF,IAAAF,eAAc9J,4BAA4BuL,EAAsBgB,GAClE,MAAM,IAAIpxB,MAAM,oCAAoCmxB,I,GAgExDE,CAAsBjB,EAAsBhtB,EAAQ0F,mBAAoB+lB,QAGnD9uB,KAAnBqD,aAAO,EAAPA,EAASgsB,SACX,IAAAhD,qBAAoBhpB,EAAQgsB,MAAO,GAAI,IAAIC,SAAoC,CAACjnB,EAAKvH,KACnF,MAAMyuB,GAAgB,IAAAH,iBAAgB/mB,EAAKymB,GACrCU,GAAkB,IAAAJ,iBAAgBtuB,EAAOguB,GAE/C,GAA6F,IAAzFpuB,EAAKskB,0BAA0BqL,EAAsBd,EAAeC,GACtE,MAAM,IAAIvvB,MAAM,qCAAqCoI,OAASvH,I,IAK7D,CAACuvB,EAAsBvB,E,CAC9B,MAAOrlB,GAKP,MAJ6B,IAAzB4mB,GACF3vB,EAAKwkB,0BAA0BmL,GAEjCvB,EAAO5Z,QAAQxU,EAAK2mB,OACd5d,C,yGCtJV,eAEa,EAAA2lB,gBAAkB,CAAC/sB,EAAcysB,KAC5C,MAAMpuB,GAAO,IAAAkuB,eAEP2C,EAAa7wB,EAAKwqB,gBAAgB7oB,GAAQ,EAC1CmvB,EAAa9wB,EAAKymB,QAAQoK,GAIhC,OAHA7wB,EAAKuqB,aAAa5oB,EAAMmvB,EAAYD,GACpCzC,EAAOxuB,KAAKkxB,GAELA,CAAU,C,kOCPnB,eACA,SACA,SACA,SAOa,EAAA3D,QAAU,CAAClC,EAAoBmC,KAC1C,MAAM2D,GAAY,IAAA7C,eAAclK,SAASiH,EAAYmC,GACrD,GAAkB,IAAd2D,EACF,MAAM,IAAIxxB,MAAM,8CAA8CwxB,I,EASlE,MAAMC,EAAiB,IAAIrwB,IAMd,EAAA2sB,sBAAyBC,IACpC,MAAMvtB,GAAO,IAAAkuB,eACP+C,EAAkBjxB,EAAKymB,QAAQ8G,EAAMplB,YAE3C,OADAnI,EAAK2P,OAAOtO,IAAIksB,EAAO0D,GAChB,CAACA,EAAiB1D,EAAMplB,WAAW,EAG/B,EAAAqlB,sBACT,CAAC2B,EAAkCxsB,KACjC,MAAM3C,GAAO,IAAAkuB,eAEb,IAAIgD,EAAgB,EAChBvB,EAAuB,EACvBvB,EAAmB,GAEvB,IAIE,IAHCuB,EAAsBvB,IAAU,IAAAsB,mBAAkB/sB,GAEnDuuB,EAAgBlxB,EAAKykB,kBAAkB0K,EAAU,GAAIA,EAAU,GAAIQ,GAC7C,IAAlBuB,EACF,MAAM,IAAI3xB,MAAM,yB,SAGlBS,EAAK2mB,MAAMwI,EAAU,IACrBnvB,EAAKwkB,0BAA0BmL,GAC/BvB,EAAO5Z,QAAQxU,EAAK2mB,M,CAGtB,MAAMwK,EAAanxB,EAAK6kB,kBAAkBqM,GACpCE,EAAcpxB,EAAK+kB,mBAAmBmM,GAEtC3pB,EAAa,GACb8pB,EAAwB,GACxBpqB,EAAc,GACdqqB,EAAyB,GAC/B,IAAK,IAAI9xB,EAAI,EAAGA,EAAI2xB,EAAY3xB,IAAK,CACnC,MAAMT,EAAOiB,EAAKilB,iBAAiBiM,EAAe1xB,GAClD,GAAa,IAATT,EACF,MAAM,IAAIQ,MAAM,2BAElB8xB,EAAsBzxB,KAAKb,GAC3BwI,EAAW3H,KAAKI,EAAKsqB,aAAavrB,G,CAEpC,IAAK,IAAIS,EAAI,EAAGA,EAAI4xB,EAAa5xB,IAAK,CACpC,MAAMT,EAAOiB,EAAKmlB,kBAAkB+L,EAAe1xB,GACnD,GAAa,IAATT,EACF,MAAM,IAAIQ,MAAM,4BAElB+xB,EAAuB1xB,KAAKb,GAC5BkI,EAAYrH,KAAKI,EAAKsqB,aAAavrB,G,CAIrC,OADAiyB,EAAe3vB,IAAI6vB,EAAe,CAACA,EAAeG,EAAuBC,IAClE,CAACJ,EAAe3pB,EAAYN,EAAY,EAQxC,EAAAymB,cACT,CAACH,EAAmB5qB,KAClB,MAAMwsB,GAAmC,IAAA7B,uBAAsBC,GAC/D,OAAO,IAAAC,uBAAsB2B,EAAWxsB,EAAQ,EAGzC,EAAAgrB,eAAkBC,IAC7B,MAAM5tB,GAAO,IAAAkuB,eACP2B,EAAUmB,EAAehvB,IAAI4rB,GACnC,IAAKiC,EACH,MAAM,IAAItwB,MAAM,sBAElB,MAAM2xB,EAAgBrB,EAAQ,GACxBwB,EAAwBxB,EAAQ,GAChCyB,EAAyBzB,EAAQ,GAEvCwB,EAAsB7c,QAAQxU,EAAKqlB,UACnCiM,EAAuB9c,QAAQxU,EAAKqlB,UACpCrlB,EAAK2kB,mBAAmBuM,GACxBF,EAAeO,OAAO3D,EAAU,EA2BlC,MAAM4D,EAA8B9vB,IAClC,OAAQA,GACN,IAAK,OACH,OAAO,EACT,IAAK,QACH,OAAO,EACT,IAAK,OACH,OAAO,EACT,IAAK,QACH,OAAO,EACT,IAAK,SACH,OAAO,EACT,IAAK,QACH,OAAO,EACT,IAAK,SACH,OAAO,GACT,IAAK,UACH,OAAO,EACT,IAAK,UACH,OAAO,GACT,IAAK,SACH,OAAO,EACT,IAAK,QACH,OAAO,EACT,IAAK,SACH,OAAO,GAET,QACE,MAAM,IAAInC,MAAM,0BAA0BmC,K,EAI1C+vB,EAA8BC,IAClC,OAAQA,GACN,KAAK,EACH,MAAO,OACT,KAAK,EACH,MAAO,QACT,KAAK,EACH,MAAO,OACT,KAAK,EACH,MAAO,QACT,KAAK,EACH,MAAO,SACT,KAAK,EACH,MAAO,QACT,KAAK,GACH,MAAO,SACT,KAAK,EACH,MAAO,UACT,KAAK,GACH,MAAO,UACT,KAAK,EACH,MAAO,SACT,KAAK,EACH,MAAO,QACT,KAAK,GACH,MAAO,SAET,QACE,MAAM,IAAInyB,MAAM,0BAA0BmyB,K,EAI1CC,EAAiCjwB,IAGjC,OAAQA,GACN,IAAK,UACH,OAAOd,aACT,IAAK,QAUL,IAAK,OACH,OAAOC,WATT,IAAK,OACH,OAAOC,UACT,IAAK,SACH,OAAOC,YACT,IAAK,QACH,OAAOC,WACT,IAAK,QACH,OAAOC,WAGT,IAAK,UACH,OAAOC,aACT,IAAK,SACH,OAAOC,YACT,IAAK,QACH,OAAOb,cACT,IAAK,SACH,OAAOG,eACT,QACE,MAAM,IAAIlB,MAAM,qBAAqBmC,K,EAOlC,EAAA+F,IACT,CAACmmB,EAAmBC,EAAwBC,EAA8BC,EACzEprB,KACC,MAAM3C,GAAO,IAAAkuB,eACP2B,EAAUmB,EAAehvB,IAAI4rB,GACnC,IAAKiC,EACH,MAAM,IAAItwB,MAAM,sBAElB,MAAM2xB,EAAgBrB,EAAQ,GACxBwB,EAAwBxB,EAAQ,GAChCyB,EAAyBzB,EAAQ,GAEjCsB,EAAatD,EAAaluB,OAC1ByxB,EAAcrD,EAAcpuB,OAElC,IAAIwuB,EAAmB,EACnByD,EAA6B,GAEjC,MAAMC,EAAwB,GACxBC,EAAwB,GAE9B,KACG3D,EAAkByD,IAAoB,IAAA3D,eAActrB,GAGrD,IAAK,IAAInD,EAAI,EAAGA,EAAI2xB,EAAY3xB,IAAK,CACnC,MAAMuyB,EAAWjE,EAAOtuB,GAAG,GACrBoC,EAAOksB,EAAOtuB,GAAG,GACjBmC,EAAOmsB,EAAOtuB,GAAG,GAEvB,IAAIsxB,EACAkB,EAEJ,GAAInwB,MAAMC,QAAQH,GAAO,CAEvBqwB,EAAiB,EAAIrwB,EAAKhC,OAC1BmxB,EAAa9wB,EAAKymB,QAAQuL,GAC1BF,EAAYlyB,KAAKkxB,GACjB,IAAImB,EAAYnB,EAAa,EAC7B,IAAK,IAAItxB,EAAI,EAAGA,EAAImC,EAAKhC,OAAQH,IAAK,CACpC,GAAuB,iBAAZmC,EAAKnC,GACd,MAAM,IAAIJ,UAAU,wBAAwBI,qBAE9CQ,EAAK6P,QAAQoiB,MAAe,IAAAvD,iBAAgB/sB,EAAKnC,GAAIsyB,E,OAGvDE,EAAiBrwB,EAAKwG,WACtB2oB,EAAa9wB,EAAKymB,QAAQuL,GAC1BF,EAAYlyB,KAAKkxB,GACjB9wB,EAAK2P,OAAOtO,IAAI,IAAIR,WAAWc,EAAKe,OAAQf,EAAKuG,WAAY8pB,GAAiBlB,GAGhF,MAAMoB,EAAQlyB,EAAKmoB,YACbgK,EAAanyB,EAAKuoB,WAAW,EAAI3mB,EAAKjC,QAC5C,IACE,IAAIyyB,EAAWD,EAAa,EAC5BvwB,EAAK4S,SAAQ3J,GAAK7K,EAAK0P,OAAO0iB,KAAcvnB,IAC5C,MAAMykB,EAAStvB,EAAKulB,iBAChBiM,EAA2BO,GAAWjB,EAAYkB,EAAgBG,EAAYvwB,EAAKjC,QACvF,GAAe,IAAX2vB,EACF,MAAM,IAAI/vB,MAAM,yBAElBsyB,EAAYjyB,KAAK0vB,E,SAEjBtvB,EAAKqoB,aAAa6J,E,EAItB,MAAMG,EAAiBryB,EAAKmoB,YACtBmK,EAAoBtyB,EAAKuoB,WAAwB,EAAb4I,GACpCoB,EAAmBvyB,EAAKuoB,WAAwB,EAAb4I,GACnCqB,EAAqBxyB,EAAKuoB,WAAyB,EAAd6I,GACrCqB,EAAoBzyB,EAAKuoB,WAAyB,EAAd6I,GAE1C,IACE,IAAIsB,EAAmBJ,EAAoB,EACvCK,EAAkBJ,EAAmB,EACrCK,EAAoBJ,EAAqB,EACzCK,EAAmBJ,EAAoB,EAC3C,IAAK,IAAIjzB,EAAI,EAAGA,EAAI2xB,EAAY3xB,IAC9BQ,EAAK6P,QAAQ6iB,KAAsBb,EAAYryB,GAC/CQ,EAAK6P,QAAQ8iB,KAAqBtB,EAAsBxD,EAAaruB,IAEvE,IAAK,IAAIA,EAAI,EAAGA,EAAI4xB,EAAa5xB,IAC/BQ,EAAK6P,QAAQ+iB,KAAuB,EACpC5yB,EAAK6P,QAAQgjB,KAAsBvB,EAAuBvD,EAAcvuB,IAI1E,IAAIuxB,EAAY/wB,EAAKmmB,QACjB+K,EAAeqB,EAAkBD,EAAmBnB,EAAYsB,EAAmBrB,EACnFoB,EAAoBrE,GAExB,MAAM2E,EAA+B,GAErC,GAAkB,IAAd/B,EACF,IAAK,IAAIvxB,EAAI,EAAGA,EAAI4xB,EAAa5xB,IAAK,CACpC,MAAM8vB,EAAStvB,EAAK6P,QAAQ2iB,EAAqB,EAAIhzB,GAE/CuzB,EAA2B/yB,EAAKmoB,YAEhC6K,EAAmBhzB,EAAKuoB,WAAW,IAEzC,IAAI7mB,EAA6BovB,EAAa,EAC9C,IAGE,GAFAC,EAAY/wB,EAAKylB,kBACb6J,EAAQ0D,EAAkBA,EAAmB,EAAGA,EAAmB,EAAGA,EAAmB,IAC3E,IAAdjC,EACF,MAAM,IAAIxxB,MAAM,iDAAiDwxB,KAEnE,IAAIkC,EAAkBD,EAAmB,EACzC,MAAMjB,EAAW/xB,EAAK6P,QAAQojB,KAC9BnC,EAAa9wB,EAAK6P,QAAQojB,KAC1B,MAAMd,EAAanyB,EAAK6P,QAAQojB,KAC1BC,EAAalzB,EAAK6P,QAAQojB,KAC1BrxB,EAAO,GACb,IAAK,IAAIpC,EAAI,EAAGA,EAAI0zB,EAAY1zB,IAC9BoC,EAAKhC,KAAKI,EAAK6P,QAAQsiB,EAAa,EAAI3yB,IAE1CQ,EAAKqlB,SAAS8M,GAEd,MAAMhwB,EAAuB,IAAhBP,EAAKjC,OAAe,EAAIiC,EAAKuxB,QAAO,CAAClpB,EAAGe,IAAMf,EAAIe,IAE/D,GADAtJ,EAAO+vB,EAA2BM,GACrB,WAATrwB,EAAmB,CACrB,MAAM0xB,EAAuB,GAC7B,IAAInB,EAAYnB,EAAa,EAC7B,IAAK,IAAItxB,EAAI,EAAGA,EAAI2C,EAAM3C,IAAK,CAC7B,MAAM+D,EAASvD,EAAK6P,QAAQoiB,KACtBoB,EAAiB7zB,IAAM2C,EAAO,OAAI7C,EAAYU,EAAK6P,QAAQoiB,GAAa1uB,EAC9E6vB,EAAWxzB,KAAKI,EAAKsqB,aAAa/mB,EAAQ8vB,G,CAE5CP,EAAOlzB,KAAK,CAAC8B,EAAME,EAAMwxB,G,KACpB,CACL,MACMzxB,EAAO,IADiBgwB,EAA8BjwB,GAC/C,CAA0BS,GACvC,IAAItB,WAAWc,EAAKe,OAAQf,EAAKuG,WAAYvG,EAAKwG,YAC7C9G,IAAIrB,EAAK2P,OAAOT,SAAS4hB,EAAYA,EAAanvB,EAAKwG,aAC5D2qB,EAAOlzB,KAAK,CAAC8B,EAAME,EAAMD,G,UAG3B3B,EAAKqoB,aAAa0K,GACL,WAATrxB,GAAqBovB,GACvB9wB,EAAK2mB,MAAMmK,GAEb9wB,EAAK2lB,kBAAkB2J,E,EAK7B,GAAkB,IAAdyB,EACF,OAAO+B,EAEP,MAAM,IAAIvzB,MAAM,yCAAyCwxB,K,SAG3D/wB,EAAKqoB,aAAagK,E,UAGpBR,EAAYrd,QAAQxU,EAAK2lB,mBACzBmM,EAAYtd,QAAQxU,EAAK2mB,OAEzB3mB,EAAKimB,sBAAsBkI,GAC3ByD,EAAiBpd,QAAQxU,EAAK2mB,M,GAOzB,EAAAvd,aAAgBwkB,IAC3B,MAAM5tB,GAAO,IAAAkuB,eACP2B,EAAUmB,EAAehvB,IAAI4rB,GACnC,IAAKiC,EACH,MAAM,IAAItwB,MAAM,sBAElB,MAAM2xB,EAAgBrB,EAAQ,GAGxByD,EAAkBtzB,EAAKqmB,iBAAiB6K,GAC9C,GAAwB,IAApBoC,EACF,MAAM,IAAI/zB,MAAM,kCAElBS,EAAKqlB,SAASiO,EAAgB,EAGnB,EAAAtF,2BAA8BuF,IACzC,MAAMC,EAA6B,GACnC,IAAK,MAAMlE,KAAUiE,EAAS,CAC5B,MAAM5xB,EAAO2tB,EAAO,IACfztB,MAAMC,QAAQH,IAASA,EAAKe,QAC/B8wB,EAAQ5zB,KAAK+B,EAAKe,O,CAGtB,OAAO8wB,CAAO,C,m2BC5ahB,kBAIA,YAEMC,EAEgC,EAAQ,KAE9C,IAAIzzB,EACA2I,GAAc,EACdyjB,GAAe,EACfxjB,GAAU,EAEd,MAiDM8qB,EAAkB,CAACC,EAAkBC,IACrCA,EACKD,EAAU,8BAAgC,yBAE1CA,EAAU,qBAAuB,gBAI/B,EAAAzG,sBAAwBrmB,MAAMgtB,IACzC,GAAIlrB,EACF,OAAOnD,QAAQC,UAEjB,GAAI2mB,EACF,MAAM,IAAI7sB,MAAM,yDAElB,GAAIqJ,EACF,MAAM,IAAIrJ,MAAM,sDAGlB6sB,GAAe,EAGf,MAAM0H,EAAUD,EAAM/I,YAChBG,EAAa4I,EAAM5I,WACnBF,EAAO8I,EAAM9I,KAEb6I,EAAa3I,EAAa,GA3EH,MAC7B,IAEE,MAAiC,oBAAtBhjB,oBAMmB,oBAAnB8rB,iBACT,IAAIA,gBAAiBC,MAAM5iB,YAAY,IAAInJ,kBAAkB,IAKxDyG,YAAYulB,SAAS,IAAIpzB,WAAW,CACzC,EAAG,GAAI,IAAK,IAAK,EAAG,EAAI,EAAI,EAAG,EAAG,EAAG,EAAI,GAAI,EAAK,EAAI,EAAG,EAAG,EAAI,EAAG,EACnE,EAAG,EAAI,EAAK,EAAK,EAAG,GAAI,GAAI,EAAG,EAAG,EAAG,GAAI,EAAI,IAAK,GAAI,EAAG,EAAG,GAAI,M,CAElE,MAAOkI,GACP,OAAO,C,GAuD4BmrB,GAC/BP,EAAU5I,GApDM,MACtB,IAeE,OAAOrc,YAAYulB,SAAS,IAAIpzB,WAAW,CACzC,EAAK,GAAI,IAAK,IAAK,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,EAAK,GAAK,EAAG,GAAI,EACvF,IAAK,GAAI,IAAK,GAAK,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAI,EAAI,IAAK,IAAK,EAAG,GAAI,K,CAEzF,MAAOkI,GACP,OAAO,C,GA+BeorB,GAElBC,EAAgD,iBAApBP,EAAM7G,UAAyB6G,EAAM7G,eAAY1tB,EAC7E+0B,EAAeX,GAAgB,EAAOE,GACtCU,EAAuBZ,EAAgBC,EAASC,GAChDW,EAA8C,iBAApBV,EAAM7G,UAAyB6G,EAAM7G,UAAUsH,QAAwBh1B,EAEvG,IAAIk1B,GAAY,EAEhB,MAAMC,EAA8B,GAgEpC,GA7DIX,EAAU,GACZW,EAAM70B,KAAK,IAAI4F,SAASC,IACtB0Z,YAAW,KACTqV,GAAY,EACZ/uB,GAAS,GACRquB,EAAQ,KAKfW,EAAM70B,KAAK,IAAI4F,SAAQ,CAACC,EAASC,KAC/B,MAAMpH,EAAUs1B,EAAaH,EAAyB,UAChDiB,EAAiC,CACrC9oB,WAAY,CAAC+oB,EAAkBC,IACUhB,GAAce,EAASE,SAAS,eACnD,oBAATC,KACFC,IAAIC,gBAAgB,IAAIF,KAC3B,CAGE,EAAQ,MAEV,CAACpzB,KAAM,qBAGTizB,IAAaN,EAERE,QAAAA,GADgBH,QAAAA,EAAsBQ,GACTN,EAG/BM,EAAkBD,GAI7B,GAAuCf,EACrC,GAAoB,oBAATkB,KACTJ,EAAOhf,oBAAsBsZ,EAAK/lB,K,IAAgB,4BAC7C,CACL,MAAMgsB,EAAmB,yDAAyD32B,EAAQ0d,kBAC1F0Y,EAAOhf,oBAAsB,IAAIof,KAAK,CAACG,GAAmB,CAACvzB,KAAM,mB,CAIrEpD,EAAQo2B,GAAQlR,MAEZhlB,IACE4tB,GAAe,EACfzjB,GAAc,EACd3I,EAAOxB,EACPiH,GAAS,IAGVyvB,IACC9I,GAAe,EACfxjB,GAAU,EACVlD,EAAOwvB,EAAK,GACZ,WAGF1vB,QAAQ2vB,KAAKV,GAEfD,EACF,MAAM,IAAIj1B,MAAM,2DAA2Du0B,M,EAIlE,EAAA5F,YAAc,KACzB,GAAIvlB,GAAe3I,EACjB,OAAOA,EAGT,MAAM,IAAIT,MAAM,sCAAsC,EAG3C,EAAA61B,QAAU,K,OACjBzsB,GAAgByjB,GAAiBxjB,IACnCwjB,GAAe,EAEwB,QAAtC,EAAApsB,EAA+BiW,eAAO,SAAEof,sBACzCr1B,OAAOV,EAEP8sB,GAAe,EACfzjB,GAAc,EACdC,GAAU,E,qEC3LC,SAAS0sB,IACtB,OAAO,IAAO,m0wEAAy7yE,cAAUh2B,OAAWA,EAC99yE,C,uBCAAd,EAAOD,QAAU,SAAUg3B,EAASC,EAAmBC,EAAeC,GACpE,IAAIC,EAAch3B,MAAQuM,OAE1B,IACE,IACE,IAAI0qB,EAEJ,IAEEA,EAAO,IAAID,EAAYb,KAAK,CAACS,GAO/B,CANE,MAAOxsB,IAGP6sB,EAAO,IADWD,EAAYE,aAAeF,EAAYG,mBAAqBH,EAAYI,gBAAkBJ,EAAYK,gBAEnHC,OAAOV,GACZK,EAAOA,EAAKM,SACd,CAEA,IAAInB,EAAMY,EAAYZ,KAAOY,EAAYQ,UACrCC,EAAYrB,EAAIC,gBAAgBY,GAChCS,EAAS,IAAIV,EAAYH,GAAmBY,EAAWX,GAE3D,OADAV,EAAIuB,gBAAgBF,GACbC,CAGT,CAFE,MAAOttB,GACP,OAAO,IAAI4sB,EAAYH,GAAmB,+BAA+Be,OAAOC,mBAAmBjB,IAAWE,EAChH,CAOF,CANE,MAAO1sB,GACP,IAAK2sB,EACH,MAAMn2B,MAAM,kCAGd,OAAO,IAAIo2B,EAAYH,GAAmBE,EAAKD,EACjD,CACF,C,q0ECrCIgB,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBr3B,IAAjBs3B,EACH,OAAOA,EAAar4B,QAGrB,IAAIC,EAASi4B,EAAyBE,GAAY,CAGjDp4B,QAAS,CAAC,GAOX,OAHAs4B,EAAoBF,GAAU9uB,KAAKrJ,EAAOD,QAASC,EAAQA,EAAOD,QAASm4B,GAGpEl4B,EAAOD,OACf,C,OCrBAm4B,EAAoBltB,EAAKhL,IACxB,IAAIs4B,EAASt4B,GAAUA,EAAOu4B,WAC7B,IAAOv4B,EAAiB,QACxB,IAAM,EAEP,OADAk4B,EAAoB7rB,EAAEisB,EAAQ,CAAE7sB,EAAG6sB,IAC5BA,CAAM,ECLdJ,EAAoB7rB,EAAI,CAACtM,EAASy4B,KACjC,IAAI,IAAIrvB,KAAOqvB,EACXN,EAAoBtsB,EAAE4sB,EAAYrvB,KAAS+uB,EAAoBtsB,EAAE7L,EAASoJ,IAC5EP,OAAO6vB,eAAe14B,EAASoJ,EAAK,CAAEuvB,YAAY,EAAMl1B,IAAKg1B,EAAWrvB,IAE1E,ECND+uB,EAAoBzrB,EAAI,WACvB,GAA0B,iBAAfksB,WAAyB,OAAOA,WAC3C,IACC,OAAOp3B,MAAQ,IAAIq3B,SAAS,cAAb,EAGhB,CAFE,MAAOruB,GACR,GAAsB,iBAAXmC,OAAqB,OAAOA,MACxC,CACA,CAPuB,GCAxBwrB,EAAoBtsB,EAAI,CAACitB,EAAKC,IAAUlwB,OAAOyW,UAAUjW,eAAeC,KAAKwvB,EAAKC,GCClFZ,EAAoB5sB,EAAKvL,IACH,oBAAXg5B,QAA0BA,OAAOC,aAC1CpwB,OAAO6vB,eAAe14B,EAASg5B,OAAOC,YAAa,CAAEp3B,MAAO,WAE7DgH,OAAO6vB,eAAe14B,EAAS,aAAc,CAAE6B,OAAO,GAAO,ECFpCs2B,EAAoB,G", "sources": ["webpack://ort/webpack/universalModuleDefinition", "webpack://ort/../common/dist/lib/backend-impl.js", "webpack://ort/../common/dist/lib/env.js", "webpack://ort/../common/dist/lib/env-impl.js", "webpack://ort/../common/dist/lib/tensor-impl.js", "webpack://ort/../common/dist/lib/tensor.js", "webpack://ort/../common/dist/lib/inference-session-impl.js", "webpack://ort/../common/dist/lib/inference-session.js", "webpack://ort/./lib/wasm/binding/ort-wasm-threaded.min.js", "webpack://ort/./lib/wasm/binding/ort-wasm.js", "webpack://ort/./lib/backend-wasm.ts", "webpack://ort/./lib/index.ts", "webpack://ort/./lib/wasm/options-utils.ts", "webpack://ort/./lib/wasm/proxy-wrapper.ts", "webpack://ort/./lib/wasm/run-options.ts", "webpack://ort/./lib/wasm/session-handler.ts", "webpack://ort/./lib/wasm/session-options.ts", "webpack://ort/./lib/wasm/string-utils.ts", "webpack://ort/./lib/wasm/wasm-core-impl.ts", "webpack://ort/./lib/wasm/wasm-factory.ts", "webpack://ort/./lib/wasm/proxy-worker/main.ts", "webpack://ort/./node_modules/worker-loader/dist/runtime/inline.js", "webpack://ort/webpack/bootstrap", "webpack://ort/webpack/runtime/compat get default export", "webpack://ort/webpack/runtime/define property getters", "webpack://ort/webpack/runtime/global", "webpack://ort/webpack/runtime/hasOwnProperty shorthand", "webpack://ort/webpack/runtime/make namespace object", "webpack://ort/webpack/startup"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ort\"] = factory();\n\telse\n\t\troot[\"ort\"] = factory();\n})(self, () => {\nreturn ", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\nconst backends = {};\nconst backendsSortedByPriority = [];\n/**\n * Register a backend.\n *\n * @param name - the name as a key to lookup as an execution provider.\n * @param backend - the backend object.\n * @param priority - an integer indicating the priority of the backend. Higher number means higher priority. if priority\n * < 0, it will be considered as a 'beta' version and will not be used as a fallback backend by default.\n *\n * @internal\n */\nexport const registerBackend = (name, backend, priority) => {\n    if (backend && typeof backend.init === 'function' && typeof backend.createSessionHandler === 'function') {\n        const currentBackend = backends[name];\n        if (currentBackend === undefined) {\n            backends[name] = { backend, priority };\n        }\n        else if (currentBackend.priority > priority) {\n            // same name is already registered with a higher priority. skip registeration.\n            return;\n        }\n        else if (currentBackend.priority === priority) {\n            if (currentBackend.backend !== backend) {\n                throw new Error(`cannot register backend \"${name}\" using priority ${priority}`);\n            }\n        }\n        if (priority >= 0) {\n            const i = backendsSortedByPriority.indexOf(name);\n            if (i !== -1) {\n                backendsSortedByPriority.splice(i, 1);\n            }\n            for (let i = 0; i < backendsSortedByPriority.length; i++) {\n                if (backends[backendsSortedByPriority[i]].priority <= priority) {\n                    backendsSortedByPriority.splice(i, 0, name);\n                    return;\n                }\n            }\n            backendsSortedByPriority.push(name);\n        }\n        return;\n    }\n    throw new TypeError('not a valid backend');\n};\n/**\n * Resolve backend by specified hints.\n *\n * @param backendHints - a list of execution provider names to lookup. If omitted use registered backends as list.\n * @returns a promise that resolves to the backend.\n *\n * @internal\n */\nexport const resolveBackend = async (backendHints) => {\n    const backendNames = backendHints.length === 0 ? backendsSortedByPriority : backendHints;\n    const errors = [];\n    for (const backendName of backendNames) {\n        const backendInfo = backends[backendName];\n        if (backendInfo) {\n            if (backendInfo.initialized) {\n                return backendInfo.backend;\n            }\n            else if (backendInfo.aborted) {\n                continue; // current backend is unavailable; try next\n            }\n            const isInitializing = !!backendInfo.initPromise;\n            try {\n                if (!isInitializing) {\n                    backendInfo.initPromise = backendInfo.backend.init();\n                }\n                await backendInfo.initPromise;\n                backendInfo.initialized = true;\n                return backendInfo.backend;\n            }\n            catch (e) {\n                if (!isInitializing) {\n                    errors.push({ name: backendName, err: e });\n                }\n                backendInfo.aborted = true;\n            }\n            finally {\n                delete backendInfo.initPromise;\n            }\n        }\n    }\n    throw new Error(`no available backend found. ERR: ${errors.map(e => `[${e.name}] ${e.err}`).join(', ')}`);\n};\n//# sourceMappingURL=backend-impl.js.map", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\nimport { EnvImpl } from './env-impl';\n/**\n * Represent a set of flags as a global singleton.\n */\nexport const env = new EnvImpl();\n//# sourceMappingURL=env.js.map", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\nexport class EnvImpl {\n    constructor() {\n        this.wasm = {};\n        this.webgl = {};\n        this.logLevelInternal = 'warning';\n    }\n    // TODO standadize the getter and setter convention in env for other fields.\n    set logLevel(value) {\n        if (value === undefined) {\n            return;\n        }\n        if (typeof value !== 'string' || ['verbose', 'info', 'warning', 'error', 'fatal'].indexOf(value) === -1) {\n            throw new Error(`Unsupported logging level: ${value}`);\n        }\n        this.logLevelInternal = value;\n    }\n    get logLevel() {\n        return this.logLevelInternal;\n    }\n}\n//# sourceMappingURL=env-impl.js.map", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\nconst isBigInt64ArrayAvailable = typeof BigInt64Array !== 'undefined' && typeof BigInt64Array.from === 'function';\nconst isBigUint64ArrayAvailable = typeof BigUint64Array !== 'undefined' && typeof BigUint64Array.from === 'function';\n// a runtime map that maps type string to TypedArray constructor. Should match Tensor.DataTypeMap.\nconst NUMERIC_TENSOR_TYPE_TO_TYPEDARRAY_MAP = new Map([\n    ['float32', Float32Array],\n    ['uint8', Uint8Array],\n    ['int8', Int8Array],\n    ['uint16', Uint16Array],\n    ['int16', Int16Array],\n    ['int32', Int32Array],\n    ['bool', Uint8Array],\n    ['float64', Float64Array],\n    ['uint32', Uint32Array],\n]);\n// a runtime map that maps type string to TypedArray constructor. Should match Tensor.DataTypeMap.\nconst NUMERIC_TENSOR_TYPEDARRAY_TO_TYPE_MAP = new Map([\n    [Float32Array, 'float32'],\n    [Uint8Array, 'uint8'],\n    [Int8Array, 'int8'],\n    [Uint16Array, 'uint16'],\n    [Int16Array, 'int16'],\n    [Int32Array, 'int32'],\n    [Float64Array, 'float64'],\n    [Uint32Array, 'uint32'],\n]);\nif (isBigInt64ArrayAvailable) {\n    NUMERIC_TENSOR_TYPE_TO_TYPEDARRAY_MAP.set('int64', BigInt64Array);\n    NUMERIC_TENSOR_TYPEDARRAY_TO_TYPE_MAP.set(BigInt64Array, 'int64');\n}\nif (isBigUint64ArrayAvailable) {\n    NUMERIC_TENSOR_TYPE_TO_TYPEDARRAY_MAP.set('uint64', BigUint64Array);\n    NUMERIC_TENSOR_TYPEDARRAY_TO_TYPE_MAP.set(BigUint64Array, 'uint64');\n}\n/**\n * calculate size from dims.\n *\n * @param dims the dims array. May be an illegal input.\n */\nconst calculateSize = (dims) => {\n    let size = 1;\n    for (let i = 0; i < dims.length; i++) {\n        const dim = dims[i];\n        if (typeof dim !== 'number' || !Number.isSafeInteger(dim)) {\n            throw new TypeError(`dims[${i}] must be an integer, got: ${dim}`);\n        }\n        if (dim < 0) {\n            throw new RangeError(`dims[${i}] must be a non-negative integer, got: ${dim}`);\n        }\n        size *= dim;\n    }\n    return size;\n};\nexport class Tensor {\n    constructor(arg0, arg1, arg2) {\n        let type;\n        let data;\n        let dims;\n        // check whether arg0 is type or data\n        if (typeof arg0 === 'string') {\n            //\n            // Override: constructor(type, data, ...)\n            //\n            type = arg0;\n            dims = arg2;\n            if (arg0 === 'string') {\n                // string tensor\n                if (!Array.isArray(arg1)) {\n                    throw new TypeError('A string tensor\\'s data must be a string array.');\n                }\n                // we don't check whether every element in the array is string; this is too slow. we assume it's correct and\n                // error will be populated at inference\n                data = arg1;\n            }\n            else {\n                // numeric tensor\n                const typedArrayConstructor = NUMERIC_TENSOR_TYPE_TO_TYPEDARRAY_MAP.get(arg0);\n                if (typedArrayConstructor === undefined) {\n                    throw new TypeError(`Unsupported tensor type: ${arg0}.`);\n                }\n                if (Array.isArray(arg1)) {\n                    // use 'as any' here because TypeScript's check on type of 'SupportedTypedArrayConstructors.from()' produces\n                    // incorrect results.\n                    // 'typedArrayConstructor' should be one of the typed array prototype objects.\n                    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                    data = typedArrayConstructor.from(arg1);\n                }\n                else if (arg1 instanceof typedArrayConstructor) {\n                    data = arg1;\n                }\n                else {\n                    throw new TypeError(`A ${type} tensor's data must be type of ${typedArrayConstructor}`);\n                }\n            }\n        }\n        else {\n            //\n            // Override: constructor(data, ...)\n            //\n            dims = arg1;\n            if (Array.isArray(arg0)) {\n                // only boolean[] and string[] is supported\n                if (arg0.length === 0) {\n                    throw new TypeError('Tensor type cannot be inferred from an empty array.');\n                }\n                const firstElementType = typeof arg0[0];\n                if (firstElementType === 'string') {\n                    type = 'string';\n                    data = arg0;\n                }\n                else if (firstElementType === 'boolean') {\n                    type = 'bool';\n                    // 'arg0' is of type 'boolean[]'. Uint8Array.from(boolean[]) actually works, but typescript thinks this is\n                    // wrong type. We use 'as any' to make it happy.\n                    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                    data = Uint8Array.from(arg0);\n                }\n                else {\n                    throw new TypeError(`Invalid element type of data array: ${firstElementType}.`);\n                }\n            }\n            else {\n                // get tensor type from TypedArray\n                const mappedType = NUMERIC_TENSOR_TYPEDARRAY_TO_TYPE_MAP.get(arg0.constructor);\n                if (mappedType === undefined) {\n                    throw new TypeError(`Unsupported type for tensor data: ${arg0.constructor}.`);\n                }\n                type = mappedType;\n                data = arg0;\n            }\n        }\n        // type and data is processed, now processing dims\n        if (dims === undefined) {\n            // assume 1-D tensor if dims omitted\n            dims = [data.length];\n        }\n        else if (!Array.isArray(dims)) {\n            throw new TypeError('A tensor\\'s dims must be a number array');\n        }\n        // perform check\n        const size = calculateSize(dims);\n        if (size !== data.length) {\n            throw new Error(`Tensor's size(${size}) does not match data length(${data.length}).`);\n        }\n        this.dims = dims;\n        this.type = type;\n        this.data = data;\n        this.size = size;\n    }\n    // #endregion\n    /**\n     * Create a new tensor object from image object\n     *\n     * @param buffer - Extracted image buffer data - assuming RGBA format\n     * @param imageFormat - input image configuration - required configurations height, width, format\n     * @param tensorFormat - output tensor configuration - Default is RGB format\n     */\n    static bufferToTensor(buffer, options) {\n        if (buffer === undefined) {\n            throw new Error('Image buffer must be defined');\n        }\n        if (options.height === undefined || options.width === undefined) {\n            throw new Error('Image height and width must be defined');\n        }\n        const { height, width } = options;\n        const norm = options.norm;\n        let normMean;\n        let normBias;\n        if (norm === undefined || norm.mean === undefined) {\n            normMean = 255;\n        }\n        else {\n            normMean = norm.mean;\n        }\n        if (norm === undefined || norm.bias === undefined) {\n            normBias = 0;\n        }\n        else {\n            normBias = norm.bias;\n        }\n        const inputformat = options.bitmapFormat !== undefined ? options.bitmapFormat : 'RGBA';\n        // default value is RGBA since imagedata and HTMLImageElement uses it\n        const outputformat = options.tensorFormat !== undefined ?\n            (options.tensorFormat !== undefined ? options.tensorFormat : 'RGB') :\n            'RGB';\n        const offset = height * width;\n        const float32Data = outputformat === 'RGBA' ? new Float32Array(offset * 4) : new Float32Array(offset * 3);\n        // Default pointer assignments\n        let step = 4, rImagePointer = 0, gImagePointer = 1, bImagePointer = 2, aImagePointer = 3;\n        let rTensorPointer = 0, gTensorPointer = offset, bTensorPointer = offset * 2, aTensorPointer = -1;\n        // Updating the pointer assignments based on the input image format\n        if (inputformat === 'RGB') {\n            step = 3;\n            rImagePointer = 0;\n            gImagePointer = 1;\n            bImagePointer = 2;\n            aImagePointer = -1;\n        }\n        // Updating the pointer assignments based on the output tensor format\n        if (outputformat === 'RGBA') {\n            aTensorPointer = offset * 3;\n        }\n        else if (outputformat === 'RBG') {\n            rTensorPointer = 0;\n            bTensorPointer = offset;\n            gTensorPointer = offset * 2;\n        }\n        else if (outputformat === 'BGR') {\n            bTensorPointer = 0;\n            gTensorPointer = offset;\n            rTensorPointer = offset * 2;\n        }\n        for (let i = 0; i < offset; i++, rImagePointer += step, bImagePointer += step, gImagePointer += step, aImagePointer += step) {\n            float32Data[rTensorPointer++] = (buffer[rImagePointer] + normBias) / normMean;\n            float32Data[gTensorPointer++] = (buffer[gImagePointer] + normBias) / normMean;\n            float32Data[bTensorPointer++] = (buffer[bImagePointer] + normBias) / normMean;\n            if (aTensorPointer !== -1 && aImagePointer !== -1) {\n                float32Data[aTensorPointer++] = (buffer[aImagePointer] + normBias) / normMean;\n            }\n        }\n        // Float32Array -> ort.Tensor\n        const outputTensor = outputformat === 'RGBA' ? new Tensor('float32', float32Data, [1, 4, height, width]) :\n            new Tensor('float32', float32Data, [1, 3, height, width]);\n        return outputTensor;\n    }\n    static async fromImage(image, options) {\n        // checking the type of image object\n        const isHTMLImageEle = typeof (HTMLImageElement) !== 'undefined' && image instanceof HTMLImageElement;\n        const isImageDataEle = typeof (ImageData) !== 'undefined' && image instanceof ImageData;\n        const isImageBitmap = typeof (ImageBitmap) !== 'undefined' && image instanceof ImageBitmap;\n        const isURL = typeof (String) !== 'undefined' && (image instanceof String || typeof image === 'string');\n        let data;\n        let tensorConfig = {};\n        // filling and checking image configuration options\n        if (isHTMLImageEle) {\n            // HTMLImageElement - image object - format is RGBA by default\n            const canvas = document.createElement('canvas');\n            const pixels2DContext = canvas.getContext('2d');\n            if (pixels2DContext != null) {\n                let height = image.naturalHeight;\n                let width = image.naturalWidth;\n                if (options !== undefined && options.resizedHeight !== undefined && options.resizedWidth !== undefined) {\n                    height = options.resizedHeight;\n                    width = options.resizedWidth;\n                }\n                if (options !== undefined) {\n                    tensorConfig = options;\n                    if (options.tensorFormat !== undefined) {\n                        throw new Error('Image input config format must be RGBA for HTMLImageElement');\n                    }\n                    else {\n                        tensorConfig.tensorFormat = 'RGBA';\n                    }\n                    if (options.height !== undefined && options.height !== height) {\n                        throw new Error('Image input config height doesn\\'t match HTMLImageElement height');\n                    }\n                    else {\n                        tensorConfig.height = height;\n                    }\n                    if (options.width !== undefined && options.width !== width) {\n                        throw new Error('Image input config width doesn\\'t match HTMLImageElement width');\n                    }\n                    else {\n                        tensorConfig.width = width;\n                    }\n                }\n                else {\n                    tensorConfig.tensorFormat = 'RGBA';\n                    tensorConfig.height = height;\n                    tensorConfig.width = width;\n                }\n                canvas.width = width;\n                canvas.height = height;\n                pixels2DContext.drawImage(image, 0, 0, width, height);\n                data = pixels2DContext.getImageData(0, 0, width, height).data;\n            }\n            else {\n                throw new Error('Can not access image data');\n            }\n        }\n        else if (isImageDataEle) {\n            // ImageData - image object - format is RGBA by default\n            const format = 'RGBA';\n            let height;\n            let width;\n            if (options !== undefined && options.resizedWidth !== undefined && options.resizedHeight !== undefined) {\n                height = options.resizedHeight;\n                width = options.resizedWidth;\n            }\n            else {\n                height = image.height;\n                width = image.width;\n            }\n            if (options !== undefined) {\n                tensorConfig = options;\n                if (options.bitmapFormat !== undefined && options.bitmapFormat !== format) {\n                    throw new Error('Image input config format must be RGBA for ImageData');\n                }\n                else {\n                    tensorConfig.bitmapFormat = 'RGBA';\n                }\n            }\n            else {\n                tensorConfig.bitmapFormat = 'RGBA';\n            }\n            tensorConfig.height = height;\n            tensorConfig.width = width;\n            if (options !== undefined) {\n                const tempCanvas = document.createElement('canvas');\n                tempCanvas.width = width;\n                tempCanvas.height = height;\n                const pixels2DContext = tempCanvas.getContext('2d');\n                if (pixels2DContext != null) {\n                    pixels2DContext.putImageData(image, 0, 0);\n                    data = pixels2DContext.getImageData(0, 0, width, height).data;\n                }\n                else {\n                    throw new Error('Can not access image data');\n                }\n            }\n            else {\n                data = image.data;\n            }\n        }\n        else if (isImageBitmap) {\n            // ImageBitmap - image object - format must be provided by user\n            if (options === undefined) {\n                throw new Error('Please provide image config with format for Imagebitmap');\n            }\n            if (options.bitmapFormat !== undefined) {\n                throw new Error('Image input config format must be defined for ImageBitmap');\n            }\n            const pixels2DContext = document.createElement('canvas').getContext('2d');\n            if (pixels2DContext != null) {\n                const height = image.height;\n                const width = image.width;\n                pixels2DContext.drawImage(image, 0, 0, width, height);\n                data = pixels2DContext.getImageData(0, 0, width, height).data;\n                if (options !== undefined) {\n                    // using square brackets to avoid TS error - type 'never'\n                    if (options.height !== undefined && options.height !== height) {\n                        throw new Error('Image input config height doesn\\'t match ImageBitmap height');\n                    }\n                    else {\n                        tensorConfig.height = height;\n                    }\n                    // using square brackets to avoid TS error - type 'never'\n                    if (options.width !== undefined && options.width !== width) {\n                        throw new Error('Image input config width doesn\\'t match ImageBitmap width');\n                    }\n                    else {\n                        tensorConfig.width = width;\n                    }\n                }\n                else {\n                    tensorConfig.height = height;\n                    tensorConfig.width = width;\n                }\n                return Tensor.bufferToTensor(data, tensorConfig);\n            }\n            else {\n                throw new Error('Can not access image data');\n            }\n        }\n        else if (isURL) {\n            return new Promise((resolve, reject) => {\n                const canvas = document.createElement('canvas');\n                const context = canvas.getContext('2d');\n                if (!image || !context) {\n                    return reject();\n                }\n                const newImage = new Image();\n                newImage.crossOrigin = 'Anonymous';\n                newImage.src = image;\n                newImage.onload = () => {\n                    canvas.width = newImage.width;\n                    canvas.height = newImage.height;\n                    context.drawImage(newImage, 0, 0, canvas.width, canvas.height);\n                    const img = context.getImageData(0, 0, canvas.width, canvas.height);\n                    if (options !== undefined) {\n                        // using square brackets to avoid TS error - type 'never'\n                        if (options.height !== undefined && options.height !== canvas.height) {\n                            throw new Error('Image input config height doesn\\'t match ImageBitmap height');\n                        }\n                        else {\n                            tensorConfig.height = canvas.height;\n                        }\n                        // using square brackets to avoid TS error - type 'never'\n                        if (options.width !== undefined && options.width !== canvas.width) {\n                            throw new Error('Image input config width doesn\\'t match ImageBitmap width');\n                        }\n                        else {\n                            tensorConfig.width = canvas.width;\n                        }\n                    }\n                    else {\n                        tensorConfig.height = canvas.height;\n                        tensorConfig.width = canvas.width;\n                    }\n                    resolve(Tensor.bufferToTensor(img.data, tensorConfig));\n                };\n            });\n        }\n        else {\n            throw new Error('Input data provided is not supported - aborted tensor creation');\n        }\n        if (data !== undefined) {\n            return Tensor.bufferToTensor(data, tensorConfig);\n        }\n        else {\n            throw new Error('Input data provided is not supported - aborted tensor creation');\n        }\n    }\n    toImageData(options) {\n        var _a, _b;\n        const pixels2DContext = document.createElement('canvas').getContext('2d');\n        let image;\n        if (pixels2DContext != null) {\n            // Default values for height and width & format\n            const width = this.dims[3];\n            const height = this.dims[2];\n            const channels = this.dims[1];\n            const inputformat = options !== undefined ? (options.format !== undefined ? options.format : 'RGB') : 'RGB';\n            const normMean = options !== undefined ? (((_a = options.norm) === null || _a === void 0 ? void 0 : _a.mean) !== undefined ? options.norm.mean : 255) : 255;\n            const normBias = options !== undefined ? (((_b = options.norm) === null || _b === void 0 ? void 0 : _b.bias) !== undefined ? options.norm.bias : 0) : 0;\n            const offset = height * width;\n            if (options !== undefined) {\n                if (options.height !== undefined && options.height !== height) {\n                    throw new Error('Image output config height doesn\\'t match tensor height');\n                }\n                if (options.width !== undefined && options.width !== width) {\n                    throw new Error('Image output config width doesn\\'t match tensor width');\n                }\n                if (options.format !== undefined && (channels === 4 && options.format !== 'RGBA') ||\n                    (channels === 3 && (options.format !== 'RGB' && options.format !== 'BGR'))) {\n                    throw new Error('Tensor format doesn\\'t match input tensor dims');\n                }\n            }\n            // Default pointer assignments\n            const step = 4;\n            let rImagePointer = 0, gImagePointer = 1, bImagePointer = 2, aImagePointer = 3;\n            let rTensorPointer = 0, gTensorPointer = offset, bTensorPointer = offset * 2, aTensorPointer = -1;\n            // Updating the pointer assignments based on the input image format\n            if (inputformat === 'RGBA') {\n                rTensorPointer = 0;\n                gTensorPointer = offset;\n                bTensorPointer = offset * 2;\n                aTensorPointer = offset * 3;\n            }\n            else if (inputformat === 'RGB') {\n                rTensorPointer = 0;\n                gTensorPointer = offset;\n                bTensorPointer = offset * 2;\n            }\n            else if (inputformat === 'RBG') {\n                rTensorPointer = 0;\n                bTensorPointer = offset;\n                gTensorPointer = offset * 2;\n            }\n            image = pixels2DContext.createImageData(width, height);\n            for (let i = 0; i < height * width; rImagePointer += step, gImagePointer += step, bImagePointer += step, aImagePointer += step, i++) {\n                image.data[rImagePointer] = (this.data[rTensorPointer++] - normBias) * normMean; // R value\n                image.data[gImagePointer] = (this.data[gTensorPointer++] - normBias) * normMean; // G value\n                image.data[bImagePointer] = (this.data[bTensorPointer++] - normBias) * normMean; // B value\n                image.data[aImagePointer] =\n                    aTensorPointer === -1 ? 255 : (this.data[aTensorPointer++] - normBias) * normMean; // A value\n            }\n        }\n        else {\n            throw new Error('Can not access image data');\n        }\n        return image;\n    }\n    // #endregion\n    // #region tensor utilities\n    reshape(dims) {\n        return new Tensor(this.type, this.data, dims);\n    }\n}\n//# sourceMappingURL=tensor-impl.js.map", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\nimport { Tensor as TensorImpl } from './tensor-impl';\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport const Tensor = TensorImpl;\n//# sourceMappingURL=tensor.js.map", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\nimport { resolveBackend } from './backend-impl';\nimport { Tensor } from './tensor';\nexport class InferenceSession {\n    constructor(handler) {\n        this.handler = handler;\n    }\n    async run(feeds, arg1, arg2) {\n        const fetches = {};\n        let options = {};\n        // check inputs\n        if (typeof feeds !== 'object' || feeds === null || feeds instanceof Tensor || Array.isArray(feeds)) {\n            throw new TypeError('\\'feeds\\' must be an object that use input names as keys and OnnxValue as corresponding values.');\n        }\n        let isFetchesEmpty = true;\n        // determine which override is being used\n        if (typeof arg1 === 'object') {\n            if (arg1 === null) {\n                throw new TypeError('Unexpected argument[1]: cannot be null.');\n            }\n            if (arg1 instanceof Tensor) {\n                throw new TypeError('\\'fetches\\' cannot be a Tensor');\n            }\n            if (Array.isArray(arg1)) {\n                if (arg1.length === 0) {\n                    throw new TypeError('\\'fetches\\' cannot be an empty array.');\n                }\n                isFetchesEmpty = false;\n                // output names\n                for (const name of arg1) {\n                    if (typeof name !== 'string') {\n                        throw new TypeError('\\'fetches\\' must be a string array or an object.');\n                    }\n                    if (this.outputNames.indexOf(name) === -1) {\n                        throw new RangeError(`'fetches' contains invalid output name: ${name}.`);\n                    }\n                    fetches[name] = null;\n                }\n                if (typeof arg2 === 'object' && arg2 !== null) {\n                    options = arg2;\n                }\n                else if (typeof arg2 !== 'undefined') {\n                    throw new TypeError('\\'options\\' must be an object.');\n                }\n            }\n            else {\n                // decide whether arg1 is fetches or options\n                // if any output name is present and its value is valid OnnxValue, we consider it fetches\n                let isFetches = false;\n                const arg1Keys = Object.getOwnPropertyNames(arg1);\n                for (const name of this.outputNames) {\n                    if (arg1Keys.indexOf(name) !== -1) {\n                        const v = arg1[name];\n                        if (v === null || v instanceof Tensor) {\n                            isFetches = true;\n                            isFetchesEmpty = false;\n                            fetches[name] = v;\n                        }\n                    }\n                }\n                if (isFetches) {\n                    if (typeof arg2 === 'object' && arg2 !== null) {\n                        options = arg2;\n                    }\n                    else if (typeof arg2 !== 'undefined') {\n                        throw new TypeError('\\'options\\' must be an object.');\n                    }\n                }\n                else {\n                    options = arg1;\n                }\n            }\n        }\n        else if (typeof arg1 !== 'undefined') {\n            throw new TypeError('Unexpected argument[1]: must be \\'fetches\\' or \\'options\\'.');\n        }\n        // check if all inputs are in feed\n        for (const name of this.inputNames) {\n            if (typeof feeds[name] === 'undefined') {\n                throw new Error(`input '${name}' is missing in 'feeds'.`);\n            }\n        }\n        // if no fetches is specified, we use the full output names list\n        if (isFetchesEmpty) {\n            for (const name of this.outputNames) {\n                fetches[name] = null;\n            }\n        }\n        // feeds, fetches and options are prepared\n        const results = await this.handler.run(feeds, fetches, options);\n        const returnValue = {};\n        for (const key in results) {\n            if (Object.hasOwnProperty.call(results, key)) {\n                returnValue[key] = new Tensor(results[key].type, results[key].data, results[key].dims);\n            }\n        }\n        return returnValue;\n    }\n    static async create(arg0, arg1, arg2, arg3) {\n        // either load from a file or buffer\n        let filePathOrUint8Array;\n        let options = {};\n        if (typeof arg0 === 'string') {\n            filePathOrUint8Array = arg0;\n            if (typeof arg1 === 'object' && arg1 !== null) {\n                options = arg1;\n            }\n            else if (typeof arg1 !== 'undefined') {\n                throw new TypeError('\\'options\\' must be an object.');\n            }\n        }\n        else if (arg0 instanceof Uint8Array) {\n            filePathOrUint8Array = arg0;\n            if (typeof arg1 === 'object' && arg1 !== null) {\n                options = arg1;\n            }\n            else if (typeof arg1 !== 'undefined') {\n                throw new TypeError('\\'options\\' must be an object.');\n            }\n        }\n        else if (arg0 instanceof ArrayBuffer ||\n            (typeof SharedArrayBuffer !== 'undefined' && arg0 instanceof SharedArrayBuffer)) {\n            const buffer = arg0;\n            let byteOffset = 0;\n            let byteLength = arg0.byteLength;\n            if (typeof arg1 === 'object' && arg1 !== null) {\n                options = arg1;\n            }\n            else if (typeof arg1 === 'number') {\n                byteOffset = arg1;\n                if (!Number.isSafeInteger(byteOffset)) {\n                    throw new RangeError('\\'byteOffset\\' must be an integer.');\n                }\n                if (byteOffset < 0 || byteOffset >= buffer.byteLength) {\n                    throw new RangeError(`'byteOffset' is out of range [0, ${buffer.byteLength}).`);\n                }\n                byteLength = arg0.byteLength - byteOffset;\n                if (typeof arg2 === 'number') {\n                    byteLength = arg2;\n                    if (!Number.isSafeInteger(byteLength)) {\n                        throw new RangeError('\\'byteLength\\' must be an integer.');\n                    }\n                    if (byteLength <= 0 || byteOffset + byteLength > buffer.byteLength) {\n                        throw new RangeError(`'byteLength' is out of range (0, ${buffer.byteLength - byteOffset}].`);\n                    }\n                    if (typeof arg3 === 'object' && arg3 !== null) {\n                        options = arg3;\n                    }\n                    else if (typeof arg3 !== 'undefined') {\n                        throw new TypeError('\\'options\\' must be an object.');\n                    }\n                }\n                else if (typeof arg2 !== 'undefined') {\n                    throw new TypeError('\\'byteLength\\' must be a number.');\n                }\n            }\n            else if (typeof arg1 !== 'undefined') {\n                throw new TypeError('\\'options\\' must be an object.');\n            }\n            filePathOrUint8Array = new Uint8Array(buffer, byteOffset, byteLength);\n        }\n        else {\n            throw new TypeError('Unexpected argument[0]: must be \\'path\\' or \\'buffer\\'.');\n        }\n        // get backend hints\n        const eps = options.executionProviders || [];\n        const backendHints = eps.map(i => typeof i === 'string' ? i : i.name);\n        const backend = await resolveBackend(backendHints);\n        const handler = await backend.createSessionHandler(filePathOrUint8Array, options);\n        return new InferenceSession(handler);\n    }\n    startProfiling() {\n        this.handler.startProfiling();\n    }\n    endProfiling() {\n        this.handler.endProfiling();\n    }\n    get inputNames() {\n        return this.handler.inputNames;\n    }\n    get outputNames() {\n        return this.handler.outputNames;\n    }\n}\n//# sourceMappingURL=inference-session-impl.js.map", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\nimport { InferenceSession as InferenceSessionImpl } from './inference-session-impl';\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport const InferenceSession = InferenceSessionImpl;\n//# sourceMappingURL=inference-session.js.map", "var _scriptDir,t=(_scriptDir=\"undefined\"!=typeof document&&document.currentScript?document.currentScript.src:void 0,\"undefined\"!=typeof __filename&&(_scriptDir=_scriptDir||__filename),function(t){function n(){return E.buffer!=D&&z(E.buffer),j}function e(){return E.buffer!=D&&z(E.buffer),F}function r(){return E.buffer!=D&&z(E.buffer),U}function i(){return E.buffer!=D&&z(E.buffer),Y}function a(){return E.buffer!=D&&z(E.buffer),I}var u,o,c;t=t||{},u||(u=void 0!==t?t:{}),u.ready=new Promise((function(t,n){o=t,c=n}));var f,s,l,p,h,m,d=Object.assign({},u),y=\"./this.program\",b=(t,n)=>{throw n},g=\"object\"==typeof window,_=\"function\"==typeof importScripts,v=\"object\"==typeof process&&\"object\"==typeof process.versions&&\"string\"==typeof process.versions.node,w=u.ENVIRONMENT_IS_PTHREAD||!1,T=\"\";function O(t){return u.locateFile?u.locateFile(t,T):T+t}if(v){let t;T=_?require(\"path\").dirname(T)+\"/\":__dirname+\"/\",m=()=>{h||(p=require(\"fs\"),h=require(\"path\"))},f=function(t,n){return m(),t=h.normalize(t),p.readFileSync(t,n?void 0:\"utf8\")},l=t=>((t=f(t,!0)).buffer||(t=new Uint8Array(t)),t),s=(t,n,e)=>{m(),t=h.normalize(t),p.readFile(t,(function(t,r){t?e(t):n(r.buffer)}))},1<process.argv.length&&(y=process.argv[1].replace(/\\\\/g,\"/\")),process.argv.slice(2),process.on(\"uncaughtException\",(function(t){if(!(t instanceof ot))throw t})),process.on(\"unhandledRejection\",(function(t){throw t})),b=(t,n)=>{if(J())throw process.exitCode=t,n;n instanceof ot||x(\"exiting due to exception: \"+n),process.exit(t)},u.inspect=function(){return\"[Emscripten Module object]\"};try{t=require(\"worker_threads\")}catch(t){throw console.error('The \"worker_threads\" module is not supported in this node.js build - perhaps a newer version is needed?'),t}global.Worker=t.Worker}else(g||_)&&(_?T=self.location.href:\"undefined\"!=typeof document&&document.currentScript&&(T=document.currentScript.src),_scriptDir&&(T=_scriptDir),T=0!==T.indexOf(\"blob:\")?T.substr(0,T.replace(/[?#].*/,\"\").lastIndexOf(\"/\")+1):\"\",v||(f=t=>{var n=new XMLHttpRequest;return n.open(\"GET\",t,!1),n.send(null),n.responseText},_&&(l=t=>{var n=new XMLHttpRequest;return n.open(\"GET\",t,!1),n.responseType=\"arraybuffer\",n.send(null),new Uint8Array(n.response)}),s=(t,n,e)=>{var r=new XMLHttpRequest;r.open(\"GET\",t,!0),r.responseType=\"arraybuffer\",r.onload=()=>{200==r.status||0==r.status&&r.response?n(r.response):e()},r.onerror=e,r.send(null)}));v&&\"undefined\"==typeof performance&&(global.performance=require(\"perf_hooks\").performance);var S=console.log.bind(console),A=console.warn.bind(console);v&&(m(),S=t=>p.writeSync(1,t+\"\\n\"),A=t=>p.writeSync(2,t+\"\\n\"));var M,C=u.print||S,x=u.printErr||A;Object.assign(u,d),d=null,u.thisProgram&&(y=u.thisProgram),u.quit&&(b=u.quit),u.wasmBinary&&(M=u.wasmBinary);var R=u.noExitRuntime||!1;\"object\"!=typeof WebAssembly&&rt(\"no native wasm support detected\");var E,k,D,j,F,U,Y,I,W=!1,P=\"undefined\"!=typeof TextDecoder?new TextDecoder(\"utf8\"):void 0;function H(t,n,e){var r=(n>>>=0)+e;for(e=n;t[e]&&!(e>=r);)++e;if(16<e-n&&t.buffer&&P)return P.decode(t.buffer instanceof SharedArrayBuffer?t.slice(n,e):t.subarray(n,e));for(r=\"\";n<e;){var i=t[n++];if(128&i){var a=63&t[n++];if(192==(224&i))r+=String.fromCharCode((31&i)<<6|a);else{var u=63&t[n++];65536>(i=224==(240&i)?(15&i)<<12|a<<6|u:(7&i)<<18|a<<12|u<<6|63&t[n++])?r+=String.fromCharCode(i):(i-=65536,r+=String.fromCharCode(55296|i>>10,56320|1023&i))}}else r+=String.fromCharCode(i)}return r}function q(t,n){return(t>>>=0)?H(e(),t,n):\"\"}function B(t,n,e,r){if(!(0<r))return 0;var i=e>>>=0;r=e+r-1;for(var a=0;a<t.length;++a){var u=t.charCodeAt(a);if(55296<=u&&57343>=u&&(u=65536+((1023&u)<<10)|1023&t.charCodeAt(++a)),127>=u){if(e>=r)break;n[e++>>>0]=u}else{if(2047>=u){if(e+1>=r)break;n[e++>>>0]=192|u>>6}else{if(65535>=u){if(e+2>=r)break;n[e++>>>0]=224|u>>12}else{if(e+3>=r)break;n[e++>>>0]=240|u>>18,n[e++>>>0]=128|u>>12&63}n[e++>>>0]=128|u>>6&63}n[e++>>>0]=128|63&u}}return n[e>>>0]=0,e-i}function G(t){for(var n=0,e=0;e<t.length;++e){var r=t.charCodeAt(e);127>=r?n++:2047>=r?n+=2:55296<=r&&57343>=r?(n+=4,++e):n+=3}return n}function z(t){D=t,u.HEAP8=j=new Int8Array(t),u.HEAP16=new Int16Array(t),u.HEAP32=U=new Int32Array(t),u.HEAPU8=F=new Uint8Array(t),u.HEAPU16=new Uint16Array(t),u.HEAPU32=Y=new Uint32Array(t),u.HEAPF32=new Float32Array(t),u.HEAPF64=I=new Float64Array(t)}w&&(D=u.buffer);var N=u.INITIAL_MEMORY||16777216;if(w)E=u.wasmMemory,D=u.buffer;else if(u.wasmMemory)E=u.wasmMemory;else if(!((E=new WebAssembly.Memory({initial:N/65536,maximum:65536,shared:!0})).buffer instanceof SharedArrayBuffer))throw x(\"requested a shared WebAssembly.Memory but the returned buffer is not a SharedArrayBuffer, indicating that while the browser has SharedArrayBuffer it does not have WebAssembly threads support - you may need to set a flag\"),v&&console.log(\"(on node you may need: --experimental-wasm-threads --experimental-wasm-bulk-memory and also use a recent version)\"),Error(\"bad memory\");E&&(D=E.buffer),N=D.byteLength,z(D);var V,L=[],X=[],Z=[],$=[];function J(){return R||!1}function Q(){var t=u.preRun.shift();L.unshift(t)}var K,tt=0,nt=null,et=null;function rt(t){throw w?postMessage({cmd:\"onAbort\",arg:t}):u.onAbort&&u.onAbort(t),x(t=\"Aborted(\"+t+\")\"),W=!0,t=new WebAssembly.RuntimeError(t+\". Build with -sASSERTIONS for more info.\"),c(t),t}function it(){return K.startsWith(\"data:application/octet-stream;base64,\")}function at(){var t=K;try{if(t==K&&M)return new Uint8Array(M);if(l)return l(t);throw\"both async and sync fetching of the wasm failed\"}catch(t){rt(t)}}K=\"ort-wasm-threaded.wasm\",it()||(K=O(K));var ut={};function ot(t){this.name=\"ExitStatus\",this.message=\"Program terminated with exit(\"+t+\")\",this.status=t}function ct(t){(t=pt.Vb[t])||rt(),pt.mc(t)}function ft(t){var n=pt.Cc();if(!n)return 6;pt.ac.push(n),pt.Vb[t.Ub]=n,n.Ub=t.Ub;var e={cmd:\"run\",start_routine:t.Ic,arg:t.zc,pthread_ptr:t.Ub};return n.$b=()=>{e.time=performance.now(),n.postMessage(e,t.Nc)},n.loaded&&(n.$b(),delete n.$b),0}function st(t){if(w)return Vt(1,1,t);J()||(pt.oc(),u.onExit&&u.onExit(t),W=!0),b(t,new ot(t))}function lt(t,n){if(!n&&w)throw dt(t),\"unwind\";J()||w||(bn(),ht(Z),yn(0),en[1].length&&rn(1,10),en[2].length&&rn(2,10),pt.oc()),st(t)}var pt={Yb:[],ac:[],qc:[],Vb:{},fc:function(){w&&pt.Ec()},Pc:function(){},Ec:function(){pt.receiveObjectTransfer=pt.Gc,pt.threadInitTLS=pt.pc,pt.setExitStatus=pt.nc,R=!1},nc:function(){},oc:function(){for(var t of Object.values(pt.Vb))pt.mc(t);for(t of pt.Yb)t.terminate();pt.Yb=[]},mc:function(t){var n=t.Ub;delete pt.Vb[n],pt.Yb.push(t),pt.ac.splice(pt.ac.indexOf(t),1),t.Ub=0,Tn(n)},Gc:function(){},pc:function(){pt.qc.forEach((t=>t()))},Fc:function(t,n){t.onmessage=e=>{var r=(e=e.data).cmd;if(t.Ub&&(pt.Bc=t.Ub),e.targetThread&&e.targetThread!=hn()){var i=pt.Vb[e.Qc];i?i.postMessage(e,e.transferList):x('Internal error! Worker sent a message \"'+r+'\" to target pthread '+e.targetThread+\", but that thread no longer exists!\")}else\"processProxyingQueue\"===r?Ht(e.queue):\"spawnThread\"===r?ft(e):\"cleanupThread\"===r?ct(e.thread):\"killThread\"===r?(e=e.thread,r=pt.Vb[e],delete pt.Vb[e],r.terminate(),Tn(e),pt.ac.splice(pt.ac.indexOf(r),1),r.Ub=0):\"cancelThread\"===r?pt.Vb[e.thread].postMessage({cmd:\"cancel\"}):\"loaded\"===r?(t.loaded=!0,n&&n(t),t.$b&&(t.$b(),delete t.$b)):\"print\"===r?C(\"Thread \"+e.threadId+\": \"+e.text):\"printErr\"===r?x(\"Thread \"+e.threadId+\": \"+e.text):\"alert\"===r?alert(\"Thread \"+e.threadId+\": \"+e.text):\"setimmediate\"===e.target?t.postMessage(e):\"onAbort\"===r?u.onAbort&&u.onAbort(e.arg):r&&x(\"worker sent an unknown command \"+r);pt.Bc=void 0},t.onerror=t=>{throw x(\"worker sent an error! \"+t.filename+\":\"+t.lineno+\": \"+t.message),t},v&&(t.on(\"message\",(function(n){t.onmessage({data:n})})),t.on(\"error\",(function(n){t.onerror(n)})),t.on(\"detachedExit\",(function(){}))),t.postMessage({cmd:\"load\",urlOrBlob:u.mainScriptUrlOrBlob||_scriptDir,wasmMemory:E,wasmModule:k})},yc:function(){var t=O(\"ort-wasm-threaded.worker.js\");pt.Yb.push(new Worker(t))},Cc:function(){return 0==pt.Yb.length&&(pt.yc(),pt.Fc(pt.Yb[0])),pt.Yb.pop()}};function ht(t){for(;0<t.length;)t.shift()(u)}function mt(t){var n=Mn();return t=t(),Cn(n),t}function dt(t){if(w)return Vt(2,0,t);try{lt(t)}catch(t){t instanceof ot||\"unwind\"==t||b(1,t)}}u.PThread=pt,u.establishStackSpace=function(){var t=hn(),n=r()[t+44>>2>>>0];t=r()[t+48>>2>>>0],An(n,n-t),Cn(n)};var yt=[];function bt(t){var n=yt[t];return n||(t>=yt.length&&(yt.length=t+1),yt[t]=n=V.get(t)),n}u.invokeEntryPoint=function(t,n){t=bt(t)(n),J()?pt.nc(t):On(t)};var gt,_t,vt=[],wt=0,Tt=0;function Ot(t){this.Zb=t,this.Sb=t-24,this.xc=function(t){i()[this.Sb+4>>2>>>0]=t},this.bc=function(){return i()[this.Sb+4>>2>>>0]},this.wc=function(t){i()[this.Sb+8>>2>>>0]=t},this.Dc=function(){return i()[this.Sb+8>>2>>>0]},this.rc=function(){r()[this.Sb>>2>>>0]=0},this.hc=function(t){t=t?1:0,n()[this.Sb+12>>0>>>0]=t},this.uc=function(){return 0!=n()[this.Sb+12>>0>>>0]},this.ic=function(t){t=t?1:0,n()[this.Sb+13>>0>>>0]=t},this.kc=function(){return 0!=n()[this.Sb+13>>0>>>0]},this.fc=function(t,n){this.cc(0),this.xc(t),this.wc(n),this.rc(),this.hc(!1),this.ic(!1)},this.sc=function(){Atomics.add(r(),this.Sb>>2,1)},this.Hc=function(){return 1===Atomics.sub(r(),this.Sb>>2,1)},this.cc=function(t){i()[this.Sb+16>>2>>>0]=t},this.tc=function(){return i()[this.Sb+16>>2>>>0]},this.vc=function(){if(En(this.bc()))return i()[this.Zb>>2>>>0];var t=this.tc();return 0!==t?t:this.Zb}}function St(t){return dn(new Ot(t).Sb)}function At(t,n,e,r){return w?Vt(3,1,t,n,e,r):Mt(t,n,e,r)}function Mt(t,n,e,r){if(\"undefined\"==typeof SharedArrayBuffer)return x(\"Current environment does not support SharedArrayBuffer, pthreads are not available!\"),6;var i=[];return w&&0===i.length?At(t,n,e,r):(t={Ic:e,Ub:t,zc:r,Nc:i},w?(t.Oc=\"spawnThread\",postMessage(t,i),0):ft(t))}function Ct(t,n,e){return w?Vt(4,1,t,n,e):0}function xt(t,n){if(w)return Vt(5,1,t,n)}function Rt(t,n){if(w)return Vt(6,1,t,n)}function Et(t,n,e){if(w)return Vt(7,1,t,n,e)}function kt(t,n,e){return w?Vt(8,1,t,n,e):0}function Dt(t,n){if(w)return Vt(9,1,t,n)}function jt(t,n,e){if(w)return Vt(10,1,t,n,e)}function Ft(t,n,e,r){if(w)return Vt(11,1,t,n,e,r)}function Ut(t,n,e,r){if(w)return Vt(12,1,t,n,e,r)}function Yt(t,n,e,r){if(w)return Vt(13,1,t,n,e,r)}function It(t){if(w)return Vt(14,1,t)}function Wt(t,n){if(w)return Vt(15,1,t,n)}function Pt(t,n,e){if(w)return Vt(16,1,t,n,e)}function Ht(t){Atomics.store(r(),t>>2,1),hn()&&wn(t),Atomics.compareExchange(r(),t>>2,1,0)}function qt(t){return i()[t>>>2]+4294967296*r()[t+4>>>2]}function Bt(t,n,e,r,i,a){return w?Vt(17,1,t,n,e,r,i,a):-52}function Gt(t,n,e,r,i,a){if(w)return Vt(18,1,t,n,e,r,i,a)}function zt(t){var e=G(t)+1,r=mn(e);return r&&B(t,n(),r,e),r}function Nt(t,n,e){function a(t){return(t=t.toTimeString().match(/\\(([A-Za-z ]+)\\)$/))?t[1]:\"GMT\"}if(w)return Vt(19,1,t,n,e);var u=(new Date).getFullYear(),o=new Date(u,0,1),c=new Date(u,6,1);u=o.getTimezoneOffset();var f=c.getTimezoneOffset(),s=Math.max(u,f);r()[t>>2>>>0]=60*s,r()[n>>2>>>0]=Number(u!=f),t=a(o),n=a(c),t=zt(t),n=zt(n),f<u?(i()[e>>2>>>0]=t,i()[e+4>>2>>>0]=n):(i()[e>>2>>>0]=n,i()[e+4>>2>>>0]=t)}function Vt(t,n){var e=arguments.length-2,r=arguments;return mt((()=>{for(var i=xn(8*e),u=i>>3,o=0;o<e;o++){var c=r[2+o];a()[u+o>>>0]=c}return vn(t,e,i,n)}))}u.executeNotifiedProxyingQueue=Ht,_t=v?()=>{var t=process.hrtime();return 1e3*t[0]+t[1]/1e6}:w?()=>performance.now()-u.__performance_now_clock_drift:()=>performance.now();var Lt,Xt=[],Zt={};function $t(){if(!Lt){var t,n={USER:\"web_user\",LOGNAME:\"web_user\",PATH:\"/\",PWD:\"/\",HOME:\"/home/<USER>\",LANG:(\"object\"==typeof navigator&&navigator.languages&&navigator.languages[0]||\"C\").replace(\"-\",\"_\")+\".UTF-8\",_:y||\"./this.program\"};for(t in Zt)void 0===Zt[t]?delete n[t]:n[t]=Zt[t];var e=[];for(t in n)e.push(t+\"=\"+n[t]);Lt=e}return Lt}function Jt(t,e){if(w)return Vt(20,1,t,e);var r=0;return $t().forEach((function(a,u){var o=e+r;for(u=i()[t+4*u>>2>>>0]=o,o=0;o<a.length;++o)n()[u++>>0>>>0]=a.charCodeAt(o);n()[u>>0>>>0]=0,r+=a.length+1})),0}function Qt(t,n){if(w)return Vt(21,1,t,n);var e=$t();i()[t>>2>>>0]=e.length;var r=0;return e.forEach((function(t){r+=t.length+1})),i()[n>>2>>>0]=r,0}function Kt(t){return w?Vt(22,1,t):52}function tn(t,n,e,r){return w?Vt(23,1,t,n,e,r):52}function nn(t,n,e,r,i){return w?Vt(24,1,t,n,e,r,i):70}var en=[null,[],[]];function rn(t,n){var e=en[t];0===n||10===n?((1===t?C:x)(H(e,0)),e.length=0):e.push(n)}function an(t,n,r,a){if(w)return Vt(25,1,t,n,r,a);for(var u=0,o=0;o<r;o++){var c=i()[n>>2>>>0],f=i()[n+4>>2>>>0];n+=8;for(var s=0;s<f;s++)rn(t,e()[c+s>>>0]);u+=f}return i()[a>>2>>>0]=u,0}var un=0;function on(t){return 0==t%4&&(0!=t%100||0==t%400)}var cn=[31,29,31,30,31,30,31,31,30,31,30,31],fn=[31,28,31,30,31,30,31,31,30,31,30,31];function sn(t,e,i,a){function u(t,n,e){for(t=\"number\"==typeof t?t.toString():t||\"\";t.length<n;)t=e[0]+t;return t}function o(t,n){return u(t,n,\"0\")}function c(t,n){function e(t){return 0>t?-1:0<t?1:0}var r;return 0===(r=e(t.getFullYear()-n.getFullYear()))&&0===(r=e(t.getMonth()-n.getMonth()))&&(r=e(t.getDate()-n.getDate())),r}function f(t){switch(t.getDay()){case 0:return new Date(t.getFullYear()-1,11,29);case 1:return t;case 2:return new Date(t.getFullYear(),0,3);case 3:return new Date(t.getFullYear(),0,2);case 4:return new Date(t.getFullYear(),0,1);case 5:return new Date(t.getFullYear()-1,11,31);case 6:return new Date(t.getFullYear()-1,11,30)}}function s(t){var n=t.Wb;for(t=new Date(new Date(t.Xb+1900,0,1).getTime());0<n;){var e=t.getMonth(),r=(on(t.getFullYear())?cn:fn)[e];if(!(n>r-t.getDate())){t.setDate(t.getDate()+n);break}n-=r-t.getDate()+1,t.setDate(1),11>e?t.setMonth(e+1):(t.setMonth(0),t.setFullYear(t.getFullYear()+1))}return e=new Date(t.getFullYear()+1,0,4),n=f(new Date(t.getFullYear(),0,4)),e=f(e),0>=c(n,t)?0>=c(e,t)?t.getFullYear()+1:t.getFullYear():t.getFullYear()-1}var l=r()[a+40>>2>>>0];for(var p in a={Lc:r()[a>>2>>>0],Kc:r()[a+4>>2>>>0],dc:r()[a+8>>2>>>0],jc:r()[a+12>>2>>>0],ec:r()[a+16>>2>>>0],Xb:r()[a+20>>2>>>0],Tb:r()[a+24>>2>>>0],Wb:r()[a+28>>2>>>0],Rc:r()[a+32>>2>>>0],Jc:r()[a+36>>2>>>0],Mc:l?q(l):\"\"},i=q(i),l={\"%c\":\"%a %b %d %H:%M:%S %Y\",\"%D\":\"%m/%d/%y\",\"%F\":\"%Y-%m-%d\",\"%h\":\"%b\",\"%r\":\"%I:%M:%S %p\",\"%R\":\"%H:%M\",\"%T\":\"%H:%M:%S\",\"%x\":\"%m/%d/%y\",\"%X\":\"%H:%M:%S\",\"%Ec\":\"%c\",\"%EC\":\"%C\",\"%Ex\":\"%m/%d/%y\",\"%EX\":\"%H:%M:%S\",\"%Ey\":\"%y\",\"%EY\":\"%Y\",\"%Od\":\"%d\",\"%Oe\":\"%e\",\"%OH\":\"%H\",\"%OI\":\"%I\",\"%Om\":\"%m\",\"%OM\":\"%M\",\"%OS\":\"%S\",\"%Ou\":\"%u\",\"%OU\":\"%U\",\"%OV\":\"%V\",\"%Ow\":\"%w\",\"%OW\":\"%W\",\"%Oy\":\"%y\"})i=i.replace(new RegExp(p,\"g\"),l[p]);var h=\"Sunday Monday Tuesday Wednesday Thursday Friday Saturday\".split(\" \"),m=\"January February March April May June July August September October November December\".split(\" \");for(p in l={\"%a\":function(t){return h[t.Tb].substring(0,3)},\"%A\":function(t){return h[t.Tb]},\"%b\":function(t){return m[t.ec].substring(0,3)},\"%B\":function(t){return m[t.ec]},\"%C\":function(t){return o((t.Xb+1900)/100|0,2)},\"%d\":function(t){return o(t.jc,2)},\"%e\":function(t){return u(t.jc,2,\" \")},\"%g\":function(t){return s(t).toString().substring(2)},\"%G\":function(t){return s(t)},\"%H\":function(t){return o(t.dc,2)},\"%I\":function(t){return 0==(t=t.dc)?t=12:12<t&&(t-=12),o(t,2)},\"%j\":function(t){for(var n=0,e=0;e<=t.ec-1;n+=(on(t.Xb+1900)?cn:fn)[e++]);return o(t.jc+n,3)},\"%m\":function(t){return o(t.ec+1,2)},\"%M\":function(t){return o(t.Kc,2)},\"%n\":function(){return\"\\n\"},\"%p\":function(t){return 0<=t.dc&&12>t.dc?\"AM\":\"PM\"},\"%S\":function(t){return o(t.Lc,2)},\"%t\":function(){return\"\\t\"},\"%u\":function(t){return t.Tb||7},\"%U\":function(t){return o(Math.floor((t.Wb+7-t.Tb)/7),2)},\"%V\":function(t){var n=Math.floor((t.Wb+7-(t.Tb+6)%7)/7);if(2>=(t.Tb+371-t.Wb-2)%7&&n++,n)53==n&&(4==(e=(t.Tb+371-t.Wb)%7)||3==e&&on(t.Xb)||(n=1));else{n=52;var e=(t.Tb+7-t.Wb-1)%7;(4==e||5==e&&on(t.Xb%400-1))&&n++}return o(n,2)},\"%w\":function(t){return t.Tb},\"%W\":function(t){return o(Math.floor((t.Wb+7-(t.Tb+6)%7)/7),2)},\"%y\":function(t){return(t.Xb+1900).toString().substring(2)},\"%Y\":function(t){return t.Xb+1900},\"%z\":function(t){var n=0<=(t=t.Jc);return t=Math.abs(t)/60,(n?\"+\":\"-\")+String(\"0000\"+(t/60*100+t%60)).slice(-4)},\"%Z\":function(t){return t.Mc},\"%%\":function(){return\"%\"}},i=i.replace(/%%/g,\"\\0\\0\"),l)i.includes(p)&&(i=i.replace(new RegExp(p,\"g\"),l[p](a)));return p=function(t){var n=Array(G(t)+1);return B(t,n,0,n.length),n}(i=i.replace(/\\0\\0/g,\"%\")),p.length>e?0:(function(t,e){n().set(t,e>>>0)}(p,t),p.length-1)}pt.fc();var ln=[null,st,dt,At,Ct,xt,Rt,Et,kt,Dt,jt,Ft,Ut,Yt,It,Wt,Pt,Bt,Gt,Nt,Jt,Qt,Kt,tn,nn,an],pn={b:function(t){return mn(t+24)+24},n:function(t){return(t=new Ot(t)).uc()||(t.hc(!0),wt--),t.ic(!1),vt.push(t),t.sc(),t.vc()},ma:function(t){throw x(\"Unexpected exception thrown, this is not properly supported - aborting\"),W=!0,t},x:function(){Sn(0);var t=vt.pop();if(t.Hc()&&!t.kc()){var n=t.Dc();n&&bt(n)(t.Zb),St(t.Zb)}Tt=0},e:function(){var t=Tt;if(!t)return un=0;var n=new Ot(t);n.cc(t);var e=n.bc();if(!e)return un=0,t;for(var r=Array.prototype.slice.call(arguments),i=0;i<r.length;i++){var a=r[i];if(0===a||a===e)break;if(Rn(a,e,n.Sb+16))return un=a,t}return un=e,t},l:function(){var t=Tt;if(!t)return un=0;var n=new Ot(t);n.cc(t);var e=n.bc();if(!e)return un=0,t;for(var r=Array.prototype.slice.call(arguments),i=0;i<r.length;i++){var a=r[i];if(0===a||a===e)break;if(Rn(a,e,n.Sb+16))return un=a,t}return un=e,t},h:function(){var t=Tt;if(!t)return un=0;var n=new Ot(t);n.cc(t);var e=n.bc();if(!e)return un=0,t;for(var r=Array.prototype.slice.call(arguments),i=0;i<r.length;i++){var a=r[i];if(0===a||a===e)break;if(Rn(a,e,n.Sb+16))return un=a,t}return un=e,t},t:St,M:function(){var t=vt.pop();t||rt(\"no exception to throw\");var n=t.Zb;throw t.kc()||(vt.push(t),t.ic(!0),t.hc(!1),wt++),Tt=n,n},c:function(t,n,e){throw new Ot(t).fc(n,e),Tt=t,wt++,t},pa:function(){return wt},Fa:function(t){gn(t,!_,1,!g),pt.pc()},T:function(t){w?postMessage({cmd:\"cleanupThread\",thread:t}):ct(t)},xa:Mt,j:function(t){throw Tt||(Tt=t),t},H:Ct,Ma:xt,ua:Rt,wa:Et,oa:kt,Ka:Dt,Ca:jt,Ja:Ft,V:Ut,va:Yt,sa:It,La:Wt,ta:Pt,Ta:function(){},X:function(){rt(\"To use dlopen, you need enable dynamic linking, see https://github.com/emscripten-core/emscripten/wiki/Linking\")},Ua:function(){rt(\"To use dlopen, you need enable dynamic linking, see https://github.com/emscripten-core/emscripten/wiki/Linking\")},W:function(){return Date.now()},ya:function(){return 2097152},Oa:function(){return!0},za:function(t,n,e,r){if(t==n)setTimeout((()=>Ht(r)));else if(w)postMessage({targetThread:t,cmd:\"processProxyingQueue\",queue:r});else{if(!(t=pt.Vb[t]))return;t.postMessage({cmd:\"processProxyingQueue\",queue:r})}return 1},Ea:function(){return-1},Pa:function(t,n){t=new Date(1e3*qt(t)),r()[n>>2>>>0]=t.getUTCSeconds(),r()[n+4>>2>>>0]=t.getUTCMinutes(),r()[n+8>>2>>>0]=t.getUTCHours(),r()[n+12>>2>>>0]=t.getUTCDate(),r()[n+16>>2>>>0]=t.getUTCMonth(),r()[n+20>>2>>>0]=t.getUTCFullYear()-1900,r()[n+24>>2>>>0]=t.getUTCDay(),t=(t.getTime()-Date.UTC(t.getUTCFullYear(),0,1,0,0,0,0))/864e5|0,r()[n+28>>2>>>0]=t},Qa:function(t,n){t=new Date(1e3*qt(t)),r()[n>>2>>>0]=t.getSeconds(),r()[n+4>>2>>>0]=t.getMinutes(),r()[n+8>>2>>>0]=t.getHours(),r()[n+12>>2>>>0]=t.getDate(),r()[n+16>>2>>>0]=t.getMonth(),r()[n+20>>2>>>0]=t.getFullYear()-1900,r()[n+24>>2>>>0]=t.getDay();var e=new Date(t.getFullYear(),0,1),i=(t.getTime()-e.getTime())/864e5|0;r()[n+28>>2>>>0]=i,r()[n+36>>2>>>0]=-60*t.getTimezoneOffset(),i=new Date(t.getFullYear(),6,1).getTimezoneOffset(),t=0|(i!=(e=e.getTimezoneOffset())&&t.getTimezoneOffset()==Math.min(e,i)),r()[n+32>>2>>>0]=t},Ra:function(t){var n=new Date(r()[t+20>>2>>>0]+1900,r()[t+16>>2>>>0],r()[t+12>>2>>>0],r()[t+8>>2>>>0],r()[t+4>>2>>>0],r()[t>>2>>>0],0),e=r()[t+32>>2>>>0],i=n.getTimezoneOffset(),a=new Date(n.getFullYear(),0,1),u=new Date(n.getFullYear(),6,1).getTimezoneOffset(),o=a.getTimezoneOffset(),c=Math.min(o,u);return 0>e?r()[t+32>>2>>>0]=Number(u!=o&&c==i):0<e!=(c==i)&&(u=Math.max(o,u),n.setTime(n.getTime()+6e4*((0<e?c:u)-i))),r()[t+24>>2>>>0]=n.getDay(),e=(n.getTime()-a.getTime())/864e5|0,r()[t+28>>2>>>0]=e,r()[t>>2>>>0]=n.getSeconds(),r()[t+4>>2>>>0]=n.getMinutes(),r()[t+8>>2>>>0]=n.getHours(),r()[t+12>>2>>>0]=n.getDate(),r()[t+16>>2>>>0]=n.getMonth(),n.getTime()/1e3|0},Aa:Bt,Ba:Gt,Sa:function t(n,e,r){t.Ac||(t.Ac=!0,Nt(n,e,r))},y:function(){rt(\"\")},U:function(){if(!v&&!_){var t=\"Blocking on the main thread is very dangerous, see https://emscripten.org/docs/porting/pthreads.html#blocking-on-the-main-browser-thread\";gt||(gt={}),gt[t]||(gt[t]=1,v&&(t=\"warning: \"+t),x(t))}},ra:function(){return 4294901760},B:_t,Ia:function(t,n,r){e().copyWithin(t>>>0,n>>>0,n+r>>>0)},F:function(){return v?require(\"os\").cpus().length:navigator.hardwareConcurrency},Da:function(t,n,e){Xt.length=n,e>>=3;for(var r=0;r<n;r++)Xt[r]=a()[e+r>>>0];return(0>t?ut[-t-1]:ln[t]).apply(null,Xt)},qa:function(t){var n=e().length;if((t>>>=0)<=n||4294901760<t)return!1;for(var r=1;4>=r;r*=2){var i=n*(1+.2/r);i=Math.min(i,t+100663296);var a=Math;i=Math.max(t,i),a=a.min.call(a,4294901760,i+(65536-i%65536)%65536);t:{try{E.grow(a-D.byteLength+65535>>>16),z(E.buffer);var u=1;break t}catch(t){}u=void 0}if(u)return!0}return!1},Na:function(){throw\"unwind\"},Ga:Jt,Ha:Qt,J:lt,I:Kt,S:tn,ga:nn,R:an,d:function(){return un},na:function t(e,r){t.lc||(t.lc=function(){if(\"object\"==typeof crypto&&\"function\"==typeof crypto.getRandomValues){var t=new Uint8Array(1);return()=>(crypto.getRandomValues(t),t[0])}if(v)try{var n=require(\"crypto\");return()=>n.randomBytes(1)[0]}catch(t){}return()=>rt(\"randomDevice\")}());for(var i=0;i<r;i++)n()[e+i>>0>>>0]=t.lc();return 0},ia:function(t,n,e){var r=Mn();try{return bt(t)(n,e)}catch(t){if(Cn(r),t!==t+0)throw t;Sn(1,0)}},ja:function(t,n,e){var r=Mn();try{return bt(t)(n,e)}catch(t){if(Cn(r),t!==t+0)throw t;Sn(1,0)}},K:function(t){var n=Mn();try{return bt(t)()}catch(t){if(Cn(n),t!==t+0)throw t;Sn(1,0)}},f:function(t,n){var e=Mn();try{return bt(t)(n)}catch(t){if(Cn(e),t!==t+0)throw t;Sn(1,0)}},P:function(t,n,e){var r=Mn();try{return bt(t)(n,e)}catch(t){if(Cn(r),t!==t+0)throw t;Sn(1,0)}},Q:function(t,n,e){var r=Mn();try{return bt(t)(n,e)}catch(t){if(Cn(r),t!==t+0)throw t;Sn(1,0)}},k:function(t,n,e){var r=Mn();try{return bt(t)(n,e)}catch(t){if(Cn(r),t!==t+0)throw t;Sn(1,0)}},p:function(t,n,e,r){var i=Mn();try{return bt(t)(n,e,r)}catch(t){if(Cn(i),t!==t+0)throw t;Sn(1,0)}},q:function(t,n,e,r,i){var a=Mn();try{return bt(t)(n,e,r,i)}catch(t){if(Cn(a),t!==t+0)throw t;Sn(1,0)}},N:function(t,n,e,r,i,a){var u=Mn();try{return bt(t)(n,e,r,i,a)}catch(t){if(Cn(u),t!==t+0)throw t;Sn(1,0)}},s:function(t,n,e,r,i,a){var u=Mn();try{return bt(t)(n,e,r,i,a)}catch(t){if(Cn(u),t!==t+0)throw t;Sn(1,0)}},w:function(t,n,e,r,i,a,u){var o=Mn();try{return bt(t)(n,e,r,i,a,u)}catch(t){if(Cn(o),t!==t+0)throw t;Sn(1,0)}},L:function(t,n,e,r,i,a,u,o){var c=Mn();try{return bt(t)(n,e,r,i,a,u,o)}catch(t){if(Cn(c),t!==t+0)throw t;Sn(1,0)}},E:function(t,n,e,r,i,a,u,o,c,f,s,l){var p=Mn();try{return bt(t)(n,e,r,i,a,u,o,c,f,s,l)}catch(t){if(Cn(p),t!==t+0)throw t;Sn(1,0)}},aa:function(t,n,e,r,i,a,u,o){var c=Mn();try{return Pn(t,n,e,r,i,a,u,o)}catch(t){if(Cn(c),t!==t+0)throw t;Sn(1,0)}},_:function(t,n,e,r,i,a,u){var o=Mn();try{return Dn(t,n,e,r,i,a,u)}catch(t){if(Cn(o),t!==t+0)throw t;Sn(1,0)}},Z:function(t,n,e,r,i){var a=Mn();try{return Hn(t,n,e,r,i)}catch(t){if(Cn(a),t!==t+0)throw t;Sn(1,0)}},ca:function(t,n,e,r){var i=Mn();try{return In(t,n,e,r)}catch(t){if(Cn(i),t!==t+0)throw t;Sn(1,0)}},$:function(t){var n=Mn();try{return kn(t)}catch(t){if(Cn(n),t!==t+0)throw t;Sn(1,0)}},ba:function(t,n){var e=Mn();try{return Wn(t,n)}catch(t){if(Cn(e),t!==t+0)throw t;Sn(1,0)}},Y:function(t,n,e){var r=Mn();try{return jn(t,n,e)}catch(t){if(Cn(r),t!==t+0)throw t;Sn(1,0)}},g:function(t){var n=Mn();try{bt(t)()}catch(t){if(Cn(n),t!==t+0)throw t;Sn(1,0)}},r:function(t,n){var e=Mn();try{bt(t)(n)}catch(t){if(Cn(e),t!==t+0)throw t;Sn(1,0)}},i:function(t,n,e){var r=Mn();try{bt(t)(n,e)}catch(t){if(Cn(r),t!==t+0)throw t;Sn(1,0)}},ha:function(t,n,e,r){var i=Mn();try{bt(t)(n,e,r)}catch(t){if(Cn(i),t!==t+0)throw t;Sn(1,0)}},m:function(t,n,e,r){var i=Mn();try{bt(t)(n,e,r)}catch(t){if(Cn(i),t!==t+0)throw t;Sn(1,0)}},v:function(t,n,e,r,i){var a=Mn();try{bt(t)(n,e,r,i)}catch(t){if(Cn(a),t!==t+0)throw t;Sn(1,0)}},u:function(t,n,e,r,i,a){var u=Mn();try{bt(t)(n,e,r,i,a)}catch(t){if(Cn(u),t!==t+0)throw t;Sn(1,0)}},O:function(t,n,e,r,i,a,u){var o=Mn();try{bt(t)(n,e,r,i,a,u)}catch(t){if(Cn(o),t!==t+0)throw t;Sn(1,0)}},A:function(t,n,e,r,i,a,u,o){var c=Mn();try{bt(t)(n,e,r,i,a,u,o)}catch(t){if(Cn(c),t!==t+0)throw t;Sn(1,0)}},ka:function(t,n,e,r,i,a,u,o,c){var f=Mn();try{bt(t)(n,e,r,i,a,u,o,c)}catch(t){if(Cn(f),t!==t+0)throw t;Sn(1,0)}},C:function(t,n,e,r,i,a,u,o,c,f,s){var l=Mn();try{bt(t)(n,e,r,i,a,u,o,c,f,s)}catch(t){if(Cn(l),t!==t+0)throw t;Sn(1,0)}},D:function(t,n,e,r,i,a,u,o,c,f,s,l,p,h,m,d){var y=Mn();try{bt(t)(n,e,r,i,a,u,o,c,f,s,l,p,h,m,d)}catch(t){if(Cn(y),t!==t+0)throw t;Sn(1,0)}},fa:function(t,n,e,r,i,a,u,o){var c=Mn();try{Fn(t,n,e,r,i,a,u,o)}catch(t){if(Cn(c),t!==t+0)throw t;Sn(1,0)}},da:function(t,n,e,r,i,a,u,o,c,f,s,l){var p=Mn();try{Yn(t,n,e,r,i,a,u,o,c,f,s,l)}catch(t){if(Cn(p),t!==t+0)throw t;Sn(1,0)}},ea:function(t,n,e,r,i,a){var u=Mn();try{Un(t,n,e,r,i,a)}catch(t){if(Cn(u),t!==t+0)throw t;Sn(1,0)}},o:function(t){return t},a:E||u.wasmMemory,G:function(t){un=t},la:sn,z:function(t,n,e,r){return sn(t,n,e,r)}};!function(){function t(t,n){u.asm=t.exports,pt.qc.push(u.asm.sb),V=u.asm.ub,X.unshift(u.asm.Va),k=n,w||(tt--,u.monitorRunDependencies&&u.monitorRunDependencies(tt),0==tt&&(null!==nt&&(clearInterval(nt),nt=null),et&&(t=et,et=null,t())))}function n(n){t(n.instance,n.module)}function e(t){return function(){if(!M&&(g||_)){if(\"function\"==typeof fetch&&!K.startsWith(\"file://\"))return fetch(K,{credentials:\"same-origin\"}).then((function(t){if(!t.ok)throw\"failed to load wasm binary file at '\"+K+\"'\";return t.arrayBuffer()})).catch((function(){return at()}));if(s)return new Promise((function(t,n){s(K,(function(n){t(new Uint8Array(n))}),n)}))}return Promise.resolve().then((function(){return at()}))}().then((function(t){return WebAssembly.instantiate(t,r)})).then((function(t){return t})).then(t,(function(t){x(\"failed to asynchronously prepare wasm: \"+t),rt(t)}))}var r={a:pn};if(w||(tt++,u.monitorRunDependencies&&u.monitorRunDependencies(tt)),u.instantiateWasm)try{return u.instantiateWasm(r,t)}catch(t){return x(\"Module.instantiateWasm callback failed with error: \"+t),!1}(M||\"function\"!=typeof WebAssembly.instantiateStreaming||it()||K.startsWith(\"file://\")||v||\"function\"!=typeof fetch?e(n):fetch(K,{credentials:\"same-origin\"}).then((function(t){return WebAssembly.instantiateStreaming(t,r).then(n,(function(t){return x(\"wasm streaming compile failed: \"+t),x(\"falling back to ArrayBuffer instantiation\"),e(n)}))}))).catch(c)}(),u.___wasm_call_ctors=function(){return(u.___wasm_call_ctors=u.asm.Va).apply(null,arguments)},u._OrtInit=function(){return(u._OrtInit=u.asm.Wa).apply(null,arguments)},u._OrtCreateSessionOptions=function(){return(u._OrtCreateSessionOptions=u.asm.Xa).apply(null,arguments)},u._OrtAppendExecutionProvider=function(){return(u._OrtAppendExecutionProvider=u.asm.Ya).apply(null,arguments)},u._OrtAddSessionConfigEntry=function(){return(u._OrtAddSessionConfigEntry=u.asm.Za).apply(null,arguments)},u._OrtReleaseSessionOptions=function(){return(u._OrtReleaseSessionOptions=u.asm._a).apply(null,arguments)},u._OrtCreateSession=function(){return(u._OrtCreateSession=u.asm.$a).apply(null,arguments)},u._OrtReleaseSession=function(){return(u._OrtReleaseSession=u.asm.ab).apply(null,arguments)},u._OrtGetInputCount=function(){return(u._OrtGetInputCount=u.asm.bb).apply(null,arguments)},u._OrtGetOutputCount=function(){return(u._OrtGetOutputCount=u.asm.cb).apply(null,arguments)},u._OrtGetInputName=function(){return(u._OrtGetInputName=u.asm.db).apply(null,arguments)},u._OrtGetOutputName=function(){return(u._OrtGetOutputName=u.asm.eb).apply(null,arguments)},u._OrtFree=function(){return(u._OrtFree=u.asm.fb).apply(null,arguments)},u._OrtCreateTensor=function(){return(u._OrtCreateTensor=u.asm.gb).apply(null,arguments)},u._OrtGetTensorData=function(){return(u._OrtGetTensorData=u.asm.hb).apply(null,arguments)},u._OrtReleaseTensor=function(){return(u._OrtReleaseTensor=u.asm.ib).apply(null,arguments)},u._OrtCreateRunOptions=function(){return(u._OrtCreateRunOptions=u.asm.jb).apply(null,arguments)},u._OrtAddRunConfigEntry=function(){return(u._OrtAddRunConfigEntry=u.asm.kb).apply(null,arguments)},u._OrtReleaseRunOptions=function(){return(u._OrtReleaseRunOptions=u.asm.lb).apply(null,arguments)},u._OrtRun=function(){return(u._OrtRun=u.asm.mb).apply(null,arguments)},u._OrtEndProfiling=function(){return(u._OrtEndProfiling=u.asm.nb).apply(null,arguments)};var hn=u._pthread_self=function(){return(hn=u._pthread_self=u.asm.ob).apply(null,arguments)},mn=u._malloc=function(){return(mn=u._malloc=u.asm.pb).apply(null,arguments)},dn=u._free=function(){return(dn=u._free=u.asm.qb).apply(null,arguments)},yn=u._fflush=function(){return(yn=u._fflush=u.asm.rb).apply(null,arguments)};u.__emscripten_tls_init=function(){return(u.__emscripten_tls_init=u.asm.sb).apply(null,arguments)};var bn=u.___funcs_on_exit=function(){return(bn=u.___funcs_on_exit=u.asm.tb).apply(null,arguments)},gn=u.__emscripten_thread_init=function(){return(gn=u.__emscripten_thread_init=u.asm.vb).apply(null,arguments)};u.__emscripten_thread_crashed=function(){return(u.__emscripten_thread_crashed=u.asm.wb).apply(null,arguments)};var _n,vn=u._emscripten_run_in_main_runtime_thread_js=function(){return(vn=u._emscripten_run_in_main_runtime_thread_js=u.asm.xb).apply(null,arguments)},wn=u.__emscripten_proxy_execute_task_queue=function(){return(wn=u.__emscripten_proxy_execute_task_queue=u.asm.yb).apply(null,arguments)},Tn=u.__emscripten_thread_free_data=function(){return(Tn=u.__emscripten_thread_free_data=u.asm.zb).apply(null,arguments)},On=u.__emscripten_thread_exit=function(){return(On=u.__emscripten_thread_exit=u.asm.Ab).apply(null,arguments)},Sn=u._setThrew=function(){return(Sn=u._setThrew=u.asm.Bb).apply(null,arguments)},An=u._emscripten_stack_set_limits=function(){return(An=u._emscripten_stack_set_limits=u.asm.Cb).apply(null,arguments)},Mn=u.stackSave=function(){return(Mn=u.stackSave=u.asm.Db).apply(null,arguments)},Cn=u.stackRestore=function(){return(Cn=u.stackRestore=u.asm.Eb).apply(null,arguments)},xn=u.stackAlloc=function(){return(xn=u.stackAlloc=u.asm.Fb).apply(null,arguments)},Rn=u.___cxa_can_catch=function(){return(Rn=u.___cxa_can_catch=u.asm.Gb).apply(null,arguments)},En=u.___cxa_is_pointer_type=function(){return(En=u.___cxa_is_pointer_type=u.asm.Hb).apply(null,arguments)},kn=u.dynCall_j=function(){return(kn=u.dynCall_j=u.asm.Ib).apply(null,arguments)},Dn=u.dynCall_iiiiij=function(){return(Dn=u.dynCall_iiiiij=u.asm.Jb).apply(null,arguments)},jn=u.dynCall_jii=function(){return(jn=u.dynCall_jii=u.asm.Kb).apply(null,arguments)},Fn=u.dynCall_viiiiij=function(){return(Fn=u.dynCall_viiiiij=u.asm.Lb).apply(null,arguments)},Un=u.dynCall_vjji=function(){return(Un=u.dynCall_vjji=u.asm.Mb).apply(null,arguments)},Yn=u.dynCall_viiijjjii=function(){return(Yn=u.dynCall_viiijjjii=u.asm.Nb).apply(null,arguments)},In=u.dynCall_iij=function(){return(In=u.dynCall_iij=u.asm.Ob).apply(null,arguments)},Wn=u.dynCall_ji=function(){return(Wn=u.dynCall_ji=u.asm.Pb).apply(null,arguments)},Pn=u.dynCall_iiiiiij=function(){return(Pn=u.dynCall_iiiiiij=u.asm.Qb).apply(null,arguments)},Hn=u.dynCall_iiij=function(){return(Hn=u.dynCall_iiij=u.asm.Rb).apply(null,arguments)};function qn(){function t(){if(!_n&&(_n=!0,u.calledRun=!0,!W)&&(w||ht(X),o(u),u.onRuntimeInitialized&&u.onRuntimeInitialized(),!w)){if(u.postRun)for(\"function\"==typeof u.postRun&&(u.postRun=[u.postRun]);u.postRun.length;){var t=u.postRun.shift();$.unshift(t)}ht($)}}if(!(0<tt))if(w)o(u),w||ht(X),postMessage({cmd:\"loaded\"});else{if(u.preRun)for(\"function\"==typeof u.preRun&&(u.preRun=[u.preRun]);u.preRun.length;)Q();ht(L),0<tt||(u.setStatus?(u.setStatus(\"Running...\"),setTimeout((function(){setTimeout((function(){u.setStatus(\"\")}),1),t()}),1)):t())}}if(u.UTF8ToString=q,u.stringToUTF8=function(t,n,r){return B(t,e(),n,r)},u.lengthBytesUTF8=G,u.keepRuntimeAlive=J,u.wasmMemory=E,u.stackSave=Mn,u.stackRestore=Cn,u.stackAlloc=xn,u.ExitStatus=ot,u.PThread=pt,et=function t(){_n||qn(),_n||(et=t)},u.preInit)for(\"function\"==typeof u.preInit&&(u.preInit=[u.preInit]);0<u.preInit.length;)u.preInit.pop()();return qn(),t.ready});\"object\"==typeof exports&&\"object\"==typeof module?module.exports=t:\"function\"==typeof define&&define.amd?define([],(function(){return t})):\"object\"==typeof exports&&(exports.ortWasmThreaded=t);\n", "\r\nvar ortWasm = (() => {\r\n  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;\r\n  if (typeof __filename !== 'undefined') _scriptDir = _scriptDir || __filename;\r\n  return (\r\nfunction(ortWasm) {\r\n  ortWasm = ortWasm || {};\r\n\r\n\r\nvar d;d||(d=typeof ortWasm !== 'undefined' ? ortWasm : {});var aa,ba;d.ready=new Promise(function(a,b){aa=a;ba=b});var ca=Object.assign({},d),da=\"./this.program\",ea=(a,b)=>{throw b;},fa=\"object\"==typeof window,m=\"function\"==typeof importScripts,p=\"object\"==typeof process&&\"object\"==typeof process.versions&&\"string\"==typeof process.versions.node,q=\"\",ha,r,v,fs,y,ia;\r\nif(p)q=m?require(\"path\").dirname(q)+\"/\":__dirname+\"/\",ia=()=>{y||(fs=require(\"fs\"),y=require(\"path\"))},ha=function(a,b){ia();a=y.normalize(a);return fs.readFileSync(a,b?void 0:\"utf8\")},v=a=>{a=ha(a,!0);a.buffer||(a=new Uint8Array(a));return a},r=(a,b,c)=>{ia();a=y.normalize(a);fs.readFile(a,function(e,f){e?c(e):b(f.buffer)})},1<process.argv.length&&(da=process.argv[1].replace(/\\\\/g,\"/\")),process.argv.slice(2),process.on(\"uncaughtException\",function(a){if(!(a instanceof ja))throw a;}),process.on(\"unhandledRejection\",\r\nfunction(a){throw a;}),ea=(a,b)=>{if(noExitRuntime||0<ka)throw process.exitCode=a,b;b instanceof ja||z(\"exiting due to exception: \"+b);process.exit(a)},d.inspect=function(){return\"[Emscripten Module object]\"};else if(fa||m)m?q=self.location.href:\"undefined\"!=typeof document&&document.currentScript&&(q=document.currentScript.src),_scriptDir&&(q=_scriptDir),0!==q.indexOf(\"blob:\")?q=q.substr(0,q.replace(/[?#].*/,\"\").lastIndexOf(\"/\")+1):q=\"\",ha=a=>{var b=new XMLHttpRequest;b.open(\"GET\",a,!1);b.send(null);\r\nreturn b.responseText},m&&(v=a=>{var b=new XMLHttpRequest;b.open(\"GET\",a,!1);b.responseType=\"arraybuffer\";b.send(null);return new Uint8Array(b.response)}),r=(a,b,c)=>{var e=new XMLHttpRequest;e.open(\"GET\",a,!0);e.responseType=\"arraybuffer\";e.onload=()=>{200==e.status||0==e.status&&e.response?b(e.response):c()};e.onerror=c;e.send(null)};var la=d.print||console.log.bind(console),z=d.printErr||console.warn.bind(console);Object.assign(d,ca);ca=null;d.thisProgram&&(da=d.thisProgram);d.quit&&(ea=d.quit);\r\nvar A;d.wasmBinary&&(A=d.wasmBinary);var noExitRuntime=d.noExitRuntime||!1;\"object\"!=typeof WebAssembly&&B(\"no native wasm support detected\");var ma,D=!1,na=\"undefined\"!=typeof TextDecoder?new TextDecoder(\"utf8\"):void 0;\r\nfunction oa(a,b,c){b>>>=0;var e=b+c;for(c=b;a[c]&&!(c>=e);)++c;if(16<c-b&&a.buffer&&na)return na.decode(a.subarray(b,c));for(e=\"\";b<c;){var f=a[b++];if(f&128){var h=a[b++]&63;if(192==(f&224))e+=String.fromCharCode((f&31)<<6|h);else{var k=a[b++]&63;f=224==(f&240)?(f&15)<<12|h<<6|k:(f&7)<<18|h<<12|k<<6|a[b++]&63;65536>f?e+=String.fromCharCode(f):(f-=65536,e+=String.fromCharCode(55296|f>>10,56320|f&1023))}}else e+=String.fromCharCode(f)}return e}function pa(a,b){return(a>>>=0)?oa(G,a,b):\"\"}\r\nfunction qa(a,b,c,e){c>>>=0;if(!(0<e))return 0;var f=c;e=c+e-1;for(var h=0;h<a.length;++h){var k=a.charCodeAt(h);if(55296<=k&&57343>=k){var l=a.charCodeAt(++h);k=65536+((k&1023)<<10)|l&1023}if(127>=k){if(c>=e)break;b[c++>>>0]=k}else{if(2047>=k){if(c+1>=e)break;b[c++>>>0]=192|k>>6}else{if(65535>=k){if(c+2>=e)break;b[c++>>>0]=224|k>>12}else{if(c+3>=e)break;b[c++>>>0]=240|k>>18;b[c++>>>0]=128|k>>12&63}b[c++>>>0]=128|k>>6&63}b[c++>>>0]=128|k&63}}b[c>>>0]=0;return c-f}\r\nfunction ra(a){for(var b=0,c=0;c<a.length;++c){var e=a.charCodeAt(c);127>=e?b++:2047>=e?b+=2:55296<=e&&57343>=e?(b+=4,++c):b+=3}return b}var sa,H,G,I,J;function ta(){var a=ma.buffer;sa=a;d.HEAP8=H=new Int8Array(a);d.HEAP16=new Int16Array(a);d.HEAP32=I=new Int32Array(a);d.HEAPU8=G=new Uint8Array(a);d.HEAPU16=new Uint16Array(a);d.HEAPU32=J=new Uint32Array(a);d.HEAPF32=new Float32Array(a);d.HEAPF64=new Float64Array(a)}var ua,va=[],wa=[],xa=[],ya=[],ka=0;\r\nfunction za(){var a=d.preRun.shift();va.unshift(a)}var K=0,Aa=null,L=null;function B(a){if(d.onAbort)d.onAbort(a);a=\"Aborted(\"+a+\")\";z(a);D=!0;a=new WebAssembly.RuntimeError(a+\". Build with -sASSERTIONS for more info.\");ba(a);throw a;}function Ba(){return N.startsWith(\"data:application/octet-stream;base64,\")}var N;N=\"ort-wasm.wasm\";if(!Ba()){var Ca=N;N=d.locateFile?d.locateFile(Ca,q):q+Ca}\r\nfunction Da(){var a=N;try{if(a==N&&A)return new Uint8Array(A);if(v)return v(a);throw\"both async and sync fetching of the wasm failed\";}catch(b){B(b)}}\r\nfunction Ea(){if(!A&&(fa||m)){if(\"function\"==typeof fetch&&!N.startsWith(\"file://\"))return fetch(N,{credentials:\"same-origin\"}).then(function(a){if(!a.ok)throw\"failed to load wasm binary file at '\"+N+\"'\";return a.arrayBuffer()}).catch(function(){return Da()});if(r)return new Promise(function(a,b){r(N,function(c){a(new Uint8Array(c))},b)})}return Promise.resolve().then(function(){return Da()})}function ja(a){this.name=\"ExitStatus\";this.message=\"Program terminated with exit(\"+a+\")\";this.status=a}\r\nfunction O(a){for(;0<a.length;)a.shift()(d)}var P=[],Q=0,R=0;\r\nfunction S(a){this.Db=a;this.zb=a-24;this.Ub=function(b){J[this.zb+4>>2>>>0]=b};this.Eb=function(){return J[this.zb+4>>2>>>0]};this.Sb=function(b){J[this.zb+8>>2>>>0]=b};this.Wb=function(){return J[this.zb+8>>2>>>0]};this.Tb=function(){I[this.zb>>2>>>0]=0};this.Ib=function(b){H[this.zb+12>>0>>>0]=b?1:0};this.Pb=function(){return 0!=H[this.zb+12>>0>>>0]};this.Jb=function(b){H[this.zb+13>>0>>>0]=b?1:0};this.Lb=function(){return 0!=H[this.zb+13>>0>>>0]};this.Rb=function(b,c){this.Fb(0);this.Ub(b);this.Sb(c);\r\nthis.Tb();this.Ib(!1);this.Jb(!1)};this.Nb=function(){I[this.zb>>2>>>0]+=1};this.Xb=function(){var b=I[this.zb>>2>>>0];I[this.zb>>2>>>0]=b-1;return 1===b};this.Fb=function(b){J[this.zb+16>>2>>>0]=b};this.Ob=function(){return J[this.zb+16>>2>>>0]};this.Qb=function(){if(Fa(this.Eb()))return J[this.Db>>2>>>0];var b=this.Ob();return 0!==b?b:this.Db}}function Ga(a){return Ha((new S(a)).zb)}var T=[];function U(a){var b=T[a];b||(a>=T.length&&(T.length=a+1),T[a]=b=ua.get(a));return b}\r\nfunction Ia(a){var b=ra(a)+1,c=Ja(b);c&&qa(a,H,c,b);return c}function Ka(a,b,c){function e(n){return(n=n.toTimeString().match(/\\(([A-Za-z ]+)\\)$/))?n[1]:\"GMT\"}var f=(new Date).getFullYear(),h=new Date(f,0,1),k=new Date(f,6,1);f=h.getTimezoneOffset();var l=k.getTimezoneOffset();I[a>>2>>>0]=60*Math.max(f,l);I[b>>2>>>0]=Number(f!=l);a=e(h);b=e(k);a=Ia(a);b=Ia(b);l<f?(J[c>>2>>>0]=a,J[c+4>>2>>>0]=b):(J[c>>2>>>0]=b,J[c+4>>2>>>0]=a)}function La(a,b,c){La.Vb||(La.Vb=!0,Ka(a,b,c))}var Ma={};\r\nfunction Na(){if(!Oa){var a={USER:\"web_user\",LOGNAME:\"web_user\",PATH:\"/\",PWD:\"/\",HOME:\"/home/<USER>\",LANG:(\"object\"==typeof navigator&&navigator.languages&&navigator.languages[0]||\"C\").replace(\"-\",\"_\")+\".UTF-8\",_:da||\"./this.program\"},b;for(b in Ma)void 0===Ma[b]?delete a[b]:a[b]=Ma[b];var c=[];for(b in a)c.push(b+\"=\"+a[b]);Oa=c}return Oa}var Oa,Pa=[null,[],[]];function Qa(a,b){var c=Pa[a];0===b||10===b?((1===a?la:z)(oa(c,0)),c.length=0):c.push(b)}var V=0;\r\nfunction Ra(){if(\"object\"==typeof crypto&&\"function\"==typeof crypto.getRandomValues){var a=new Uint8Array(1);return()=>{crypto.getRandomValues(a);return a[0]}}if(p)try{var b=require(\"crypto\");return()=>b.randomBytes(1)[0]}catch(c){}return()=>B(\"randomDevice\")}function W(a,b){W.Mb||(W.Mb=Ra());for(var c=0;c<b;c++)H[a+c>>0>>>0]=W.Mb();return 0}function Sa(a){return 0===a%4&&(0!==a%100||0===a%400)}var Ta=[31,29,31,30,31,30,31,31,30,31,30,31],Ua=[31,28,31,30,31,30,31,31,30,31,30,31];\r\nfunction Va(a){var b=Array(ra(a)+1);qa(a,b,0,b.length);return b}\r\nfunction Wa(a,b,c,e){function f(g,u,w){for(g=\"number\"==typeof g?g.toString():g||\"\";g.length<u;)g=w[0]+g;return g}function h(g,u){return f(g,u,\"0\")}function k(g,u){function w(M){return 0>M?-1:0<M?1:0}var F;0===(F=w(g.getFullYear()-u.getFullYear()))&&0===(F=w(g.getMonth()-u.getMonth()))&&(F=w(g.getDate()-u.getDate()));return F}function l(g){switch(g.getDay()){case 0:return new Date(g.getFullYear()-1,11,29);case 1:return g;case 2:return new Date(g.getFullYear(),0,3);case 3:return new Date(g.getFullYear(),\r\n0,2);case 4:return new Date(g.getFullYear(),0,1);case 5:return new Date(g.getFullYear()-1,11,31);case 6:return new Date(g.getFullYear()-1,11,30)}}function n(g){var u=g.Bb;for(g=new Date((new Date(g.Cb+1900,0,1)).getTime());0<u;){var w=g.getMonth(),F=(Sa(g.getFullYear())?Ta:Ua)[w];if(u>F-g.getDate())u-=F-g.getDate()+1,g.setDate(1),11>w?g.setMonth(w+1):(g.setMonth(0),g.setFullYear(g.getFullYear()+1));else{g.setDate(g.getDate()+u);break}}w=new Date(g.getFullYear()+1,0,4);u=l(new Date(g.getFullYear(),\r\n0,4));w=l(w);return 0>=k(u,g)?0>=k(w,g)?g.getFullYear()+1:g.getFullYear():g.getFullYear()-1}var t=I[e+40>>2>>>0];e={$b:I[e>>2>>>0],Zb:I[e+4>>2>>>0],Gb:I[e+8>>2>>>0],Kb:I[e+12>>2>>>0],Hb:I[e+16>>2>>>0],Cb:I[e+20>>2>>>0],Ab:I[e+24>>2>>>0],Bb:I[e+28>>2>>>0],bc:I[e+32>>2>>>0],Yb:I[e+36>>2>>>0],ac:t?pa(t):\"\"};c=pa(c);t={\"%c\":\"%a %b %d %H:%M:%S %Y\",\"%D\":\"%m/%d/%y\",\"%F\":\"%Y-%m-%d\",\"%h\":\"%b\",\"%r\":\"%I:%M:%S %p\",\"%R\":\"%H:%M\",\"%T\":\"%H:%M:%S\",\"%x\":\"%m/%d/%y\",\"%X\":\"%H:%M:%S\",\"%Ec\":\"%c\",\"%EC\":\"%C\",\"%Ex\":\"%m/%d/%y\",\r\n\"%EX\":\"%H:%M:%S\",\"%Ey\":\"%y\",\"%EY\":\"%Y\",\"%Od\":\"%d\",\"%Oe\":\"%e\",\"%OH\":\"%H\",\"%OI\":\"%I\",\"%Om\":\"%m\",\"%OM\":\"%M\",\"%OS\":\"%S\",\"%Ou\":\"%u\",\"%OU\":\"%U\",\"%OV\":\"%V\",\"%Ow\":\"%w\",\"%OW\":\"%W\",\"%Oy\":\"%y\"};for(var x in t)c=c.replace(new RegExp(x,\"g\"),t[x]);var E=\"Sunday Monday Tuesday Wednesday Thursday Friday Saturday\".split(\" \"),C=\"January February March April May June July August September October November December\".split(\" \");t={\"%a\":function(g){return E[g.Ab].substring(0,3)},\"%A\":function(g){return E[g.Ab]},\"%b\":function(g){return C[g.Hb].substring(0,\r\n3)},\"%B\":function(g){return C[g.Hb]},\"%C\":function(g){return h((g.Cb+1900)/100|0,2)},\"%d\":function(g){return h(g.Kb,2)},\"%e\":function(g){return f(g.Kb,2,\" \")},\"%g\":function(g){return n(g).toString().substring(2)},\"%G\":function(g){return n(g)},\"%H\":function(g){return h(g.Gb,2)},\"%I\":function(g){g=g.Gb;0==g?g=12:12<g&&(g-=12);return h(g,2)},\"%j\":function(g){for(var u=0,w=0;w<=g.Hb-1;u+=(Sa(g.Cb+1900)?Ta:Ua)[w++]);return h(g.Kb+u,3)},\"%m\":function(g){return h(g.Hb+1,2)},\"%M\":function(g){return h(g.Zb,\r\n2)},\"%n\":function(){return\"\\n\"},\"%p\":function(g){return 0<=g.Gb&&12>g.Gb?\"AM\":\"PM\"},\"%S\":function(g){return h(g.$b,2)},\"%t\":function(){return\"\\t\"},\"%u\":function(g){return g.Ab||7},\"%U\":function(g){return h(Math.floor((g.Bb+7-g.Ab)/7),2)},\"%V\":function(g){var u=Math.floor((g.Bb+7-(g.Ab+6)%7)/7);2>=(g.Ab+371-g.Bb-2)%7&&u++;if(u)53==u&&(w=(g.Ab+371-g.Bb)%7,4==w||3==w&&Sa(g.Cb)||(u=1));else{u=52;var w=(g.Ab+7-g.Bb-1)%7;(4==w||5==w&&Sa(g.Cb%400-1))&&u++}return h(u,2)},\"%w\":function(g){return g.Ab},\"%W\":function(g){return h(Math.floor((g.Bb+\r\n7-(g.Ab+6)%7)/7),2)},\"%y\":function(g){return(g.Cb+1900).toString().substring(2)},\"%Y\":function(g){return g.Cb+1900},\"%z\":function(g){g=g.Yb;var u=0<=g;g=Math.abs(g)/60;return(u?\"+\":\"-\")+String(\"0000\"+(g/60*100+g%60)).slice(-4)},\"%Z\":function(g){return g.ac},\"%%\":function(){return\"%\"}};c=c.replace(/%%/g,\"\\x00\\x00\");for(x in t)c.includes(x)&&(c=c.replace(new RegExp(x,\"g\"),t[x](e)));c=c.replace(/\\0\\0/g,\"%\");x=Va(c);if(x.length>b)return 0;H.set(x,a>>>0);return x.length-1}\r\nvar Jb={a:function(a){return Ja(a+24)+24},m:function(a){a=new S(a);a.Pb()||(a.Ib(!0),Q--);a.Jb(!1);P.push(a);a.Nb();return a.Qb()},ia:function(a){z(\"Unexpected exception thrown, this is not properly supported - aborting\");D=!0;throw a;},w:function(){X(0);var a=P.pop();if(a.Xb()&&!a.Lb()){var b=a.Wb();b&&U(b)(a.Db);Ga(a.Db)}R=0},d:function(){var a=R;if(!a)return V=0;var b=new S(a);b.Fb(a);var c=b.Eb();if(!c)return V=0,a;for(var e=Array.prototype.slice.call(arguments),f=0;f<e.length;f++){var h=e[f];\r\nif(0===h||h===c)break;if(Xa(h,c,b.zb+16))return V=h,a}V=c;return a},k:function(){var a=R;if(!a)return V=0;var b=new S(a);b.Fb(a);var c=b.Eb();if(!c)return V=0,a;for(var e=Array.prototype.slice.call(arguments),f=0;f<e.length;f++){var h=e[f];if(0===h||h===c)break;if(Xa(h,c,b.zb+16))return V=h,a}V=c;return a},g:function(){var a=R;if(!a)return V=0;var b=new S(a);b.Fb(a);var c=b.Eb();if(!c)return V=0,a;for(var e=Array.prototype.slice.call(arguments),f=0;f<e.length;f++){var h=e[f];if(0===h||h===c)break;\r\nif(Xa(h,c,b.zb+16))return V=h,a}V=c;return a},s:Ga,L:function(){var a=P.pop();a||B(\"no exception to throw\");var b=a.Db;a.Lb()||(P.push(a),a.Jb(!0),a.Ib(!1),Q++);R=b;throw b;},b:function(a,b,c){(new S(a)).Rb(b,c);R=a;Q++;throw a;},la:function(){return Q},i:function(a){R||(R=a);throw a;},H:function(){return 0},Ba:function(){},pa:function(){},ra:function(){},ka:function(){return 0},za:function(){},ua:function(){},ya:function(){},R:function(){},qa:function(){},na:function(){},Aa:function(){},oa:function(){},\r\nHa:function(){},Ja:function(){B(\"To use dlopen, you need enable dynamic linking, see https://github.com/emscripten-core/emscripten/wiki/Linking\")},Ia:function(){B(\"To use dlopen, you need enable dynamic linking, see https://github.com/emscripten-core/emscripten/wiki/Linking\")},S:function(){return Date.now()},Ca:function(){return!0},Da:function(a,b){a=new Date(1E3*(J[a>>>2]+4294967296*I[a+4>>>2]));I[b>>2>>>0]=a.getUTCSeconds();I[b+4>>2>>>0]=a.getUTCMinutes();I[b+8>>2>>>0]=a.getUTCHours();I[b+12>>2>>>\r\n0]=a.getUTCDate();I[b+16>>2>>>0]=a.getUTCMonth();I[b+20>>2>>>0]=a.getUTCFullYear()-1900;I[b+24>>2>>>0]=a.getUTCDay();I[b+28>>2>>>0]=(a.getTime()-Date.UTC(a.getUTCFullYear(),0,1,0,0,0,0))/864E5|0},Ea:function(a,b){a=new Date(1E3*(J[a>>>2]+4294967296*I[a+4>>>2]));I[b>>2>>>0]=a.getSeconds();I[b+4>>2>>>0]=a.getMinutes();I[b+8>>2>>>0]=a.getHours();I[b+12>>2>>>0]=a.getDate();I[b+16>>2>>>0]=a.getMonth();I[b+20>>2>>>0]=a.getFullYear()-1900;I[b+24>>2>>>0]=a.getDay();var c=new Date(a.getFullYear(),0,1);I[b+\r\n28>>2>>>0]=(a.getTime()-c.getTime())/864E5|0;I[b+36>>2>>>0]=-(60*a.getTimezoneOffset());var e=(new Date(a.getFullYear(),6,1)).getTimezoneOffset();c=c.getTimezoneOffset();I[b+32>>2>>>0]=(e!=c&&a.getTimezoneOffset()==Math.min(c,e))|0},Fa:function(a){var b=new Date(I[a+20>>2>>>0]+1900,I[a+16>>2>>>0],I[a+12>>2>>>0],I[a+8>>2>>>0],I[a+4>>2>>>0],I[a>>2>>>0],0),c=I[a+32>>2>>>0],e=b.getTimezoneOffset(),f=new Date(b.getFullYear(),0,1),h=(new Date(b.getFullYear(),6,1)).getTimezoneOffset(),k=f.getTimezoneOffset(),\r\nl=Math.min(k,h);0>c?I[a+32>>2>>>0]=Number(h!=k&&l==e):0<c!=(l==e)&&(h=Math.max(k,h),b.setTime(b.getTime()+6E4*((0<c?l:h)-e)));I[a+24>>2>>>0]=b.getDay();I[a+28>>2>>>0]=(b.getTime()-f.getTime())/864E5|0;I[a>>2>>>0]=b.getSeconds();I[a+4>>2>>>0]=b.getMinutes();I[a+8>>2>>>0]=b.getHours();I[a+12>>2>>>0]=b.getDate();I[a+16>>2>>>0]=b.getMonth();return b.getTime()/1E3|0},sa:function(){return-52},ta:function(){},Ga:La,B:function(){B(\"\")},ma:function(){return 4294901760},I:p?()=>{var a=process.hrtime();return 1E3*\r\na[0]+a[1]/1E6}:()=>performance.now(),xa:function(a,b,c){G.copyWithin(a>>>0,b>>>0,b+c>>>0)},G:function(a){var b=G.length;a>>>=0;if(4294901760<a)return!1;for(var c=1;4>=c;c*=2){var e=b*(1+.2/c);e=Math.min(e,a+100663296);var f=Math;e=Math.max(a,e);f=f.min.call(f,4294901760,e+(65536-e%65536)%65536);a:{try{ma.grow(f-sa.byteLength+65535>>>16);ta();var h=1;break a}catch(k){}h=void 0}if(h)return!0}return!1},va:function(a,b){var c=0;Na().forEach(function(e,f){var h=b+c;f=J[a+4*f>>2>>>0]=h;for(h=0;h<e.length;++h)H[f++>>\r\n0>>>0]=e.charCodeAt(h);H[f>>0>>>0]=0;c+=e.length+1});return 0},wa:function(a,b){var c=Na();J[a>>2>>>0]=c.length;var e=0;c.forEach(function(f){e+=f.length+1});J[b>>2>>>0]=e;return 0},ba:function(a){noExitRuntime||0<ka||(Ya(),O(xa),Za(0),Pa[1].length&&Qa(1,10),Pa[2].length&&Qa(2,10));if(!(noExitRuntime||0<ka)){if(d.onExit)d.onExit(a);D=!0}ea(a,new ja(a))},E:function(){return 52},Q:function(){return 52},ca:function(){return 70},P:function(a,b,c,e){for(var f=0,h=0;h<c;h++){var k=J[b>>2>>>0],l=J[b+4>>\r\n2>>>0];b+=8;for(var n=0;n<l;n++)Qa(a,G[k+n>>>0]);f+=l}J[e>>2>>>0]=f;return 0},c:function(){return V},ja:W,ea:$a,fa:ab,J:bb,e:cb,N:db,O:eb,j:fb,o:gb,p:hb,M:ib,r:jb,v:kb,K:lb,D:mb,X:nb,V:ob,U:pb,Z:qb,W:rb,Y:sb,T:tb,f:ub,q:vb,h:wb,da:xb,l:yb,t:zb,u:Ab,x:Bb,z:Cb,ga:Db,A:Eb,C:Fb,aa:Gb,_:Hb,$:Ib,n:function(a){return a},F:function(a){V=a},ha:Wa,y:function(a,b,c,e){return Wa(a,b,c,e)}};\r\n(function(){function a(f){d.asm=f.exports;ma=d.asm.Ka;ta();ua=d.asm.ib;wa.unshift(d.asm.La);K--;d.monitorRunDependencies&&d.monitorRunDependencies(K);0==K&&(null!==Aa&&(clearInterval(Aa),Aa=null),L&&(f=L,L=null,f()))}function b(f){a(f.instance)}function c(f){return Ea().then(function(h){return WebAssembly.instantiate(h,e)}).then(function(h){return h}).then(f,function(h){z(\"failed to asynchronously prepare wasm: \"+h);B(h)})}var e={a:Jb};K++;d.monitorRunDependencies&&d.monitorRunDependencies(K);if(d.instantiateWasm)try{return d.instantiateWasm(e,\r\na)}catch(f){return z(\"Module.instantiateWasm callback failed with error: \"+f),!1}(function(){return A||\"function\"!=typeof WebAssembly.instantiateStreaming||Ba()||N.startsWith(\"file://\")||p||\"function\"!=typeof fetch?c(b):fetch(N,{credentials:\"same-origin\"}).then(function(f){return WebAssembly.instantiateStreaming(f,e).then(b,function(h){z(\"wasm streaming compile failed: \"+h);z(\"falling back to ArrayBuffer instantiation\");return c(b)})})})().catch(ba);return{}})();\r\nd.___wasm_call_ctors=function(){return(d.___wasm_call_ctors=d.asm.La).apply(null,arguments)};d._OrtInit=function(){return(d._OrtInit=d.asm.Ma).apply(null,arguments)};d._OrtCreateSessionOptions=function(){return(d._OrtCreateSessionOptions=d.asm.Na).apply(null,arguments)};d._OrtAppendExecutionProvider=function(){return(d._OrtAppendExecutionProvider=d.asm.Oa).apply(null,arguments)};d._OrtAddSessionConfigEntry=function(){return(d._OrtAddSessionConfigEntry=d.asm.Pa).apply(null,arguments)};\r\nd._OrtReleaseSessionOptions=function(){return(d._OrtReleaseSessionOptions=d.asm.Qa).apply(null,arguments)};d._OrtCreateSession=function(){return(d._OrtCreateSession=d.asm.Ra).apply(null,arguments)};d._OrtReleaseSession=function(){return(d._OrtReleaseSession=d.asm.Sa).apply(null,arguments)};d._OrtGetInputCount=function(){return(d._OrtGetInputCount=d.asm.Ta).apply(null,arguments)};d._OrtGetOutputCount=function(){return(d._OrtGetOutputCount=d.asm.Ua).apply(null,arguments)};\r\nd._OrtGetInputName=function(){return(d._OrtGetInputName=d.asm.Va).apply(null,arguments)};d._OrtGetOutputName=function(){return(d._OrtGetOutputName=d.asm.Wa).apply(null,arguments)};d._OrtFree=function(){return(d._OrtFree=d.asm.Xa).apply(null,arguments)};d._OrtCreateTensor=function(){return(d._OrtCreateTensor=d.asm.Ya).apply(null,arguments)};d._OrtGetTensorData=function(){return(d._OrtGetTensorData=d.asm.Za).apply(null,arguments)};\r\nd._OrtReleaseTensor=function(){return(d._OrtReleaseTensor=d.asm._a).apply(null,arguments)};d._OrtCreateRunOptions=function(){return(d._OrtCreateRunOptions=d.asm.$a).apply(null,arguments)};d._OrtAddRunConfigEntry=function(){return(d._OrtAddRunConfigEntry=d.asm.ab).apply(null,arguments)};d._OrtReleaseRunOptions=function(){return(d._OrtReleaseRunOptions=d.asm.bb).apply(null,arguments)};d._OrtRun=function(){return(d._OrtRun=d.asm.cb).apply(null,arguments)};\r\nd._OrtEndProfiling=function(){return(d._OrtEndProfiling=d.asm.db).apply(null,arguments)};\r\nvar Ja=d._malloc=function(){return(Ja=d._malloc=d.asm.eb).apply(null,arguments)},Ha=d._free=function(){return(Ha=d._free=d.asm.fb).apply(null,arguments)},Za=d._fflush=function(){return(Za=d._fflush=d.asm.gb).apply(null,arguments)},Ya=d.___funcs_on_exit=function(){return(Ya=d.___funcs_on_exit=d.asm.hb).apply(null,arguments)},X=d._setThrew=function(){return(X=d._setThrew=d.asm.jb).apply(null,arguments)},Y=d.stackSave=function(){return(Y=d.stackSave=d.asm.kb).apply(null,arguments)},Z=d.stackRestore=\r\nfunction(){return(Z=d.stackRestore=d.asm.lb).apply(null,arguments)},Kb=d.stackAlloc=function(){return(Kb=d.stackAlloc=d.asm.mb).apply(null,arguments)},Xa=d.___cxa_can_catch=function(){return(Xa=d.___cxa_can_catch=d.asm.nb).apply(null,arguments)},Fa=d.___cxa_is_pointer_type=function(){return(Fa=d.___cxa_is_pointer_type=d.asm.ob).apply(null,arguments)},Lb=d.dynCall_j=function(){return(Lb=d.dynCall_j=d.asm.pb).apply(null,arguments)},Mb=d.dynCall_iiiiij=function(){return(Mb=d.dynCall_iiiiij=d.asm.qb).apply(null,\r\narguments)},Nb=d.dynCall_jii=function(){return(Nb=d.dynCall_jii=d.asm.rb).apply(null,arguments)},Ob=d.dynCall_viiiiij=function(){return(Ob=d.dynCall_viiiiij=d.asm.sb).apply(null,arguments)},Pb=d.dynCall_vjji=function(){return(Pb=d.dynCall_vjji=d.asm.tb).apply(null,arguments)},Qb=d.dynCall_viiijjjii=function(){return(Qb=d.dynCall_viiijjjii=d.asm.ub).apply(null,arguments)},Rb=d.dynCall_iij=function(){return(Rb=d.dynCall_iij=d.asm.vb).apply(null,arguments)},Sb=d.dynCall_ji=function(){return(Sb=d.dynCall_ji=\r\nd.asm.wb).apply(null,arguments)},Tb=d.dynCall_iiiiiij=function(){return(Tb=d.dynCall_iiiiiij=d.asm.xb).apply(null,arguments)},Ub=d.dynCall_iiij=function(){return(Ub=d.dynCall_iiij=d.asm.yb).apply(null,arguments)};function cb(a,b){var c=Y();try{return U(a)(b)}catch(e){Z(c);if(e!==e+0)throw e;X(1,0)}}function vb(a,b){var c=Y();try{U(a)(b)}catch(e){Z(c);if(e!==e+0)throw e;X(1,0)}}function wb(a,b,c){var e=Y();try{U(a)(b,c)}catch(f){Z(e);if(f!==f+0)throw f;X(1,0)}}\r\nfunction fb(a,b,c){var e=Y();try{return U(a)(b,c)}catch(f){Z(e);if(f!==f+0)throw f;X(1,0)}}function gb(a,b,c,e){var f=Y();try{return U(a)(b,c,e)}catch(h){Z(f);if(h!==h+0)throw h;X(1,0)}}function kb(a,b,c,e,f,h,k){var l=Y();try{return U(a)(b,c,e,f,h,k)}catch(n){Z(l);if(n!==n+0)throw n;X(1,0)}}function ub(a){var b=Y();try{U(a)()}catch(c){Z(b);if(c!==c+0)throw c;X(1,0)}}function jb(a,b,c,e,f,h){var k=Y();try{return U(a)(b,c,e,f,h)}catch(l){Z(k);if(l!==l+0)throw l;X(1,0)}}\r\nfunction hb(a,b,c,e,f){var h=Y();try{return U(a)(b,c,e,f)}catch(k){Z(h);if(k!==k+0)throw k;X(1,0)}}function yb(a,b,c,e){var f=Y();try{U(a)(b,c,e)}catch(h){Z(f);if(h!==h+0)throw h;X(1,0)}}function Ab(a,b,c,e,f,h){var k=Y();try{U(a)(b,c,e,f,h)}catch(l){Z(k);if(l!==l+0)throw l;X(1,0)}}function zb(a,b,c,e,f){var h=Y();try{U(a)(b,c,e,f)}catch(k){Z(h);if(k!==k+0)throw k;X(1,0)}}function Bb(a,b,c,e,f,h,k){var l=Y();try{U(a)(b,c,e,f,h,k)}catch(n){Z(l);if(n!==n+0)throw n;X(1,0)}}\r\nfunction Cb(a,b,c,e,f,h,k,l){var n=Y();try{U(a)(b,c,e,f,h,k,l)}catch(t){Z(n);if(t!==t+0)throw t;X(1,0)}}function eb(a,b,c){var e=Y();try{return U(a)(b,c)}catch(f){Z(e);if(f!==f+0)throw f;X(1,0)}}function db(a,b,c){var e=Y();try{return U(a)(b,c)}catch(f){Z(e);if(f!==f+0)throw f;X(1,0)}}function Db(a,b,c,e,f,h,k,l,n){var t=Y();try{U(a)(b,c,e,f,h,k,l,n)}catch(x){Z(t);if(x!==x+0)throw x;X(1,0)}}function ib(a,b,c,e,f,h){var k=Y();try{return U(a)(b,c,e,f,h)}catch(l){Z(k);if(l!==l+0)throw l;X(1,0)}}\r\nfunction lb(a,b,c,e,f,h,k,l){var n=Y();try{return U(a)(b,c,e,f,h,k,l)}catch(t){Z(n);if(t!==t+0)throw t;X(1,0)}}function mb(a,b,c,e,f,h,k,l,n,t,x,E){var C=Y();try{return U(a)(b,c,e,f,h,k,l,n,t,x,E)}catch(g){Z(C);if(g!==g+0)throw g;X(1,0)}}function Eb(a,b,c,e,f,h,k,l,n,t,x){var E=Y();try{U(a)(b,c,e,f,h,k,l,n,t,x)}catch(C){Z(E);if(C!==C+0)throw C;X(1,0)}}function Fb(a,b,c,e,f,h,k,l,n,t,x,E,C,g,u,w){var F=Y();try{U(a)(b,c,e,f,h,k,l,n,t,x,E,C,g,u,w)}catch(M){Z(F);if(M!==M+0)throw M;X(1,0)}}\r\nfunction bb(a){var b=Y();try{return U(a)()}catch(c){Z(b);if(c!==c+0)throw c;X(1,0)}}function ab(a,b,c){var e=Y();try{return U(a)(b,c)}catch(f){Z(e);if(f!==f+0)throw f;X(1,0)}}function $a(a,b,c){var e=Y();try{return U(a)(b,c)}catch(f){Z(e);if(f!==f+0)throw f;X(1,0)}}function xb(a,b,c,e){var f=Y();try{U(a)(b,c,e)}catch(h){Z(f);if(h!==h+0)throw h;X(1,0)}}function Gb(a,b,c,e,f,h,k,l){var n=Y();try{Ob(a,b,c,e,f,h,k,l)}catch(t){Z(n);if(t!==t+0)throw t;X(1,0)}}\r\nfunction Ib(a,b,c,e,f,h){var k=Y();try{Pb(a,b,c,e,f,h)}catch(l){Z(k);if(l!==l+0)throw l;X(1,0)}}function Hb(a,b,c,e,f,h,k,l,n,t,x,E){var C=Y();try{Qb(a,b,c,e,f,h,k,l,n,t,x,E)}catch(g){Z(C);if(g!==g+0)throw g;X(1,0)}}function qb(a,b,c,e){var f=Y();try{return Rb(a,b,c,e)}catch(h){Z(f);if(h!==h+0)throw h;X(1,0)}}function sb(a,b){var c=Y();try{return Sb(a,b)}catch(e){Z(c);if(e!==e+0)throw e;X(1,0)}}\r\nfunction nb(a,b,c,e,f,h,k,l){var n=Y();try{return Tb(a,b,c,e,f,h,k,l)}catch(t){Z(n);if(t!==t+0)throw t;X(1,0)}}function rb(a){var b=Y();try{return Lb(a)}catch(c){Z(b);if(c!==c+0)throw c;X(1,0)}}function ob(a,b,c,e,f,h,k){var l=Y();try{return Mb(a,b,c,e,f,h,k)}catch(n){Z(l);if(n!==n+0)throw n;X(1,0)}}function pb(a,b,c,e,f){var h=Y();try{return Ub(a,b,c,e,f)}catch(k){Z(h);if(k!==k+0)throw k;X(1,0)}}function tb(a,b,c){var e=Y();try{return Nb(a,b,c)}catch(f){Z(e);if(f!==f+0)throw f;X(1,0)}}\r\nd.UTF8ToString=pa;d.stringToUTF8=function(a,b,c){return qa(a,G,b,c)};d.lengthBytesUTF8=ra;d.stackSave=Y;d.stackRestore=Z;d.stackAlloc=Kb;var Vb;L=function Wb(){Vb||Xb();Vb||(L=Wb)};\r\nfunction Xb(){function a(){if(!Vb&&(Vb=!0,d.calledRun=!0,!D)){O(wa);aa(d);if(d.onRuntimeInitialized)d.onRuntimeInitialized();if(d.postRun)for(\"function\"==typeof d.postRun&&(d.postRun=[d.postRun]);d.postRun.length;){var b=d.postRun.shift();ya.unshift(b)}O(ya)}}if(!(0<K)){if(d.preRun)for(\"function\"==typeof d.preRun&&(d.preRun=[d.preRun]);d.preRun.length;)za();O(va);0<K||(d.setStatus?(d.setStatus(\"Running...\"),setTimeout(function(){setTimeout(function(){d.setStatus(\"\")},1);a()},1)):a())}}\r\nif(d.preInit)for(\"function\"==typeof d.preInit&&(d.preInit=[d.preInit]);0<d.preInit.length;)d.preInit.pop()();Xb();\r\n\r\n\r\n  return ortWasm.ready\r\n}\r\n);\r\n})();\r\nif (typeof exports === 'object' && typeof module === 'object')\r\n  module.exports = ortWasm;\r\nelse if (typeof define === 'function' && define['amd'])\r\n  define([], function() { return ortWasm; });\r\nelse if (typeof exports === 'object')\r\n  exports[\"ortWasm\"] = ortWasm;\r\n", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\nimport {Backend, env, InferenceSession, SessionHandler} from 'onnxruntime-common';\nimport {cpus} from 'os';\n\nimport {initWasm} from './wasm/proxy-wrapper';\nimport {OnnxruntimeWebAssemblySessionHandler} from './wasm/session-handler';\n\n/**\n * This function initializes all flags for WebAssembly.\n *\n * Those flags are accessible from `ort.env.wasm`. Users are allow to set those flags before the first inference session\n * being created, to override default value.\n */\nexport const initializeFlags = (): void => {\n  if (typeof env.wasm.initTimeout !== 'number' || env.wasm.initTimeout < 0) {\n    env.wasm.initTimeout = 0;\n  }\n\n  if (typeof env.wasm.simd !== 'boolean') {\n    env.wasm.simd = true;\n  }\n\n  if (typeof env.wasm.proxy !== 'boolean') {\n    env.wasm.proxy = false;\n  }\n\n  if (typeof env.wasm.numThreads !== 'number' || !Number.isInteger(env.wasm.numThreads) || env.wasm.numThreads <= 0) {\n    const numCpuLogicalCores = typeof navigator === 'undefined' ? cpus().length : navigator.hardwareConcurrency;\n    env.wasm.numThreads = Math.min(4, Math.ceil((numCpuLogicalCores || 1) / 2));\n  }\n};\n\nclass OnnxruntimeWebAssemblyBackend implements Backend {\n  async init(): Promise<void> {\n    // populate wasm flags\n    initializeFlags();\n\n    // init wasm\n    await initWasm();\n  }\n  createSessionHandler(path: string, options?: InferenceSession.SessionOptions): Promise<SessionHandler>;\n  createSessionHandler(buffer: Uint8Array, options?: InferenceSession.SessionOptions): Promise<SessionHandler>;\n  async createSessionHandler(pathOrBuffer: string|Uint8Array, options?: InferenceSession.SessionOptions):\n      Promise<SessionHandler> {\n    const handler = new OnnxruntimeWebAssemblySessionHandler();\n    await handler.loadModel(pathOrBuffer, options);\n    return Promise.resolve(handler);\n  }\n}\n\nexport const wasmBackend = new OnnxruntimeWebAssemblyBackend();\n", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\n/* eslint-disable @typescript-eslint/no-var-requires, @typescript-eslint/no-require-imports */\n// We use \"require\" instead of \"import\" here because import statement must be put in top level. Our current code does\n// not allow terser to tree-shaking code as expected because some codes are treated as having side effects.\n// So we import code inside the if-clause to allow terser remove the code safely.\n\nexport * from 'onnxruntime-common';\nimport {registerBackend} from 'onnxruntime-common';\n\nif (!BUILD_DEFS.DISABLE_WEBGL) {\n  const onnxjsBackend = require('./backend-onnxjs').onnxjsBackend;\n  registerBackend('webgl', onnxjsBackend, -10);\n}\nif (!BUILD_DEFS.DISABLE_WASM) {\n  const wasmBackend = require('./backend-wasm').wasmBackend;\n  registerBackend('cpu', wasmBackend, 10);\n  registerBackend('wasm', wasmBackend, 10);\n  registerBackend('xnnpack', wasmBackend, 9);\n}\n", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\ninterface ExtraOptionsHandler {\n  (name: string, value: string): void;\n}\n\nexport const iterateExtraOptions =\n    (options: Record<string, unknown>, prefix: string, seen: WeakSet<Record<string, unknown>>,\n     handler: ExtraOptionsHandler): void => {\n      if (typeof options == 'object' && options !== null) {\n        if (seen.has(options)) {\n          throw new Error('Circular reference in options');\n        } else {\n          seen.add(options);\n        }\n      }\n\n      Object.entries(options).forEach(([key, value]) => {\n        const name = (prefix) ? prefix + key : key;\n        if (typeof value === 'object') {\n          iterateExtraOptions(value as Record<string, unknown>, name + '.', seen, handler);\n        } else if (typeof value === 'string' || typeof value === 'number') {\n          handler(name, value.toString());\n        } else if (typeof value === 'boolean') {\n          handler(name, (value) ? '1' : '0');\n        } else {\n          throw new Error(`Can't handle extra config type: ${typeof value}`);\n        }\n      });\n    };\n", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\nimport {env, InferenceSession} from 'onnxruntime-common';\n\nimport {OrtWasmMessage, SerializableModeldata, SerializableSessionMetadata, SerializableTensor} from './proxy-messages';\nimport * as core from './wasm-core-impl';\nimport {initializeWebAssembly} from './wasm-factory';\n\nconst isProxy = (): boolean => !!env.wasm.proxy && typeof document !== 'undefined';\nlet proxyWorker: Worker|undefined;\nlet initializing = false;\nlet initialized = false;\nlet aborted = false;\n\n// resolve; reject\ntype PromiseCallbacks<T = void> = [(result: T) => void, (reason: unknown) => void];\n\nlet initWasmCallbacks: PromiseCallbacks;\nlet initOrtCallbacks: PromiseCallbacks;\nconst createSessionAllocateCallbacks: Array<PromiseCallbacks<SerializableModeldata>> = [];\nconst createSessionFinalizeCallbacks: Array<PromiseCallbacks<SerializableSessionMetadata>> = [];\nconst createSessionCallbacks: Array<PromiseCallbacks<SerializableSessionMetadata>> = [];\nconst releaseSessionCallbacks: Array<PromiseCallbacks<void>> = [];\nconst runCallbacks: Array<PromiseCallbacks<SerializableTensor[]>> = [];\nconst endProfilingCallbacks: Array<PromiseCallbacks<void>> = [];\n\nconst ensureWorker = (): void => {\n  if (initializing || !initialized || aborted || !proxyWorker) {\n    throw new Error('worker not ready');\n  }\n};\n\nconst onProxyWorkerMessage = (ev: MessageEvent<OrtWasmMessage>): void => {\n  switch (ev.data.type) {\n    case 'init-wasm':\n      initializing = false;\n      if (ev.data.err) {\n        aborted = true;\n        initWasmCallbacks[1](ev.data.err);\n      } else {\n        initialized = true;\n        initWasmCallbacks[0]();\n      }\n      break;\n    case 'init-ort':\n      if (ev.data.err) {\n        initOrtCallbacks[1](ev.data.err);\n      } else {\n        initOrtCallbacks[0]();\n      }\n      break;\n    case 'create_allocate':\n      if (ev.data.err) {\n        createSessionAllocateCallbacks.shift()![1](ev.data.err);\n      } else {\n        createSessionAllocateCallbacks.shift()![0](ev.data.out!);\n      }\n      break;\n    case 'create_finalize':\n      if (ev.data.err) {\n        createSessionFinalizeCallbacks.shift()![1](ev.data.err);\n      } else {\n        createSessionFinalizeCallbacks.shift()![0](ev.data.out!);\n      }\n      break;\n    case 'create':\n      if (ev.data.err) {\n        createSessionCallbacks.shift()![1](ev.data.err);\n      } else {\n        createSessionCallbacks.shift()![0](ev.data.out!);\n      }\n      break;\n    case 'release':\n      if (ev.data.err) {\n        releaseSessionCallbacks.shift()![1](ev.data.err);\n      } else {\n        releaseSessionCallbacks.shift()![0]();\n      }\n      break;\n    case 'run':\n      if (ev.data.err) {\n        runCallbacks.shift()![1](ev.data.err);\n      } else {\n        runCallbacks.shift()![0](ev.data.out!);\n      }\n      break;\n    case 'end-profiling':\n      if (ev.data.err) {\n        endProfilingCallbacks.shift()![1](ev.data.err);\n      } else {\n        endProfilingCallbacks.shift()![0]();\n      }\n      break;\n    default:\n  }\n};\n\nconst scriptSrc = typeof document !== 'undefined' ? (document?.currentScript as HTMLScriptElement)?.src : undefined;\n\nexport const initWasm = async(): Promise<void> => {\n  if (!BUILD_DEFS.DISABLE_WASM_PROXY && isProxy()) {\n    if (initialized) {\n      return;\n    }\n    if (initializing) {\n      throw new Error('multiple calls to \\'initWasm()\\' detected.');\n    }\n    if (aborted) {\n      throw new Error('previous call to \\'initWasm()\\' failed.');\n    }\n\n    initializing = true;\n\n    // overwrite wasm filepaths\n    if (env.wasm.wasmPaths === undefined) {\n      if (scriptSrc && scriptSrc.indexOf('blob:') !== 0) {\n        env.wasm.wasmPaths = scriptSrc.substr(0, +(scriptSrc).lastIndexOf('/') + 1);\n      }\n    }\n\n    return new Promise<void>((resolve, reject) => {\n      proxyWorker?.terminate();\n      // eslint-disable-next-line @typescript-eslint/no-var-requires, @typescript-eslint/no-require-imports\n      proxyWorker = require('worker-loader?inline=no-fallback!./proxy-worker/main').default() as Worker;\n      proxyWorker.onmessage = onProxyWorkerMessage;\n      initWasmCallbacks = [resolve, reject];\n      const message: OrtWasmMessage = {type: 'init-wasm', in : env.wasm};\n      proxyWorker.postMessage(message);\n    });\n\n  } else {\n    return initializeWebAssembly(env.wasm);\n  }\n};\n\nexport const initOrt = async(numThreads: number, loggingLevel: number): Promise<void> => {\n  if (!BUILD_DEFS.DISABLE_WASM_PROXY && isProxy()) {\n    ensureWorker();\n    return new Promise<void>((resolve, reject) => {\n      initOrtCallbacks = [resolve, reject];\n      const message: OrtWasmMessage = {type: 'init-ort', in : {numThreads, loggingLevel}};\n      proxyWorker!.postMessage(message);\n    });\n  } else {\n    core.initOrt(numThreads, loggingLevel);\n  }\n};\n\nexport const createSessionAllocate = async(model: Uint8Array): Promise<SerializableModeldata> => {\n  if (!BUILD_DEFS.DISABLE_WASM_PROXY && isProxy()) {\n    ensureWorker();\n    return new Promise<SerializableModeldata>((resolve, reject) => {\n      createSessionAllocateCallbacks.push([resolve, reject]);\n      const message: OrtWasmMessage = {type: 'create_allocate', in : {model}};\n      proxyWorker!.postMessage(message, [model.buffer]);\n    });\n  } else {\n    return core.createSessionAllocate(model);\n  }\n};\n\nexport const createSessionFinalize = async(modeldata: SerializableModeldata, options?: InferenceSession.SessionOptions):\n    Promise<SerializableSessionMetadata> => {\n      if (!BUILD_DEFS.DISABLE_WASM_PROXY && isProxy()) {\n        ensureWorker();\n        return new Promise<SerializableSessionMetadata>((resolve, reject) => {\n          createSessionFinalizeCallbacks.push([resolve, reject]);\n          const message: OrtWasmMessage = {type: 'create_finalize', in : {modeldata, options}};\n          proxyWorker!.postMessage(message);\n        });\n      } else {\n        return core.createSessionFinalize(modeldata, options);\n      }\n    };\n\nexport const createSession =\n    async(model: Uint8Array, options?: InferenceSession.SessionOptions): Promise<SerializableSessionMetadata> => {\n  if (!BUILD_DEFS.DISABLE_WASM_PROXY && isProxy()) {\n    ensureWorker();\n    return new Promise<SerializableSessionMetadata>((resolve, reject) => {\n      createSessionCallbacks.push([resolve, reject]);\n      const message: OrtWasmMessage = {type: 'create', in : {model, options}};\n      proxyWorker!.postMessage(message, [model.buffer]);\n    });\n  } else {\n    return core.createSession(model, options);\n  }\n};\n\nexport const releaseSession = async(sessionId: number): Promise<void> => {\n  if (!BUILD_DEFS.DISABLE_WASM_PROXY && isProxy()) {\n    ensureWorker();\n    return new Promise<void>((resolve, reject) => {\n      releaseSessionCallbacks.push([resolve, reject]);\n      const message: OrtWasmMessage = {type: 'release', in : sessionId};\n      proxyWorker!.postMessage(message);\n    });\n  } else {\n    core.releaseSession(sessionId);\n  }\n};\n\nexport const run = async(\n    sessionId: number, inputIndices: number[], inputs: SerializableTensor[], outputIndices: number[],\n    options: InferenceSession.RunOptions): Promise<SerializableTensor[]> => {\n  if (!BUILD_DEFS.DISABLE_WASM_PROXY && isProxy()) {\n    ensureWorker();\n    return new Promise<SerializableTensor[]>((resolve, reject) => {\n      runCallbacks.push([resolve, reject]);\n      const message: OrtWasmMessage = {type: 'run', in : {sessionId, inputIndices, inputs, outputIndices, options}};\n      proxyWorker!.postMessage(message, core.extractTransferableBuffers(inputs));\n    });\n  } else {\n    return core.run(sessionId, inputIndices, inputs, outputIndices, options);\n  }\n};\n\nexport const endProfiling = async(sessionId: number): Promise<void> => {\n  if (!BUILD_DEFS.DISABLE_WASM_PROXY && isProxy()) {\n    ensureWorker();\n    return new Promise<void>((resolve, reject) => {\n      endProfilingCallbacks.push([resolve, reject]);\n      const message: OrtWasmMessage = {type: 'end-profiling', in : sessionId};\n      proxyWorker!.postMessage(message);\n    });\n  } else {\n    core.endProfiling(sessionId);\n  }\n};\n", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\nimport {InferenceSession} from 'onnxruntime-common';\n\nimport {iterateExtraOptions} from './options-utils';\nimport {allocWasmString} from './string-utils';\nimport {getInstance} from './wasm-factory';\n\nexport const setRunOptions = (options: InferenceSession.RunOptions): [number, number[]] => {\n  const wasm = getInstance();\n  let runOptionsHandle = 0;\n  const allocs: number[] = [];\n\n  const runOptions: InferenceSession.RunOptions = options || {};\n\n  try {\n    if (options?.logSeverityLevel === undefined) {\n      runOptions.logSeverityLevel = 2;  // Default to warning\n    } else if (\n        typeof options.logSeverityLevel !== 'number' || !Number.isInteger(options.logSeverityLevel) ||\n        options.logSeverityLevel < 0 || options.logSeverityLevel > 4) {\n      throw new Error(`log serverity level is not valid: ${options.logSeverityLevel}`);\n    }\n\n    if (options?.logVerbosityLevel === undefined) {\n      runOptions.logVerbosityLevel = 0;  // Default to 0\n    } else if (typeof options.logVerbosityLevel !== 'number' || !Number.isInteger(options.logVerbosityLevel)) {\n      throw new Error(`log verbosity level is not valid: ${options.logVerbosityLevel}`);\n    }\n\n    if (options?.terminate === undefined) {\n      runOptions.terminate = false;\n    }\n\n    let tagDataOffset = 0;\n    if (options?.tag !== undefined) {\n      tagDataOffset = allocWasmString(options.tag, allocs);\n    }\n\n    runOptionsHandle = wasm._OrtCreateRunOptions(\n        runOptions.logSeverityLevel!, runOptions.logVerbosityLevel!, !!runOptions.terminate!, tagDataOffset);\n    if (runOptionsHandle === 0) {\n      throw new Error('Can\\'t create run options');\n    }\n\n    if (options?.extra !== undefined) {\n      iterateExtraOptions(options.extra, '', new WeakSet<Record<string, unknown>>(), (key, value) => {\n        const keyDataOffset = allocWasmString(key, allocs);\n        const valueDataOffset = allocWasmString(value, allocs);\n\n        if (wasm._OrtAddRunConfigEntry(runOptionsHandle, keyDataOffset, valueDataOffset) !== 0) {\n          throw new Error(`Can't set a run config entry: ${key} - ${value}`);\n        }\n      });\n    }\n\n    return [runOptionsHandle, allocs];\n  } catch (e) {\n    if (runOptionsHandle !== 0) {\n      wasm._OrtReleaseRunOptions(runOptionsHandle);\n    }\n    allocs.forEach(wasm._free);\n    throw e;\n  }\n};\n", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\nimport {readFile} from 'fs';\nimport {env, InferenceSession, SessionHandler, Tensor} from 'onnxruntime-common';\nimport {promisify} from 'util';\n\nimport {SerializableModeldata} from './proxy-messages';\nimport {createSession, createSessionAllocate, createSessionFinalize, endProfiling, initOrt, releaseSession, run} from './proxy-wrapper';\n\nlet ortInit: boolean;\n\n\nconst getLogLevel = (logLevel: 'verbose'|'info'|'warning'|'error'|'fatal'): number => {\n  switch (logLevel) {\n    case 'verbose':\n      return 0;\n    case 'info':\n      return 1;\n    case 'warning':\n      return 2;\n    case 'error':\n      return 3;\n    case 'fatal':\n      return 4;\n    default:\n      throw new Error(`unsupported logging level: ${logLevel}`);\n  }\n};\n\n\nexport class OnnxruntimeWebAssemblySessionHandler implements SessionHandler {\n  private sessionId: number;\n\n  inputNames: string[];\n  outputNames: string[];\n\n  async createSessionAllocate(path: string): Promise<SerializableModeldata> {\n    // fetch model from url and move to wasm heap. The arraybufffer that held the http\n    // response is freed once we return\n    const response = await fetch(path);\n    const arrayBuffer = await response.arrayBuffer();\n    return createSessionAllocate(new Uint8Array(arrayBuffer));\n  }\n\n  async loadModel(pathOrBuffer: string|Uint8Array, options?: InferenceSession.SessionOptions): Promise<void> {\n    if (!ortInit) {\n      await initOrt(env.wasm.numThreads!, getLogLevel(env.logLevel!));\n      ortInit = true;\n    }\n\n    if (typeof pathOrBuffer === 'string') {\n      if (typeof fetch === 'undefined') {\n        // node\n        const model = await promisify(readFile)(pathOrBuffer);\n        [this.sessionId, this.inputNames, this.outputNames] = await createSession(model, options);\n      } else {\n        // browser\n        // fetch model and move to wasm heap.\n        const modelData: SerializableModeldata = await this.createSessionAllocate(pathOrBuffer);\n        // create the session\n        [this.sessionId, this.inputNames, this.outputNames] = await createSessionFinalize(modelData, options);\n      }\n    } else {\n      [this.sessionId, this.inputNames, this.outputNames] = await createSession(pathOrBuffer, options);\n    }\n  }\n\n  async dispose(): Promise<void> {\n    return releaseSession(this.sessionId);\n  }\n\n  async run(feeds: SessionHandler.FeedsType, fetches: SessionHandler.FetchesType, options: InferenceSession.RunOptions):\n      Promise<SessionHandler.ReturnType> {\n    const inputArray: Tensor[] = [];\n    const inputIndices: number[] = [];\n    Object.entries(feeds).forEach(kvp => {\n      const name = kvp[0];\n      const tensor = kvp[1];\n      const index = this.inputNames.indexOf(name);\n      if (index === -1) {\n        throw new Error(`invalid input '${name}'`);\n      }\n      inputArray.push(tensor);\n      inputIndices.push(index);\n    });\n\n    const outputIndices: number[] = [];\n    Object.entries(fetches).forEach(kvp => {\n      const name = kvp[0];\n      // TODO: support pre-allocated output\n      const index = this.outputNames.indexOf(name);\n      if (index === -1) {\n        throw new Error(`invalid output '${name}'`);\n      }\n      outputIndices.push(index);\n    });\n\n    const outputs =\n        await run(this.sessionId, inputIndices, inputArray.map(t => [t.type, t.dims, t.data]), outputIndices, options);\n\n    const result: SessionHandler.ReturnType = {};\n    for (let i = 0; i < outputs.length; i++) {\n      result[this.outputNames[outputIndices[i]]] = new Tensor(outputs[i][0], outputs[i][2], outputs[i][1]);\n    }\n    return result;\n  }\n\n  startProfiling(): void {\n    // TODO: implement profiling\n  }\n\n  endProfiling(): void {\n    void endProfiling(this.sessionId);\n  }\n}\n", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\nimport {InferenceSession} from 'onnxruntime-common';\n\nimport {iterateExtraOptions} from './options-utils';\nimport {allocWasmString} from './string-utils';\nimport {getInstance} from './wasm-factory';\n\nconst getGraphOptimzationLevel = (graphOptimizationLevel: string|unknown): number => {\n  switch (graphOptimizationLevel) {\n    case 'disabled':\n      return 0;\n    case 'basic':\n      return 1;\n    case 'extended':\n      return 2;\n    case 'all':\n      return 99;\n    default:\n      throw new Error(`unsupported graph optimization level: ${graphOptimizationLevel}`);\n  }\n};\n\nconst getExecutionMode = (executionMode: 'sequential'|'parallel'): number => {\n  switch (executionMode) {\n    case 'sequential':\n      return 0;\n    case 'parallel':\n      return 1;\n    default:\n      throw new Error(`unsupported execution mode: ${executionMode}`);\n  }\n};\n\nconst appendDefaultOptions = (options: InferenceSession.SessionOptions): void => {\n  if (!options.extra) {\n    options.extra = {};\n  }\n  if (!options.extra.session) {\n    options.extra.session = {};\n  }\n  const session = options.extra.session as Record<string, string>;\n  if (!session.use_ort_model_bytes_directly) {\n    // eslint-disable-next-line camelcase\n    session.use_ort_model_bytes_directly = '1';\n  }\n};\n\nconst setExecutionProviders =\n    (sessionOptionsHandle: number, executionProviders: readonly InferenceSession.ExecutionProviderConfig[],\n     allocs: number[]): void => {\n      for (const ep of executionProviders) {\n        let epName = typeof ep === 'string' ? ep : ep.name;\n\n        // check EP name\n        switch (epName) {\n          case 'xnnpack':\n            epName = 'XNNPACK';\n            break;\n          case 'wasm':\n          case 'cpu':\n            continue;\n          default:\n            throw new Error(`not supported EP: ${epName}`);\n        }\n\n        const epNameDataOffset = allocWasmString(epName, allocs);\n        if (getInstance()._OrtAppendExecutionProvider(sessionOptionsHandle, epNameDataOffset) !== 0) {\n          throw new Error(`Can't append execution provider: ${epName}`);\n        }\n      }\n    };\n\nexport const setSessionOptions = (options?: InferenceSession.SessionOptions): [number, number[]] => {\n  const wasm = getInstance();\n  let sessionOptionsHandle = 0;\n  const allocs: number[] = [];\n\n  const sessionOptions: InferenceSession.SessionOptions = options || {};\n  appendDefaultOptions(sessionOptions);\n\n  try {\n    if (options?.graphOptimizationLevel === undefined) {\n      sessionOptions.graphOptimizationLevel = 'all';\n    }\n    const graphOptimizationLevel = getGraphOptimzationLevel(sessionOptions.graphOptimizationLevel!);\n\n    if (options?.enableCpuMemArena === undefined) {\n      sessionOptions.enableCpuMemArena = true;\n    }\n\n    if (options?.enableMemPattern === undefined) {\n      sessionOptions.enableMemPattern = true;\n    }\n\n    if (options?.executionMode === undefined) {\n      sessionOptions.executionMode = 'sequential';\n    }\n    const executionMode = getExecutionMode(sessionOptions.executionMode!);\n\n    let logIdDataOffset = 0;\n    if (options?.logId !== undefined) {\n      logIdDataOffset = allocWasmString(options.logId, allocs);\n    }\n\n    if (options?.logSeverityLevel === undefined) {\n      sessionOptions.logSeverityLevel = 2;  // Default to warning\n    } else if (\n        typeof options.logSeverityLevel !== 'number' || !Number.isInteger(options.logSeverityLevel) ||\n        options.logSeverityLevel < 0 || options.logSeverityLevel > 4) {\n      throw new Error(`log serverity level is not valid: ${options.logSeverityLevel}`);\n    }\n\n    if (options?.logVerbosityLevel === undefined) {\n      sessionOptions.logVerbosityLevel = 0;  // Default to 0\n    } else if (typeof options.logVerbosityLevel !== 'number' || !Number.isInteger(options.logVerbosityLevel)) {\n      throw new Error(`log verbosity level is not valid: ${options.logVerbosityLevel}`);\n    }\n\n    if (options?.enableProfiling === undefined) {\n      sessionOptions.enableProfiling = false;\n    }\n\n    sessionOptionsHandle = wasm._OrtCreateSessionOptions(\n        graphOptimizationLevel, !!sessionOptions.enableCpuMemArena!, !!sessionOptions.enableMemPattern!, executionMode,\n        !!sessionOptions.enableProfiling!, 0, logIdDataOffset, sessionOptions.logSeverityLevel!,\n        sessionOptions.logVerbosityLevel!);\n    if (sessionOptionsHandle === 0) {\n      throw new Error('Can\\'t create session options');\n    }\n\n    if (options?.executionProviders) {\n      setExecutionProviders(sessionOptionsHandle, options.executionProviders, allocs);\n    }\n\n    if (options?.extra !== undefined) {\n      iterateExtraOptions(options.extra, '', new WeakSet<Record<string, unknown>>(), (key, value) => {\n        const keyDataOffset = allocWasmString(key, allocs);\n        const valueDataOffset = allocWasmString(value, allocs);\n\n        if (wasm._OrtAddSessionConfigEntry(sessionOptionsHandle, keyDataOffset, valueDataOffset) !== 0) {\n          throw new Error(`Can't set a session config entry: ${key} - ${value}`);\n        }\n      });\n    }\n\n    return [sessionOptionsHandle, allocs];\n  } catch (e) {\n    if (sessionOptionsHandle !== 0) {\n      wasm._OrtReleaseSessionOptions(sessionOptionsHandle);\n    }\n    allocs.forEach(wasm._free);\n    throw e;\n  }\n};\n", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\nimport {getInstance} from './wasm-factory';\n\nexport const allocWasmString = (data: string, allocs: number[]): number => {\n  const wasm = getInstance();\n\n  const dataLength = wasm.lengthBytesUTF8(data) + 1;\n  const dataOffset = wasm._malloc(dataLength);\n  wasm.stringToUTF8(data, dataOffset, dataLength);\n  allocs.push(dataOffset);\n\n  return dataOffset;\n};\n", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\nimport {InferenceSession, Tensor} from 'onnxruntime-common';\n\nimport {SerializableModeldata, SerializableSessionMetadata, SerializableTensor} from './proxy-messages';\nimport {setRunOptions} from './run-options';\nimport {setSessionOptions} from './session-options';\nimport {allocWasmString} from './string-utils';\nimport {getInstance} from './wasm-factory';\n\n/**\n * initialize ORT environment.\n * @param numThreads SetGlobalIntraOpNumThreads(numThreads)\n * @param loggingLevel CreateEnv(static_cast<OrtLoggingLevel>(logging_level))\n */\nexport const initOrt = (numThreads: number, loggingLevel: number): void => {\n  const errorCode = getInstance()._OrtInit(numThreads, loggingLevel);\n  if (errorCode !== 0) {\n    throw new Error(`Can't initialize onnxruntime. error code = ${errorCode}`);\n  }\n};\n\n/**\n *  tuple elements are: InferenceSession ID; inputNamesUTF8Encoded; outputNamesUTF8Encoded\n */\ntype SessionMetadata = [number, number[], number[]];\n\nconst activeSessions = new Map<number, SessionMetadata>();\n\n/**\n * create an instance of InferenceSession.\n * @returns the metadata of InferenceSession. 0-value handle for failure.\n */\nexport const createSessionAllocate = (model: Uint8Array): [number, number] => {\n  const wasm = getInstance();\n  const modelDataOffset = wasm._malloc(model.byteLength);\n  wasm.HEAPU8.set(model, modelDataOffset);\n  return [modelDataOffset, model.byteLength];\n};\n\nexport const createSessionFinalize =\n    (modelData: SerializableModeldata, options?: InferenceSession.SessionOptions): SerializableSessionMetadata => {\n      const wasm = getInstance();\n\n      let sessionHandle = 0;\n      let sessionOptionsHandle = 0;\n      let allocs: number[] = [];\n\n      try {\n        [sessionOptionsHandle, allocs] = setSessionOptions(options);\n\n        sessionHandle = wasm._OrtCreateSession(modelData[0], modelData[1], sessionOptionsHandle);\n        if (sessionHandle === 0) {\n          throw new Error('Can\\'t create a session');\n        }\n      } finally {\n        wasm._free(modelData[0]);\n        wasm._OrtReleaseSessionOptions(sessionOptionsHandle);\n        allocs.forEach(wasm._free);\n      }\n\n      const inputCount = wasm._OrtGetInputCount(sessionHandle);\n      const outputCount = wasm._OrtGetOutputCount(sessionHandle);\n\n      const inputNames = [];\n      const inputNamesUTF8Encoded = [];\n      const outputNames = [];\n      const outputNamesUTF8Encoded = [];\n      for (let i = 0; i < inputCount; i++) {\n        const name = wasm._OrtGetInputName(sessionHandle, i);\n        if (name === 0) {\n          throw new Error('Can\\'t get an input name');\n        }\n        inputNamesUTF8Encoded.push(name);\n        inputNames.push(wasm.UTF8ToString(name));\n      }\n      for (let i = 0; i < outputCount; i++) {\n        const name = wasm._OrtGetOutputName(sessionHandle, i);\n        if (name === 0) {\n          throw new Error('Can\\'t get an output name');\n        }\n        outputNamesUTF8Encoded.push(name);\n        outputNames.push(wasm.UTF8ToString(name));\n      }\n\n      activeSessions.set(sessionHandle, [sessionHandle, inputNamesUTF8Encoded, outputNamesUTF8Encoded]);\n      return [sessionHandle, inputNames, outputNames];\n    };\n\n\n/**\n * create an instance of InferenceSession.\n * @returns the metadata of InferenceSession. 0-value handle for failure.\n */\nexport const createSession =\n    (model: Uint8Array, options?: InferenceSession.SessionOptions): SerializableSessionMetadata => {\n      const modelData: SerializableModeldata = createSessionAllocate(model);\n      return createSessionFinalize(modelData, options);\n    };\n\nexport const releaseSession = (sessionId: number): void => {\n  const wasm = getInstance();\n  const session = activeSessions.get(sessionId);\n  if (!session) {\n    throw new Error('invalid session id');\n  }\n  const sessionHandle = session[0];\n  const inputNamesUTF8Encoded = session[1];\n  const outputNamesUTF8Encoded = session[2];\n\n  inputNamesUTF8Encoded.forEach(wasm._OrtFree);\n  outputNamesUTF8Encoded.forEach(wasm._OrtFree);\n  wasm._OrtReleaseSession(sessionHandle);\n  activeSessions.delete(sessionId);\n};\n\n/**\n * Copied from ONNX definition. Use this to drop dependency 'onnx_proto' to decrease compiled .js file size.\n */\nconst enum DataType {\n  undefined = 0,\n  float = 1,\n  uint8 = 2,\n  int8 = 3,\n  uint16 = 4,\n  int16 = 5,\n  int32 = 6,\n  int64 = 7,\n  string = 8,\n  bool = 9,\n  float16 = 10,\n  double = 11,\n  uint32 = 12,\n  uint64 = 13,\n  complex64 = 14,\n  complex128 = 15,\n  bfloat16 = 16\n}\n\n\nconst tensorDataTypeStringToEnum = (type: string): DataType => {\n  switch (type) {\n    case 'int8':\n      return DataType.int8;\n    case 'uint8':\n      return DataType.uint8;\n    case 'bool':\n      return DataType.bool;\n    case 'int16':\n      return DataType.int16;\n    case 'uint16':\n      return DataType.uint16;\n    case 'int32':\n      return DataType.int32;\n    case 'uint32':\n      return DataType.uint32;\n    case 'float32':\n      return DataType.float;\n    case 'float64':\n      return DataType.double;\n    case 'string':\n      return DataType.string;\n    case 'int64':\n      return DataType.int64;\n    case 'uint64':\n      return DataType.uint64;\n\n    default:\n      throw new Error(`unsupported data type: ${type}`);\n  }\n};\n\nconst tensorDataTypeEnumToString = (typeProto: DataType): Tensor.Type => {\n  switch (typeProto) {\n    case DataType.int8:\n      return 'int8';\n    case DataType.uint8:\n      return 'uint8';\n    case DataType.bool:\n      return 'bool';\n    case DataType.int16:\n      return 'int16';\n    case DataType.uint16:\n      return 'uint16';\n    case DataType.int32:\n      return 'int32';\n    case DataType.uint32:\n      return 'uint32';\n    case DataType.float:\n      return 'float32';\n    case DataType.double:\n      return 'float64';\n    case DataType.string:\n      return 'string';\n    case DataType.int64:\n      return 'int64';\n    case DataType.uint64:\n      return 'uint64';\n\n    default:\n      throw new Error(`unsupported data type: ${typeProto}`);\n  }\n};\n\nconst numericTensorTypeToTypedArray = (type: Tensor.Type): Float32ArrayConstructor|Uint8ArrayConstructor|\n    Int8ArrayConstructor|Uint16ArrayConstructor|Int16ArrayConstructor|Int32ArrayConstructor|BigInt64ArrayConstructor|\n    Uint8ArrayConstructor|Float64ArrayConstructor|Uint32ArrayConstructor|BigUint64ArrayConstructor => {\n      switch (type) {\n        case 'float32':\n          return Float32Array;\n        case 'uint8':\n          return Uint8Array;\n        case 'int8':\n          return Int8Array;\n        case 'uint16':\n          return Uint16Array;\n        case 'int16':\n          return Int16Array;\n        case 'int32':\n          return Int32Array;\n        case 'bool':\n          return Uint8Array;\n        case 'float64':\n          return Float64Array;\n        case 'uint32':\n          return Uint32Array;\n        case 'int64':\n          return BigInt64Array;\n        case 'uint64':\n          return BigUint64Array;\n        default:\n          throw new Error(`unsupported type: ${type}`);\n      }\n    };\n\n/**\n * perform inference run\n */\nexport const run =\n    (sessionId: number, inputIndices: number[], inputs: SerializableTensor[], outputIndices: number[],\n     options: InferenceSession.RunOptions): SerializableTensor[] => {\n      const wasm = getInstance();\n      const session = activeSessions.get(sessionId);\n      if (!session) {\n        throw new Error('invalid session id');\n      }\n      const sessionHandle = session[0];\n      const inputNamesUTF8Encoded = session[1];\n      const outputNamesUTF8Encoded = session[2];\n\n      const inputCount = inputIndices.length;\n      const outputCount = outputIndices.length;\n\n      let runOptionsHandle = 0;\n      let runOptionsAllocs: number[] = [];\n\n      const inputValues: number[] = [];\n      const inputAllocs: number[] = [];\n\n      try {\n        [runOptionsHandle, runOptionsAllocs] = setRunOptions(options);\n\n        // create input tensors\n        for (let i = 0; i < inputCount; i++) {\n          const dataType = inputs[i][0];\n          const dims = inputs[i][1];\n          const data = inputs[i][2];\n\n          let dataOffset: number;\n          let dataByteLength: number;\n\n          if (Array.isArray(data)) {\n            // string tensor\n            dataByteLength = 4 * data.length;\n            dataOffset = wasm._malloc(dataByteLength);\n            inputAllocs.push(dataOffset);\n            let dataIndex = dataOffset / 4;\n            for (let i = 0; i < data.length; i++) {\n              if (typeof data[i] !== 'string') {\n                throw new TypeError(`tensor data at index ${i} is not a string`);\n              }\n              wasm.HEAPU32[dataIndex++] = allocWasmString(data[i], inputAllocs);\n            }\n          } else {\n            dataByteLength = data.byteLength;\n            dataOffset = wasm._malloc(dataByteLength);\n            inputAllocs.push(dataOffset);\n            wasm.HEAPU8.set(new Uint8Array(data.buffer, data.byteOffset, dataByteLength), dataOffset);\n          }\n\n          const stack = wasm.stackSave();\n          const dimsOffset = wasm.stackAlloc(4 * dims.length);\n          try {\n            let dimIndex = dimsOffset / 4;\n            dims.forEach(d => wasm.HEAP32[dimIndex++] = d);\n            const tensor = wasm._OrtCreateTensor(\n                tensorDataTypeStringToEnum(dataType), dataOffset, dataByteLength, dimsOffset, dims.length);\n            if (tensor === 0) {\n              throw new Error('Can\\'t create a tensor');\n            }\n            inputValues.push(tensor);\n          } finally {\n            wasm.stackRestore(stack);\n          }\n        }\n\n        const beforeRunStack = wasm.stackSave();\n        const inputValuesOffset = wasm.stackAlloc(inputCount * 4);\n        const inputNamesOffset = wasm.stackAlloc(inputCount * 4);\n        const outputValuesOffset = wasm.stackAlloc(outputCount * 4);\n        const outputNamesOffset = wasm.stackAlloc(outputCount * 4);\n\n        try {\n          let inputValuesIndex = inputValuesOffset / 4;\n          let inputNamesIndex = inputNamesOffset / 4;\n          let outputValuesIndex = outputValuesOffset / 4;\n          let outputNamesIndex = outputNamesOffset / 4;\n          for (let i = 0; i < inputCount; i++) {\n            wasm.HEAPU32[inputValuesIndex++] = inputValues[i];\n            wasm.HEAPU32[inputNamesIndex++] = inputNamesUTF8Encoded[inputIndices[i]];\n          }\n          for (let i = 0; i < outputCount; i++) {\n            wasm.HEAPU32[outputValuesIndex++] = 0;\n            wasm.HEAPU32[outputNamesIndex++] = outputNamesUTF8Encoded[outputIndices[i]];\n          }\n\n          // support RunOptions\n          let errorCode = wasm._OrtRun(\n              sessionHandle, inputNamesOffset, inputValuesOffset, inputCount, outputNamesOffset, outputCount,\n              outputValuesOffset, runOptionsHandle);\n\n          const output: SerializableTensor[] = [];\n\n          if (errorCode === 0) {\n            for (let i = 0; i < outputCount; i++) {\n              const tensor = wasm.HEAPU32[outputValuesOffset / 4 + i];\n\n              const beforeGetTensorDataStack = wasm.stackSave();\n              // stack allocate 4 pointer value\n              const tensorDataOffset = wasm.stackAlloc(4 * 4);\n\n              let type: Tensor.Type|undefined, dataOffset = 0;\n              try {\n                errorCode = wasm._OrtGetTensorData(\n                    tensor, tensorDataOffset, tensorDataOffset + 4, tensorDataOffset + 8, tensorDataOffset + 12);\n                if (errorCode !== 0) {\n                  throw new Error(`Can't access output tensor data. error code = ${errorCode}`);\n                }\n                let tensorDataIndex = tensorDataOffset / 4;\n                const dataType = wasm.HEAPU32[tensorDataIndex++];\n                dataOffset = wasm.HEAPU32[tensorDataIndex++];\n                const dimsOffset = wasm.HEAPU32[tensorDataIndex++];\n                const dimsLength = wasm.HEAPU32[tensorDataIndex++];\n                const dims = [];\n                for (let i = 0; i < dimsLength; i++) {\n                  dims.push(wasm.HEAPU32[dimsOffset / 4 + i]);\n                }\n                wasm._OrtFree(dimsOffset);\n\n                const size = dims.length === 0 ? 1 : dims.reduce((a, b) => a * b);\n                type = tensorDataTypeEnumToString(dataType);\n                if (type === 'string') {\n                  const stringData: string[] = [];\n                  let dataIndex = dataOffset / 4;\n                  for (let i = 0; i < size; i++) {\n                    const offset = wasm.HEAPU32[dataIndex++];\n                    const maxBytesToRead = i === size - 1 ? undefined : wasm.HEAPU32[dataIndex] - offset;\n                    stringData.push(wasm.UTF8ToString(offset, maxBytesToRead));\n                  }\n                  output.push([type, dims, stringData]);\n                } else {\n                  const typedArrayConstructor = numericTensorTypeToTypedArray(type);\n                  const data = new typedArrayConstructor(size);\n                  new Uint8Array(data.buffer, data.byteOffset, data.byteLength)\n                      .set(wasm.HEAPU8.subarray(dataOffset, dataOffset + data.byteLength));\n                  output.push([type, dims, data]);\n                }\n              } finally {\n                wasm.stackRestore(beforeGetTensorDataStack);\n                if (type === 'string' && dataOffset) {\n                  wasm._free(dataOffset);\n                }\n                wasm._OrtReleaseTensor(tensor);\n              }\n            }\n          }\n\n          if (errorCode === 0) {\n            return output;\n          } else {\n            throw new Error(`failed to call OrtRun(). error code = ${errorCode}.`);\n          }\n        } finally {\n          wasm.stackRestore(beforeRunStack);\n        }\n      } finally {\n        inputValues.forEach(wasm._OrtReleaseTensor);\n        inputAllocs.forEach(wasm._free);\n\n        wasm._OrtReleaseRunOptions(runOptionsHandle);\n        runOptionsAllocs.forEach(wasm._free);\n      }\n    };\n\n/**\n * end profiling\n */\nexport const endProfiling = (sessionId: number): void => {\n  const wasm = getInstance();\n  const session = activeSessions.get(sessionId);\n  if (!session) {\n    throw new Error('invalid session id');\n  }\n  const sessionHandle = session[0];\n\n  // profile file name is not used yet, but it must be freed.\n  const profileFileName = wasm._OrtEndProfiling(sessionHandle);\n  if (profileFileName === 0) {\n    throw new Error('Can\\'t get an profile file name');\n  }\n  wasm._OrtFree(profileFileName);\n};\n\nexport const extractTransferableBuffers = (tensors: readonly SerializableTensor[]): ArrayBufferLike[] => {\n  const buffers: ArrayBufferLike[] = [];\n  for (const tensor of tensors) {\n    const data = tensor[2];\n    if (!Array.isArray(data) && data.buffer) {\n      buffers.push(data.buffer);\n    }\n  }\n  return buffers;\n};\n", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\nimport {Env} from 'onnxruntime-common';\nimport * as path from 'path';\n\nimport {OrtWasmModule} from './binding/ort-wasm';\nimport {OrtWasmThreadedModule} from './binding/ort-wasm-threaded';\nimport ortWasmFactory from './binding/ort-wasm.js';\n\nconst ortWasmFactoryThreaded: EmscriptenModuleFactory<OrtWasmModule> =\n    // eslint-disable-next-line @typescript-eslint/no-require-imports\n    !BUILD_DEFS.DISABLE_WASM_THREAD ? require('./binding/ort-wasm-threaded.js') : ortWasmFactory;\n\nlet wasm: OrtWasmModule|undefined;\nlet initialized = false;\nlet initializing = false;\nlet aborted = false;\n\nconst isMultiThreadSupported = (): boolean => {\n  try {\n    // If 'SharedArrayBuffer' is not available, WebAssembly threads will not work.\n    if (typeof SharedArrayBuffer === 'undefined') {\n      return false;\n    }\n\n    // Test for transferability of SABs (for browsers. needed for Firefox)\n    // https://groups.google.com/forum/#!msg/mozilla.dev.platform/IHkBZlHETpA/dwsMNchWEQAJ\n    if (typeof MessageChannel !== 'undefined') {\n      new MessageChannel().port1.postMessage(new SharedArrayBuffer(1));\n    }\n\n    // Test for WebAssembly threads capability (for both browsers and Node.js)\n    // This typed array is a WebAssembly program containing threaded instructions.\n    return WebAssembly.validate(new Uint8Array([\n      0, 97, 115, 109, 1, 0,  0,  0, 1, 4, 1,  96, 0,   0,  3, 2, 1,  0, 5,\n      4, 1,  3,   1,   1, 10, 11, 1, 9, 0, 65, 0,  254, 16, 2, 0, 26, 11\n    ]));\n  } catch (e) {\n    return false;\n  }\n};\n\nconst isSimdSupported = (): boolean => {\n  try {\n    // Test for WebAssembly SIMD capability (for both browsers and Node.js)\n    // This typed array is a WebAssembly program containing SIMD instructions.\n\n    // The binary data is generated from the following code by wat2wasm:\n    //\n    // (module\n    //   (type $t0 (func))\n    //   (func $f0 (type $t0)\n    //     (drop\n    //       (i32x4.dot_i16x8_s\n    //         (i8x16.splat\n    //           (i32.const 0))\n    //         (v128.const i32x4 0x00000000 0x00000000 0x00000000 0x00000000)))))\n\n    return WebAssembly.validate(new Uint8Array([\n      0,   97, 115, 109, 1, 0, 0, 0, 1, 4, 1, 96, 0, 0, 3, 2, 1, 0, 10, 30, 1,   28,  0, 65, 0,\n      253, 15, 253, 12,  0, 0, 0, 0, 0, 0, 0, 0,  0, 0, 0, 0, 0, 0, 0,  0,  253, 186, 1, 26, 11\n    ]));\n  } catch (e) {\n    return false;\n  }\n};\n\nconst getWasmFileName = (useSimd: boolean, useThreads: boolean) => {\n  if (useThreads) {\n    return useSimd ? 'ort-wasm-simd-threaded.wasm' : 'ort-wasm-threaded.wasm';\n  } else {\n    return useSimd ? 'ort-wasm-simd.wasm' : 'ort-wasm.wasm';\n  }\n};\n\nexport const initializeWebAssembly = async(flags: Env.WebAssemblyFlags): Promise<void> => {\n  if (initialized) {\n    return Promise.resolve();\n  }\n  if (initializing) {\n    throw new Error('multiple calls to \\'initializeWebAssembly()\\' detected.');\n  }\n  if (aborted) {\n    throw new Error('previous call to \\'initializeWebAssembly()\\' failed.');\n  }\n\n  initializing = true;\n\n  // wasm flags are already initialized\n  const timeout = flags.initTimeout!;\n  const numThreads = flags.numThreads!;\n  const simd = flags.simd!;\n\n  const useThreads = numThreads > 1 && isMultiThreadSupported();\n  const useSimd = simd && isSimdSupported();\n\n  const wasmPrefixOverride = typeof flags.wasmPaths === 'string' ? flags.wasmPaths : undefined;\n  const wasmFileName = getWasmFileName(false, useThreads);\n  const wasmOverrideFileName = getWasmFileName(useSimd, useThreads);\n  const wasmPathOverride = typeof flags.wasmPaths === 'object' ? flags.wasmPaths[wasmOverrideFileName] : undefined;\n\n  let isTimeout = false;\n\n  const tasks: Array<Promise<void>> = [];\n\n  // promise for timeout\n  if (timeout > 0) {\n    tasks.push(new Promise((resolve) => {\n      setTimeout(() => {\n        isTimeout = true;\n        resolve();\n      }, timeout);\n    }));\n  }\n\n  // promise for module initialization\n  tasks.push(new Promise((resolve, reject) => {\n    const factory = useThreads ? ortWasmFactoryThreaded : ortWasmFactory;\n    const config: Partial<OrtWasmModule> = {\n      locateFile: (fileName: string, scriptDirectory: string) => {\n        if (!BUILD_DEFS.DISABLE_WASM_THREAD && useThreads && fileName.endsWith('.worker.js') &&\n            typeof Blob !== 'undefined') {\n          return URL.createObjectURL(new Blob(\n              [\n                // This require() function is handled by webpack to load file content of the corresponding .worker.js\n                // eslint-disable-next-line @typescript-eslint/no-require-imports\n                require('./binding/ort-wasm-threaded.worker.js')\n              ],\n              {type: 'text/javascript'}));\n        }\n\n        if (fileName === wasmFileName) {\n          const prefix: string = wasmPrefixOverride ?? scriptDirectory;\n          return wasmPathOverride ?? prefix + wasmOverrideFileName;\n        }\n\n        return scriptDirectory + fileName;\n      }\n    };\n\n    if (!BUILD_DEFS.DISABLE_WASM_THREAD && useThreads) {\n      if (typeof Blob === 'undefined') {\n        config.mainScriptUrlOrBlob = path.join(__dirname, 'ort-wasm-threaded.js');\n      } else {\n        const scriptSourceCode = `var ortWasmThreaded=(function(){var _scriptDir;return ${factory.toString()}})();`;\n        config.mainScriptUrlOrBlob = new Blob([scriptSourceCode], {type: 'text/javascript'});\n      }\n    }\n\n    factory(config).then(\n        // wasm module initialized successfully\n        module => {\n          initializing = false;\n          initialized = true;\n          wasm = module;\n          resolve();\n        },\n        // wasm module failed to initialize\n        (what) => {\n          initializing = false;\n          aborted = true;\n          reject(what);\n        });\n  }));\n\n  await Promise.race(tasks);\n\n  if (isTimeout) {\n    throw new Error(`WebAssembly backend initializing failed due to timeout: ${timeout}ms`);\n  }\n};\n\nexport const getInstance = (): OrtWasmModule => {\n  if (initialized && wasm) {\n    return wasm;\n  }\n\n  throw new Error('WebAssembly is not initialized yet.');\n};\n\nexport const dispose = (): void => {\n  if (initialized && !initializing && !aborted) {\n    initializing = true;\n\n    (wasm as OrtWasmThreadedModule).PThread?.terminateAllThreads();\n    wasm = undefined;\n\n    initializing = false;\n    initialized = false;\n    aborted = true;\n  }\n};\n", "\nimport worker from \"!!../../../node_modules/worker-loader/dist/runtime/inline.js\";\n\nexport default function Worker_fn() {\n  return worker(\"/*!\\n* ONNX Runtime Web v1.14.0\\n* Copyright (c) Microsoft Corporation. All rights reserved.\\n* Licensed under the MIT License.\\n*/\\n(()=>{var t={474:(t,e,n)=>{var _scriptDir,r=(_scriptDir=(_scriptDir=\\\"undefined\\\"!=typeof document&&document.currentScript?document.currentScript.src:void 0)||\\\"/index.js\\\",function(t){function e(){return j.buffer!=D&&N(j.buffer),P}function r(){return j.buffer!=D&&N(j.buffer),U}function a(){return j.buffer!=D&&N(j.buffer),F}function i(){return j.buffer!=D&&N(j.buffer),I}function o(){return j.buffer!=D&&N(j.buffer),W}var u,c,s;t=t||{},u||(u=void 0!==t?t:{}),u.ready=new Promise((function(t,e){c=t,s=e}));var l,f,p,h,d,y,b=Object.assign({},u),m=\\\"./this.program\\\",g=(t,e)=>{throw e},v=\\\"object\\\"==typeof window,w=\\\"function\\\"==typeof importScripts,_=\\\"object\\\"==typeof process&&\\\"object\\\"==typeof process.versions&&\\\"string\\\"==typeof process.versions.node,O=u.ENVIRONMENT_IS_PTHREAD||!1,A=\\\"\\\";function S(t){return u.locateFile?u.locateFile(t,A):A+t}if(_){let e;A=w?n(908).dirname(A)+\\\"/\\\":\\\"//\\\",y=()=>{d||(h=n(384),d=n(908))},l=function(t,e){return y(),t=d.normalize(t),h.readFileSync(t,e?void 0:\\\"utf8\\\")},p=t=>((t=l(t,!0)).buffer||(t=new Uint8Array(t)),t),f=(t,e,n)=>{y(),t=d.normalize(t),h.readFile(t,(function(t,r){t?n(t):e(r.buffer)}))},1<process.argv.length&&(m=process.argv[1].replace(/\\\\\\\\/g,\\\"/\\\")),process.argv.slice(2),process.on(\\\"uncaughtException\\\",(function(t){if(!(t instanceof ct))throw t})),process.on(\\\"unhandledRejection\\\",(function(t){throw t})),g=(t,e)=>{if(Q())throw process.exitCode=t,e;e instanceof ct||x(\\\"exiting due to exception: \\\"+e),process.exit(t)},u.inspect=function(){return\\\"[Emscripten Module object]\\\"};try{e=n(925)}catch(t){throw console.error('The \\\"worker_threads\\\" module is not supported in this node.js build - perhaps a newer version is needed?'),t}n.g.Worker=e.Worker}else(v||w)&&(w?A=self.location.href:\\\"undefined\\\"!=typeof document&&document.currentScript&&(A=document.currentScript.src),_scriptDir&&(A=_scriptDir),A=0!==A.indexOf(\\\"blob:\\\")?A.substr(0,A.replace(/[?#].*/,\\\"\\\").lastIndexOf(\\\"/\\\")+1):\\\"\\\",_||(l=t=>{var e=new XMLHttpRequest;return e.open(\\\"GET\\\",t,!1),e.send(null),e.responseText},w&&(p=t=>{var e=new XMLHttpRequest;return e.open(\\\"GET\\\",t,!1),e.responseType=\\\"arraybuffer\\\",e.send(null),new Uint8Array(e.response)}),f=(t,e,n)=>{var r=new XMLHttpRequest;r.open(\\\"GET\\\",t,!0),r.responseType=\\\"arraybuffer\\\",r.onload=()=>{200==r.status||0==r.status&&r.response?e(r.response):n()},r.onerror=n,r.send(null)}));_&&\\\"undefined\\\"==typeof performance&&(n.g.performance=n(953).performance);var T=console.log.bind(console),E=console.warn.bind(console);_&&(y(),T=t=>h.writeSync(1,t+\\\"\\\\n\\\"),E=t=>h.writeSync(2,t+\\\"\\\\n\\\"));var M,C=u.print||T,x=u.printErr||E;Object.assign(u,b),b=null,u.thisProgram&&(m=u.thisProgram),u.quit&&(g=u.quit),u.wasmBinary&&(M=u.wasmBinary);var R=u.noExitRuntime||!1;\\\"object\\\"!=typeof WebAssembly&&at(\\\"no native wasm support detected\\\");var j,k,D,P,U,F,I,W,H=!1,L=\\\"undefined\\\"!=typeof TextDecoder?new TextDecoder(\\\"utf8\\\"):void 0;function z(t,e,n){var r=(e>>>=0)+n;for(n=e;t[n]&&!(n>=r);)++n;if(16<n-e&&t.buffer&&L)return L.decode(t.buffer instanceof SharedArrayBuffer?t.slice(e,n):t.subarray(e,n));for(r=\\\"\\\";e<n;){var a=t[e++];if(128&a){var i=63&t[e++];if(192==(224&a))r+=String.fromCharCode((31&a)<<6|i);else{var o=63&t[e++];65536>(a=224==(240&a)?(15&a)<<12|i<<6|o:(7&a)<<18|i<<12|o<<6|63&t[e++])?r+=String.fromCharCode(a):(a-=65536,r+=String.fromCharCode(55296|a>>10,56320|1023&a))}}else r+=String.fromCharCode(a)}return r}function Y(t,e){return(t>>>=0)?z(r(),t,e):\\\"\\\"}function B(t,e,n,r){if(!(0<r))return 0;var a=n>>>=0;r=n+r-1;for(var i=0;i<t.length;++i){var o=t.charCodeAt(i);if(55296<=o&&57343>=o&&(o=65536+((1023&o)<<10)|1023&t.charCodeAt(++i)),127>=o){if(n>=r)break;e[n++>>>0]=o}else{if(2047>=o){if(n+1>=r)break;e[n++>>>0]=192|o>>6}else{if(65535>=o){if(n+2>=r)break;e[n++>>>0]=224|o>>12}else{if(n+3>=r)break;e[n++>>>0]=240|o>>18,e[n++>>>0]=128|o>>12&63}e[n++>>>0]=128|o>>6&63}e[n++>>>0]=128|63&o}}return e[n>>>0]=0,n-a}function G(t){for(var e=0,n=0;n<t.length;++n){var r=t.charCodeAt(n);127>=r?e++:2047>=r?e+=2:55296<=r&&57343>=r?(e+=4,++n):e+=3}return e}function N(t){D=t,u.HEAP8=P=new Int8Array(t),u.HEAP16=new Int16Array(t),u.HEAP32=F=new Int32Array(t),u.HEAPU8=U=new Uint8Array(t),u.HEAPU16=new Uint16Array(t),u.HEAPU32=I=new Uint32Array(t),u.HEAPF32=new Float32Array(t),u.HEAPF64=W=new Float64Array(t)}O&&(D=u.buffer);var V=u.INITIAL_MEMORY||16777216;if(O)j=u.wasmMemory,D=u.buffer;else if(u.wasmMemory)j=u.wasmMemory;else if(!((j=new WebAssembly.Memory({initial:V/65536,maximum:65536,shared:!0})).buffer instanceof SharedArrayBuffer))throw x(\\\"requested a shared WebAssembly.Memory but the returned buffer is not a SharedArrayBuffer, indicating that while the browser has SharedArrayBuffer it does not have WebAssembly threads support - you may need to set a flag\\\"),_&&console.log(\\\"(on node you may need: --experimental-wasm-threads --experimental-wasm-bulk-memory and also use a recent version)\\\"),Error(\\\"bad memory\\\");j&&(D=j.buffer),V=D.byteLength,N(D);var $,q=[],X=[],J=[],Z=[];function Q(){return R||!1}function K(){var t=u.preRun.shift();q.unshift(t)}var tt,et=0,nt=null,rt=null;function at(t){throw O?postMessage({cmd:\\\"onAbort\\\",arg:t}):u.onAbort&&u.onAbort(t),x(t=\\\"Aborted(\\\"+t+\\\")\\\"),H=!0,t=new WebAssembly.RuntimeError(t+\\\". Build with -sASSERTIONS for more info.\\\"),s(t),t}function it(){return tt.startsWith(\\\"data:application/octet-stream;base64,\\\")}function ot(){var t=tt;try{if(t==tt&&M)return new Uint8Array(M);if(p)return p(t);throw\\\"both async and sync fetching of the wasm failed\\\"}catch(t){at(t)}}tt=\\\"ort-wasm-threaded.wasm\\\",it()||(tt=S(tt));var ut={};function ct(t){this.name=\\\"ExitStatus\\\",this.message=\\\"Program terminated with exit(\\\"+t+\\\")\\\",this.status=t}function st(t){(t=ht.Vb[t])||at(),ht.mc(t)}function lt(t){var e=ht.Cc();if(!e)return 6;ht.ac.push(e),ht.Vb[t.Ub]=e,e.Ub=t.Ub;var n={cmd:\\\"run\\\",start_routine:t.Ic,arg:t.zc,pthread_ptr:t.Ub};return e.$b=()=>{n.time=performance.now(),e.postMessage(n,t.Nc)},e.loaded&&(e.$b(),delete e.$b),0}function ft(t){if(O)return $t(1,1,t);Q()||(ht.oc(),u.onExit&&u.onExit(t),H=!0),g(t,new ct(t))}function pt(t,e){if(!e&&O)throw bt(t),\\\"unwind\\\";Q()||O||(me(),dt(J),be(0),re[1].length&&ae(1,10),re[2].length&&ae(2,10),ht.oc()),ft(t)}var ht={Yb:[],ac:[],qc:[],Vb:{},fc:function(){O&&ht.Ec()},Pc:function(){},Ec:function(){ht.receiveObjectTransfer=ht.Gc,ht.threadInitTLS=ht.pc,ht.setExitStatus=ht.nc,R=!1},nc:function(){},oc:function(){for(var t of Object.values(ht.Vb))ht.mc(t);for(t of ht.Yb)t.terminate();ht.Yb=[]},mc:function(t){var e=t.Ub;delete ht.Vb[e],ht.Yb.push(t),ht.ac.splice(ht.ac.indexOf(t),1),t.Ub=0,Oe(e)},Gc:function(){},pc:function(){ht.qc.forEach((t=>t()))},Fc:function(t,e){t.onmessage=n=>{var r=(n=n.data).cmd;if(t.Ub&&(ht.Bc=t.Ub),n.targetThread&&n.targetThread!=he()){var a=ht.Vb[n.Qc];a?a.postMessage(n,n.transferList):x('Internal error! Worker sent a message \\\"'+r+'\\\" to target pthread '+n.targetThread+\\\", but that thread no longer exists!\\\")}else\\\"processProxyingQueue\\\"===r?zt(n.queue):\\\"spawnThread\\\"===r?lt(n):\\\"cleanupThread\\\"===r?st(n.thread):\\\"killThread\\\"===r?(n=n.thread,r=ht.Vb[n],delete ht.Vb[n],r.terminate(),Oe(n),ht.ac.splice(ht.ac.indexOf(r),1),r.Ub=0):\\\"cancelThread\\\"===r?ht.Vb[n.thread].postMessage({cmd:\\\"cancel\\\"}):\\\"loaded\\\"===r?(t.loaded=!0,e&&e(t),t.$b&&(t.$b(),delete t.$b)):\\\"print\\\"===r?C(\\\"Thread \\\"+n.threadId+\\\": \\\"+n.text):\\\"printErr\\\"===r?x(\\\"Thread \\\"+n.threadId+\\\": \\\"+n.text):\\\"alert\\\"===r?alert(\\\"Thread \\\"+n.threadId+\\\": \\\"+n.text):\\\"setimmediate\\\"===n.target?t.postMessage(n):\\\"onAbort\\\"===r?u.onAbort&&u.onAbort(n.arg):r&&x(\\\"worker sent an unknown command \\\"+r);ht.Bc=void 0},t.onerror=t=>{throw x(\\\"worker sent an error! \\\"+t.filename+\\\":\\\"+t.lineno+\\\": \\\"+t.message),t},_&&(t.on(\\\"message\\\",(function(e){t.onmessage({data:e})})),t.on(\\\"error\\\",(function(e){t.onerror(e)})),t.on(\\\"detachedExit\\\",(function(){}))),t.postMessage({cmd:\\\"load\\\",urlOrBlob:u.mainScriptUrlOrBlob||_scriptDir,wasmMemory:j,wasmModule:k})},yc:function(){var t=S(\\\"ort-wasm-threaded.worker.js\\\");ht.Yb.push(new Worker(t))},Cc:function(){return 0==ht.Yb.length&&(ht.yc(),ht.Fc(ht.Yb[0])),ht.Yb.pop()}};function dt(t){for(;0<t.length;)t.shift()(u)}function yt(t){var e=Ee();return t=t(),Me(e),t}function bt(t){if(O)return $t(2,0,t);try{pt(t)}catch(t){t instanceof ct||\\\"unwind\\\"==t||g(1,t)}}u.PThread=ht,u.establishStackSpace=function(){var t=he(),e=a()[t+44>>2>>>0];t=a()[t+48>>2>>>0],Te(e,e-t),Me(e)};var mt=[];function gt(t){var e=mt[t];return e||(t>=mt.length&&(mt.length=t+1),mt[t]=e=$.get(t)),e}u.invokeEntryPoint=function(t,e){t=gt(t)(e),Q()?ht.nc(t):Ae(t)};var vt,wt,_t=[],Ot=0,At=0;function St(t){this.Zb=t,this.Sb=t-24,this.xc=function(t){i()[this.Sb+4>>2>>>0]=t},this.bc=function(){return i()[this.Sb+4>>2>>>0]},this.wc=function(t){i()[this.Sb+8>>2>>>0]=t},this.Dc=function(){return i()[this.Sb+8>>2>>>0]},this.rc=function(){a()[this.Sb>>2>>>0]=0},this.hc=function(t){t=t?1:0,e()[this.Sb+12>>0>>>0]=t},this.uc=function(){return 0!=e()[this.Sb+12>>0>>>0]},this.ic=function(t){t=t?1:0,e()[this.Sb+13>>0>>>0]=t},this.kc=function(){return 0!=e()[this.Sb+13>>0>>>0]},this.fc=function(t,e){this.cc(0),this.xc(t),this.wc(e),this.rc(),this.hc(!1),this.ic(!1)},this.sc=function(){Atomics.add(a(),this.Sb>>2,1)},this.Hc=function(){return 1===Atomics.sub(a(),this.Sb>>2,1)},this.cc=function(t){i()[this.Sb+16>>2>>>0]=t},this.tc=function(){return i()[this.Sb+16>>2>>>0]},this.vc=function(){if(Re(this.bc()))return i()[this.Zb>>2>>>0];var t=this.tc();return 0!==t?t:this.Zb}}function Tt(t){return ye(new St(t).Sb)}function Et(t,e,n,r){return O?$t(3,1,t,e,n,r):Mt(t,e,n,r)}function Mt(t,e,n,r){if(\\\"undefined\\\"==typeof SharedArrayBuffer)return x(\\\"Current environment does not support SharedArrayBuffer, pthreads are not available!\\\"),6;var a=[];return O&&0===a.length?Et(t,e,n,r):(t={Ic:n,Ub:t,zc:r,Nc:a},O?(t.Oc=\\\"spawnThread\\\",postMessage(t,a),0):lt(t))}function Ct(t,e,n){return O?$t(4,1,t,e,n):0}function xt(t,e){if(O)return $t(5,1,t,e)}function Rt(t,e){if(O)return $t(6,1,t,e)}function jt(t,e,n){if(O)return $t(7,1,t,e,n)}function kt(t,e,n){return O?$t(8,1,t,e,n):0}function Dt(t,e){if(O)return $t(9,1,t,e)}function Pt(t,e,n){if(O)return $t(10,1,t,e,n)}function Ut(t,e,n,r){if(O)return $t(11,1,t,e,n,r)}function Ft(t,e,n,r){if(O)return $t(12,1,t,e,n,r)}function It(t,e,n,r){if(O)return $t(13,1,t,e,n,r)}function Wt(t){if(O)return $t(14,1,t)}function Ht(t,e){if(O)return $t(15,1,t,e)}function Lt(t,e,n){if(O)return $t(16,1,t,e,n)}function zt(t){Atomics.store(a(),t>>2,1),he()&&_e(t),Atomics.compareExchange(a(),t>>2,1,0)}function Yt(t){return i()[t>>>2]+4294967296*a()[t+4>>>2]}function Bt(t,e,n,r,a,i){return O?$t(17,1,t,e,n,r,a,i):-52}function Gt(t,e,n,r,a,i){if(O)return $t(18,1,t,e,n,r,a,i)}function Nt(t){var n=G(t)+1,r=de(n);return r&&B(t,e(),r,n),r}function Vt(t,e,n){function r(t){return(t=t.toTimeString().match(/\\\\(([A-Za-z ]+)\\\\)$/))?t[1]:\\\"GMT\\\"}if(O)return $t(19,1,t,e,n);var o=(new Date).getFullYear(),u=new Date(o,0,1),c=new Date(o,6,1);o=u.getTimezoneOffset();var s=c.getTimezoneOffset(),l=Math.max(o,s);a()[t>>2>>>0]=60*l,a()[e>>2>>>0]=Number(o!=s),t=r(u),e=r(c),t=Nt(t),e=Nt(e),s<o?(i()[n>>2>>>0]=t,i()[n+4>>2>>>0]=e):(i()[n>>2>>>0]=e,i()[n+4>>2>>>0]=t)}function $t(t,e){var n=arguments.length-2,r=arguments;return yt((()=>{for(var a=Ce(8*n),i=a>>3,u=0;u<n;u++){var c=r[2+u];o()[i+u>>>0]=c}return we(t,n,a,e)}))}u.executeNotifiedProxyingQueue=zt,wt=_?()=>{var t=process.hrtime();return 1e3*t[0]+t[1]/1e6}:O?()=>performance.now()-u.__performance_now_clock_drift:()=>performance.now();var qt,Xt=[],Jt={};function Zt(){if(!qt){var t,e={USER:\\\"web_user\\\",LOGNAME:\\\"web_user\\\",PATH:\\\"/\\\",PWD:\\\"/\\\",HOME:\\\"/home/<USER>\",LANG:(\\\"object\\\"==typeof navigator&&navigator.languages&&navigator.languages[0]||\\\"C\\\").replace(\\\"-\\\",\\\"_\\\")+\\\".UTF-8\\\",_:m||\\\"./this.program\\\"};for(t in Jt)void 0===Jt[t]?delete e[t]:e[t]=Jt[t];var n=[];for(t in e)n.push(t+\\\"=\\\"+e[t]);qt=n}return qt}function Qt(t,n){if(O)return $t(20,1,t,n);var r=0;return Zt().forEach((function(a,o){var u=n+r;for(o=i()[t+4*o>>2>>>0]=u,u=0;u<a.length;++u)e()[o++>>0>>>0]=a.charCodeAt(u);e()[o>>0>>>0]=0,r+=a.length+1})),0}function Kt(t,e){if(O)return $t(21,1,t,e);var n=Zt();i()[t>>2>>>0]=n.length;var r=0;return n.forEach((function(t){r+=t.length+1})),i()[e>>2>>>0]=r,0}function te(t){return O?$t(22,1,t):52}function ee(t,e,n,r){return O?$t(23,1,t,e,n,r):52}function ne(t,e,n,r,a){return O?$t(24,1,t,e,n,r,a):70}var re=[null,[],[]];function ae(t,e){var n=re[t];0===e||10===e?((1===t?C:x)(z(n,0)),n.length=0):n.push(e)}function ie(t,e,n,a){if(O)return $t(25,1,t,e,n,a);for(var o=0,u=0;u<n;u++){var c=i()[e>>2>>>0],s=i()[e+4>>2>>>0];e+=8;for(var l=0;l<s;l++)ae(t,r()[c+l>>>0]);o+=s}return i()[a>>2>>>0]=o,0}var oe=0;function ue(t){return 0==t%4&&(0!=t%100||0==t%400)}var ce=[31,29,31,30,31,30,31,31,30,31,30,31],se=[31,28,31,30,31,30,31,31,30,31,30,31];function le(t,n,r,i){function o(t,e,n){for(t=\\\"number\\\"==typeof t?t.toString():t||\\\"\\\";t.length<e;)t=n[0]+t;return t}function u(t,e){return o(t,e,\\\"0\\\")}function c(t,e){function n(t){return 0>t?-1:0<t?1:0}var r;return 0===(r=n(t.getFullYear()-e.getFullYear()))&&0===(r=n(t.getMonth()-e.getMonth()))&&(r=n(t.getDate()-e.getDate())),r}function s(t){switch(t.getDay()){case 0:return new Date(t.getFullYear()-1,11,29);case 1:return t;case 2:return new Date(t.getFullYear(),0,3);case 3:return new Date(t.getFullYear(),0,2);case 4:return new Date(t.getFullYear(),0,1);case 5:return new Date(t.getFullYear()-1,11,31);case 6:return new Date(t.getFullYear()-1,11,30)}}function l(t){var e=t.Wb;for(t=new Date(new Date(t.Xb+1900,0,1).getTime());0<e;){var n=t.getMonth(),r=(ue(t.getFullYear())?ce:se)[n];if(!(e>r-t.getDate())){t.setDate(t.getDate()+e);break}e-=r-t.getDate()+1,t.setDate(1),11>n?t.setMonth(n+1):(t.setMonth(0),t.setFullYear(t.getFullYear()+1))}return n=new Date(t.getFullYear()+1,0,4),e=s(new Date(t.getFullYear(),0,4)),n=s(n),0>=c(e,t)?0>=c(n,t)?t.getFullYear()+1:t.getFullYear():t.getFullYear()-1}var f=a()[i+40>>2>>>0];for(var p in i={Lc:a()[i>>2>>>0],Kc:a()[i+4>>2>>>0],dc:a()[i+8>>2>>>0],jc:a()[i+12>>2>>>0],ec:a()[i+16>>2>>>0],Xb:a()[i+20>>2>>>0],Tb:a()[i+24>>2>>>0],Wb:a()[i+28>>2>>>0],Rc:a()[i+32>>2>>>0],Jc:a()[i+36>>2>>>0],Mc:f?Y(f):\\\"\\\"},r=Y(r),f={\\\"%c\\\":\\\"%a %b %d %H:%M:%S %Y\\\",\\\"%D\\\":\\\"%m/%d/%y\\\",\\\"%F\\\":\\\"%Y-%m-%d\\\",\\\"%h\\\":\\\"%b\\\",\\\"%r\\\":\\\"%I:%M:%S %p\\\",\\\"%R\\\":\\\"%H:%M\\\",\\\"%T\\\":\\\"%H:%M:%S\\\",\\\"%x\\\":\\\"%m/%d/%y\\\",\\\"%X\\\":\\\"%H:%M:%S\\\",\\\"%Ec\\\":\\\"%c\\\",\\\"%EC\\\":\\\"%C\\\",\\\"%Ex\\\":\\\"%m/%d/%y\\\",\\\"%EX\\\":\\\"%H:%M:%S\\\",\\\"%Ey\\\":\\\"%y\\\",\\\"%EY\\\":\\\"%Y\\\",\\\"%Od\\\":\\\"%d\\\",\\\"%Oe\\\":\\\"%e\\\",\\\"%OH\\\":\\\"%H\\\",\\\"%OI\\\":\\\"%I\\\",\\\"%Om\\\":\\\"%m\\\",\\\"%OM\\\":\\\"%M\\\",\\\"%OS\\\":\\\"%S\\\",\\\"%Ou\\\":\\\"%u\\\",\\\"%OU\\\":\\\"%U\\\",\\\"%OV\\\":\\\"%V\\\",\\\"%Ow\\\":\\\"%w\\\",\\\"%OW\\\":\\\"%W\\\",\\\"%Oy\\\":\\\"%y\\\"})r=r.replace(new RegExp(p,\\\"g\\\"),f[p]);var h=\\\"Sunday Monday Tuesday Wednesday Thursday Friday Saturday\\\".split(\\\" \\\"),d=\\\"January February March April May June July August September October November December\\\".split(\\\" \\\");for(p in f={\\\"%a\\\":function(t){return h[t.Tb].substring(0,3)},\\\"%A\\\":function(t){return h[t.Tb]},\\\"%b\\\":function(t){return d[t.ec].substring(0,3)},\\\"%B\\\":function(t){return d[t.ec]},\\\"%C\\\":function(t){return u((t.Xb+1900)/100|0,2)},\\\"%d\\\":function(t){return u(t.jc,2)},\\\"%e\\\":function(t){return o(t.jc,2,\\\" \\\")},\\\"%g\\\":function(t){return l(t).toString().substring(2)},\\\"%G\\\":function(t){return l(t)},\\\"%H\\\":function(t){return u(t.dc,2)},\\\"%I\\\":function(t){return 0==(t=t.dc)?t=12:12<t&&(t-=12),u(t,2)},\\\"%j\\\":function(t){for(var e=0,n=0;n<=t.ec-1;e+=(ue(t.Xb+1900)?ce:se)[n++]);return u(t.jc+e,3)},\\\"%m\\\":function(t){return u(t.ec+1,2)},\\\"%M\\\":function(t){return u(t.Kc,2)},\\\"%n\\\":function(){return\\\"\\\\n\\\"},\\\"%p\\\":function(t){return 0<=t.dc&&12>t.dc?\\\"AM\\\":\\\"PM\\\"},\\\"%S\\\":function(t){return u(t.Lc,2)},\\\"%t\\\":function(){return\\\"\\\\t\\\"},\\\"%u\\\":function(t){return t.Tb||7},\\\"%U\\\":function(t){return u(Math.floor((t.Wb+7-t.Tb)/7),2)},\\\"%V\\\":function(t){var e=Math.floor((t.Wb+7-(t.Tb+6)%7)/7);if(2>=(t.Tb+371-t.Wb-2)%7&&e++,e)53==e&&(4==(n=(t.Tb+371-t.Wb)%7)||3==n&&ue(t.Xb)||(e=1));else{e=52;var n=(t.Tb+7-t.Wb-1)%7;(4==n||5==n&&ue(t.Xb%400-1))&&e++}return u(e,2)},\\\"%w\\\":function(t){return t.Tb},\\\"%W\\\":function(t){return u(Math.floor((t.Wb+7-(t.Tb+6)%7)/7),2)},\\\"%y\\\":function(t){return(t.Xb+1900).toString().substring(2)},\\\"%Y\\\":function(t){return t.Xb+1900},\\\"%z\\\":function(t){var e=0<=(t=t.Jc);return t=Math.abs(t)/60,(e?\\\"+\\\":\\\"-\\\")+String(\\\"0000\\\"+(t/60*100+t%60)).slice(-4)},\\\"%Z\\\":function(t){return t.Mc},\\\"%%\\\":function(){return\\\"%\\\"}},r=r.replace(/%%/g,\\\"\\\\0\\\\0\\\"),f)r.includes(p)&&(r=r.replace(new RegExp(p,\\\"g\\\"),f[p](i)));return p=function(t){var e=Array(G(t)+1);return B(t,e,0,e.length),e}(r=r.replace(/\\\\0\\\\0/g,\\\"%\\\")),p.length>n?0:(function(t,n){e().set(t,n>>>0)}(p,t),p.length-1)}ht.fc();var fe=[null,ft,bt,Et,Ct,xt,Rt,jt,kt,Dt,Pt,Ut,Ft,It,Wt,Ht,Lt,Bt,Gt,Vt,Qt,Kt,te,ee,ne,ie],pe={b:function(t){return de(t+24)+24},n:function(t){return(t=new St(t)).uc()||(t.hc(!0),Ot--),t.ic(!1),_t.push(t),t.sc(),t.vc()},ma:function(t){throw x(\\\"Unexpected exception thrown, this is not properly supported - aborting\\\"),H=!0,t},x:function(){Se(0);var t=_t.pop();if(t.Hc()&&!t.kc()){var e=t.Dc();e&&gt(e)(t.Zb),Tt(t.Zb)}At=0},e:function(){var t=At;if(!t)return oe=0;var e=new St(t);e.cc(t);var n=e.bc();if(!n)return oe=0,t;for(var r=Array.prototype.slice.call(arguments),a=0;a<r.length;a++){var i=r[a];if(0===i||i===n)break;if(xe(i,n,e.Sb+16))return oe=i,t}return oe=n,t},l:function(){var t=At;if(!t)return oe=0;var e=new St(t);e.cc(t);var n=e.bc();if(!n)return oe=0,t;for(var r=Array.prototype.slice.call(arguments),a=0;a<r.length;a++){var i=r[a];if(0===i||i===n)break;if(xe(i,n,e.Sb+16))return oe=i,t}return oe=n,t},h:function(){var t=At;if(!t)return oe=0;var e=new St(t);e.cc(t);var n=e.bc();if(!n)return oe=0,t;for(var r=Array.prototype.slice.call(arguments),a=0;a<r.length;a++){var i=r[a];if(0===i||i===n)break;if(xe(i,n,e.Sb+16))return oe=i,t}return oe=n,t},t:Tt,M:function(){var t=_t.pop();t||at(\\\"no exception to throw\\\");var e=t.Zb;throw t.kc()||(_t.push(t),t.ic(!0),t.hc(!1),Ot++),At=e,e},c:function(t,e,n){throw new St(t).fc(e,n),At=t,Ot++,t},pa:function(){return Ot},Fa:function(t){ge(t,!w,1,!v),ht.pc()},T:function(t){O?postMessage({cmd:\\\"cleanupThread\\\",thread:t}):st(t)},xa:Mt,j:function(t){throw At||(At=t),t},H:Ct,Ma:xt,ua:Rt,wa:jt,oa:kt,Ka:Dt,Ca:Pt,Ja:Ut,V:Ft,va:It,sa:Wt,La:Ht,ta:Lt,Ta:function(){},X:function(){at(\\\"To use dlopen, you need enable dynamic linking, see https://github.com/emscripten-core/emscripten/wiki/Linking\\\")},Ua:function(){at(\\\"To use dlopen, you need enable dynamic linking, see https://github.com/emscripten-core/emscripten/wiki/Linking\\\")},W:function(){return Date.now()},ya:function(){return 2097152},Oa:function(){return!0},za:function(t,e,n,r){if(t==e)setTimeout((()=>zt(r)));else if(O)postMessage({targetThread:t,cmd:\\\"processProxyingQueue\\\",queue:r});else{if(!(t=ht.Vb[t]))return;t.postMessage({cmd:\\\"processProxyingQueue\\\",queue:r})}return 1},Ea:function(){return-1},Pa:function(t,e){t=new Date(1e3*Yt(t)),a()[e>>2>>>0]=t.getUTCSeconds(),a()[e+4>>2>>>0]=t.getUTCMinutes(),a()[e+8>>2>>>0]=t.getUTCHours(),a()[e+12>>2>>>0]=t.getUTCDate(),a()[e+16>>2>>>0]=t.getUTCMonth(),a()[e+20>>2>>>0]=t.getUTCFullYear()-1900,a()[e+24>>2>>>0]=t.getUTCDay(),t=(t.getTime()-Date.UTC(t.getUTCFullYear(),0,1,0,0,0,0))/864e5|0,a()[e+28>>2>>>0]=t},Qa:function(t,e){t=new Date(1e3*Yt(t)),a()[e>>2>>>0]=t.getSeconds(),a()[e+4>>2>>>0]=t.getMinutes(),a()[e+8>>2>>>0]=t.getHours(),a()[e+12>>2>>>0]=t.getDate(),a()[e+16>>2>>>0]=t.getMonth(),a()[e+20>>2>>>0]=t.getFullYear()-1900,a()[e+24>>2>>>0]=t.getDay();var n=new Date(t.getFullYear(),0,1),r=(t.getTime()-n.getTime())/864e5|0;a()[e+28>>2>>>0]=r,a()[e+36>>2>>>0]=-60*t.getTimezoneOffset(),r=new Date(t.getFullYear(),6,1).getTimezoneOffset(),t=0|(r!=(n=n.getTimezoneOffset())&&t.getTimezoneOffset()==Math.min(n,r)),a()[e+32>>2>>>0]=t},Ra:function(t){var e=new Date(a()[t+20>>2>>>0]+1900,a()[t+16>>2>>>0],a()[t+12>>2>>>0],a()[t+8>>2>>>0],a()[t+4>>2>>>0],a()[t>>2>>>0],0),n=a()[t+32>>2>>>0],r=e.getTimezoneOffset(),i=new Date(e.getFullYear(),0,1),o=new Date(e.getFullYear(),6,1).getTimezoneOffset(),u=i.getTimezoneOffset(),c=Math.min(u,o);return 0>n?a()[t+32>>2>>>0]=Number(o!=u&&c==r):0<n!=(c==r)&&(o=Math.max(u,o),e.setTime(e.getTime()+6e4*((0<n?c:o)-r))),a()[t+24>>2>>>0]=e.getDay(),n=(e.getTime()-i.getTime())/864e5|0,a()[t+28>>2>>>0]=n,a()[t>>2>>>0]=e.getSeconds(),a()[t+4>>2>>>0]=e.getMinutes(),a()[t+8>>2>>>0]=e.getHours(),a()[t+12>>2>>>0]=e.getDate(),a()[t+16>>2>>>0]=e.getMonth(),e.getTime()/1e3|0},Aa:Bt,Ba:Gt,Sa:function t(e,n,r){t.Ac||(t.Ac=!0,Vt(e,n,r))},y:function(){at(\\\"\\\")},U:function(){if(!_&&!w){var t=\\\"Blocking on the main thread is very dangerous, see https://emscripten.org/docs/porting/pthreads.html#blocking-on-the-main-browser-thread\\\";vt||(vt={}),vt[t]||(vt[t]=1,_&&(t=\\\"warning: \\\"+t),x(t))}},ra:function(){return 4294901760},B:wt,Ia:function(t,e,n){r().copyWithin(t>>>0,e>>>0,e+n>>>0)},F:function(){return _?n(993).cpus().length:navigator.hardwareConcurrency},Da:function(t,e,n){Xt.length=e,n>>=3;for(var r=0;r<e;r++)Xt[r]=o()[n+r>>>0];return(0>t?ut[-t-1]:fe[t]).apply(null,Xt)},qa:function(t){var e=r().length;if((t>>>=0)<=e||4294901760<t)return!1;for(var n=1;4>=n;n*=2){var a=e*(1+.2/n);a=Math.min(a,t+100663296);var i=Math;a=Math.max(t,a),i=i.min.call(i,4294901760,a+(65536-a%65536)%65536);t:{try{j.grow(i-D.byteLength+65535>>>16),N(j.buffer);var o=1;break t}catch(t){}o=void 0}if(o)return!0}return!1},Na:function(){throw\\\"unwind\\\"},Ga:Qt,Ha:Kt,J:pt,I:te,S:ee,ga:ne,R:ie,d:function(){return oe},na:function t(r,a){t.lc||(t.lc=function(){if(\\\"object\\\"==typeof crypto&&\\\"function\\\"==typeof crypto.getRandomValues){var t=new Uint8Array(1);return()=>(crypto.getRandomValues(t),t[0])}if(_)try{var e=n(Object(function(){var t=new Error(\\\"Cannot find module 'crypto'\\\");throw t.code=\\\"MODULE_NOT_FOUND\\\",t}()));return()=>e.randomBytes(1)[0]}catch(t){}return()=>at(\\\"randomDevice\\\")}());for(var i=0;i<a;i++)e()[r+i>>0>>>0]=t.lc();return 0},ia:function(t,e,n){var r=Ee();try{return gt(t)(e,n)}catch(t){if(Me(r),t!==t+0)throw t;Se(1,0)}},ja:function(t,e,n){var r=Ee();try{return gt(t)(e,n)}catch(t){if(Me(r),t!==t+0)throw t;Se(1,0)}},K:function(t){var e=Ee();try{return gt(t)()}catch(t){if(Me(e),t!==t+0)throw t;Se(1,0)}},f:function(t,e){var n=Ee();try{return gt(t)(e)}catch(t){if(Me(n),t!==t+0)throw t;Se(1,0)}},P:function(t,e,n){var r=Ee();try{return gt(t)(e,n)}catch(t){if(Me(r),t!==t+0)throw t;Se(1,0)}},Q:function(t,e,n){var r=Ee();try{return gt(t)(e,n)}catch(t){if(Me(r),t!==t+0)throw t;Se(1,0)}},k:function(t,e,n){var r=Ee();try{return gt(t)(e,n)}catch(t){if(Me(r),t!==t+0)throw t;Se(1,0)}},p:function(t,e,n,r){var a=Ee();try{return gt(t)(e,n,r)}catch(t){if(Me(a),t!==t+0)throw t;Se(1,0)}},q:function(t,e,n,r,a){var i=Ee();try{return gt(t)(e,n,r,a)}catch(t){if(Me(i),t!==t+0)throw t;Se(1,0)}},N:function(t,e,n,r,a,i){var o=Ee();try{return gt(t)(e,n,r,a,i)}catch(t){if(Me(o),t!==t+0)throw t;Se(1,0)}},s:function(t,e,n,r,a,i){var o=Ee();try{return gt(t)(e,n,r,a,i)}catch(t){if(Me(o),t!==t+0)throw t;Se(1,0)}},w:function(t,e,n,r,a,i,o){var u=Ee();try{return gt(t)(e,n,r,a,i,o)}catch(t){if(Me(u),t!==t+0)throw t;Se(1,0)}},L:function(t,e,n,r,a,i,o,u){var c=Ee();try{return gt(t)(e,n,r,a,i,o,u)}catch(t){if(Me(c),t!==t+0)throw t;Se(1,0)}},E:function(t,e,n,r,a,i,o,u,c,s,l,f){var p=Ee();try{return gt(t)(e,n,r,a,i,o,u,c,s,l,f)}catch(t){if(Me(p),t!==t+0)throw t;Se(1,0)}},aa:function(t,e,n,r,a,i,o,u){var c=Ee();try{return He(t,e,n,r,a,i,o,u)}catch(t){if(Me(c),t!==t+0)throw t;Se(1,0)}},_:function(t,e,n,r,a,i,o){var u=Ee();try{return ke(t,e,n,r,a,i,o)}catch(t){if(Me(u),t!==t+0)throw t;Se(1,0)}},Z:function(t,e,n,r,a){var i=Ee();try{return Le(t,e,n,r,a)}catch(t){if(Me(i),t!==t+0)throw t;Se(1,0)}},ca:function(t,e,n,r){var a=Ee();try{return Ie(t,e,n,r)}catch(t){if(Me(a),t!==t+0)throw t;Se(1,0)}},$:function(t){var e=Ee();try{return je(t)}catch(t){if(Me(e),t!==t+0)throw t;Se(1,0)}},ba:function(t,e){var n=Ee();try{return We(t,e)}catch(t){if(Me(n),t!==t+0)throw t;Se(1,0)}},Y:function(t,e,n){var r=Ee();try{return De(t,e,n)}catch(t){if(Me(r),t!==t+0)throw t;Se(1,0)}},g:function(t){var e=Ee();try{gt(t)()}catch(t){if(Me(e),t!==t+0)throw t;Se(1,0)}},r:function(t,e){var n=Ee();try{gt(t)(e)}catch(t){if(Me(n),t!==t+0)throw t;Se(1,0)}},i:function(t,e,n){var r=Ee();try{gt(t)(e,n)}catch(t){if(Me(r),t!==t+0)throw t;Se(1,0)}},ha:function(t,e,n,r){var a=Ee();try{gt(t)(e,n,r)}catch(t){if(Me(a),t!==t+0)throw t;Se(1,0)}},m:function(t,e,n,r){var a=Ee();try{gt(t)(e,n,r)}catch(t){if(Me(a),t!==t+0)throw t;Se(1,0)}},v:function(t,e,n,r,a){var i=Ee();try{gt(t)(e,n,r,a)}catch(t){if(Me(i),t!==t+0)throw t;Se(1,0)}},u:function(t,e,n,r,a,i){var o=Ee();try{gt(t)(e,n,r,a,i)}catch(t){if(Me(o),t!==t+0)throw t;Se(1,0)}},O:function(t,e,n,r,a,i,o){var u=Ee();try{gt(t)(e,n,r,a,i,o)}catch(t){if(Me(u),t!==t+0)throw t;Se(1,0)}},A:function(t,e,n,r,a,i,o,u){var c=Ee();try{gt(t)(e,n,r,a,i,o,u)}catch(t){if(Me(c),t!==t+0)throw t;Se(1,0)}},ka:function(t,e,n,r,a,i,o,u,c){var s=Ee();try{gt(t)(e,n,r,a,i,o,u,c)}catch(t){if(Me(s),t!==t+0)throw t;Se(1,0)}},C:function(t,e,n,r,a,i,o,u,c,s,l){var f=Ee();try{gt(t)(e,n,r,a,i,o,u,c,s,l)}catch(t){if(Me(f),t!==t+0)throw t;Se(1,0)}},D:function(t,e,n,r,a,i,o,u,c,s,l,f,p,h,d,y){var b=Ee();try{gt(t)(e,n,r,a,i,o,u,c,s,l,f,p,h,d,y)}catch(t){if(Me(b),t!==t+0)throw t;Se(1,0)}},fa:function(t,e,n,r,a,i,o,u){var c=Ee();try{Pe(t,e,n,r,a,i,o,u)}catch(t){if(Me(c),t!==t+0)throw t;Se(1,0)}},da:function(t,e,n,r,a,i,o,u,c,s,l,f){var p=Ee();try{Fe(t,e,n,r,a,i,o,u,c,s,l,f)}catch(t){if(Me(p),t!==t+0)throw t;Se(1,0)}},ea:function(t,e,n,r,a,i){var o=Ee();try{Ue(t,e,n,r,a,i)}catch(t){if(Me(o),t!==t+0)throw t;Se(1,0)}},o:function(t){return t},a:j||u.wasmMemory,G:function(t){oe=t},la:le,z:function(t,e,n,r){return le(t,e,n,r)}};!function(){function t(t,e){u.asm=t.exports,ht.qc.push(u.asm.sb),$=u.asm.ub,X.unshift(u.asm.Va),k=e,O||(et--,u.monitorRunDependencies&&u.monitorRunDependencies(et),0==et&&(null!==nt&&(clearInterval(nt),nt=null),rt&&(t=rt,rt=null,t())))}function e(e){t(e.instance,e.module)}function n(t){return function(){if(!M&&(v||w)){if(\\\"function\\\"==typeof fetch&&!tt.startsWith(\\\"file://\\\"))return fetch(tt,{credentials:\\\"same-origin\\\"}).then((function(t){if(!t.ok)throw\\\"failed to load wasm binary file at '\\\"+tt+\\\"'\\\";return t.arrayBuffer()})).catch((function(){return ot()}));if(f)return new Promise((function(t,e){f(tt,(function(e){t(new Uint8Array(e))}),e)}))}return Promise.resolve().then((function(){return ot()}))}().then((function(t){return WebAssembly.instantiate(t,r)})).then((function(t){return t})).then(t,(function(t){x(\\\"failed to asynchronously prepare wasm: \\\"+t),at(t)}))}var r={a:pe};if(O||(et++,u.monitorRunDependencies&&u.monitorRunDependencies(et)),u.instantiateWasm)try{return u.instantiateWasm(r,t)}catch(t){return x(\\\"Module.instantiateWasm callback failed with error: \\\"+t),!1}(M||\\\"function\\\"!=typeof WebAssembly.instantiateStreaming||it()||tt.startsWith(\\\"file://\\\")||_||\\\"function\\\"!=typeof fetch?n(e):fetch(tt,{credentials:\\\"same-origin\\\"}).then((function(t){return WebAssembly.instantiateStreaming(t,r).then(e,(function(t){return x(\\\"wasm streaming compile failed: \\\"+t),x(\\\"falling back to ArrayBuffer instantiation\\\"),n(e)}))}))).catch(s)}(),u.___wasm_call_ctors=function(){return(u.___wasm_call_ctors=u.asm.Va).apply(null,arguments)},u._OrtInit=function(){return(u._OrtInit=u.asm.Wa).apply(null,arguments)},u._OrtCreateSessionOptions=function(){return(u._OrtCreateSessionOptions=u.asm.Xa).apply(null,arguments)},u._OrtAppendExecutionProvider=function(){return(u._OrtAppendExecutionProvider=u.asm.Ya).apply(null,arguments)},u._OrtAddSessionConfigEntry=function(){return(u._OrtAddSessionConfigEntry=u.asm.Za).apply(null,arguments)},u._OrtReleaseSessionOptions=function(){return(u._OrtReleaseSessionOptions=u.asm._a).apply(null,arguments)},u._OrtCreateSession=function(){return(u._OrtCreateSession=u.asm.$a).apply(null,arguments)},u._OrtReleaseSession=function(){return(u._OrtReleaseSession=u.asm.ab).apply(null,arguments)},u._OrtGetInputCount=function(){return(u._OrtGetInputCount=u.asm.bb).apply(null,arguments)},u._OrtGetOutputCount=function(){return(u._OrtGetOutputCount=u.asm.cb).apply(null,arguments)},u._OrtGetInputName=function(){return(u._OrtGetInputName=u.asm.db).apply(null,arguments)},u._OrtGetOutputName=function(){return(u._OrtGetOutputName=u.asm.eb).apply(null,arguments)},u._OrtFree=function(){return(u._OrtFree=u.asm.fb).apply(null,arguments)},u._OrtCreateTensor=function(){return(u._OrtCreateTensor=u.asm.gb).apply(null,arguments)},u._OrtGetTensorData=function(){return(u._OrtGetTensorData=u.asm.hb).apply(null,arguments)},u._OrtReleaseTensor=function(){return(u._OrtReleaseTensor=u.asm.ib).apply(null,arguments)},u._OrtCreateRunOptions=function(){return(u._OrtCreateRunOptions=u.asm.jb).apply(null,arguments)},u._OrtAddRunConfigEntry=function(){return(u._OrtAddRunConfigEntry=u.asm.kb).apply(null,arguments)},u._OrtReleaseRunOptions=function(){return(u._OrtReleaseRunOptions=u.asm.lb).apply(null,arguments)},u._OrtRun=function(){return(u._OrtRun=u.asm.mb).apply(null,arguments)},u._OrtEndProfiling=function(){return(u._OrtEndProfiling=u.asm.nb).apply(null,arguments)};var he=u._pthread_self=function(){return(he=u._pthread_self=u.asm.ob).apply(null,arguments)},de=u._malloc=function(){return(de=u._malloc=u.asm.pb).apply(null,arguments)},ye=u._free=function(){return(ye=u._free=u.asm.qb).apply(null,arguments)},be=u._fflush=function(){return(be=u._fflush=u.asm.rb).apply(null,arguments)};u.__emscripten_tls_init=function(){return(u.__emscripten_tls_init=u.asm.sb).apply(null,arguments)};var me=u.___funcs_on_exit=function(){return(me=u.___funcs_on_exit=u.asm.tb).apply(null,arguments)},ge=u.__emscripten_thread_init=function(){return(ge=u.__emscripten_thread_init=u.asm.vb).apply(null,arguments)};u.__emscripten_thread_crashed=function(){return(u.__emscripten_thread_crashed=u.asm.wb).apply(null,arguments)};var ve,we=u._emscripten_run_in_main_runtime_thread_js=function(){return(we=u._emscripten_run_in_main_runtime_thread_js=u.asm.xb).apply(null,arguments)},_e=u.__emscripten_proxy_execute_task_queue=function(){return(_e=u.__emscripten_proxy_execute_task_queue=u.asm.yb).apply(null,arguments)},Oe=u.__emscripten_thread_free_data=function(){return(Oe=u.__emscripten_thread_free_data=u.asm.zb).apply(null,arguments)},Ae=u.__emscripten_thread_exit=function(){return(Ae=u.__emscripten_thread_exit=u.asm.Ab).apply(null,arguments)},Se=u._setThrew=function(){return(Se=u._setThrew=u.asm.Bb).apply(null,arguments)},Te=u._emscripten_stack_set_limits=function(){return(Te=u._emscripten_stack_set_limits=u.asm.Cb).apply(null,arguments)},Ee=u.stackSave=function(){return(Ee=u.stackSave=u.asm.Db).apply(null,arguments)},Me=u.stackRestore=function(){return(Me=u.stackRestore=u.asm.Eb).apply(null,arguments)},Ce=u.stackAlloc=function(){return(Ce=u.stackAlloc=u.asm.Fb).apply(null,arguments)},xe=u.___cxa_can_catch=function(){return(xe=u.___cxa_can_catch=u.asm.Gb).apply(null,arguments)},Re=u.___cxa_is_pointer_type=function(){return(Re=u.___cxa_is_pointer_type=u.asm.Hb).apply(null,arguments)},je=u.dynCall_j=function(){return(je=u.dynCall_j=u.asm.Ib).apply(null,arguments)},ke=u.dynCall_iiiiij=function(){return(ke=u.dynCall_iiiiij=u.asm.Jb).apply(null,arguments)},De=u.dynCall_jii=function(){return(De=u.dynCall_jii=u.asm.Kb).apply(null,arguments)},Pe=u.dynCall_viiiiij=function(){return(Pe=u.dynCall_viiiiij=u.asm.Lb).apply(null,arguments)},Ue=u.dynCall_vjji=function(){return(Ue=u.dynCall_vjji=u.asm.Mb).apply(null,arguments)},Fe=u.dynCall_viiijjjii=function(){return(Fe=u.dynCall_viiijjjii=u.asm.Nb).apply(null,arguments)},Ie=u.dynCall_iij=function(){return(Ie=u.dynCall_iij=u.asm.Ob).apply(null,arguments)},We=u.dynCall_ji=function(){return(We=u.dynCall_ji=u.asm.Pb).apply(null,arguments)},He=u.dynCall_iiiiiij=function(){return(He=u.dynCall_iiiiiij=u.asm.Qb).apply(null,arguments)},Le=u.dynCall_iiij=function(){return(Le=u.dynCall_iiij=u.asm.Rb).apply(null,arguments)};function ze(){function t(){if(!ve&&(ve=!0,u.calledRun=!0,!H)&&(O||dt(X),c(u),u.onRuntimeInitialized&&u.onRuntimeInitialized(),!O)){if(u.postRun)for(\\\"function\\\"==typeof u.postRun&&(u.postRun=[u.postRun]);u.postRun.length;){var t=u.postRun.shift();Z.unshift(t)}dt(Z)}}if(!(0<et))if(O)c(u),O||dt(X),postMessage({cmd:\\\"loaded\\\"});else{if(u.preRun)for(\\\"function\\\"==typeof u.preRun&&(u.preRun=[u.preRun]);u.preRun.length;)K();dt(q),0<et||(u.setStatus?(u.setStatus(\\\"Running...\\\"),setTimeout((function(){setTimeout((function(){u.setStatus(\\\"\\\")}),1),t()}),1)):t())}}if(u.UTF8ToString=Y,u.stringToUTF8=function(t,e,n){return B(t,r(),e,n)},u.lengthBytesUTF8=G,u.keepRuntimeAlive=Q,u.wasmMemory=j,u.stackSave=Ee,u.stackRestore=Me,u.stackAlloc=Ce,u.ExitStatus=ct,u.PThread=ht,rt=function t(){ve||ze(),ve||(rt=t)},u.preInit)for(\\\"function\\\"==typeof u.preInit&&(u.preInit=[u.preInit]);0<u.preInit.length;)u.preInit.pop()();return ze(),t.ready});t.exports=r},932:(t,e,n)=>{var _scriptDir,r=(_scriptDir=(_scriptDir=\\\"undefined\\\"!=typeof document&&document.currentScript?document.currentScript.src:void 0)||\\\"/index.js\\\",function(t){var e,r,a;t=t||{},e||(e=void 0!==t?t:{}),e.ready=new Promise((function(t,e){r=t,a=e}));var i,o,u,c,s,l,f=Object.assign({},e),p=\\\"./this.program\\\",h=(t,e)=>{throw e},d=\\\"object\\\"==typeof window,y=\\\"function\\\"==typeof importScripts,b=\\\"object\\\"==typeof process&&\\\"object\\\"==typeof process.versions&&\\\"string\\\"==typeof process.versions.node,m=\\\"\\\";b?(m=y?n(908).dirname(m)+\\\"/\\\":\\\"//\\\",l=()=>{s||(c=n(384),s=n(908))},i=function(t,e){return l(),t=s.normalize(t),c.readFileSync(t,e?void 0:\\\"utf8\\\")},u=t=>((t=i(t,!0)).buffer||(t=new Uint8Array(t)),t),o=(t,e,n)=>{l(),t=s.normalize(t),c.readFile(t,(function(t,r){t?n(t):e(r.buffer)}))},1<process.argv.length&&(p=process.argv[1].replace(/\\\\\\\\/g,\\\"/\\\")),process.argv.slice(2),process.on(\\\"uncaughtException\\\",(function(t){if(!(t instanceof J))throw t})),process.on(\\\"unhandledRejection\\\",(function(t){throw t})),h=(t,e)=>{if(_||0<L)throw process.exitCode=t,e;e instanceof J||w(\\\"exiting due to exception: \\\"+e),process.exit(t)},e.inspect=function(){return\\\"[Emscripten Module object]\\\"}):(d||y)&&(y?m=self.location.href:\\\"undefined\\\"!=typeof document&&document.currentScript&&(m=document.currentScript.src),_scriptDir&&(m=_scriptDir),m=0!==m.indexOf(\\\"blob:\\\")?m.substr(0,m.replace(/[?#].*/,\\\"\\\").lastIndexOf(\\\"/\\\")+1):\\\"\\\",i=t=>{var e=new XMLHttpRequest;return e.open(\\\"GET\\\",t,!1),e.send(null),e.responseText},y&&(u=t=>{var e=new XMLHttpRequest;return e.open(\\\"GET\\\",t,!1),e.responseType=\\\"arraybuffer\\\",e.send(null),new Uint8Array(e.response)}),o=(t,e,n)=>{var r=new XMLHttpRequest;r.open(\\\"GET\\\",t,!0),r.responseType=\\\"arraybuffer\\\",r.onload=()=>{200==r.status||0==r.status&&r.response?e(r.response):n()},r.onerror=n,r.send(null)});var g,v=e.print||console.log.bind(console),w=e.printErr||console.warn.bind(console);Object.assign(e,f),f=null,e.thisProgram&&(p=e.thisProgram),e.quit&&(h=e.quit),e.wasmBinary&&(g=e.wasmBinary);var _=e.noExitRuntime||!1;\\\"object\\\"!=typeof WebAssembly&&V(\\\"no native wasm support detected\\\");var O,A,S,T,E,M,C=!1,x=\\\"undefined\\\"!=typeof TextDecoder?new TextDecoder(\\\"utf8\\\"):void 0;function R(t,e,n){var r=(e>>>=0)+n;for(n=e;t[n]&&!(n>=r);)++n;if(16<n-e&&t.buffer&&x)return x.decode(t.subarray(e,n));for(r=\\\"\\\";e<n;){var a=t[e++];if(128&a){var i=63&t[e++];if(192==(224&a))r+=String.fromCharCode((31&a)<<6|i);else{var o=63&t[e++];65536>(a=224==(240&a)?(15&a)<<12|i<<6|o:(7&a)<<18|i<<12|o<<6|63&t[e++])?r+=String.fromCharCode(a):(a-=65536,r+=String.fromCharCode(55296|a>>10,56320|1023&a))}}else r+=String.fromCharCode(a)}return r}function j(t,e){return(t>>>=0)?R(T,t,e):\\\"\\\"}function k(t,e,n,r){if(!(0<r))return 0;var a=n>>>=0;r=n+r-1;for(var i=0;i<t.length;++i){var o=t.charCodeAt(i);if(55296<=o&&57343>=o&&(o=65536+((1023&o)<<10)|1023&t.charCodeAt(++i)),127>=o){if(n>=r)break;e[n++>>>0]=o}else{if(2047>=o){if(n+1>=r)break;e[n++>>>0]=192|o>>6}else{if(65535>=o){if(n+2>=r)break;e[n++>>>0]=224|o>>12}else{if(n+3>=r)break;e[n++>>>0]=240|o>>18,e[n++>>>0]=128|o>>12&63}e[n++>>>0]=128|o>>6&63}e[n++>>>0]=128|63&o}}return e[n>>>0]=0,n-a}function D(t){for(var e=0,n=0;n<t.length;++n){var r=t.charCodeAt(n);127>=r?e++:2047>=r?e+=2:55296<=r&&57343>=r?(e+=4,++n):e+=3}return e}function P(){var t=O.buffer;A=t,e.HEAP8=S=new Int8Array(t),e.HEAP16=new Int16Array(t),e.HEAP32=E=new Int32Array(t),e.HEAPU8=T=new Uint8Array(t),e.HEAPU16=new Uint16Array(t),e.HEAPU32=M=new Uint32Array(t),e.HEAPF32=new Float32Array(t),e.HEAPF64=new Float64Array(t)}var U,F=[],I=[],W=[],H=[],L=0;function z(){var t=e.preRun.shift();F.unshift(t)}var Y,B=0,G=null,N=null;function V(t){throw e.onAbort&&e.onAbort(t),w(t=\\\"Aborted(\\\"+t+\\\")\\\"),C=!0,t=new WebAssembly.RuntimeError(t+\\\". Build with -sASSERTIONS for more info.\\\"),a(t),t}function $(){return Y.startsWith(\\\"data:application/octet-stream;base64,\\\")}if(Y=\\\"ort-wasm.wasm\\\",!$()){var q=Y;Y=e.locateFile?e.locateFile(q,m):m+q}function X(){var t=Y;try{if(t==Y&&g)return new Uint8Array(g);if(u)return u(t);throw\\\"both async and sync fetching of the wasm failed\\\"}catch(t){V(t)}}function J(t){this.name=\\\"ExitStatus\\\",this.message=\\\"Program terminated with exit(\\\"+t+\\\")\\\",this.status=t}function Z(t){for(;0<t.length;)t.shift()(e)}var Q=[],K=0,tt=0;function et(t){this.Db=t,this.zb=t-24,this.Ub=function(t){M[this.zb+4>>2>>>0]=t},this.Eb=function(){return M[this.zb+4>>2>>>0]},this.Sb=function(t){M[this.zb+8>>2>>>0]=t},this.Wb=function(){return M[this.zb+8>>2>>>0]},this.Tb=function(){E[this.zb>>2>>>0]=0},this.Ib=function(t){S[this.zb+12>>0>>>0]=t?1:0},this.Pb=function(){return 0!=S[this.zb+12>>0>>>0]},this.Jb=function(t){S[this.zb+13>>0>>>0]=t?1:0},this.Lb=function(){return 0!=S[this.zb+13>>0>>>0]},this.Rb=function(t,e){this.Fb(0),this.Ub(t),this.Sb(e),this.Tb(),this.Ib(!1),this.Jb(!1)},this.Nb=function(){E[this.zb>>2>>>0]+=1},this.Xb=function(){var t=E[this.zb>>2>>>0];return E[this.zb>>2>>>0]=t-1,1===t},this.Fb=function(t){M[this.zb+16>>2>>>0]=t},this.Ob=function(){return M[this.zb+16>>2>>>0]},this.Qb=function(){if(Mt(this.Eb()))return M[this.Db>>2>>>0];var t=this.Ob();return 0!==t?t:this.Db}}function nt(t){return vt(new et(t).zb)}var rt=[];function at(t){var e=rt[t];return e||(t>=rt.length&&(rt.length=t+1),rt[t]=e=U.get(t)),e}function it(t){var e=D(t)+1,n=gt(e);return n&&k(t,S,n,e),n}var ot={};function ut(){if(!ct){var t,e={USER:\\\"web_user\\\",LOGNAME:\\\"web_user\\\",PATH:\\\"/\\\",PWD:\\\"/\\\",HOME:\\\"/home/<USER>\",LANG:(\\\"object\\\"==typeof navigator&&navigator.languages&&navigator.languages[0]||\\\"C\\\").replace(\\\"-\\\",\\\"_\\\")+\\\".UTF-8\\\",_:p||\\\"./this.program\\\"};for(t in ot)void 0===ot[t]?delete e[t]:e[t]=ot[t];var n=[];for(t in e)n.push(t+\\\"=\\\"+e[t]);ct=n}return ct}var ct,st=[null,[],[]];function lt(t,e){var n=st[t];0===e||10===e?((1===t?v:w)(R(n,0)),n.length=0):n.push(e)}var ft=0;function pt(t){return 0==t%4&&(0!=t%100||0==t%400)}var ht=[31,29,31,30,31,30,31,31,30,31,30,31],dt=[31,28,31,30,31,30,31,31,30,31,30,31];function yt(t,e,n,r){function a(t,e,n){for(t=\\\"number\\\"==typeof t?t.toString():t||\\\"\\\";t.length<e;)t=n[0]+t;return t}function i(t,e){return a(t,e,\\\"0\\\")}function o(t,e){function n(t){return 0>t?-1:0<t?1:0}var r;return 0===(r=n(t.getFullYear()-e.getFullYear()))&&0===(r=n(t.getMonth()-e.getMonth()))&&(r=n(t.getDate()-e.getDate())),r}function u(t){switch(t.getDay()){case 0:return new Date(t.getFullYear()-1,11,29);case 1:return t;case 2:return new Date(t.getFullYear(),0,3);case 3:return new Date(t.getFullYear(),0,2);case 4:return new Date(t.getFullYear(),0,1);case 5:return new Date(t.getFullYear()-1,11,31);case 6:return new Date(t.getFullYear()-1,11,30)}}function c(t){var e=t.Bb;for(t=new Date(new Date(t.Cb+1900,0,1).getTime());0<e;){var n=t.getMonth(),r=(pt(t.getFullYear())?ht:dt)[n];if(!(e>r-t.getDate())){t.setDate(t.getDate()+e);break}e-=r-t.getDate()+1,t.setDate(1),11>n?t.setMonth(n+1):(t.setMonth(0),t.setFullYear(t.getFullYear()+1))}return n=new Date(t.getFullYear()+1,0,4),e=u(new Date(t.getFullYear(),0,4)),n=u(n),0>=o(e,t)?0>=o(n,t)?t.getFullYear()+1:t.getFullYear():t.getFullYear()-1}var s=E[r+40>>2>>>0];for(var l in r={$b:E[r>>2>>>0],Zb:E[r+4>>2>>>0],Gb:E[r+8>>2>>>0],Kb:E[r+12>>2>>>0],Hb:E[r+16>>2>>>0],Cb:E[r+20>>2>>>0],Ab:E[r+24>>2>>>0],Bb:E[r+28>>2>>>0],bc:E[r+32>>2>>>0],Yb:E[r+36>>2>>>0],ac:s?j(s):\\\"\\\"},n=j(n),s={\\\"%c\\\":\\\"%a %b %d %H:%M:%S %Y\\\",\\\"%D\\\":\\\"%m/%d/%y\\\",\\\"%F\\\":\\\"%Y-%m-%d\\\",\\\"%h\\\":\\\"%b\\\",\\\"%r\\\":\\\"%I:%M:%S %p\\\",\\\"%R\\\":\\\"%H:%M\\\",\\\"%T\\\":\\\"%H:%M:%S\\\",\\\"%x\\\":\\\"%m/%d/%y\\\",\\\"%X\\\":\\\"%H:%M:%S\\\",\\\"%Ec\\\":\\\"%c\\\",\\\"%EC\\\":\\\"%C\\\",\\\"%Ex\\\":\\\"%m/%d/%y\\\",\\\"%EX\\\":\\\"%H:%M:%S\\\",\\\"%Ey\\\":\\\"%y\\\",\\\"%EY\\\":\\\"%Y\\\",\\\"%Od\\\":\\\"%d\\\",\\\"%Oe\\\":\\\"%e\\\",\\\"%OH\\\":\\\"%H\\\",\\\"%OI\\\":\\\"%I\\\",\\\"%Om\\\":\\\"%m\\\",\\\"%OM\\\":\\\"%M\\\",\\\"%OS\\\":\\\"%S\\\",\\\"%Ou\\\":\\\"%u\\\",\\\"%OU\\\":\\\"%U\\\",\\\"%OV\\\":\\\"%V\\\",\\\"%Ow\\\":\\\"%w\\\",\\\"%OW\\\":\\\"%W\\\",\\\"%Oy\\\":\\\"%y\\\"})n=n.replace(new RegExp(l,\\\"g\\\"),s[l]);var f=\\\"Sunday Monday Tuesday Wednesday Thursday Friday Saturday\\\".split(\\\" \\\"),p=\\\"January February March April May June July August September October November December\\\".split(\\\" \\\");for(l in s={\\\"%a\\\":function(t){return f[t.Ab].substring(0,3)},\\\"%A\\\":function(t){return f[t.Ab]},\\\"%b\\\":function(t){return p[t.Hb].substring(0,3)},\\\"%B\\\":function(t){return p[t.Hb]},\\\"%C\\\":function(t){return i((t.Cb+1900)/100|0,2)},\\\"%d\\\":function(t){return i(t.Kb,2)},\\\"%e\\\":function(t){return a(t.Kb,2,\\\" \\\")},\\\"%g\\\":function(t){return c(t).toString().substring(2)},\\\"%G\\\":function(t){return c(t)},\\\"%H\\\":function(t){return i(t.Gb,2)},\\\"%I\\\":function(t){return 0==(t=t.Gb)?t=12:12<t&&(t-=12),i(t,2)},\\\"%j\\\":function(t){for(var e=0,n=0;n<=t.Hb-1;e+=(pt(t.Cb+1900)?ht:dt)[n++]);return i(t.Kb+e,3)},\\\"%m\\\":function(t){return i(t.Hb+1,2)},\\\"%M\\\":function(t){return i(t.Zb,2)},\\\"%n\\\":function(){return\\\"\\\\n\\\"},\\\"%p\\\":function(t){return 0<=t.Gb&&12>t.Gb?\\\"AM\\\":\\\"PM\\\"},\\\"%S\\\":function(t){return i(t.$b,2)},\\\"%t\\\":function(){return\\\"\\\\t\\\"},\\\"%u\\\":function(t){return t.Ab||7},\\\"%U\\\":function(t){return i(Math.floor((t.Bb+7-t.Ab)/7),2)},\\\"%V\\\":function(t){var e=Math.floor((t.Bb+7-(t.Ab+6)%7)/7);if(2>=(t.Ab+371-t.Bb-2)%7&&e++,e)53==e&&(4==(n=(t.Ab+371-t.Bb)%7)||3==n&&pt(t.Cb)||(e=1));else{e=52;var n=(t.Ab+7-t.Bb-1)%7;(4==n||5==n&&pt(t.Cb%400-1))&&e++}return i(e,2)},\\\"%w\\\":function(t){return t.Ab},\\\"%W\\\":function(t){return i(Math.floor((t.Bb+7-(t.Ab+6)%7)/7),2)},\\\"%y\\\":function(t){return(t.Cb+1900).toString().substring(2)},\\\"%Y\\\":function(t){return t.Cb+1900},\\\"%z\\\":function(t){var e=0<=(t=t.Yb);return t=Math.abs(t)/60,(e?\\\"+\\\":\\\"-\\\")+String(\\\"0000\\\"+(t/60*100+t%60)).slice(-4)},\\\"%Z\\\":function(t){return t.ac},\\\"%%\\\":function(){return\\\"%\\\"}},n=n.replace(/%%/g,\\\"\\\\0\\\\0\\\"),s)n.includes(l)&&(n=n.replace(new RegExp(l,\\\"g\\\"),s[l](r)));return l=function(t){var e=Array(D(t)+1);return k(t,e,0,e.length),e}(n=n.replace(/\\\\0\\\\0/g,\\\"%\\\")),l.length>e?0:(S.set(l,t>>>0),l.length-1)}var bt={a:function(t){return gt(t+24)+24},m:function(t){return(t=new et(t)).Pb()||(t.Ib(!0),K--),t.Jb(!1),Q.push(t),t.Nb(),t.Qb()},ia:function(t){throw w(\\\"Unexpected exception thrown, this is not properly supported - aborting\\\"),C=!0,t},w:function(){Ot(0);var t=Q.pop();if(t.Xb()&&!t.Lb()){var e=t.Wb();e&&at(e)(t.Db),nt(t.Db)}tt=0},d:function(){var t=tt;if(!t)return ft=0;var e=new et(t);e.Fb(t);var n=e.Eb();if(!n)return ft=0,t;for(var r=Array.prototype.slice.call(arguments),a=0;a<r.length;a++){var i=r[a];if(0===i||i===n)break;if(Et(i,n,e.zb+16))return ft=i,t}return ft=n,t},k:function(){var t=tt;if(!t)return ft=0;var e=new et(t);e.Fb(t);var n=e.Eb();if(!n)return ft=0,t;for(var r=Array.prototype.slice.call(arguments),a=0;a<r.length;a++){var i=r[a];if(0===i||i===n)break;if(Et(i,n,e.zb+16))return ft=i,t}return ft=n,t},g:function(){var t=tt;if(!t)return ft=0;var e=new et(t);e.Fb(t);var n=e.Eb();if(!n)return ft=0,t;for(var r=Array.prototype.slice.call(arguments),a=0;a<r.length;a++){var i=r[a];if(0===i||i===n)break;if(Et(i,n,e.zb+16))return ft=i,t}return ft=n,t},s:nt,L:function(){var t=Q.pop();t||V(\\\"no exception to throw\\\");var e=t.Db;throw t.Lb()||(Q.push(t),t.Jb(!0),t.Ib(!1),K++),tt=e,e},b:function(t,e,n){throw new et(t).Rb(e,n),tt=t,K++,t},la:function(){return K},i:function(t){throw tt||(tt=t),t},H:function(){return 0},Ba:function(){},pa:function(){},ra:function(){},ka:function(){return 0},za:function(){},ua:function(){},ya:function(){},R:function(){},qa:function(){},na:function(){},Aa:function(){},oa:function(){},Ha:function(){},Ja:function(){V(\\\"To use dlopen, you need enable dynamic linking, see https://github.com/emscripten-core/emscripten/wiki/Linking\\\")},Ia:function(){V(\\\"To use dlopen, you need enable dynamic linking, see https://github.com/emscripten-core/emscripten/wiki/Linking\\\")},S:function(){return Date.now()},Ca:function(){return!0},Da:function(t,e){t=new Date(1e3*(M[t>>>2]+4294967296*E[t+4>>>2])),E[e>>2>>>0]=t.getUTCSeconds(),E[e+4>>2>>>0]=t.getUTCMinutes(),E[e+8>>2>>>0]=t.getUTCHours(),E[e+12>>2>>>0]=t.getUTCDate(),E[e+16>>2>>>0]=t.getUTCMonth(),E[e+20>>2>>>0]=t.getUTCFullYear()-1900,E[e+24>>2>>>0]=t.getUTCDay(),E[e+28>>2>>>0]=(t.getTime()-Date.UTC(t.getUTCFullYear(),0,1,0,0,0,0))/864e5|0},Ea:function(t,e){t=new Date(1e3*(M[t>>>2]+4294967296*E[t+4>>>2])),E[e>>2>>>0]=t.getSeconds(),E[e+4>>2>>>0]=t.getMinutes(),E[e+8>>2>>>0]=t.getHours(),E[e+12>>2>>>0]=t.getDate(),E[e+16>>2>>>0]=t.getMonth(),E[e+20>>2>>>0]=t.getFullYear()-1900,E[e+24>>2>>>0]=t.getDay();var n=new Date(t.getFullYear(),0,1);E[e+28>>2>>>0]=(t.getTime()-n.getTime())/864e5|0,E[e+36>>2>>>0]=-60*t.getTimezoneOffset();var r=new Date(t.getFullYear(),6,1).getTimezoneOffset();n=n.getTimezoneOffset(),E[e+32>>2>>>0]=0|(r!=n&&t.getTimezoneOffset()==Math.min(n,r))},Fa:function(t){var e=new Date(E[t+20>>2>>>0]+1900,E[t+16>>2>>>0],E[t+12>>2>>>0],E[t+8>>2>>>0],E[t+4>>2>>>0],E[t>>2>>>0],0),n=E[t+32>>2>>>0],r=e.getTimezoneOffset(),a=new Date(e.getFullYear(),0,1),i=new Date(e.getFullYear(),6,1).getTimezoneOffset(),o=a.getTimezoneOffset(),u=Math.min(o,i);return 0>n?E[t+32>>2>>>0]=Number(i!=o&&u==r):0<n!=(u==r)&&(i=Math.max(o,i),e.setTime(e.getTime()+6e4*((0<n?u:i)-r))),E[t+24>>2>>>0]=e.getDay(),E[t+28>>2>>>0]=(e.getTime()-a.getTime())/864e5|0,E[t>>2>>>0]=e.getSeconds(),E[t+4>>2>>>0]=e.getMinutes(),E[t+8>>2>>>0]=e.getHours(),E[t+12>>2>>>0]=e.getDate(),E[t+16>>2>>>0]=e.getMonth(),e.getTime()/1e3|0},sa:function(){return-52},ta:function(){},Ga:function t(e,n,r){t.Vb||(t.Vb=!0,function(t,e,n){function r(t){return(t=t.toTimeString().match(/\\\\(([A-Za-z ]+)\\\\)$/))?t[1]:\\\"GMT\\\"}var a=(new Date).getFullYear(),i=new Date(a,0,1),o=new Date(a,6,1);a=i.getTimezoneOffset();var u=o.getTimezoneOffset();E[t>>2>>>0]=60*Math.max(a,u),E[e>>2>>>0]=Number(a!=u),t=r(i),e=r(o),t=it(t),e=it(e),u<a?(M[n>>2>>>0]=t,M[n+4>>2>>>0]=e):(M[n>>2>>>0]=e,M[n+4>>2>>>0]=t)}(e,n,r))},B:function(){V(\\\"\\\")},ma:function(){return 4294901760},I:b?()=>{var t=process.hrtime();return 1e3*t[0]+t[1]/1e6}:()=>performance.now(),xa:function(t,e,n){T.copyWithin(t>>>0,e>>>0,e+n>>>0)},G:function(t){var e=T.length;if(4294901760<(t>>>=0))return!1;for(var n=1;4>=n;n*=2){var r=e*(1+.2/n);r=Math.min(r,t+100663296);var a=Math;r=Math.max(t,r),a=a.min.call(a,4294901760,r+(65536-r%65536)%65536);t:{try{O.grow(a-A.byteLength+65535>>>16),P();var i=1;break t}catch(t){}i=void 0}if(i)return!0}return!1},va:function(t,e){var n=0;return ut().forEach((function(r,a){var i=e+n;for(a=M[t+4*a>>2>>>0]=i,i=0;i<r.length;++i)S[a++>>0>>>0]=r.charCodeAt(i);S[a>>0>>>0]=0,n+=r.length+1})),0},wa:function(t,e){var n=ut();M[t>>2>>>0]=n.length;var r=0;return n.forEach((function(t){r+=t.length+1})),M[e>>2>>>0]=r,0},ba:function(t){_||0<L||(_t(),Z(W),wt(0),st[1].length&&lt(1,10),st[2].length&&lt(2,10)),_||0<L||(e.onExit&&e.onExit(t),C=!0),h(t,new J(t))},E:function(){return 52},Q:function(){return 52},ca:function(){return 70},P:function(t,e,n,r){for(var a=0,i=0;i<n;i++){var o=M[e>>2>>>0],u=M[e+4>>2>>>0];e+=8;for(var c=0;c<u;c++)lt(t,T[o+c>>>0]);a+=u}return M[r>>2>>>0]=a,0},c:function(){return ft},ja:function t(e,r){t.Mb||(t.Mb=function(){if(\\\"object\\\"==typeof crypto&&\\\"function\\\"==typeof crypto.getRandomValues){var t=new Uint8Array(1);return()=>(crypto.getRandomValues(t),t[0])}if(b)try{var e=n(Object(function(){var t=new Error(\\\"Cannot find module 'crypto'\\\");throw t.code=\\\"MODULE_NOT_FOUND\\\",t}()));return()=>e.randomBytes(1)[0]}catch(t){}return()=>V(\\\"randomDevice\\\")}());for(var a=0;a<r;a++)S[e+a>>0>>>0]=t.Mb();return 0},ea:function(t,e,n){var r=At();try{return at(t)(e,n)}catch(t){if(St(r),t!==t+0)throw t;Ot(1,0)}},fa:function(t,e,n){var r=At();try{return at(t)(e,n)}catch(t){if(St(r),t!==t+0)throw t;Ot(1,0)}},J:function(t){var e=At();try{return at(t)()}catch(t){if(St(e),t!==t+0)throw t;Ot(1,0)}},e:function(t,e){var n=At();try{return at(t)(e)}catch(t){if(St(n),t!==t+0)throw t;Ot(1,0)}},N:function(t,e,n){var r=At();try{return at(t)(e,n)}catch(t){if(St(r),t!==t+0)throw t;Ot(1,0)}},O:function(t,e,n){var r=At();try{return at(t)(e,n)}catch(t){if(St(r),t!==t+0)throw t;Ot(1,0)}},j:function(t,e,n){var r=At();try{return at(t)(e,n)}catch(t){if(St(r),t!==t+0)throw t;Ot(1,0)}},o:function(t,e,n,r){var a=At();try{return at(t)(e,n,r)}catch(t){if(St(a),t!==t+0)throw t;Ot(1,0)}},p:function(t,e,n,r,a){var i=At();try{return at(t)(e,n,r,a)}catch(t){if(St(i),t!==t+0)throw t;Ot(1,0)}},M:function(t,e,n,r,a,i){var o=At();try{return at(t)(e,n,r,a,i)}catch(t){if(St(o),t!==t+0)throw t;Ot(1,0)}},r:function(t,e,n,r,a,i){var o=At();try{return at(t)(e,n,r,a,i)}catch(t){if(St(o),t!==t+0)throw t;Ot(1,0)}},v:function(t,e,n,r,a,i,o){var u=At();try{return at(t)(e,n,r,a,i,o)}catch(t){if(St(u),t!==t+0)throw t;Ot(1,0)}},K:function(t,e,n,r,a,i,o,u){var c=At();try{return at(t)(e,n,r,a,i,o,u)}catch(t){if(St(c),t!==t+0)throw t;Ot(1,0)}},D:function(t,e,n,r,a,i,o,u,c,s,l,f){var p=At();try{return at(t)(e,n,r,a,i,o,u,c,s,l,f)}catch(t){if(St(p),t!==t+0)throw t;Ot(1,0)}},X:function(t,e,n,r,a,i,o,u){var c=At();try{return Ft(t,e,n,r,a,i,o,u)}catch(t){if(St(c),t!==t+0)throw t;Ot(1,0)}},V:function(t,e,n,r,a,i,o){var u=At();try{return xt(t,e,n,r,a,i,o)}catch(t){if(St(u),t!==t+0)throw t;Ot(1,0)}},U:function(t,e,n,r,a){var i=At();try{return It(t,e,n,r,a)}catch(t){if(St(i),t!==t+0)throw t;Ot(1,0)}},Z:function(t,e,n,r){var a=At();try{return Pt(t,e,n,r)}catch(t){if(St(a),t!==t+0)throw t;Ot(1,0)}},W:function(t){var e=At();try{return Ct(t)}catch(t){if(St(e),t!==t+0)throw t;Ot(1,0)}},Y:function(t,e){var n=At();try{return Ut(t,e)}catch(t){if(St(n),t!==t+0)throw t;Ot(1,0)}},T:function(t,e,n){var r=At();try{return Rt(t,e,n)}catch(t){if(St(r),t!==t+0)throw t;Ot(1,0)}},f:function(t){var e=At();try{at(t)()}catch(t){if(St(e),t!==t+0)throw t;Ot(1,0)}},q:function(t,e){var n=At();try{at(t)(e)}catch(t){if(St(n),t!==t+0)throw t;Ot(1,0)}},h:function(t,e,n){var r=At();try{at(t)(e,n)}catch(t){if(St(r),t!==t+0)throw t;Ot(1,0)}},da:function(t,e,n,r){var a=At();try{at(t)(e,n,r)}catch(t){if(St(a),t!==t+0)throw t;Ot(1,0)}},l:function(t,e,n,r){var a=At();try{at(t)(e,n,r)}catch(t){if(St(a),t!==t+0)throw t;Ot(1,0)}},t:function(t,e,n,r,a){var i=At();try{at(t)(e,n,r,a)}catch(t){if(St(i),t!==t+0)throw t;Ot(1,0)}},u:function(t,e,n,r,a,i){var o=At();try{at(t)(e,n,r,a,i)}catch(t){if(St(o),t!==t+0)throw t;Ot(1,0)}},x:function(t,e,n,r,a,i,o){var u=At();try{at(t)(e,n,r,a,i,o)}catch(t){if(St(u),t!==t+0)throw t;Ot(1,0)}},z:function(t,e,n,r,a,i,o,u){var c=At();try{at(t)(e,n,r,a,i,o,u)}catch(t){if(St(c),t!==t+0)throw t;Ot(1,0)}},ga:function(t,e,n,r,a,i,o,u,c){var s=At();try{at(t)(e,n,r,a,i,o,u,c)}catch(t){if(St(s),t!==t+0)throw t;Ot(1,0)}},A:function(t,e,n,r,a,i,o,u,c,s,l){var f=At();try{at(t)(e,n,r,a,i,o,u,c,s,l)}catch(t){if(St(f),t!==t+0)throw t;Ot(1,0)}},C:function(t,e,n,r,a,i,o,u,c,s,l,f,p,h,d,y){var b=At();try{at(t)(e,n,r,a,i,o,u,c,s,l,f,p,h,d,y)}catch(t){if(St(b),t!==t+0)throw t;Ot(1,0)}},aa:function(t,e,n,r,a,i,o,u){var c=At();try{jt(t,e,n,r,a,i,o,u)}catch(t){if(St(c),t!==t+0)throw t;Ot(1,0)}},_:function(t,e,n,r,a,i,o,u,c,s,l,f){var p=At();try{Dt(t,e,n,r,a,i,o,u,c,s,l,f)}catch(t){if(St(p),t!==t+0)throw t;Ot(1,0)}},$:function(t,e,n,r,a,i){var o=At();try{kt(t,e,n,r,a,i)}catch(t){if(St(o),t!==t+0)throw t;Ot(1,0)}},n:function(t){return t},F:function(t){ft=t},ha:yt,y:function(t,e,n,r){return yt(t,e,n,r)}};!function(){function t(t){e.asm=t.exports,O=e.asm.Ka,P(),U=e.asm.ib,I.unshift(e.asm.La),B--,e.monitorRunDependencies&&e.monitorRunDependencies(B),0==B&&(null!==G&&(clearInterval(G),G=null),N&&(t=N,N=null,t()))}function n(e){t(e.instance)}function r(t){return function(){if(!g&&(d||y)){if(\\\"function\\\"==typeof fetch&&!Y.startsWith(\\\"file://\\\"))return fetch(Y,{credentials:\\\"same-origin\\\"}).then((function(t){if(!t.ok)throw\\\"failed to load wasm binary file at '\\\"+Y+\\\"'\\\";return t.arrayBuffer()})).catch((function(){return X()}));if(o)return new Promise((function(t,e){o(Y,(function(e){t(new Uint8Array(e))}),e)}))}return Promise.resolve().then((function(){return X()}))}().then((function(t){return WebAssembly.instantiate(t,i)})).then((function(t){return t})).then(t,(function(t){w(\\\"failed to asynchronously prepare wasm: \\\"+t),V(t)}))}var i={a:bt};if(B++,e.monitorRunDependencies&&e.monitorRunDependencies(B),e.instantiateWasm)try{return e.instantiateWasm(i,t)}catch(t){return w(\\\"Module.instantiateWasm callback failed with error: \\\"+t),!1}(g||\\\"function\\\"!=typeof WebAssembly.instantiateStreaming||$()||Y.startsWith(\\\"file://\\\")||b||\\\"function\\\"!=typeof fetch?r(n):fetch(Y,{credentials:\\\"same-origin\\\"}).then((function(t){return WebAssembly.instantiateStreaming(t,i).then(n,(function(t){return w(\\\"wasm streaming compile failed: \\\"+t),w(\\\"falling back to ArrayBuffer instantiation\\\"),r(n)}))}))).catch(a)}(),e.___wasm_call_ctors=function(){return(e.___wasm_call_ctors=e.asm.La).apply(null,arguments)},e._OrtInit=function(){return(e._OrtInit=e.asm.Ma).apply(null,arguments)},e._OrtCreateSessionOptions=function(){return(e._OrtCreateSessionOptions=e.asm.Na).apply(null,arguments)},e._OrtAppendExecutionProvider=function(){return(e._OrtAppendExecutionProvider=e.asm.Oa).apply(null,arguments)},e._OrtAddSessionConfigEntry=function(){return(e._OrtAddSessionConfigEntry=e.asm.Pa).apply(null,arguments)},e._OrtReleaseSessionOptions=function(){return(e._OrtReleaseSessionOptions=e.asm.Qa).apply(null,arguments)},e._OrtCreateSession=function(){return(e._OrtCreateSession=e.asm.Ra).apply(null,arguments)},e._OrtReleaseSession=function(){return(e._OrtReleaseSession=e.asm.Sa).apply(null,arguments)},e._OrtGetInputCount=function(){return(e._OrtGetInputCount=e.asm.Ta).apply(null,arguments)},e._OrtGetOutputCount=function(){return(e._OrtGetOutputCount=e.asm.Ua).apply(null,arguments)},e._OrtGetInputName=function(){return(e._OrtGetInputName=e.asm.Va).apply(null,arguments)},e._OrtGetOutputName=function(){return(e._OrtGetOutputName=e.asm.Wa).apply(null,arguments)},e._OrtFree=function(){return(e._OrtFree=e.asm.Xa).apply(null,arguments)},e._OrtCreateTensor=function(){return(e._OrtCreateTensor=e.asm.Ya).apply(null,arguments)},e._OrtGetTensorData=function(){return(e._OrtGetTensorData=e.asm.Za).apply(null,arguments)},e._OrtReleaseTensor=function(){return(e._OrtReleaseTensor=e.asm._a).apply(null,arguments)},e._OrtCreateRunOptions=function(){return(e._OrtCreateRunOptions=e.asm.$a).apply(null,arguments)},e._OrtAddRunConfigEntry=function(){return(e._OrtAddRunConfigEntry=e.asm.ab).apply(null,arguments)},e._OrtReleaseRunOptions=function(){return(e._OrtReleaseRunOptions=e.asm.bb).apply(null,arguments)},e._OrtRun=function(){return(e._OrtRun=e.asm.cb).apply(null,arguments)},e._OrtEndProfiling=function(){return(e._OrtEndProfiling=e.asm.db).apply(null,arguments)};var mt,gt=e._malloc=function(){return(gt=e._malloc=e.asm.eb).apply(null,arguments)},vt=e._free=function(){return(vt=e._free=e.asm.fb).apply(null,arguments)},wt=e._fflush=function(){return(wt=e._fflush=e.asm.gb).apply(null,arguments)},_t=e.___funcs_on_exit=function(){return(_t=e.___funcs_on_exit=e.asm.hb).apply(null,arguments)},Ot=e._setThrew=function(){return(Ot=e._setThrew=e.asm.jb).apply(null,arguments)},At=e.stackSave=function(){return(At=e.stackSave=e.asm.kb).apply(null,arguments)},St=e.stackRestore=function(){return(St=e.stackRestore=e.asm.lb).apply(null,arguments)},Tt=e.stackAlloc=function(){return(Tt=e.stackAlloc=e.asm.mb).apply(null,arguments)},Et=e.___cxa_can_catch=function(){return(Et=e.___cxa_can_catch=e.asm.nb).apply(null,arguments)},Mt=e.___cxa_is_pointer_type=function(){return(Mt=e.___cxa_is_pointer_type=e.asm.ob).apply(null,arguments)},Ct=e.dynCall_j=function(){return(Ct=e.dynCall_j=e.asm.pb).apply(null,arguments)},xt=e.dynCall_iiiiij=function(){return(xt=e.dynCall_iiiiij=e.asm.qb).apply(null,arguments)},Rt=e.dynCall_jii=function(){return(Rt=e.dynCall_jii=e.asm.rb).apply(null,arguments)},jt=e.dynCall_viiiiij=function(){return(jt=e.dynCall_viiiiij=e.asm.sb).apply(null,arguments)},kt=e.dynCall_vjji=function(){return(kt=e.dynCall_vjji=e.asm.tb).apply(null,arguments)},Dt=e.dynCall_viiijjjii=function(){return(Dt=e.dynCall_viiijjjii=e.asm.ub).apply(null,arguments)},Pt=e.dynCall_iij=function(){return(Pt=e.dynCall_iij=e.asm.vb).apply(null,arguments)},Ut=e.dynCall_ji=function(){return(Ut=e.dynCall_ji=e.asm.wb).apply(null,arguments)},Ft=e.dynCall_iiiiiij=function(){return(Ft=e.dynCall_iiiiiij=e.asm.xb).apply(null,arguments)},It=e.dynCall_iiij=function(){return(It=e.dynCall_iiij=e.asm.yb).apply(null,arguments)};function Wt(){function t(){if(!mt&&(mt=!0,e.calledRun=!0,!C)){if(Z(I),r(e),e.onRuntimeInitialized&&e.onRuntimeInitialized(),e.postRun)for(\\\"function\\\"==typeof e.postRun&&(e.postRun=[e.postRun]);e.postRun.length;){var t=e.postRun.shift();H.unshift(t)}Z(H)}}if(!(0<B)){if(e.preRun)for(\\\"function\\\"==typeof e.preRun&&(e.preRun=[e.preRun]);e.preRun.length;)z();Z(F),0<B||(e.setStatus?(e.setStatus(\\\"Running...\\\"),setTimeout((function(){setTimeout((function(){e.setStatus(\\\"\\\")}),1),t()}),1)):t())}}if(e.UTF8ToString=j,e.stringToUTF8=function(t,e,n){return k(t,T,e,n)},e.lengthBytesUTF8=D,e.stackSave=At,e.stackRestore=St,e.stackAlloc=Tt,N=function t(){mt||Wt(),mt||(N=t)},e.preInit)for(\\\"function\\\"==typeof e.preInit&&(e.preInit=[e.preInit]);0<e.preInit.length;)e.preInit.pop()();return Wt(),t.ready});t.exports=r},967:(t,e)=>{\\\"use strict\\\";Object.defineProperty(e,\\\"__esModule\\\",{value:!0}),e.iterateExtraOptions=void 0,e.iterateExtraOptions=(t,n,r,a)=>{if(\\\"object\\\"==typeof t&&null!==t){if(r.has(t))throw new Error(\\\"Circular reference in options\\\");r.add(t)}Object.entries(t).forEach((([t,i])=>{const o=n?n+t:t;if(\\\"object\\\"==typeof i)(0,e.iterateExtraOptions)(i,o+\\\".\\\",r,a);else if(\\\"string\\\"==typeof i||\\\"number\\\"==typeof i)a(o,i.toString());else{if(\\\"boolean\\\"!=typeof i)throw new Error(\\\"Can't handle extra config type: \\\"+typeof i);a(o,i?\\\"1\\\":\\\"0\\\")}}))}},586:(t,e,n)=>{\\\"use strict\\\";Object.defineProperty(e,\\\"__esModule\\\",{value:!0}),e.setRunOptions=void 0;const r=n(967),a=n(983),i=n(361);e.setRunOptions=t=>{const e=(0,i.getInstance)();let n=0;const o=[],u=t||{};try{if(void 0===(null==t?void 0:t.logSeverityLevel))u.logSeverityLevel=2;else if(\\\"number\\\"!=typeof t.logSeverityLevel||!Number.isInteger(t.logSeverityLevel)||t.logSeverityLevel<0||t.logSeverityLevel>4)throw new Error(`log serverity level is not valid: ${t.logSeverityLevel}`);if(void 0===(null==t?void 0:t.logVerbosityLevel))u.logVerbosityLevel=0;else if(\\\"number\\\"!=typeof t.logVerbosityLevel||!Number.isInteger(t.logVerbosityLevel))throw new Error(`log verbosity level is not valid: ${t.logVerbosityLevel}`);void 0===(null==t?void 0:t.terminate)&&(u.terminate=!1);let i=0;if(void 0!==(null==t?void 0:t.tag)&&(i=(0,a.allocWasmString)(t.tag,o)),n=e._OrtCreateRunOptions(u.logSeverityLevel,u.logVerbosityLevel,!!u.terminate,i),0===n)throw new Error(\\\"Can't create run options\\\");return void 0!==(null==t?void 0:t.extra)&&(0,r.iterateExtraOptions)(t.extra,\\\"\\\",new WeakSet,((t,r)=>{const i=(0,a.allocWasmString)(t,o),u=(0,a.allocWasmString)(r,o);if(0!==e._OrtAddRunConfigEntry(n,i,u))throw new Error(`Can't set a run config entry: ${t} - ${r}`)})),[n,o]}catch(t){throw 0!==n&&e._OrtReleaseRunOptions(n),o.forEach(e._free),t}}},919:(t,e,n)=>{\\\"use strict\\\";Object.defineProperty(e,\\\"__esModule\\\",{value:!0}),e.setSessionOptions=void 0;const r=n(967),a=n(983),i=n(361);e.setSessionOptions=t=>{const e=(0,i.getInstance)();let n=0;const o=[],u=t||{};(t=>{t.extra||(t.extra={}),t.extra.session||(t.extra.session={});const e=t.extra.session;e.use_ort_model_bytes_directly||(e.use_ort_model_bytes_directly=\\\"1\\\")})(u);try{void 0===(null==t?void 0:t.graphOptimizationLevel)&&(u.graphOptimizationLevel=\\\"all\\\");const c=(t=>{switch(t){case\\\"disabled\\\":return 0;case\\\"basic\\\":return 1;case\\\"extended\\\":return 2;case\\\"all\\\":return 99;default:throw new Error(`unsupported graph optimization level: ${t}`)}})(u.graphOptimizationLevel);void 0===(null==t?void 0:t.enableCpuMemArena)&&(u.enableCpuMemArena=!0),void 0===(null==t?void 0:t.enableMemPattern)&&(u.enableMemPattern=!0),void 0===(null==t?void 0:t.executionMode)&&(u.executionMode=\\\"sequential\\\");const s=(t=>{switch(t){case\\\"sequential\\\":return 0;case\\\"parallel\\\":return 1;default:throw new Error(`unsupported execution mode: ${t}`)}})(u.executionMode);let l=0;if(void 0!==(null==t?void 0:t.logId)&&(l=(0,a.allocWasmString)(t.logId,o)),void 0===(null==t?void 0:t.logSeverityLevel))u.logSeverityLevel=2;else if(\\\"number\\\"!=typeof t.logSeverityLevel||!Number.isInteger(t.logSeverityLevel)||t.logSeverityLevel<0||t.logSeverityLevel>4)throw new Error(`log serverity level is not valid: ${t.logSeverityLevel}`);if(void 0===(null==t?void 0:t.logVerbosityLevel))u.logVerbosityLevel=0;else if(\\\"number\\\"!=typeof t.logVerbosityLevel||!Number.isInteger(t.logVerbosityLevel))throw new Error(`log verbosity level is not valid: ${t.logVerbosityLevel}`);if(void 0===(null==t?void 0:t.enableProfiling)&&(u.enableProfiling=!1),n=e._OrtCreateSessionOptions(c,!!u.enableCpuMemArena,!!u.enableMemPattern,s,!!u.enableProfiling,0,l,u.logSeverityLevel,u.logVerbosityLevel),0===n)throw new Error(\\\"Can't create session options\\\");return(null==t?void 0:t.executionProviders)&&((t,e,n)=>{for(const r of e){let e=\\\"string\\\"==typeof r?r:r.name;switch(e){case\\\"xnnpack\\\":e=\\\"XNNPACK\\\";break;case\\\"wasm\\\":case\\\"cpu\\\":continue;default:throw new Error(`not supported EP: ${e}`)}const o=(0,a.allocWasmString)(e,n);if(0!==(0,i.getInstance)()._OrtAppendExecutionProvider(t,o))throw new Error(`Can't append execution provider: ${e}`)}})(n,t.executionProviders,o),void 0!==(null==t?void 0:t.extra)&&(0,r.iterateExtraOptions)(t.extra,\\\"\\\",new WeakSet,((t,r)=>{const i=(0,a.allocWasmString)(t,o),u=(0,a.allocWasmString)(r,o);if(0!==e._OrtAddSessionConfigEntry(n,i,u))throw new Error(`Can't set a session config entry: ${t} - ${r}`)})),[n,o]}catch(t){throw 0!==n&&e._OrtReleaseSessionOptions(n),o.forEach(e._free),t}}},983:(t,e,n)=>{\\\"use strict\\\";Object.defineProperty(e,\\\"__esModule\\\",{value:!0}),e.allocWasmString=void 0;const r=n(361);e.allocWasmString=(t,e)=>{const n=(0,r.getInstance)(),a=n.lengthBytesUTF8(t)+1,i=n._malloc(a);return n.stringToUTF8(t,i,a),e.push(i),i}},349:(t,e,n)=>{\\\"use strict\\\";Object.defineProperty(e,\\\"__esModule\\\",{value:!0}),e.extractTransferableBuffers=e.endProfiling=e.run=e.releaseSession=e.createSession=e.createSessionFinalize=e.createSessionAllocate=e.initOrt=void 0;const r=n(586),a=n(919),i=n(983),o=n(361);e.initOrt=(t,e)=>{const n=(0,o.getInstance)()._OrtInit(t,e);if(0!==n)throw new Error(`Can't initialize onnxruntime. error code = ${n}`)};const u=new Map;e.createSessionAllocate=t=>{const e=(0,o.getInstance)(),n=e._malloc(t.byteLength);return e.HEAPU8.set(t,n),[n,t.byteLength]},e.createSessionFinalize=(t,e)=>{const n=(0,o.getInstance)();let r=0,i=0,c=[];try{if([i,c]=(0,a.setSessionOptions)(e),r=n._OrtCreateSession(t[0],t[1],i),0===r)throw new Error(\\\"Can't create a session\\\")}finally{n._free(t[0]),n._OrtReleaseSessionOptions(i),c.forEach(n._free)}const s=n._OrtGetInputCount(r),l=n._OrtGetOutputCount(r),f=[],p=[],h=[],d=[];for(let t=0;t<s;t++){const e=n._OrtGetInputName(r,t);if(0===e)throw new Error(\\\"Can't get an input name\\\");p.push(e),f.push(n.UTF8ToString(e))}for(let t=0;t<l;t++){const e=n._OrtGetOutputName(r,t);if(0===e)throw new Error(\\\"Can't get an output name\\\");d.push(e),h.push(n.UTF8ToString(e))}return u.set(r,[r,p,d]),[r,f,h]},e.createSession=(t,n)=>{const r=(0,e.createSessionAllocate)(t);return(0,e.createSessionFinalize)(r,n)},e.releaseSession=t=>{const e=(0,o.getInstance)(),n=u.get(t);if(!n)throw new Error(\\\"invalid session id\\\");const r=n[0],a=n[1],i=n[2];a.forEach(e._OrtFree),i.forEach(e._OrtFree),e._OrtReleaseSession(r),u.delete(t)};const c=t=>{switch(t){case\\\"int8\\\":return 3;case\\\"uint8\\\":return 2;case\\\"bool\\\":return 9;case\\\"int16\\\":return 5;case\\\"uint16\\\":return 4;case\\\"int32\\\":return 6;case\\\"uint32\\\":return 12;case\\\"float32\\\":return 1;case\\\"float64\\\":return 11;case\\\"string\\\":return 8;case\\\"int64\\\":return 7;case\\\"uint64\\\":return 13;default:throw new Error(`unsupported data type: ${t}`)}},s=t=>{switch(t){case 3:return\\\"int8\\\";case 2:return\\\"uint8\\\";case 9:return\\\"bool\\\";case 5:return\\\"int16\\\";case 4:return\\\"uint16\\\";case 6:return\\\"int32\\\";case 12:return\\\"uint32\\\";case 1:return\\\"float32\\\";case 11:return\\\"float64\\\";case 8:return\\\"string\\\";case 7:return\\\"int64\\\";case 13:return\\\"uint64\\\";default:throw new Error(`unsupported data type: ${t}`)}},l=t=>{switch(t){case\\\"float32\\\":return Float32Array;case\\\"uint8\\\":case\\\"bool\\\":return Uint8Array;case\\\"int8\\\":return Int8Array;case\\\"uint16\\\":return Uint16Array;case\\\"int16\\\":return Int16Array;case\\\"int32\\\":return Int32Array;case\\\"float64\\\":return Float64Array;case\\\"uint32\\\":return Uint32Array;case\\\"int64\\\":return BigInt64Array;case\\\"uint64\\\":return BigUint64Array;default:throw new Error(`unsupported type: ${t}`)}};e.run=(t,e,n,a,f)=>{const p=(0,o.getInstance)(),h=u.get(t);if(!h)throw new Error(\\\"invalid session id\\\");const d=h[0],y=h[1],b=h[2],m=e.length,g=a.length;let v=0,w=[];const _=[],O=[];try{[v,w]=(0,r.setRunOptions)(f);for(let t=0;t<m;t++){const e=n[t][0],r=n[t][1],a=n[t][2];let o,u;if(Array.isArray(a)){u=4*a.length,o=p._malloc(u),O.push(o);let t=o/4;for(let e=0;e<a.length;e++){if(\\\"string\\\"!=typeof a[e])throw new TypeError(`tensor data at index ${e} is not a string`);p.HEAPU32[t++]=(0,i.allocWasmString)(a[e],O)}}else u=a.byteLength,o=p._malloc(u),O.push(o),p.HEAPU8.set(new Uint8Array(a.buffer,a.byteOffset,u),o);const s=p.stackSave(),l=p.stackAlloc(4*r.length);try{let t=l/4;r.forEach((e=>p.HEAP32[t++]=e));const n=p._OrtCreateTensor(c(e),o,u,l,r.length);if(0===n)throw new Error(\\\"Can't create a tensor\\\");_.push(n)}finally{p.stackRestore(s)}}const t=p.stackSave(),o=p.stackAlloc(4*m),u=p.stackAlloc(4*m),h=p.stackAlloc(4*g),A=p.stackAlloc(4*g);try{let n=o/4,r=u/4,i=h/4,c=A/4;for(let t=0;t<m;t++)p.HEAPU32[n++]=_[t],p.HEAPU32[r++]=y[e[t]];for(let t=0;t<g;t++)p.HEAPU32[i++]=0,p.HEAPU32[c++]=b[a[t]];let f=p._OrtRun(d,u,o,m,A,g,h,v);const w=[];if(0===f)for(let t=0;t<g;t++){const e=p.HEAPU32[h/4+t],n=p.stackSave(),r=p.stackAlloc(16);let a,i=0;try{if(f=p._OrtGetTensorData(e,r,r+4,r+8,r+12),0!==f)throw new Error(`Can't access output tensor data. error code = ${f}`);let t=r/4;const o=p.HEAPU32[t++];i=p.HEAPU32[t++];const u=p.HEAPU32[t++],c=p.HEAPU32[t++],h=[];for(let t=0;t<c;t++)h.push(p.HEAPU32[u/4+t]);p._OrtFree(u);const d=0===h.length?1:h.reduce(((t,e)=>t*e));if(a=s(o),\\\"string\\\"===a){const t=[];let e=i/4;for(let n=0;n<d;n++){const r=p.HEAPU32[e++],a=n===d-1?void 0:p.HEAPU32[e]-r;t.push(p.UTF8ToString(r,a))}w.push([a,h,t])}else{const t=new(l(a))(d);new Uint8Array(t.buffer,t.byteOffset,t.byteLength).set(p.HEAPU8.subarray(i,i+t.byteLength)),w.push([a,h,t])}}finally{p.stackRestore(n),\\\"string\\\"===a&&i&&p._free(i),p._OrtReleaseTensor(e)}}if(0===f)return w;throw new Error(`failed to call OrtRun(). error code = ${f}.`)}finally{p.stackRestore(t)}}finally{_.forEach(p._OrtReleaseTensor),O.forEach(p._free),p._OrtReleaseRunOptions(v),w.forEach(p._free)}},e.endProfiling=t=>{const e=(0,o.getInstance)(),n=u.get(t);if(!n)throw new Error(\\\"invalid session id\\\");const r=n[0],a=e._OrtEndProfiling(r);if(0===a)throw new Error(\\\"Can't get an profile file name\\\");e._OrtFree(a)},e.extractTransferableBuffers=t=>{const e=[];for(const n of t){const t=n[2];!Array.isArray(t)&&t.buffer&&e.push(t.buffer)}return e}},361:function(t,e,n){\\\"use strict\\\";var r=this&&this.__createBinding||(Object.create?function(t,e,n,r){void 0===r&&(r=n);var a=Object.getOwnPropertyDescriptor(e,n);a&&!(\\\"get\\\"in a?!e.__esModule:a.writable||a.configurable)||(a={enumerable:!0,get:function(){return e[n]}}),Object.defineProperty(t,r,a)}:function(t,e,n,r){void 0===r&&(r=n),t[r]=e[n]}),a=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,\\\"default\\\",{enumerable:!0,value:e})}:function(t,e){t.default=e}),i=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var n in t)\\\"default\\\"!==n&&Object.prototype.hasOwnProperty.call(t,n)&&r(e,t,n);return a(e,t),e},o=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,\\\"__esModule\\\",{value:!0}),e.dispose=e.getInstance=e.initializeWebAssembly=void 0;const u=i(n(449)),c=o(n(932)),s=n(474);let l,f=!1,p=!1,h=!1;const d=(t,e)=>e?t?\\\"ort-wasm-simd-threaded.wasm\\\":\\\"ort-wasm-threaded.wasm\\\":t?\\\"ort-wasm-simd.wasm\\\":\\\"ort-wasm.wasm\\\";e.initializeWebAssembly=async t=>{if(f)return Promise.resolve();if(p)throw new Error(\\\"multiple calls to 'initializeWebAssembly()' detected.\\\");if(h)throw new Error(\\\"previous call to 'initializeWebAssembly()' failed.\\\");p=!0;const e=t.initTimeout,r=t.numThreads,a=t.simd,i=r>1&&(()=>{try{return\\\"undefined\\\"!=typeof SharedArrayBuffer&&(\\\"undefined\\\"!=typeof MessageChannel&&(new MessageChannel).port1.postMessage(new SharedArrayBuffer(1)),WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,2,1,0,5,4,1,3,1,1,10,11,1,9,0,65,0,254,16,2,0,26,11])))}catch(t){return!1}})(),o=a&&(()=>{try{return WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,2,1,0,10,30,1,28,0,65,0,253,15,253,12,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,253,186,1,26,11]))}catch(t){return!1}})(),y=\\\"string\\\"==typeof t.wasmPaths?t.wasmPaths:void 0,b=d(!1,i),m=d(o,i),g=\\\"object\\\"==typeof t.wasmPaths?t.wasmPaths[m]:void 0;let v=!1;const w=[];if(e>0&&w.push(new Promise((t=>{setTimeout((()=>{v=!0,t()}),e)}))),w.push(new Promise(((t,e)=>{const r=i?s:c.default,a={locateFile:(t,e)=>i&&t.endsWith(\\\".worker.js\\\")&&\\\"undefined\\\"!=typeof Blob?URL.createObjectURL(new Blob([n(154)],{type:\\\"text/javascript\\\"})):t===b?null!=g?g:(null!=y?y:e)+m:e+t};if(i)if(\\\"undefined\\\"==typeof Blob)a.mainScriptUrlOrBlob=u.join(\\\"/\\\",\\\"ort-wasm-threaded.js\\\");else{const t=`var ortWasmThreaded=(function(){var _scriptDir;return ${r.toString()}})();`;a.mainScriptUrlOrBlob=new Blob([t],{type:\\\"text/javascript\\\"})}r(a).then((e=>{p=!1,f=!0,l=e,t()}),(t=>{p=!1,h=!0,e(t)}))}))),await Promise.race(w),v)throw new Error(`WebAssembly backend initializing failed due to timeout: ${e}ms`)},e.getInstance=()=>{if(f&&l)return l;throw new Error(\\\"WebAssembly is not initialized yet.\\\")},e.dispose=()=>{var t;!f||p||h||(p=!0,null===(t=l.PThread)||void 0===t||t.terminateAllThreads(),l=void 0,p=!1,f=!1,h=!0)}},154:t=>{\\\"use strict\\\";t.exports='\\\"use strict\\\";var e={},t=\\\"object\\\"==typeof process&&\\\"object\\\"==typeof process.versions&&\\\"string\\\"==typeof process.versions.node;if(t){var r=require(\\\"worker_threads\\\"),a=r.parentPort;a.on(\\\"message\\\",(e=>onmessage({data:e})));var o=require(\\\"fs\\\");Object.assign(global,{self:global,require:require,Module:e,location:{href:__filename},Worker:r.Worker,importScripts:function(e){(0,eval)(o.readFileSync(e,\\\"utf8\\\"))},postMessage:function(e){a.postMessage(e)},performance:global.performance||{now:function(){return Date.now()}}})}var s=!1,n=[],i=function(){var e=Array.prototype.slice.call(arguments).join(\\\" \\\");t?o.writeSync(2,e+\\\"\\\\\\\\n\\\"):console.error(e)};self.alert=function(){var t=Array.prototype.slice.call(arguments).join(\\\" \\\");postMessage({cmd:\\\"alert\\\",text:t,threadId:e._pthread_self()})},e.instantiateWasm=(t,r)=>{var a=new WebAssembly.Instance(e.wasmModule,t);return r(a),e.wasmModule=null,a.exports},self.onunhandledrejection=e=>{throw e.reason??e},self.onmessage=t=>{try{if(\\\"load\\\"===t.data.cmd){if(e.wasmModule=t.data.wasmModule,e.wasmMemory=t.data.wasmMemory,e.buffer=e.wasmMemory.buffer,e.ENVIRONMENT_IS_PTHREAD=!0,\\\"string\\\"==typeof t.data.urlOrBlob)importScripts(t.data.urlOrBlob);else{var r=URL.createObjectURL(t.data.urlOrBlob);importScripts(r),URL.revokeObjectURL(r)}ortWasmThreaded(e).then((function(t){e=t}))}else if(\\\"run\\\"===t.data.cmd){e.__performance_now_clock_drift=performance.now()-t.data.time,e.__emscripten_thread_init(t.data.pthread_ptr,0,0,1),e.establishStackSpace(),e.PThread.receiveObjectTransfer(t.data),e.PThread.threadInitTLS(),s||(n.forEach((t=>{e.executeNotifiedProxyingQueue(t)})),n=[],s=!0);try{e.invokeEntryPoint(t.data.start_routine,t.data.arg)}catch(t){if(\\\"unwind\\\"!=t){if(!(t instanceof e.ExitStatus))throw t;e.keepRuntimeAlive()||e.__emscripten_thread_exit(t.status)}}}else\\\"cancel\\\"===t.data.cmd?e._pthread_self()&&e.__emscripten_thread_exit(-1):\\\"setimmediate\\\"===t.data.target||(\\\"processProxyingQueue\\\"===t.data.cmd?s?e.executeNotifiedProxyingQueue(t.data.queue):n.push(t.data.queue):(i(\\\"worker.js received unknown command \\\"+t.data.cmd),i(t.data)))}catch(t){throw i(\\\"worker.js onmessage() captured an uncaught exception: \\\"+t),t&&t.stack&&i(t.stack),e.__emscripten_thread_crashed&&e.__emscripten_thread_crashed(),t}};\\\\n'},384:()=>{},993:()=>{},908:()=>{},953:()=>{},925:()=>{},449:()=>{}},e={};function n(r){var a=e[r];if(void 0!==a)return a.exports;var i=e[r]={exports:{}};return t[r].call(i.exports,i,i.exports,n),i.exports}n.g=function(){if(\\\"object\\\"==typeof globalThis)return globalThis;try{return this||new Function(\\\"return this\\\")()}catch(t){if(\\\"object\\\"==typeof window)return window}}(),(()=>{\\\"use strict\\\";const t=n(349),e=n(361);self.onmessage=n=>{switch(n.data.type){case\\\"init-wasm\\\":(0,e.initializeWebAssembly)(n.data.in).then((()=>postMessage({type:\\\"init-wasm\\\"})),(t=>postMessage({type:\\\"init-wasm\\\",err:t})));break;case\\\"init-ort\\\":try{const{numThreads:e,loggingLevel:r}=n.data.in;(0,t.initOrt)(e,r),postMessage({type:\\\"init-ort\\\"})}catch(t){postMessage({type:\\\"init-ort\\\",err:t})}break;case\\\"create_allocate\\\":try{const{model:e}=n.data.in,r=(0,t.createSessionAllocate)(e);postMessage({type:\\\"create_allocate\\\",out:r})}catch(t){postMessage({type:\\\"create_allocate\\\",err:t})}break;case\\\"create_finalize\\\":try{const{modeldata:e,options:r}=n.data.in,a=(0,t.createSessionFinalize)(e,r);postMessage({type:\\\"create_finalize\\\",out:a})}catch(t){postMessage({type:\\\"create_finalize\\\",err:t})}break;case\\\"create\\\":try{const{model:e,options:r}=n.data.in,a=(0,t.createSession)(e,r);postMessage({type:\\\"create\\\",out:a})}catch(t){postMessage({type:\\\"create\\\",err:t})}break;case\\\"release\\\":try{const e=n.data.in;(0,t.releaseSession)(e),postMessage({type:\\\"release\\\"})}catch(t){postMessage({type:\\\"release\\\",err:t})}break;case\\\"run\\\":try{const{sessionId:e,inputIndices:r,inputs:a,outputIndices:i,options:o}=n.data.in,u=(0,t.run)(e,r,a,i,o);postMessage({type:\\\"run\\\",out:u},(0,t.extractTransferableBuffers)(u))}catch(t){postMessage({type:\\\"run\\\",err:t})}break;case\\\"end-profiling\\\":try{const e=n.data.in;(0,t.endProfiling)(e),postMessage({type:\\\"end-profiling\\\"})}catch(t){postMessage({type:\\\"end-profiling\\\",err:t})}}}})()})();\\n\", \"Worker\", undefined, undefined);\n}\n", "\"use strict\";\n\n/* eslint-env browser */\n\n/* eslint-disable no-undef, no-use-before-define, new-cap */\nmodule.exports = function (content, workerConstructor, workerOptions, url) {\n  var globalScope = self || window;\n\n  try {\n    try {\n      var blob;\n\n      try {\n        // New API\n        blob = new globalScope.Blob([content]);\n      } catch (e) {\n        // BlobBuilder = Deprecated, but widely implemented\n        var BlobBuilder = globalScope.BlobBuilder || globalScope.WebKitBlobBuilder || globalScope.MozBlobBuilder || globalScope.MSBlobBuilder;\n        blob = new BlobBuilder();\n        blob.append(content);\n        blob = blob.getBlob();\n      }\n\n      var URL = globalScope.URL || globalScope.webkitURL;\n      var objectURL = URL.createObjectURL(blob);\n      var worker = new globalScope[workerConstructor](objectURL, workerOptions);\n      URL.revokeObjectURL(objectURL);\n      return worker;\n    } catch (e) {\n      return new globalScope[workerConstructor](\"data:application/javascript,\".concat(encodeURIComponent(content)), workerOptions);\n    }\n  } catch (e) {\n    if (!url) {\n      throw Error(\"Inline worker is not supported\");\n    }\n\n    return new globalScope[workerConstructor](url, workerOptions);\n  }\n};", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// startup\n// Load entry module and return exports\n// This entry module is referenced by other modules so it can't be inlined\nvar __webpack_exports__ = __webpack_require__(18);\n"], "names": ["root", "factory", "exports", "module", "define", "amd", "self", "backends", "backendsSortedByPriority", "registerBackend", "name", "backend", "priority", "init", "createSessionHandler", "TypeError", "currentBackend", "undefined", "Error", "i", "indexOf", "splice", "length", "push", "env", "constructor", "this", "wasm", "webgl", "logLevelInternal", "logLevel", "value", "isBigInt64ArrayAvailable", "BigInt64Array", "from", "isBigUint64ArrayAvailable", "BigUint64Array", "NUMERIC_TENSOR_TYPE_TO_TYPEDARRAY_MAP", "Map", "Float32Array", "Uint8Array", "Int8Array", "Uint16Array", "Int16Array", "Int32Array", "Float64Array", "Uint32Array", "NUMERIC_TENSOR_TYPEDARRAY_TO_TYPE_MAP", "set", "Tensor", "arg0", "arg1", "arg2", "type", "data", "dims", "Array", "isArray", "typedArrayConstructor", "get", "firstElementType", "mappedType", "size", "dim", "Number", "isSafeInteger", "RangeError", "calculateSize", "static", "buffer", "options", "height", "width", "norm", "norm<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "mean", "bias", "inputformat", "bitmapFormat", "outputformat", "tensorFormat", "offset", "float32Data", "step", "rImagePointer", "gImagePointer", "bImagePointer", "aImagePointer", "rTensorPointer", "gTensorPointer", "b<PERSON>ensor<PERSON>oint<PERSON>", "aTensorPointer", "image", "isHTMLImageEle", "HTMLImageElement", "isImageDataEle", "ImageData", "isImageBitmap", "ImageBitmap", "isURL", "String", "tensorConfig", "canvas", "document", "createElement", "pixels2DContext", "getContext", "naturalHeight", "naturalWidth", "resizedHeight", "resizedWidth", "drawImage", "getImageData", "bufferToTensor", "Promise", "resolve", "reject", "context", "newImage", "Image", "crossOrigin", "src", "onload", "img", "format", "tempCanvas", "putImageData", "toImageData", "_a", "_b", "channels", "createImageData", "reshape", "InferenceSession", "handler", "async", "feeds", "fetches", "isFetchesEmpty", "outputNames", "isFetches", "arg1Keys", "Object", "getOwnPropertyNames", "v", "inputNames", "results", "run", "returnValue", "key", "hasOwnProperty", "call", "arg3", "filePathOrUint8Array", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SharedArrayBuffer", "byteOffset", "byteLength", "backendHints", "executionProviders", "map", "backendNames", "errors", "backendName", "backendInfo", "initialized", "aborted", "isInitializing", "initPromise", "e", "err", "join", "resolveBackend", "startProfiling", "endProfiling", "_scriptDir", "t", "currentScript", "n", "E", "D", "z", "j", "F", "r", "U", "Y", "a", "I", "u", "o", "c", "ready", "f", "s", "l", "p", "h", "m", "d", "assign", "y", "b", "g", "window", "_", "importScripts", "process", "versions", "node", "w", "ENVIRONMENT_IS_PTHREAD", "T", "O", "locateFile", "__dirname", "normalize", "readFileSync", "readFile", "argv", "replace", "slice", "on", "ot", "J", "exitCode", "x", "exit", "inspect", "console", "error", "Worker", "location", "href", "substr", "lastIndexOf", "XMLHttpRequest", "open", "send", "responseText", "responseType", "response", "status", "onerror", "performance", "S", "log", "bind", "A", "warn", "writeSync", "M", "C", "print", "printErr", "thisProgram", "quit", "wasmBinary", "R", "noExitRuntime", "WebAssembly", "rt", "k", "W", "P", "TextDecoder", "H", "decode", "subarray", "fromCharCode", "q", "B", "charCodeAt", "G", "HEAP8", "HEAP16", "HEAP32", "HEAPU8", "HEAPU16", "HEAPU32", "HEAPF32", "HEAPF64", "N", "INITIAL_MEMORY", "was<PERSON><PERSON><PERSON><PERSON>", "Memory", "initial", "maximum", "shared", "V", "L", "X", "Z", "$", "Q", "preRun", "shift", "unshift", "K", "tt", "nt", "et", "postMessage", "cmd", "arg", "onAbort", "RuntimeError", "it", "startsWith", "at", "ut", "message", "ct", "pt", "Vb", "mc", "ft", "Cc", "ac", "Ub", "start_routine", "Ic", "zc", "pthread_ptr", "$b", "time", "now", "Nc", "loaded", "st", "Vt", "oc", "onExit", "lt", "dt", "bn", "ht", "yn", "en", "rn", "Yb", "qc", "fc", "Ec", "Pc", "receiveObjectTransfer", "Gc", "threadInitTLS", "pc", "setExitStatus", "nc", "values", "terminate", "Tn", "for<PERSON>ach", "Fc", "onmessage", "Bc", "targetThread", "hn", "Qc", "transferList", "Ht", "queue", "thread", "threadId", "text", "alert", "target", "filename", "lineno", "urlOrBlob", "mainScriptUrlOrBlob", "wasmModule", "yc", "pop", "mt", "Mn", "Cn", "PThread", "establishStackSpace", "An", "yt", "bt", "invokeEntryPoint", "On", "gt", "_t", "vt", "wt", "Tt", "<PERSON>t", "Zb", "Sb", "xc", "bc", "wc", "Dc", "rc", "hc", "uc", "ic", "kc", "cc", "sc", "Atomics", "add", "Hc", "sub", "tc", "vc", "En", "St", "dn", "At", "Mt", "Oc", "Ct", "xt", "Rt", "Et", "kt", "Dt", "jt", "Ft", "Ut", "Yt", "It", "Wt", "Pt", "store", "wn", "compareExchange", "qt", "Bt", "Gt", "zt", "mn", "Nt", "toTimeString", "match", "Date", "getFullYear", "getTimezoneOffset", "Math", "max", "arguments", "xn", "vn", "executeNotifiedProxyingQueue", "hrtime", "__performance_now_clock_drift", "Lt", "Xt", "Zt", "$t", "USER", "LOGNAME", "PATH", "PWD", "HOME", "LANG", "navigator", "languages", "Jt", "Qt", "Kt", "tn", "nn", "an", "un", "cn", "fn", "sn", "toString", "getMonth", "getDate", "getDay", "Wb", "Xb", "getTime", "setDate", "setMonth", "setFullYear", "Lc", "Kc", "dc", "jc", "ec", "Tb", "Rc", "Jc", "Mc", "RegExp", "split", "substring", "floor", "abs", "includes", "ln", "pn", "ma", "Sn", "prototype", "Rn", "pa", "Fa", "gn", "xa", "Ma", "ua", "wa", "oa", "<PERSON>", "Ca", "<PERSON>a", "va", "sa", "La", "ta", "Ta", "Ua", "ya", "Oa", "za", "setTimeout", "Ea", "Pa", "getUTCSeconds", "getUTCMinutes", "getUTCHours", "getUTCDate", "getUTCMonth", "getUTCFullYear", "getUTCDay", "UTC", "Qa", "getSeconds", "getMinutes", "getHours", "min", "Ra", "setTime", "Aa", "Ba", "Sa", "Ac", "ra", "Ia", "copyWithin", "hardwareConcurrency", "Da", "apply", "qa", "grow", "Na", "Ga", "Ha", "ga", "na", "lc", "crypto", "getRandomValues", "randomBytes", "ia", "ja", "aa", "Pn", "Dn", "Hn", "ca", "In", "kn", "ba", "Wn", "jn", "ha", "ka", "fa", "Fn", "da", "Yn", "ea", "Un", "la", "asm", "sb", "ub", "Va", "monitorRunDependencies", "clearInterval", "instance", "fetch", "credentials", "then", "ok", "arrayBuffer", "catch", "instantiate", "instantiateWasm", "instantiateStreaming", "___wasm_call_ctors", "_OrtInit", "Wa", "_OrtCreateSessionOptions", "Xa", "_OrtAppendExecutionProvider", "Ya", "_OrtAddSessionConfigEntry", "<PERSON>a", "_OrtReleaseSessionOptions", "_OrtCreateSession", "$a", "_OrtReleaseSession", "ab", "_OrtGetInputCount", "bb", "_OrtGetOutputCount", "cb", "_OrtGetInputName", "db", "_OrtGetOutputName", "eb", "_OrtFree", "fb", "_OrtCreateTensor", "gb", "_OrtGetTensorData", "hb", "_OrtReleaseTensor", "ib", "_OrtCreateRunOptions", "jb", "_OrtAddRunConfigEntry", "kb", "_OrtReleaseRunOptions", "lb", "_OrtRun", "mb", "_OrtEndProfiling", "nb", "_pthread_self", "ob", "_malloc", "pb", "_free", "qb", "_fflush", "rb", "__emscripten_tls_init", "___funcs_on_exit", "tb", "__emscripten_thread_init", "vb", "__emscripten_thread_crashed", "wb", "_n", "_emscripten_run_in_main_runtime_thread_js", "xb", "__emscripten_proxy_execute_task_queue", "yb", "__emscripten_thread_free_data", "zb", "__emscripten_thread_exit", "Ab", "_setThrew", "Bb", "_emscripten_stack_set_limits", "Cb", "stackSave", "Db", "stackRestore", "Eb", "stackAlloc", "Fb", "___cxa_can_catch", "Gb", "___cxa_is_pointer_type", "Hb", "dynCall_j", "Ib", "dynCall_iiiiij", "Jb", "dynCall_jii", "Kb", "dynCall_viiiiij", "Lb", "dynCall_vjji", "Mb", "dynCall_viiijjjii", "Nb", "dynCall_iij", "Ob", "dyn<PERSON>all_ji", "Pb", "dynCall_iiiiiij", "Qb", "dynCall_iiij", "Rb", "qn", "calledRun", "onRuntimeInitialized", "postRun", "setStatus", "UTF8ToString", "stringToUTF8", "lengthBytesUTF8", "keepRuntimeAlive", "ExitStatus", "preInit", "ortWasm", "fs", "initTimeout", "simd", "proxy", "numThreads", "isInteger", "numCpuLogicalCores", "cpus", "ceil", "initWasm", "pathOr<PERSON><PERSON>er", "OnnxruntimeWebAssemblySessionHandler", "loadModel", "wasmBackend", "iterateExtraOptions", "prefix", "seen", "has", "entries", "isProxy", "proxyWorker", "initWasmCallbacks", "initOrtCallbacks", "initializing", "createSessionAllocateCallbacks", "createSessionFinalizeCallbacks", "createSessionCallbacks", "releaseSessionCallbacks", "runCallbacks", "endProfilingCallbacks", "ensureWorker", "onProxyWorkerMessage", "ev", "out", "scriptSrc", "<PERSON>m<PERSON><PERSON><PERSON>", "in", "initializeWebAssembly", "initOrt", "loggingLevel", "core", "createSessionAllocate", "model", "createSessionFinalize", "modeldata", "createSession", "releaseSession", "sessionId", "inputIndices", "inputs", "outputIndices", "extractTransferableBuffers", "setRunOptions", "getInstance", "runOptionsHandle", "allocs", "runOptions", "logSeverityLevel", "logVerbosityLevel", "tagDataOffset", "tag", "allocWasmString", "extra", "WeakSet", "keyDataOffset", "valueDataOffset", "ortInit", "path", "getLogLevel", "promisify", "modelData", "inputArray", "kvp", "tensor", "index", "outputs", "result", "setSessionOptions", "sessionOptionsHandle", "sessionOptions", "session", "use_ort_model_bytes_directly", "appendDefaultOptions", "graphOptimizationLevel", "getGraphOptimzationLevel", "enableCpuMemArena", "enableMemPattern", "executionMode", "getExecutionMode", "logIdDataOffset", "logId", "enableProfiling", "ep", "epName", "epNameDataOffset", "setExecutionProviders", "dataLength", "dataOffset", "errorCode", "activeSessions", "modelDataOffset", "<PERSON><PERSON><PERSON><PERSON>", "inputCount", "outputCount", "inputNamesUTF8Encoded", "outputNamesUTF8Encoded", "delete", "tensorDataTypeStringToEnum", "tensorDataTypeEnumToString", "typeProto", "numericTensorTypeToTypedArray", "runOptionsAllocs", "inputValues", "inputAllocs", "dataType", "dataByteLength", "dataIndex", "stack", "dimsOffset", "dimIndex", "beforeRunStack", "inputValuesOffset", "inputNamesOffset", "outputValuesOffset", "outputNamesOffset", "inputValuesIndex", "inputNamesIndex", "outputValuesIndex", "outputNamesIndex", "output", "beforeGetTensorDataStack", "tensorDataOffset", "tensorDataIndex", "dimsLength", "reduce", "stringData", "maxBytesToRead", "profileFileName", "tensors", "buffers", "ortWasmFactoryThreaded", "getWasmFileName", "useSimd", "useThreads", "flags", "timeout", "MessageChannel", "port1", "validate", "isMultiThreadSupported", "isSimdSupported", "wasmPrefixOverride", "wasmFileName", "wasmOverrideFileName", "wasmPathOverride", "isTimeout", "tasks", "config", "fileName", "scriptDirectory", "endsWith", "Blob", "URL", "createObjectURL", "scriptSourceCode", "what", "race", "dispose", "terminateAllThreads", "Worker_fn", "content", "workerConstructor", "workerOptions", "url", "globalScope", "blob", "BlobBuilder", "WebKitBlobBuilder", "MozBlobBuilder", "MSBlobBuilder", "append", "getBlob", "webkitURL", "objectURL", "worker", "revokeObjectURL", "concat", "encodeURIComponent", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "getter", "__esModule", "definition", "defineProperty", "enumerable", "globalThis", "Function", "obj", "prop", "Symbol", "toStringTag"], "sourceRoot": ""}