{"version": 3, "file": "backend.js", "sourceRoot": "", "sources": ["backend.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAElC,4DAAsD;AAyEtD,2CAA2C;AAC3C,MAAM,aAAa,GAAyB,IAAI,GAAG,EAAE,CAAC;AAEzC,QAAA,OAAO,GAA8B;IAChD,KAAK,EAAE,IAAI,4BAAY,EAAE;CAC1B,CAAC;AAEF;;;GAGG;AACI,KAAK,UAAU,cAAc,CAAC,IAA+B;IAClE,IAAI,CAAC,IAAI,EAAE;QACT,OAAO,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;KAClC;SAAM;QACL,MAAM,KAAK,GAAG,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAEvD,KAAK,MAAM,WAAW,IAAI,KAAK,EAAE;YAC/B,MAAM,KAAK,GAAG,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YAC7C,IAAI,KAAK,EAAE;gBACT,OAAO,KAAK,CAAC;aACd;YAED,MAAM,OAAO,GAAG,MAAM,cAAc,CAAC,WAAW,CAAC,CAAC;YAClD,IAAI,OAAO,EAAE;gBACX,OAAO,OAAO,CAAC;aAChB;SACF;KACF;IAED,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;AACjD,CAAC;AApBD,wCAoBC;AAED,KAAK,UAAU,cAAc,CAAC,WAAmB;IAC/C,MAAM,UAAU,GAAG,eAAO,CAAC;IAE3B,IAAI,OAAO,UAAU,CAAC,WAAW,CAAC,KAAK,WAAW,IAAI,SAAS,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,EAAE;QACxF,MAAM,OAAO,GAAG,UAAU,CAAC,WAAW,CAAC,CAAC;QACxC,IAAI,IAAI,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC;QAChC,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,MAAM,IAAI,IAAI,EAAE;YAC9C,IAAI,GAAG,MAAM,IAAI,CAAC;SACnB;QACD,IAAI,IAAI,EAAE;YACR,aAAa,CAAC,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;YACxC,OAAO,OAAO,CAAC;SAChB;KACF;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,SAAS,CAAC,GAAY;IAC7B,8DAA8D;IAC9D,MAAM,CAAC,GAAG,GAAU,CAAC;IAErB,2CAA2C;IAC3C,IACI,YAAY,IAAI,CAAC,IAAI,OAAO,CAAC,CAAC,UAAU,KAAK,UAAU,IAAyB,eAAe;QAC/F,sBAAsB,IAAI,CAAC,IAAI,OAAO,CAAC,CAAC,oBAAoB,KAAK,UAAU,IAAK,yBAAyB;QACzG,SAAS,IAAI,CAAC,IAAI,OAAO,CAAC,CAAC,OAAO,KAAK,UAAU,CAA+B,YAAY;MAC9F;QACA,OAAO,IAAI,CAAC;KACb;IAED,OAAO,KAAK,CAAC;AACf,CAAC"}