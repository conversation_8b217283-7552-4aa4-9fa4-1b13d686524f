import { Attribute<PERSON>ithCache<PERSON>ey } from '../../../attribute-with-cache-key';
import { OperatorImplementation, OperatorInitialization } from '../../../operators';
export interface TransposeAttributes extends AttributeWithCacheKey {
    readonly perm: number[];
}
export declare const transpose: OperatorImplementation<TransposeAttributes>;
export declare const parseTransposeAttributes: OperatorInitialization<TransposeAttributes>;
