{"version": 3, "file": "pad.js", "sourceRoot": "", "sources": ["pad.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAElC,gFAAqG;AAIrG,wCAAwC;AACxC,gDAA6C;AAE7C,oCAAkD;AAQlD,MAAM,kBAAkB,GAAG;IACzB,IAAI,EAAE,KAAK;IACX,UAAU,EAAE,CAAC,GAAG,CAAC;IACjB,UAAU,EAAE,CAAC,mBAAW,CAAC,QAAQ,CAAC;CACnC,CAAC;AAEK,MAAM,KAAK,GACd,CAAC,gBAAuC,EAAE,MAAgB,EAAE,UAAyB,EAAY,EAAE;IACjG,gBAAgB,CAAC,MAAM,CAAC,CAAC;IACzB,MAAM,MAAM,GAAG,gBAAgB,CAAC,GAAG,iCAE1B,kBAAkB,KACrB,SAAS,EAAE,UAAU,CAAC,QAAQ,EAC9B,GAAG,EAAE,GAAG,EAAE,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,KAE1E,MAAM,CAAC,CAAC;IACZ,OAAO,CAAC,MAAM,CAAC,CAAC;AAClB,CAAC,CAAC;AAXO,QAAA,KAAK,SAWZ;AAEC,MAAM,oBAAoB,GAA0C,CAAC,IAAgB,EAAiB,EAAE;IAC7G,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;IAC3D,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IACrD,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC7C,OAAO,IAAA,sDAA2B,EAAC,EAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAC,CAAC,CAAC;AAC1D,CAAC,CAAC;AALW,QAAA,oBAAoB,wBAK/B;AAEK,MAAM,MAAM,GACf,CAAC,gBAAuC,EAAE,MAAgB,EAAE,IAAY,EAAY,EAAE;IACpF,iBAAiB,CAAC,MAAM,CAAC,CAAC;IAC1B,MAAM,UAAU,GAAG,+BAA+B,CAAC,gBAAgB,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IACnF,OAAO,IAAA,aAAK,EAAC,gBAAgB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;AAC1D,CAAC,CAAC;AALO,QAAA,MAAM,UAKb;AAEC,MAAM,qBAAqB,GAAmC,CAAC,IAAgB,EAAU,EAAE,CAC9F,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;AADrC,QAAA,qBAAqB,yBACgB;AAElD,MAAM,+BAA+B,GACjC,CAAC,gBAAuC,EAAE,MAAgB,EAAE,IAAY,EAAiB,EAAE;IACzF,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QACzD,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE;QACrF,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;KAC3D;IAED,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;IAC/C,MAAM,KAAK,GAAG,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;IAElE,OAAO,IAAA,sDAA2B,EAAC,EAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC;AAC1D,CAAC,CAAC;AAEN,MAAM,oBAAoB,GACtB,CAAC,gBAAuC,EAAE,KAAa,EAAE,UAAyB,EAAe,EAAE;IACjG,MAAM,WAAW,GAAG,gBAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC;IAC5E,MAAM,IAAI,GAAG,WAAW,CAAC,MAAM,CAAC;IAChC,MAAM,WAAW,GAAG,cAAc,CAAC,gBAAgB,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;IACxE,MAAM,YAAY,GAAG;QACnB,WAAW;0BACO,IAAI;;QAEtB,CAAC;IACH,OAAO;QACL,IAAI,EAAE,KAAK;QACX,UAAU,EAAE,CAAC,GAAG,CAAC;QACjB,UAAU,EAAE,CAAC,mBAAW,CAAC,QAAQ,CAAC;QAClC,MAAM,EAAE,EAAC,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,WAAW,EAAE,mBAAW,CAAC,QAAQ,EAAC;QAChF,YAAY;KACb,CAAC;AACJ,CAAC,CAAC;AAEN,MAAM,gBAAgB,GAAG,CAAC,MAAgB,EAAQ,EAAE;IAClD,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QAClC,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;KACzC;IACD,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,EAAE;QAChE,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;KACxC;AACH,CAAC,CAAC;AAEF,MAAM,iBAAiB,GAAG,CAAC,MAAgB,EAAQ,EAAE;IACnD,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE;QAC3D,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;KAC/C;IACD,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,EAAE;QAC9B,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;KACxC;IACD,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,EAAE;QACrD,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;KACxC;AACH,CAAC,CAAC;AAEF,MAAM,cAAc,GAAG,CAAC,gBAAuC,EAAE,KAAa,EAAE,UAAyB,EAAU,EAAE;IACnH,MAAM,IAAI,GAAG,IAAA,qBAAO,EAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;IACzE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,gBAAgB,CAAC,8BAA8B,CAAC,KAAK,CAAC,IAAI,EAAE,mBAAW,CAAC,QAAQ,CAAC,CAAC;IAC1G,MAAM,OAAO,GAAG,gBAAS,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAErD,QAAQ,UAAU,CAAC,IAAI,EAAE;QACvB,KAAK,UAAU;YACb,OAAO,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC;QACrG,KAAK,SAAS;YACZ,OAAO,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC;QAClF,KAAK,MAAM;YACT,OAAO,UAAU,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC;QAC/E;YACE,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;KACnC;AACH,CAAC,CAAC;AAEF,MAAM,cAAc,GAChB,CAAC,IAAU,EAAE,KAAwB,EAAE,OAA0B,EAAE,KAAa,EAAE,MAAc,EAAE,IAAc,EAC/G,KAAa,EAAU,EAAE;IACxB,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC;IAC1B,IAAI,KAAK,GAAG,EAAE,CAAC;IACf,KAAK,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;QAClC,KAAK,IAAI;gBACD,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC;;mBAEZ,KAAK,CAAC,CAAC,CAAC;wBACH,OAAO,CAAC,CAAC,CAAC;SACzB,CAAC;KACH;IACD,OAAO;yBACY,IAAI;uCACU,KAAK;;;UAGlC,KAAK;+CACgC,KAAK,KAAK,MAAM;wCACvB,IAAI,CAAC,SAAS;;;OAG/C,CAAC;AACJ,CAAC,CAAC;AAEN,MAAM,aAAa,GACf,CAAC,IAAU,EAAE,KAAwB,EAAE,OAA0B,EAAE,KAAa,EAAE,MAAc,EAAE,IAAc,EACrG,EAAE;IACP,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC;IAE1B,IAAI,KAAK,GAAG,EAAE,CAAC;IACf,KAAK,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;QAClC,KAAK,IAAI;gBACL,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC;;;8BAGD,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;;oBAE5B,KAAK,CAAC,CAAC,CAAC;;wBAEJ,OAAO,CAAC,CAAC,CAAC;SACzB,CAAC;KACC;IACD,OAAO;yBACQ,IAAI;;;UAGnB,KAAK;+CACgC,KAAK,KAAK,MAAM;wCACvB,IAAI,CAAC,SAAS;;;OAG/C,CAAC;AACA,CAAC,CAAC;AAEV,MAAM,UAAU,GACZ,CAAC,IAAU,EAAE,KAAwB,EAAE,OAA0B,EAAE,KAAa,EAAE,MAAc,EAAE,IAAc,EACrG,EAAE;IACP,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC;IAE1B,IAAI,KAAK,GAAG,EAAE,CAAC;IACf,KAAK,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;QAClC,KAAK,IAAI;gBACL,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC;;mBAEZ,KAAK,CAAC,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;wBACxB,OAAO,CAAC,CAAC,CAAC;OAC3B,CAAC;KACG;IACD,OAAO;yBACQ,IAAI;;;UAGnB,KAAK;+CACgC,KAAK,KAAK,MAAM;wCACvB,IAAI,CAAC,SAAS;;;OAG/C,CAAC;AACA,CAAC,CAAC"}