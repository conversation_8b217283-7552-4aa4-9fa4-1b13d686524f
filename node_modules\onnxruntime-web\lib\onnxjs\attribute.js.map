{"version": 3, "file": "attribute.js", "sourceRoot": "", "sources": ["attribute.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAGlC,2CAAgC;AAEhC,8DAAuD;AACvD,qCAAgC;AAChC,iCAAkD;AAElD,IAAO,MAAM,GAAG,2BAAW,CAAC,YAAY,CAAC,GAAG,CAAC;AAqB7C,MAAa,SAAS;IACpB,YAAY,UAAoE;QAC9E,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC;QAC7B,IAAI,UAAU,KAAK,IAAI,IAAI,UAAU,KAAK,SAAS,EAAE;YACnD,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE;gBAC7B,IAAI,IAAI,YAAY,iBAAI,CAAC,cAAc,EAAE;oBACvC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBACtF;qBAAM,IAAI,IAAI,YAAY,MAAM,CAAC,SAAS,EAAE;oBAC3C,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAG,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBACzF;aACF;YACD,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,UAAU,CAAC,MAAM,EAAE;gBAC7C,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;aAC/C;SACF;IACH,CAAC;IAED,GAAG,CAAC,GAAW,EAAE,IAAwB,EAAE,KAAiB;QAC1D,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;IAC3C,CAAC;IACD,MAAM,CAAC,GAAW;QAChB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAC/B,CAAC;IACD,QAAQ,CAAC,GAAW,EAAE,YAA6C;QACjE,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;IAC9C,CAAC;IAED,MAAM,CAAC,GAAW,EAAE,YAA2C;QAC7D,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;IAC5C,CAAC;IAED,SAAS,CAAC,GAAW,EAAE,YAA8C;QACnE,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;IAC/C,CAAC;IAED,SAAS,CAAC,GAAW,EAAE,YAA8C;QACnE,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;IAC/C,CAAC;IAED,SAAS,CAAC,GAAW,EAAE,YAA8C;QACnE,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;IAC/C,CAAC;IAED,OAAO,CAAC,GAAW,EAAE,YAA4C;QAC/D,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;IAC7C,CAAC;IAED,UAAU,CAAC,GAAW,EAAE,YAA+C;QACrE,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;IAChD,CAAC;IAED,UAAU,CAAC,GAAW,EAAE,YAA+C;QACrE,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;IAChD,CAAC;IAEO,GAAG,CACP,GAAW,EAAE,IAAwB,EAAE,YAAgB;QACzD,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC/C,IAAI,YAAY,KAAK,SAAS,EAAE;YAC9B,IAAI,YAAY,KAAK,SAAS,EAAE;gBAC9B,OAAO,YAAY,CAAC;aACrB;YACD,MAAM,IAAI,KAAK,CAAC,iCAAiC,GAAG,EAAE,CAAC,CAAC;SACzD;QACD,IAAI,YAAY,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;YAC5B,MAAM,IAAI,KAAK,CAAC,2BAA2B,IAAI,YAAY,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;SAC/E;QACD,OAAO,YAAY,CAAC,CAAC,CAAM,CAAC;IAC9B,CAAC;IAEO,MAAM,CAAC,OAAO,CAAC,IAA2C;QAChE,MAAM,IAAI,GAAG,IAAI,YAAY,iBAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAE,IAAyB,CAAC,IAAI,EAAE,CAAC;QACnG,QAAQ,IAAI,EAAE;YACZ,KAAK,iBAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK;gBAC1C,OAAO,OAAO,CAAC;YACjB,KAAK,iBAAI,CAAC,cAAc,CAAC,aAAa,CAAC,GAAG;gBACxC,OAAO,KAAK,CAAC;YACf,KAAK,iBAAI,CAAC,cAAc,CAAC,aAAa,CAAC,MAAM;gBAC3C,OAAO,QAAQ,CAAC;YAClB,KAAK,iBAAI,CAAC,cAAc,CAAC,aAAa,CAAC,MAAM;gBAC3C,OAAO,QAAQ,CAAC;YAClB,KAAK,iBAAI,CAAC,cAAc,CAAC,aAAa,CAAC,MAAM;gBAC3C,OAAO,QAAQ,CAAC;YAClB,KAAK,iBAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI;gBACzC,OAAO,MAAM,CAAC;YAChB,KAAK,iBAAI,CAAC,cAAc,CAAC,aAAa,CAAC,OAAO;gBAC5C,OAAO,SAAS,CAAC;YACnB,KAAK,iBAAI,CAAC,cAAc,CAAC,aAAa,CAAC,OAAO;gBAC5C,OAAO,SAAS,CAAC;YACnB;gBACE,MAAM,IAAI,KAAK,CAAC,wCAAwC,iBAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SACtG;IACH,CAAC;IAEO,MAAM,CAAC,QAAQ,CAAC,IAA2C;QACjE,MAAM,QAAQ,GAAG,IAAI,YAAY,iBAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAE,IAAyB,CAAC,IAAI,EAAE,CAAC;QACrG,IAAI,QAAQ,KAAK,iBAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK,IAAI,QAAQ,KAAK,iBAAI,CAAC,cAAc,CAAC,aAAa,CAAC,MAAM,EAAE;YACjH,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;SACzD;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAEzC,sBAAsB;QACtB,IAAI,QAAQ,KAAK,iBAAI,CAAC,cAAc,CAAC,aAAa,CAAC,GAAG,IAAI,eAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YAChF,OAAO,eAAQ,CAAC,YAAY,CAAC,KAAgC,CAAC,CAAC;SAChE;QAED,0BAA0B;QAC1B,IAAI,QAAQ,KAAK,iBAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,EAAE;YACvD,MAAM,GAAG,GAAI,KAA6C,CAAC;YAC3D,MAAM,WAAW,GAAa,IAAI,KAAK,CAAS,GAAG,CAAC,MAAM,CAAC,CAAC;YAE5D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACnC,MAAM,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;gBACzB,WAAW,CAAC,CAAC,CAAC,GAAG,eAAQ,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;aACnD;YAED,OAAO,WAAW,CAAC;SACpB;QAED,yCAAyC;QACzC,IAAI,QAAQ,KAAK,iBAAI,CAAC,cAAc,CAAC,aAAa,CAAC,MAAM,EAAE;YACzD,OAAO,IAAI,YAAY,iBAAI,CAAC,cAAc,CAAC,CAAC,CAAC,eAAM,CAAC,SAAS,CAAC,KAA0B,CAAC,CAAC,CAAC;gBAC9C,eAAM,CAAC,aAAa,CAAC,KAAsB,CAAC,CAAC;SAC3F;QAED,6CAA6C;QAC7C,IAAI,QAAQ,KAAK,iBAAI,CAAC,cAAc,CAAC,aAAa,CAAC,OAAO,EAAE;YAC1D,IAAI,IAAI,YAAY,iBAAI,CAAC,cAAc,EAAE;gBACvC,MAAM,YAAY,GAAG,KAA4B,CAAC;gBAClD,OAAO,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,eAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;aAC3D;iBAAM,IAAI,IAAI,YAAY,MAAM,CAAC,SAAS,EAAE;gBAC3C,MAAM,YAAY,GAAG,KAAwB,CAAC;gBAC9C,OAAO,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,eAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;aAC/D;SACF;QAED,4BAA4B;QAC5B,IAAI,QAAQ,KAAK,iBAAI,CAAC,cAAc,CAAC,aAAa,CAAC,MAAM,EAAE;YACzD,iHAAiH;YACjH,wEAAwE;YACxE,IAAI,IAAI,YAAY,iBAAI,CAAC,cAAc,EAAE;gBACvC,MAAM,UAAU,GAAG,KAAmB,CAAC;gBACvC,OAAO,IAAA,uBAAgB,EAAC,UAAU,CAAC,CAAC;aACrC;SACF;QAED,gCAAgC;QAChC,IAAI,QAAQ,KAAK,iBAAI,CAAC,cAAc,CAAC,aAAa,CAAC,OAAO,EAAE;YAC1D,kHAAkH;YAClH,kFAAkF;YAClF,IAAI,IAAI,YAAY,iBAAI,CAAC,cAAc,EAAE;gBACvC,MAAM,WAAW,GAAG,KAAqB,CAAC;gBAC1C,OAAO,WAAW,CAAC,GAAG,CAAC,uBAAgB,CAAC,CAAC;aAC1C;SACF;QAED,OAAO,KAAmB,CAAC;IAC7B,CAAC;IAEO,MAAM,CAAC,eAAe,CAAC,IAA2C;QACxE,OAAO,IAAI,YAAY,CAAC,iBAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,CAAC,CAAC;YAC1C,IAAI,CAAC,4BAA4B,CAAC,IAAwB,CAAC,CAAC;IAC7G,CAAC;IAEO,MAAM,CAAC,6BAA6B,CAAC,IAA0B;QACrE,QAAQ,IAAI,CAAC,IAAK,EAAE;YAClB,KAAK,iBAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK;gBAC1C,OAAO,IAAI,CAAC,CAAC,CAAC;YAChB,KAAK,iBAAI,CAAC,cAAc,CAAC,aAAa,CAAC,GAAG;gBACxC,OAAO,IAAI,CAAC,CAAC,CAAC;YAChB,KAAK,iBAAI,CAAC,cAAc,CAAC,aAAa,CAAC,MAAM;gBAC3C,OAAO,IAAI,CAAC,CAAC,CAAC;YAChB,KAAK,iBAAI,CAAC,cAAc,CAAC,aAAa,CAAC,MAAM;gBAC3C,OAAO,IAAI,CAAC,CAAC,CAAC;YAChB,KAAK,iBAAI,CAAC,cAAc,CAAC,aAAa,CAAC,KAAK;gBAC1C,OAAO,IAAI,CAAC,CAAC,CAAC;YAChB,KAAK,iBAAI,CAAC,cAAc,CAAC,aAAa,CAAC,MAAM;gBAC3C,OAAO,IAAI,CAAC,MAAM,CAAC;YACrB,KAAK,iBAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI;gBACzC,OAAO,IAAI,CAAC,IAAI,CAAC;YACnB,KAAK,iBAAI,CAAC,cAAc,CAAC,aAAa,CAAC,OAAO;gBAC5C,OAAO,IAAI,CAAC,OAAO,CAAC;YACtB,KAAK,iBAAI,CAAC,cAAc,CAAC,aAAa,CAAC,OAAO;gBAC5C,OAAO,IAAI,CAAC,OAAO,CAAC;YACtB,KAAK,iBAAI,CAAC,cAAc,CAAC,aAAa,CAAC,MAAM;gBAC3C,OAAO,IAAI,CAAC,MAAM,CAAC;YACrB;gBACE,MAAM,IAAI,KAAK,CAAC,+BAA+B,iBAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,IAAK,CAAC,EAAE,CAAC,CAAC;SACnG;IACH,CAAC;IAEO,MAAM,CAAC,4BAA4B,CAAC,IAAsB;QAChE,QAAQ,IAAI,CAAC,IAAI,EAAE,EAAE;YACnB,KAAK,MAAM,CAAC,aAAa,CAAC,KAAK;gBAC7B,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC;YAClB,KAAK,MAAM,CAAC,aAAa,CAAC,GAAG;gBAC3B,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC;YAClB,KAAK,MAAM,CAAC,aAAa,CAAC,MAAM;gBAC9B,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC;YAClB,KAAK,MAAM,CAAC,aAAa,CAAC,MAAM;gBAC9B,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC;YAClB,KAAK,MAAM,CAAC,aAAa,CAAC,KAAK;gBAC7B,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC;YAClB,KAAK,MAAM,CAAC,aAAa,CAAC,MAAM;gBAC9B,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;YAC5B,KAAK,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;gBAC9B,MAAM,IAAI,GAAG,EAAE,CAAC;gBAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC,EAAE,EAAE;oBAC1C,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAE,CAAC,CAAC;iBAC1B;gBACD,OAAO,IAAI,CAAC;aACb;YACD,KAAK,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;gBACjC,MAAM,OAAO,GAAG,EAAE,CAAC;gBACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC,EAAE,EAAE;oBAC7C,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC/B;gBACD,OAAO,OAAO,CAAC;aAChB;YACD,KAAK,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;gBACjC,MAAM,OAAO,GAAG,EAAE,CAAC;gBACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC,EAAE,EAAE;oBAC7C,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAE,CAAC,CAAC;iBAChC;gBACD,OAAO,OAAO,CAAC;aAChB;YACD,oCAAoC;YACpC,oCAAoC;YACpC,qBAAqB;YACrB,kDAAkD;YAClD,kCAAkC;YAClC,IAAI;YACJ,iBAAiB;YACjB;gBACE,MAAM,IAAI,KAAK,CAAC,+BAA+B,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;SACvF;IACH,CAAC;CAGF;AAhPD,8BAgPC"}