import { AttributeWithCache<PERSON>ey } from '../../../attribute-with-cache-key';
import { OperatorImplementation, OperatorInitialization } from '../../../operators';
export interface ConcatAttributes extends AttributeWithCacheKey {
    readonly axis: number;
}
export declare const concat: OperatorImplementation<ConcatAttributes>;
export declare const parseConcatAttributes: OperatorInitialization<ConcatAttributes>;
