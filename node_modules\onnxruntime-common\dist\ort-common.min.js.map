{"version": 3, "file": "ort-common.min.js", "mappings": ";;;;;CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAa,IAAID,IAEjBD,EAAU,IAAIC,GACf,CATD,CASGK,MAAM,I,mBCRT,IAAIC,EAAsB,CCA1BA,EAAwB,CAACL,EAASM,KACjC,IAAI,IAAIC,KAAOD,EACXD,EAAoBG,EAAEF,EAAYC,KAASF,EAAoBG,EAAER,EAASO,IAC5EE,OAAOC,eAAeV,EAASO,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,IAE1E,ECNDF,EAAwB,CAACQ,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,GCClFT,EAAyBL,IACH,oBAAXkB,QAA0BA,OAAOC,aAC1CV,OAAOC,eAAeV,EAASkB,OAAOC,YAAa,CAAEC,MAAO,WAE7DX,OAAOC,eAAeV,EAAS,aAAc,CAAEoB,OAAO,GAAO,G,yFCS9D,MAAMC,EAA0C,CAAC,EAC3CC,EAAqC,GAY9BC,EAAkB,CAACC,EAAcC,EAAkBC,KAC9D,IAAID,GAAmC,mBAAjBA,EAAQE,MAA+D,mBAAjCF,EAAQG,qBA8BpE,MAAM,IAAIC,UAAU,uBA9BpB,CACE,MAAMC,EAAiBT,EAASG,GAChC,QAAuBO,IAAnBD,EACFT,EAASG,GAAQ,CAACC,UAASC,gBACtB,IAAII,EAAeJ,SAAWA,EAEnC,OACK,GAAII,EAAeJ,WAAaA,GACjCI,EAAeL,UAAYA,EAC7B,MAAM,IAAIO,MAAM,4BAA4BR,qBAAwBE,I,CAIxE,GAAIA,GAAY,EAAG,CACjB,MAAMO,EAAIX,EAAyBY,QAAQV,IAChC,IAAPS,GACFX,EAAyBa,OAAOF,EAAG,GAGrC,IAAK,IAAIA,EAAI,EAAGA,EAAIX,EAAyBc,OAAQH,IACnD,GAAIZ,EAASC,EAAyBW,IAAIP,UAAYA,EAEpD,YADAJ,EAAyBa,OAAOF,EAAG,EAAGT,GAI1CF,EAAyBe,KAAKb,E,EAKQ,EC2D/Bc,EAAW,IC/GjB,MACLC,cACEC,KAAKC,KAAO,CAAC,EACbD,KAAKE,MAAQ,CAAC,EACdF,KAAKG,iBAAmB,SAC1B,CAGIC,aAASxB,GACX,QAAcW,IAAVX,EAAJ,CAGA,GAAqB,iBAAVA,IAA2F,IAArE,CAAC,UAAW,OAAQ,UAAW,QAAS,SAASc,QAAQd,GACxF,MAAM,IAAIY,MAAM,8BAA8BZ,KAEhDoB,KAAKG,iBAAmBvB,C,CAC1B,CACIwB,eACF,OAAOJ,KAAKG,gBACd,GCZIE,EAAoD,oBAAlBC,eAA+D,mBAAvBA,cAAcC,KACxFC,EAAsD,oBAAnBC,gBAAiE,mBAAxBA,eAAeF,KAG3FG,EAAwC,IAAIC,IAA6C,CAC7F,CAAC,UAAWC,cACZ,CAAC,QAASC,YACV,CAAC,OAAQC,WACT,CAAC,SAAUC,aACX,CAAC,QAASC,YACV,CAAC,QAASC,YACV,CAAC,OAAQJ,YACT,CAAC,UAAWK,cACZ,CAAC,SAAUC,eAIPC,EAAwC,IAAIT,IAAiD,CACjG,CAACC,aAAc,WACf,CAACC,WAAY,SACb,CAACC,UAAW,QACZ,CAACC,YAAa,UACd,CAACC,WAAY,SACb,CAACC,WAAY,SACb,CAACC,aAAc,WACf,CAACC,YAAa,YAGZd,IACFK,EAAsCW,IAAI,QAASf,eACnDc,EAAsCC,IAAIf,cAAe,UAEvDE,IACFE,EAAsCW,IAAI,SAAUZ,gBACpDW,EAAsCC,IAAIZ,eAAgB,WAuBrD,MAAMa,EAIXvB,YACIwB,EAAoDC,EACpDC,GACF,IAAIC,EACAC,EACAC,EAEJ,GAAoB,iBAATL,EAMT,GAFAG,EAAOH,EACPK,EAAOH,EACM,WAATF,EAAmB,CAErB,IAAKM,MAAMC,QAAQN,GACjB,MAAM,IAAInC,UAAU,kDAItBsC,EAAOH,C,KACF,CAEL,MAAMO,EAAwBrB,EAAsCtC,IAAImD,GACxE,QAA8BhC,IAA1BwC,EACF,MAAM,IAAI1C,UAAU,4BAA4BkC,MAElD,GAAIM,MAAMC,QAAQN,GAKhBG,EAAQI,EAA8BxB,KAAKiB,OACtC,MAAIA,aAAgBO,GAGzB,MAAM,IAAI1C,UAAU,KAAKqC,mCAAsCK,KAF/DJ,EAAOH,C,OAUX,GADAI,EAAOJ,EACHK,MAAMC,QAAQP,GAAO,CAEvB,GAAoB,IAAhBA,EAAK3B,OACP,MAAM,IAAIP,UAAU,uDAEtB,MAAM2C,SAA0BT,EAAK,GACrC,GAAyB,WAArBS,EACFN,EAAO,SACPC,EAAOJ,MACF,IAAyB,YAArBS,EAOT,MAAM,IAAI3C,UAAU,uCAAuC2C,MAN3DN,EAAO,OAIPC,EAAOd,WAAWN,KAAKgB,E,MAIpB,CAEL,MAAMU,EACFb,EAAsChD,IAAImD,EAAKxB,aACnD,QAAmBR,IAAf0C,EACF,MAAM,IAAI5C,UAAU,qCAAqCkC,EAAKxB,gBAEhE2B,EAAOO,EACPN,EAAOJ,C,CAKX,QAAahC,IAATqC,EAEFA,EAAO,CAACD,EAAK/B,aACR,IAAKiC,MAAMC,QAAQF,GACxB,MAAM,IAAIvC,UAAU,0CAItB,MAAM6C,EAtGY,CAACN,IACrB,IAAIM,EAAO,EACX,IAAK,IAAIzC,EAAI,EAAGA,EAAImC,EAAKhC,OAAQH,IAAK,CACpC,MAAM0C,EAAMP,EAAKnC,GACjB,GAAmB,iBAAR0C,IAAqBC,OAAOC,cAAcF,GACnD,MAAM,IAAI9C,UAAU,QAAQI,+BAA+B0C,KAE7D,GAAIA,EAAM,EACR,MAAM,IAAIG,WAAW,QAAQ7C,2CAA2C0C,KAE1ED,GAAQC,C,CAEV,OAAOD,CAAI,EA0FIK,CAAcX,GAC3B,GAAIM,IAASP,EAAK/B,OAChB,MAAM,IAAIJ,MAAM,iBAAiB0C,iCAAoCP,EAAK/B,YAG5EI,KAAK4B,KAAOA,EACZ5B,KAAK0B,KAAOA,EACZ1B,KAAK2B,KAAOA,EACZ3B,KAAKkC,KAAOA,CACd,CASQM,sBAAsBC,EAAqCC,GACjE,QAAenD,IAAXkD,EACF,MAAM,IAAIjD,MAAM,gCAElB,QAAuBD,IAAnBmD,EAAQC,aAA0CpD,IAAlBmD,EAAQE,MAC1C,MAAM,IAAIpD,MAAM,0CAGlB,MAAM,OAACmD,EAAM,MAAEC,GAASF,EAElBG,EAAOH,EAAQG,KACrB,IAAIC,EACAC,EAEFD,OADWvD,IAATsD,QAAoCtD,IAAdsD,EAAKG,KAClB,IAEAH,EAAKG,KAGhBD,OADWxD,IAATsD,QAAoCtD,IAAdsD,EAAKI,KAClB,EAEAJ,EAAKI,KAGlB,MAAMC,OAAuC3D,IAAzBmD,EAAQS,aAA6BT,EAAQS,aAAe,OAG1EC,OAAwC7D,IAAzBmD,EAAQW,mBACC9D,IAAzBmD,EAAQW,aAA6BX,EAAQW,aAC9C,MACEC,EAASX,EAASC,EAClBW,EAA+B,SAAjBH,EAA0B,IAAIxC,aAAsB,EAAT0C,GAAc,IAAI1C,aAAsB,EAAT0C,GAG9F,IAAIE,EAAO,EAAGC,EAAgB,EAAGC,EAAgB,EAAGC,EAAgB,EAAGC,EAAgB,EACnFC,EAAiB,EAAGC,EAAiBR,EAAQS,EAA0B,EAATT,EAAYU,GAAkB,EAG5E,QAAhBd,IACFM,EAAO,EACPC,EAAgB,EAChBC,EAAgB,EAChBC,EAAgB,EAChBC,GAAiB,GAIE,SAAjBR,EACFY,EAA0B,EAATV,EACS,QAAjBF,GACTS,EAAiB,EACjBE,EAAiBT,EACjBQ,EAA0B,EAATR,GACS,QAAjBF,IACTW,EAAiB,EACjBD,EAAiBR,EACjBO,EAA0B,EAATP,GAGnB,IAAK,IAAI7D,EAAI,EAAGA,EAAI6D,EACf7D,IAAKgE,GAAiBD,EAAMG,GAAiBH,EAAME,GAAiBF,EAAMI,GAAiBJ,EAC9FD,EAAYM,MAAqBpB,EAAOgB,GAAiBV,GAAYD,EACrES,EAAYO,MAAqBrB,EAAOiB,GAAiBX,GAAYD,EACrES,EAAYQ,MAAqBtB,EAAOkB,GAAiBZ,GAAYD,GAC7C,IAApBkB,IAA4C,IAAnBJ,IAC3BL,EAAYS,MAAqBvB,EAAOmB,GAAiBb,GAAYD,GAOzE,OAF+C,IAAIxB,EAAO,UAAWiC,EAA/B,SAAjBH,EAA6D,CAAC,EAAG,EAAGT,EAAQC,GACf,CAAC,EAAG,EAAGD,EAAQC,GAEnG,CAQAJ,uBAAuByB,EAAsDvB,GAG3E,MAAMwB,EAA+C,oBAAvB,kBAAsCD,aAAiBE,iBAC/EC,EAAwC,oBAAhB,WAA+BH,aAAiBI,UACxEC,EAAyC,oBAAlB,aAAiCL,aAAiBM,YACzEC,EAA4B,oBAAb,SAA6BP,aAAiBQ,QAA2B,iBAAVR,GAEpF,IAAItC,EACA+C,EAAuC,CAAC,EAG5C,GAAIR,EAAgB,CAElB,MAAMS,EAASC,SAASC,cAAc,UAChCC,EAAkBH,EAAOI,WAAW,MAE1C,GAAuB,MAAnBD,EAsCF,MAAM,IAAItF,MAAM,6BAtCW,CAC3B,IAAImD,EAASsB,EAAMe,cACfpC,EAAQqB,EAAMgB,aAOlB,QALgB1F,IAAZmD,QAAmDnD,IAA1BmD,EAAQwC,oBAAwD3F,IAAzBmD,EAAQyC,eAC1ExC,EAASD,EAAQwC,cACjBtC,EAAQF,EAAQyC,mBAGF5F,IAAZmD,EAAuB,CAEzB,GADAgC,EAAehC,OACcnD,IAAzBmD,EAAQW,aACV,MAAM,IAAI7D,MAAM,+DAIlB,GAFEkF,EAAarB,aAAe,YAEP9D,IAAnBmD,EAAQC,QAAwBD,EAAQC,SAAWA,EACrD,MAAM,IAAInD,MAAM,mEAIlB,GAFEkF,EAAa/B,OAASA,OAEFpD,IAAlBmD,EAAQE,OAAuBF,EAAQE,QAAUA,EACnD,MAAM,IAAIpD,MAAM,iEAEhBkF,EAAa9B,MAAQA,C,MAGvB8B,EAAarB,aAAe,OAC5BqB,EAAa/B,OAASA,EACtB+B,EAAa9B,MAAQA,EAGvB+B,EAAO/B,MAAQA,EACf+B,EAAOhC,OAASA,EAEhBmC,EAAgBM,UAAUnB,EAAO,EAAG,EAAGrB,EAAOD,GAC9ChB,EAAOmD,EAAgBO,aAAa,EAAG,EAAGzC,EAAOD,GAAQhB,I,MAKtD,KAAIyC,EA8CJ,IAAIE,EAAe,CAExB,QAAgB/E,IAAZmD,EACF,MAAM,IAAIlD,MAAM,2DAElB,QAA6BD,IAAzBmD,EAAQS,aACV,MAAM,IAAI3D,MAAM,6DAGlB,MAAMsF,EAAkBF,SAASC,cAAc,UAAUE,WAAW,MAEpE,GAAuB,MAAnBD,EAAyB,CAC3B,MAAMnC,EAASsB,EAAMtB,OACfC,EAAQqB,EAAMrB,MAGpB,GAFAkC,EAAgBM,UAAUnB,EAAO,EAAG,EAAGrB,EAAOD,GAC9ChB,EAAOmD,EAAgBO,aAAa,EAAG,EAAGzC,EAAOD,GAAQhB,UACzCpC,IAAZmD,EAAuB,CAEzB,QAAuBnD,IAAnBmD,EAAQC,QAAwBD,EAAQC,SAAWA,EACrD,MAAM,IAAInD,MAAM,8DAKlB,GAHEkF,EAAa/B,OAASA,OAGFpD,IAAlBmD,EAAQE,OAAuBF,EAAQE,QAAUA,EACnD,MAAM,IAAIpD,MAAM,4DAEhBkF,EAAa9B,MAAQA,C,MAGvB8B,EAAa/B,OAASA,EACtB+B,EAAa9B,MAAQA,EAEvB,OAAOtB,EAAOgE,eAAe3D,EAAM+C,E,CAEnC,MAAM,IAAIlF,MAAM,4B,CAGb,GAAIgF,EACT,OAAO,IAAIe,SAAQ,CAACC,EAASC,KAC3B,MAAMd,EAASC,SAASC,cAAc,UAChCa,EAAUf,EAAOI,WAAW,MAClC,IAAKd,IAAUyB,EACb,OAAOD,IAET,MAAME,EAAW,IAAIC,MACrBD,EAASE,YAAc,YACvBF,EAASG,IAAM7B,EACf0B,EAASI,OAAS,KAChBpB,EAAO/B,MAAQ+C,EAAS/C,MACxB+B,EAAOhC,OAASgD,EAAShD,OACzB+C,EAAQN,UAAUO,EAAU,EAAG,EAAGhB,EAAO/B,MAAO+B,EAAOhC,QACvD,MAAMqD,EAAMN,EAAQL,aAAa,EAAG,EAAGV,EAAO/B,MAAO+B,EAAOhC,QAC5D,QAAgBpD,IAAZmD,EAAuB,CAEzB,QAAuBnD,IAAnBmD,EAAQC,QAAwBD,EAAQC,SAAWgC,EAAOhC,OAC5D,MAAM,IAAInD,MAAM,8DAKlB,GAHEkF,EAAa/B,OAASgC,EAAOhC,YAGTpD,IAAlBmD,EAAQE,OAAuBF,EAAQE,QAAU+B,EAAO/B,MAC1D,MAAM,IAAIpD,MAAM,4DAEhBkF,EAAa9B,MAAQ+B,EAAO/B,K,MAG9B8B,EAAa/B,OAASgC,EAAOhC,OAC7B+B,EAAa9B,MAAQ+B,EAAO/B,MAE9B4C,EAAQlE,EAAOgE,eAAeU,EAAIrE,KAAM+C,GAAc,CACvD,IAGH,MAAM,IAAIlF,MAAM,iE,CAxHS,CAEzB,MAAMyG,EAAS,OACf,IAAItD,EACAC,EAUJ,QARgBrD,IAAZmD,QAAkDnD,IAAzBmD,EAAQyC,mBAAwD5F,IAA1BmD,EAAQwC,eACzEvC,EAASD,EAAQwC,cACjBtC,EAAQF,EAAQyC,eAEhBxC,EAASsB,EAAMtB,OACfC,EAAQqB,EAAMrB,YAGArD,IAAZmD,EAAuB,CAEzB,GADAgC,EAAehC,OACcnD,IAAzBmD,EAAQS,cAA8BT,EAAQS,eAAiB8C,EACjE,MAAM,IAAIzG,MAAM,wDAEhBkF,EAAavB,aAAe,M,MAG9BuB,EAAavB,aAAe,OAM9B,GAHAuB,EAAa/B,OAASA,EACtB+B,EAAa9B,MAAQA,OAELrD,IAAZmD,EAAuB,CACzB,MAAMwD,EAAatB,SAASC,cAAc,UAE1CqB,EAAWtD,MAAQA,EACnBsD,EAAWvD,OAASA,EAEpB,MAAMmC,EAAkBoB,EAAWnB,WAAW,MAE9C,GAAuB,MAAnBD,EAIF,MAAM,IAAItF,MAAM,6BAHhBsF,EAAgBqB,aAAalC,EAAO,EAAG,GACvCtC,EAAOmD,EAAgBO,aAAa,EAAG,EAAGzC,EAAOD,GAAQhB,I,MAK3DA,EAAOsC,EAAMtC,I,EAgFjB,QAAapC,IAAToC,EACF,OAAOL,EAAOgE,eAAe3D,EAAM+C,GAEnC,MAAM,IAAIlF,MAAM,iEAEpB,CAEA4G,YAAY1D,G,QACV,MAAMoC,EAAkBF,SAASC,cAAc,UAAUE,WAAW,MACpE,IAAId,EACJ,GAAuB,MAAnBa,EAyDF,MAAM,IAAItF,MAAM,6BAzDW,CAE3B,MAAMoD,EAAQ5C,KAAK4B,KAAK,GAClBe,EAAS3C,KAAK4B,KAAK,GACnByE,EAAWrG,KAAK4B,KAAK,GAErBsB,OAA0B3D,IAAZmD,QAA4CnD,IAAnBmD,EAAQuD,OAAuBvD,EAAQuD,OAAkB,MAChGnD,OAAuBvD,IAAZmD,QAAgDnD,KAAX,QAAZ,EAAAmD,EAAQG,YAAI,eAAEG,MAAqBN,EAAQG,KAAKG,KAAc,IAClGD,OAAuBxD,IAAZmD,QAAgDnD,KAAX,QAAZ,EAAAmD,EAAQG,YAAI,eAAEI,MAAqBP,EAAQG,KAAKI,KAAY,EAChGK,EAASX,EAASC,EAExB,QAAgBrD,IAAZmD,EAAuB,CACzB,QAAuBnD,IAAnBmD,EAAQC,QAAwBD,EAAQC,SAAWA,EACrD,MAAM,IAAInD,MAAM,0DAElB,QAAsBD,IAAlBmD,EAAQE,OAAuBF,EAAQE,QAAUA,EACnD,MAAM,IAAIpD,MAAM,wDAElB,QAAuBD,IAAnBmD,EAAQuD,QAAsC,IAAbI,GAAqC,SAAnB3D,EAAQuD,QAC7C,IAAbI,GAAsC,QAAnB3D,EAAQuD,QAAuC,QAAnBvD,EAAQuD,OAC1D,MAAM,IAAIzG,MAAM,gD,CAKpB,MAAMgE,EAAO,EACb,IAAIC,EAAgB,EAAGC,EAAgB,EAAGC,EAAgB,EAAGC,EAAgB,EACzEC,EAAiB,EAAGC,EAAiBR,EAAQS,EAA0B,EAATT,EAAYU,GAAkB,EAG5E,SAAhBd,GACFW,EAAiB,EACjBC,EAAiBR,EACjBS,EAA0B,EAATT,EACjBU,EAA0B,EAATV,GACQ,QAAhBJ,GACTW,EAAiB,EACjBC,EAAiBR,EACjBS,EAA0B,EAATT,GACQ,QAAhBJ,IACTW,EAAiB,EACjBE,EAAiBT,EACjBQ,EAA0B,EAATR,GAGnBW,EAAQa,EAAgBwB,gBAAgB1D,EAAOD,GAE/C,IAAK,IAAIlD,EAAI,EAAGA,EAAIkD,EAASC,EACxBa,GAAiBD,EAAME,GAAiBF,EAAMG,GAAiBH,EAAMI,GAAiBJ,EAAM/D,IAC/FwE,EAAMtC,KAAK8B,IAAmBzD,KAAK2B,KAAKkC,KAA+Bd,GAAYD,EACnFmB,EAAMtC,KAAK+B,IAAmB1D,KAAK2B,KAAKmC,KAA+Bf,GAAYD,EACnFmB,EAAMtC,KAAKgC,IAAmB3D,KAAK2B,KAAKoC,KAA+BhB,GAAYD,EACnFmB,EAAMtC,KAAKiC,IACa,IAApBI,EAAwB,KAAQhE,KAAK2B,KAAKqC,KAA+BjB,GAAYD,C,CAM7F,OAAOmB,CACT,CAUAsC,QAAQ3E,GACN,OAAO,IAAIN,EAAOtB,KAAK0B,KAAM1B,KAAK2B,KAAMC,EAC1C,ECnKK,MAAM,EAASN,ECrVf,MAAMkF,EACX,YAAoBC,GAClBzG,KAAKyG,QAAUA,CACjB,CAGAC,UAAUC,EAAkBnF,EAA+BC,GACzD,MAAMmF,EAA4C,CAAC,EACnD,IAAIlE,EAAsB,CAAC,EAE3B,GAAqB,iBAAViE,GAAgC,OAAVA,GAAkBA,aAAiB,GAAU9E,MAAMC,QAAQ6E,GAC1F,MAAM,IAAItH,UACN,iGAGN,IAAIwH,GAAiB,EAErB,GAAoB,iBAATrF,EAAmB,CAC5B,GAAa,OAATA,EACF,MAAM,IAAInC,UAAU,2CAEtB,GAAImC,aAAgB,EAClB,MAAM,IAAInC,UAAU,gCAGtB,GAAIwC,MAAMC,QAAQN,GAAO,CACvB,GAAoB,IAAhBA,EAAK5B,OACP,MAAM,IAAIP,UAAU,uCAEtBwH,GAAiB,EAEjB,IAAK,MAAM7H,KAAQwC,EAAM,CACvB,GAAoB,iBAATxC,EACT,MAAM,IAAIK,UAAU,kDAEtB,IAAwC,IAApCW,KAAK8G,YAAYpH,QAAQV,GAC3B,MAAM,IAAIsD,WAAW,2CAA2CtD,MAElE4H,EAAQ5H,GAAQ,I,CAGlB,GAAoB,iBAATyC,GAA8B,OAATA,EAC9BiB,EAAUjB,OACL,QAAoB,IAATA,EAChB,MAAM,IAAIpC,UAAU,+B,KAEjB,CAGL,IAAI0H,GAAY,EAChB,MAAMC,EAAW/I,OAAOgJ,oBAAoBzF,GAC5C,IAAK,MAAMxC,KAAQgB,KAAK8G,YACtB,IAAgC,IAA5BE,EAAStH,QAAQV,GAAc,CACjC,MAAMkI,EAAK1F,EAA4DxC,IAC7D,OAANkI,GAAcA,aAAa,KAC7BH,GAAY,EACZF,GAAiB,EACjBD,EAAQ5H,GAAQkI,E,CAKtB,GAAIH,GACF,GAAoB,iBAATtF,GAA8B,OAATA,EAC9BiB,EAAUjB,OACL,QAAoB,IAATA,EAChB,MAAM,IAAIpC,UAAU,qCAGtBqD,EAAUlB,C,OAGT,QAAoB,IAATA,EAChB,MAAM,IAAInC,UAAU,2DAItB,IAAK,MAAML,KAAQgB,KAAKmH,WACtB,QAA2B,IAAhBR,EAAM3H,GACf,MAAM,IAAIQ,MAAM,UAAUR,6BAK9B,GAAI6H,EACF,IAAK,MAAM7H,KAAQgB,KAAK8G,YACtBF,EAAQ5H,GAAQ,KAMpB,MAAMoI,QAAgBpH,KAAKyG,QAAQY,IAAIV,EAAOC,EAASlE,GACjD4E,EAA2C,CAAC,EAClD,IAAK,MAAMvJ,KAAOqJ,EACZnJ,OAAOO,eAAeC,KAAK2I,EAASrJ,KACtCuJ,EAAYvJ,GAAO,IAAI,EAAOqJ,EAAQrJ,GAAK2D,KAAM0F,EAAQrJ,GAAK4D,KAAMyF,EAAQrJ,GAAK6D,OAGrF,OAAO0F,CACT,CAOA9E,oBACIjB,EAAyCC,EAA8BC,EACvE8F,GAEF,IAAIC,EACA9E,EAA0B,CAAC,EAE/B,GAAoB,iBAATnB,GAET,GADAiG,EAAuBjG,EACH,iBAATC,GAA8B,OAATA,EAC9BkB,EAAUlB,OACL,QAAoB,IAATA,EAChB,MAAM,IAAInC,UAAU,qCAEjB,GAAIkC,aAAgBV,YAEzB,GADA2G,EAAuBjG,EACH,iBAATC,GAA8B,OAATA,EAC9BkB,EAAUlB,OACL,QAAoB,IAATA,EAChB,MAAM,IAAInC,UAAU,oCAEjB,MACHkC,aAAgBkG,aACc,oBAAtBC,mBAAqCnG,aAAgBmG,mBAoC/D,MAAM,IAAIrI,UAAU,uDApC+D,CACnF,MAAMoD,EAASlB,EACf,IAAIoG,EAAa,EACbC,EAAarG,EAAKqG,WACtB,GAAoB,iBAATpG,GAA8B,OAATA,EAC9BkB,EAAUlB,OACL,GAAoB,iBAATA,EAAmB,CAEnC,GADAmG,EAAanG,GACRY,OAAOC,cAAcsF,GACxB,MAAM,IAAIrF,WAAW,oCAEvB,GAAIqF,EAAa,GAAKA,GAAclF,EAAOmF,WACzC,MAAM,IAAItF,WAAW,oCAAoCG,EAAOmF,gBAGlE,GADAA,EAAarG,EAAKqG,WAAaD,EACX,iBAATlG,EAAmB,CAE5B,GADAmG,EAAanG,GACRW,OAAOC,cAAcuF,GACxB,MAAM,IAAItF,WAAW,oCAEvB,GAAIsF,GAAc,GAAKD,EAAaC,EAAanF,EAAOmF,WACtD,MAAM,IAAItF,WAAW,oCAAoCG,EAAOmF,WAAaD,OAE/E,GAAoB,iBAATJ,GAA8B,OAATA,EAC9B7E,EAAU6E,OACL,QAAoB,IAATA,EAChB,MAAM,IAAIlI,UAAU,+B,MAEjB,QAAoB,IAAToC,EAChB,MAAM,IAAIpC,UAAU,iC,MAEjB,QAAoB,IAATmC,EAChB,MAAM,IAAInC,UAAU,gCAEtBmI,EAAuB,IAAI3G,WAAW4B,EAAQkF,EAAYC,E,EAM5D,MACMC,GADMnF,EAAQoF,oBAAsB,IACjBC,KAAItI,GAAkB,iBAANA,EAAiBA,EAAIA,EAAET,OAC1DC,OLtHoByH,OAAMmB,IAClC,MAAMG,EAAuC,IAAxBH,EAAajI,OAAed,EAA2B+I,EACtEI,EAAS,GACf,IAAK,MAAMC,KAAeF,EAAc,CACtC,MAAMG,EAActJ,EAASqJ,GAC7B,GAAIC,EAAa,CACf,GAAIA,EAAYC,YACd,OAAOD,EAAYlJ,QACd,GAAIkJ,EAAYE,QACrB,SAGF,MAAMC,IAAmBH,EAAYI,YACrC,IAME,OALKD,IACHH,EAAYI,YAAcJ,EAAYlJ,QAAQE,cAE1CgJ,EAAYI,YAClBJ,EAAYC,aAAc,EACnBD,EAAYlJ,O,CACnB,MAAOuJ,GACFF,GACHL,EAAOpI,KAAK,CAACb,KAAMkJ,EAAaO,IAAKD,IAEvCL,EAAYE,SAAU,C,gBAEfF,EAAYI,W,GAKzB,MAAM,IAAI/I,MAAM,oCAAoCyI,EAAOF,KAAIS,GAAK,IAAIA,EAAExJ,SAASwJ,EAAEC,QAAOC,KAAK,QAAQ,EKuFjFC,CAAed,GAC/BpB,QAAgBxH,EAAQG,qBAAqBoI,EAAsB9E,GACzE,OAAO,IAAI8D,EAAiBC,EAC9B,CAEAmC,iBACE5I,KAAKyG,QAAQmC,gBACf,CACAC,eACE7I,KAAKyG,QAAQoC,cACf,CAEI1B,iBACF,OAAOnH,KAAKyG,QAAQU,UACtB,CACIL,kBACF,OAAO9G,KAAKyG,QAAQK,WACtB,EC+KK,MAAM,EAA4CN,E", "sources": ["webpack://ort/webpack/universalModuleDefinition", "webpack://ort/webpack/bootstrap", "webpack://ort/webpack/runtime/define property getters", "webpack://ort/webpack/runtime/hasOwnProperty shorthand", "webpack://ort/webpack/runtime/make namespace object", "webpack://ort/./lib/backend-impl.ts", "webpack://ort/./lib/env.ts", "webpack://ort/./lib/env-impl.ts", "webpack://ort/./lib/tensor-impl.ts", "webpack://ort/./lib/tensor.ts", "webpack://ort/./lib/inference-session-impl.ts", "webpack://ort/./lib/inference-session.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ort\"] = factory();\n\telse\n\t\troot[\"ort\"] = factory();\n})(self, () => {\nreturn ", "// The require scope\nvar __webpack_require__ = {};\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\nimport {Backend} from './backend';\n\ninterface BackendInfo {\n  backend: Backend;\n  priority: number;\n\n  initPromise?: Promise<void>;\n  initialized?: boolean;\n  aborted?: boolean;\n}\n\nconst backends: {[name: string]: BackendInfo} = {};\nconst backendsSortedByPriority: string[] = [];\n\n/**\n * Register a backend.\n *\n * @param name - the name as a key to lookup as an execution provider.\n * @param backend - the backend object.\n * @param priority - an integer indicating the priority of the backend. Higher number means higher priority. if priority\n * < 0, it will be considered as a 'beta' version and will not be used as a fallback backend by default.\n *\n * @internal\n */\nexport const registerBackend = (name: string, backend: Backend, priority: number): void => {\n  if (backend && typeof backend.init === 'function' && typeof backend.createSessionHandler === 'function') {\n    const currentBackend = backends[name];\n    if (currentBackend === undefined) {\n      backends[name] = {backend, priority};\n    } else if (currentBackend.priority > priority) {\n      // same name is already registered with a higher priority. skip registeration.\n      return;\n    } else if (currentBackend.priority === priority) {\n      if (currentBackend.backend !== backend) {\n        throw new Error(`cannot register backend \"${name}\" using priority ${priority}`);\n      }\n    }\n\n    if (priority >= 0) {\n      const i = backendsSortedByPriority.indexOf(name);\n      if (i !== -1) {\n        backendsSortedByPriority.splice(i, 1);\n      }\n\n      for (let i = 0; i < backendsSortedByPriority.length; i++) {\n        if (backends[backendsSortedByPriority[i]].priority <= priority) {\n          backendsSortedByPriority.splice(i, 0, name);\n          return;\n        }\n      }\n      backendsSortedByPriority.push(name);\n    }\n    return;\n  }\n\n  throw new TypeError('not a valid backend');\n};\n\n/**\n * Resolve backend by specified hints.\n *\n * @param backendHints - a list of execution provider names to lookup. If omitted use registered backends as list.\n * @returns a promise that resolves to the backend.\n *\n * @internal\n */\nexport const resolveBackend = async(backendHints: readonly string[]): Promise<Backend> => {\n  const backendNames = backendHints.length === 0 ? backendsSortedByPriority : backendHints;\n  const errors = [];\n  for (const backendName of backendNames) {\n    const backendInfo = backends[backendName];\n    if (backendInfo) {\n      if (backendInfo.initialized) {\n        return backendInfo.backend;\n      } else if (backendInfo.aborted) {\n        continue;  // current backend is unavailable; try next\n      }\n\n      const isInitializing = !!backendInfo.initPromise;\n      try {\n        if (!isInitializing) {\n          backendInfo.initPromise = backendInfo.backend.init();\n        }\n        await backendInfo.initPromise;\n        backendInfo.initialized = true;\n        return backendInfo.backend;\n      } catch (e) {\n        if (!isInitializing) {\n          errors.push({name: backendName, err: e});\n        }\n        backendInfo.aborted = true;\n      } finally {\n        delete backendInfo.initPromise;\n      }\n    }\n  }\n\n  throw new Error(`no available backend found. ERR: ${errors.map(e => `[${e.name}] ${e.err}`).join(', ')}`);\n};\n", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\nimport {EnvImpl} from './env-impl';\nexport declare namespace Env {\n  export type WasmPrefixOrFilePaths = string|{\n    'ort-wasm.wasm'?: string;\n    'ort-wasm-threaded.wasm'?: string;\n    'ort-wasm-simd.wasm'?: string;\n    'ort-wasm-simd-threaded.wasm'?: string;\n  };\n  export interface WebAssemblyFlags {\n    /**\n     * set or get number of thread(s). If omitted or set to 0, number of thread(s) will be determined by system. If set\n     * to 1, no worker thread will be spawned.\n     *\n     * This setting is available only when WebAssembly multithread feature is available in current context.\n     *\n     * @defaultValue `0`\n     */\n    numThreads?: number;\n\n    /**\n     * set or get a boolean value indicating whether to enable SIMD. If set to false, SIMD will be forcely disabled.\n     *\n     * This setting is available only when WebAssembly SIMD feature is available in current context.\n     *\n     * @defaultValue `true`\n     */\n    simd?: boolean;\n\n    /**\n     * Set or get a number specifying the timeout for initialization of WebAssembly backend, in milliseconds. A zero\n     * value indicates no timeout is set.\n     *\n     * @defaultValue `0`\n     */\n    initTimeout?: number;\n\n    /**\n     * Set a custom URL prefix to the .wasm files or a set of overrides for each .wasm file. The override path should be\n     * an absolute path.\n     */\n    wasmPaths?: WasmPrefixOrFilePaths;\n\n    /**\n     * Set or get a boolean value indicating whether to proxy the execution of main thread to a worker thread.\n     *\n     * @defaultValue `false`\n     */\n    proxy?: boolean;\n  }\n\n  export interface WebGLFlags {\n    /**\n     * Set or get the WebGL Context ID (webgl or webgl2).\n     *\n     * @defaultValue `'webgl2'`\n     */\n    contextId?: 'webgl'|'webgl2';\n    /**\n     * Set or get the maximum batch size for matmul. 0 means to disable batching.\n     *\n     * @deprecated\n     */\n    matmulMaxBatchSize?: number;\n    /**\n     * Set or get the texture cache mode.\n     *\n     * @defaultValue `'full'`\n     */\n    textureCacheMode?: 'initializerOnly'|'full';\n    /**\n     * Set or get the packed texture mode\n     *\n     * @defaultValue `false`\n     */\n    pack?: boolean;\n    /**\n     * Set or get whether enable async download.\n     *\n     * @defaultValue `false`\n     */\n    async?: boolean;\n  }\n}\n\nexport interface Env {\n  /**\n   * set the severity level for logging.\n   *\n   * @defaultValue `'warning'`\n   */\n  logLevel?: 'verbose'|'info'|'warning'|'error'|'fatal';\n  /**\n   * Indicate whether run in debug mode.\n   *\n   * @defaultValue `false`\n   */\n  debug?: boolean;\n\n  /**\n   * Represent a set of flags for WebAssembly\n   */\n  wasm: Env.WebAssemblyFlags;\n\n  /**\n   * Represent a set of flags for WebGL\n   */\n  webgl: Env.WebGLFlags;\n\n  [name: string]: unknown;\n}\n\n/**\n * Represent a set of flags as a global singleton.\n */\nexport const env: Env = new EnvImpl();\n", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\nimport {Env} from './env';\n\ntype LogLevelType = Env['logLevel'];\nexport class EnvImpl implements Env {\n  constructor() {\n    this.wasm = {};\n    this.webgl = {};\n    this.logLevelInternal = 'warning';\n  }\n\n  // TODO standadize the getter and setter convention in env for other fields.\n  set logLevel(value: LogLevelType) {\n    if (value === undefined) {\n      return;\n    }\n    if (typeof value !== 'string' || ['verbose', 'info', 'warning', 'error', 'fatal'].indexOf(value) === -1) {\n      throw new Error(`Unsupported logging level: ${value}`);\n    }\n    this.logLevelInternal = value;\n  }\n  get logLevel(): LogLevelType {\n    return this.logLevelInternal;\n  }\n\n  debug?: boolean;\n\n  wasm: Env.WebAssemblyFlags;\n\n  webgl: Env.WebGLFlags;\n\n  [name: string]: unknown;\n\n  private logLevelInternal: Required<LogLevelType>;\n}\n", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\nimport {Tensor as TensorInterface, TensorFromImageOptions, TensorToImageDataOptions} from './tensor';\n\ntype TensorType = TensorInterface.Type;\ntype TensorDataType = TensorInterface.DataType;\n\ntype SupportedTypedArrayConstructors = Float32ArrayConstructor|Uint8ArrayConstructor|Int8ArrayConstructor|\n    Uint16ArrayConstructor|Int16ArrayConstructor|Int32ArrayConstructor|BigInt64ArrayConstructor|Uint8ArrayConstructor|\n    Float64ArrayConstructor|Uint32ArrayConstructor|BigUint64ArrayConstructor;\ntype SupportedTypedArray = InstanceType<SupportedTypedArrayConstructors>;\n\nconst isBigInt64ArrayAvailable = typeof BigInt64Array !== 'undefined' && typeof BigInt64Array.from === 'function';\nconst isBigUint64ArrayAvailable = typeof BigUint64Array !== 'undefined' && typeof BigUint64Array.from === 'function';\n\n// a runtime map that maps type string to TypedArray constructor. Should match Tensor.DataTypeMap.\nconst NUMERIC_TENSOR_TYPE_TO_TYPEDARRAY_MAP = new Map<string, SupportedTypedArrayConstructors>([\n  ['float32', Float32Array],\n  ['uint8', Uint8Array],\n  ['int8', Int8Array],\n  ['uint16', Uint16Array],\n  ['int16', Int16Array],\n  ['int32', Int32Array],\n  ['bool', Uint8Array],\n  ['float64', Float64Array],\n  ['uint32', Uint32Array],\n]);\n\n// a runtime map that maps type string to TypedArray constructor. Should match Tensor.DataTypeMap.\nconst NUMERIC_TENSOR_TYPEDARRAY_TO_TYPE_MAP = new Map<SupportedTypedArrayConstructors, TensorType>([\n  [Float32Array, 'float32'],\n  [Uint8Array, 'uint8'],\n  [Int8Array, 'int8'],\n  [Uint16Array, 'uint16'],\n  [Int16Array, 'int16'],\n  [Int32Array, 'int32'],\n  [Float64Array, 'float64'],\n  [Uint32Array, 'uint32'],\n]);\n\nif (isBigInt64ArrayAvailable) {\n  NUMERIC_TENSOR_TYPE_TO_TYPEDARRAY_MAP.set('int64', BigInt64Array);\n  NUMERIC_TENSOR_TYPEDARRAY_TO_TYPE_MAP.set(BigInt64Array, 'int64');\n}\nif (isBigUint64ArrayAvailable) {\n  NUMERIC_TENSOR_TYPE_TO_TYPEDARRAY_MAP.set('uint64', BigUint64Array);\n  NUMERIC_TENSOR_TYPEDARRAY_TO_TYPE_MAP.set(BigUint64Array, 'uint64');\n}\n\n/**\n * calculate size from dims.\n *\n * @param dims the dims array. May be an illegal input.\n */\nconst calculateSize = (dims: readonly unknown[]): number => {\n  let size = 1;\n  for (let i = 0; i < dims.length; i++) {\n    const dim = dims[i];\n    if (typeof dim !== 'number' || !Number.isSafeInteger(dim)) {\n      throw new TypeError(`dims[${i}] must be an integer, got: ${dim}`);\n    }\n    if (dim < 0) {\n      throw new RangeError(`dims[${i}] must be a non-negative integer, got: ${dim}`);\n    }\n    size *= dim;\n  }\n  return size;\n};\n\nexport class Tensor implements TensorInterface {\n  // #region constructors\n  constructor(type: TensorType, data: TensorDataType|readonly number[]|readonly boolean[], dims?: readonly number[]);\n  constructor(data: TensorDataType|readonly boolean[], dims?: readonly number[]);\n  constructor(\n      arg0: TensorType|TensorDataType|readonly boolean[], arg1?: TensorDataType|readonly number[]|readonly boolean[],\n      arg2?: readonly number[]) {\n    let type: TensorType;\n    let data: TensorDataType;\n    let dims: typeof arg1|typeof arg2;\n    // check whether arg0 is type or data\n    if (typeof arg0 === 'string') {\n      //\n      // Override: constructor(type, data, ...)\n      //\n      type = arg0;\n      dims = arg2;\n      if (arg0 === 'string') {\n        // string tensor\n        if (!Array.isArray(arg1)) {\n          throw new TypeError('A string tensor\\'s data must be a string array.');\n        }\n        // we don't check whether every element in the array is string; this is too slow. we assume it's correct and\n        // error will be populated at inference\n        data = arg1;\n      } else {\n        // numeric tensor\n        const typedArrayConstructor = NUMERIC_TENSOR_TYPE_TO_TYPEDARRAY_MAP.get(arg0);\n        if (typedArrayConstructor === undefined) {\n          throw new TypeError(`Unsupported tensor type: ${arg0}.`);\n        }\n        if (Array.isArray(arg1)) {\n          // use 'as any' here because TypeScript's check on type of 'SupportedTypedArrayConstructors.from()' produces\n          // incorrect results.\n          // 'typedArrayConstructor' should be one of the typed array prototype objects.\n          // eslint-disable-next-line @typescript-eslint/no-explicit-any\n          data = (typedArrayConstructor as any).from(arg1);\n        } else if (arg1 instanceof typedArrayConstructor) {\n          data = arg1;\n        } else {\n          throw new TypeError(`A ${type} tensor's data must be type of ${typedArrayConstructor}`);\n        }\n      }\n    } else {\n      //\n      // Override: constructor(data, ...)\n      //\n      dims = arg1;\n      if (Array.isArray(arg0)) {\n        // only boolean[] and string[] is supported\n        if (arg0.length === 0) {\n          throw new TypeError('Tensor type cannot be inferred from an empty array.');\n        }\n        const firstElementType = typeof arg0[0];\n        if (firstElementType === 'string') {\n          type = 'string';\n          data = arg0;\n        } else if (firstElementType === 'boolean') {\n          type = 'bool';\n          // 'arg0' is of type 'boolean[]'. Uint8Array.from(boolean[]) actually works, but typescript thinks this is\n          // wrong type. We use 'as any' to make it happy.\n          // eslint-disable-next-line @typescript-eslint/no-explicit-any\n          data = Uint8Array.from(arg0 as any[]);\n        } else {\n          throw new TypeError(`Invalid element type of data array: ${firstElementType}.`);\n        }\n      } else {\n        // get tensor type from TypedArray\n        const mappedType =\n            NUMERIC_TENSOR_TYPEDARRAY_TO_TYPE_MAP.get(arg0.constructor as SupportedTypedArrayConstructors);\n        if (mappedType === undefined) {\n          throw new TypeError(`Unsupported type for tensor data: ${arg0.constructor}.`);\n        }\n        type = mappedType;\n        data = arg0 as SupportedTypedArray;\n      }\n    }\n\n    // type and data is processed, now processing dims\n    if (dims === undefined) {\n      // assume 1-D tensor if dims omitted\n      dims = [data.length];\n    } else if (!Array.isArray(dims)) {\n      throw new TypeError('A tensor\\'s dims must be a number array');\n    }\n\n    // perform check\n    const size = calculateSize(dims);\n    if (size !== data.length) {\n      throw new Error(`Tensor's size(${size}) does not match data length(${data.length}).`);\n    }\n\n    this.dims = dims as readonly number[];\n    this.type = type;\n    this.data = data;\n    this.size = size;\n  }\n  // #endregion\n  /**\n   * Create a new tensor object from image object\n   *\n   * @param buffer - Extracted image buffer data - assuming RGBA format\n   * @param imageFormat - input image configuration - required configurations height, width, format\n   * @param tensorFormat - output tensor configuration - Default is RGB format\n   */\n  private static bufferToTensor(buffer: Uint8ClampedArray|undefined, options: TensorFromImageOptions): Tensor {\n    if (buffer === undefined) {\n      throw new Error('Image buffer must be defined');\n    }\n    if (options.height === undefined || options.width === undefined) {\n      throw new Error('Image height and width must be defined');\n    }\n\n    const {height, width} = options;\n\n    const norm = options.norm;\n    let normMean: number;\n    let normBias: number;\n    if (norm === undefined || norm.mean === undefined) {\n      normMean = 255;\n    } else {\n      normMean = norm.mean;\n    }\n    if (norm === undefined || norm.bias === undefined) {\n      normBias = 0;\n    } else {\n      normBias = norm.bias;\n    }\n\n    const inputformat = options.bitmapFormat !== undefined ? options.bitmapFormat : 'RGBA';\n    // default value is RGBA since imagedata and HTMLImageElement uses it\n\n    const outputformat = options.tensorFormat !== undefined ?\n        (options.tensorFormat !== undefined ? options.tensorFormat : 'RGB') :\n        'RGB';\n    const offset = height * width;\n    const float32Data = outputformat === 'RGBA' ? new Float32Array(offset * 4) : new Float32Array(offset * 3);\n\n    // Default pointer assignments\n    let step = 4, rImagePointer = 0, gImagePointer = 1, bImagePointer = 2, aImagePointer = 3;\n    let rTensorPointer = 0, gTensorPointer = offset, bTensorPointer = offset * 2, aTensorPointer = -1;\n\n    // Updating the pointer assignments based on the input image format\n    if (inputformat === 'RGB') {\n      step = 3;\n      rImagePointer = 0;\n      gImagePointer = 1;\n      bImagePointer = 2;\n      aImagePointer = -1;\n    }\n\n    // Updating the pointer assignments based on the output tensor format\n    if (outputformat === 'RGBA') {\n      aTensorPointer = offset * 3;\n    } else if (outputformat === 'RBG') {\n      rTensorPointer = 0;\n      bTensorPointer = offset;\n      gTensorPointer = offset * 2;\n    } else if (outputformat === 'BGR') {\n      bTensorPointer = 0;\n      gTensorPointer = offset;\n      rTensorPointer = offset * 2;\n    }\n\n    for (let i = 0; i < offset;\n         i++, rImagePointer += step, bImagePointer += step, gImagePointer += step, aImagePointer += step) {\n      float32Data[rTensorPointer++] = (buffer[rImagePointer] + normBias) / normMean;\n      float32Data[gTensorPointer++] = (buffer[gImagePointer] + normBias) / normMean;\n      float32Data[bTensorPointer++] = (buffer[bImagePointer] + normBias) / normMean;\n      if (aTensorPointer !== -1 && aImagePointer !== -1) {\n        float32Data[aTensorPointer++] = (buffer[aImagePointer] + normBias) / normMean;\n      }\n    }\n\n    // Float32Array -> ort.Tensor\n    const outputTensor = outputformat === 'RGBA' ? new Tensor('float32', float32Data, [1, 4, height, width]) :\n                                                   new Tensor('float32', float32Data, [1, 3, height, width]);\n    return outputTensor;\n  }\n\n  // #region factory\n  static async fromImage(imageData: ImageData, options?: TensorFromImageOptions): Promise<Tensor>;\n  static async fromImage(imageElement: HTMLImageElement, options?: TensorFromImageOptions): Promise<Tensor>;\n  static async fromImage(bitmap: ImageBitmap, options: TensorFromImageOptions): Promise<Tensor>;\n  static async fromImage(url: string, options?: TensorFromImageOptions): Promise<Tensor>;\n\n  static async fromImage(image: ImageData|HTMLImageElement|ImageBitmap|string, options?: TensorFromImageOptions):\n      Promise<Tensor> {\n    // checking the type of image object\n    const isHTMLImageEle = typeof (HTMLImageElement) !== 'undefined' && image instanceof HTMLImageElement;\n    const isImageDataEle = typeof (ImageData) !== 'undefined' && image instanceof ImageData;\n    const isImageBitmap = typeof (ImageBitmap) !== 'undefined' && image instanceof ImageBitmap;\n    const isURL = typeof (String) !== 'undefined' && (image instanceof String || typeof image === 'string');\n\n    let data: Uint8ClampedArray|undefined;\n    let tensorConfig: TensorFromImageOptions = {};\n\n    // filling and checking image configuration options\n    if (isHTMLImageEle) {\n      // HTMLImageElement - image object - format is RGBA by default\n      const canvas = document.createElement('canvas');\n      const pixels2DContext = canvas.getContext('2d');\n\n      if (pixels2DContext != null) {\n        let height = image.naturalHeight;\n        let width = image.naturalWidth;\n\n        if (options !== undefined && options.resizedHeight !== undefined && options.resizedWidth !== undefined) {\n          height = options.resizedHeight;\n          width = options.resizedWidth;\n        }\n\n        if (options !== undefined) {\n          tensorConfig = options;\n          if (options.tensorFormat !== undefined) {\n            throw new Error('Image input config format must be RGBA for HTMLImageElement');\n          } else {\n            tensorConfig.tensorFormat = 'RGBA';\n          }\n          if (options.height !== undefined && options.height !== height) {\n            throw new Error('Image input config height doesn\\'t match HTMLImageElement height');\n          } else {\n            tensorConfig.height = height;\n          }\n          if (options.width !== undefined && options.width !== width) {\n            throw new Error('Image input config width doesn\\'t match HTMLImageElement width');\n          } else {\n            tensorConfig.width = width;\n          }\n        } else {\n          tensorConfig.tensorFormat = 'RGBA';\n          tensorConfig.height = height;\n          tensorConfig.width = width;\n        }\n\n        canvas.width = width;\n        canvas.height = height;\n\n        pixels2DContext.drawImage(image, 0, 0, width, height);\n        data = pixels2DContext.getImageData(0, 0, width, height).data;\n      } else {\n        throw new Error('Can not access image data');\n      }\n\n    } else if (isImageDataEle) {\n      // ImageData - image object - format is RGBA by default\n      const format = 'RGBA';\n      let height: number;\n      let width: number;\n\n      if (options !== undefined && options.resizedWidth !== undefined && options.resizedHeight !== undefined) {\n        height = options.resizedHeight;\n        width = options.resizedWidth;\n      } else {\n        height = image.height;\n        width = image.width;\n      }\n\n      if (options !== undefined) {\n        tensorConfig = options;\n        if (options.bitmapFormat !== undefined && options.bitmapFormat !== format) {\n          throw new Error('Image input config format must be RGBA for ImageData');\n        } else {\n          tensorConfig.bitmapFormat = 'RGBA';\n        }\n      } else {\n        tensorConfig.bitmapFormat = 'RGBA';\n      }\n\n      tensorConfig.height = height;\n      tensorConfig.width = width;\n\n      if (options !== undefined) {\n        const tempCanvas = document.createElement('canvas');\n\n        tempCanvas.width = width;\n        tempCanvas.height = height;\n\n        const pixels2DContext = tempCanvas.getContext('2d');\n\n        if (pixels2DContext != null) {\n          pixels2DContext.putImageData(image, 0, 0);\n          data = pixels2DContext.getImageData(0, 0, width, height).data;\n        } else {\n          throw new Error('Can not access image data');\n        }\n      } else {\n        data = image.data;\n      }\n\n    } else if (isImageBitmap) {\n      // ImageBitmap - image object - format must be provided by user\n      if (options === undefined) {\n        throw new Error('Please provide image config with format for Imagebitmap');\n      }\n      if (options.bitmapFormat !== undefined) {\n        throw new Error('Image input config format must be defined for ImageBitmap');\n      }\n\n      const pixels2DContext = document.createElement('canvas').getContext('2d');\n\n      if (pixels2DContext != null) {\n        const height = image.height;\n        const width = image.width;\n        pixels2DContext.drawImage(image, 0, 0, width, height);\n        data = pixels2DContext.getImageData(0, 0, width, height).data;\n        if (options !== undefined) {\n          // using square brackets to avoid TS error - type 'never'\n          if (options.height !== undefined && options.height !== height) {\n            throw new Error('Image input config height doesn\\'t match ImageBitmap height');\n          } else {\n            tensorConfig.height = height;\n          }\n          // using square brackets to avoid TS error - type 'never'\n          if (options.width !== undefined && options.width !== width) {\n            throw new Error('Image input config width doesn\\'t match ImageBitmap width');\n          } else {\n            tensorConfig.width = width;\n          }\n        } else {\n          tensorConfig.height = height;\n          tensorConfig.width = width;\n        }\n        return Tensor.bufferToTensor(data, tensorConfig);\n      } else {\n        throw new Error('Can not access image data');\n      }\n\n    } else if (isURL) {\n      return new Promise((resolve, reject) => {\n        const canvas = document.createElement('canvas');\n        const context = canvas.getContext('2d');\n        if (!image || !context) {\n          return reject();\n        }\n        const newImage = new Image();\n        newImage.crossOrigin = 'Anonymous';\n        newImage.src = image as string;\n        newImage.onload = () => {\n          canvas.width = newImage.width;\n          canvas.height = newImage.height;\n          context.drawImage(newImage, 0, 0, canvas.width, canvas.height);\n          const img = context.getImageData(0, 0, canvas.width, canvas.height);\n          if (options !== undefined) {\n            // using square brackets to avoid TS error - type 'never'\n            if (options.height !== undefined && options.height !== canvas.height) {\n              throw new Error('Image input config height doesn\\'t match ImageBitmap height');\n            } else {\n              tensorConfig.height = canvas.height;\n            }\n            // using square brackets to avoid TS error - type 'never'\n            if (options.width !== undefined && options.width !== canvas.width) {\n              throw new Error('Image input config width doesn\\'t match ImageBitmap width');\n            } else {\n              tensorConfig.width = canvas.width;\n            }\n          } else {\n            tensorConfig.height = canvas.height;\n            tensorConfig.width = canvas.width;\n          }\n          resolve(Tensor.bufferToTensor(img.data, tensorConfig));\n        };\n      });\n    } else {\n      throw new Error('Input data provided is not supported - aborted tensor creation');\n    }\n\n    if (data !== undefined) {\n      return Tensor.bufferToTensor(data, tensorConfig);\n    } else {\n      throw new Error('Input data provided is not supported - aborted tensor creation');\n    }\n  }\n\n  toImageData(options?: TensorToImageDataOptions): ImageData {\n    const pixels2DContext = document.createElement('canvas').getContext('2d');\n    let image: ImageData;\n    if (pixels2DContext != null) {\n      // Default values for height and width & format\n      const width = this.dims[3];\n      const height = this.dims[2];\n      const channels = this.dims[1];\n\n      const inputformat = options !== undefined ? (options.format !== undefined ? options.format : 'RGB') : 'RGB';\n      const normMean = options !== undefined ? (options.norm?.mean !== undefined ? options.norm.mean : 255) : 255;\n      const normBias = options !== undefined ? (options.norm?.bias !== undefined ? options.norm.bias : 0) : 0;\n      const offset = height * width;\n\n      if (options !== undefined) {\n        if (options.height !== undefined && options.height !== height) {\n          throw new Error('Image output config height doesn\\'t match tensor height');\n        }\n        if (options.width !== undefined && options.width !== width) {\n          throw new Error('Image output config width doesn\\'t match tensor width');\n        }\n        if (options.format !== undefined && (channels === 4 && options.format !== 'RGBA') ||\n            (channels === 3 && (options.format !== 'RGB' && options.format !== 'BGR'))) {\n          throw new Error('Tensor format doesn\\'t match input tensor dims');\n        }\n      }\n\n      // Default pointer assignments\n      const step = 4;\n      let rImagePointer = 0, gImagePointer = 1, bImagePointer = 2, aImagePointer = 3;\n      let rTensorPointer = 0, gTensorPointer = offset, bTensorPointer = offset * 2, aTensorPointer = -1;\n\n      // Updating the pointer assignments based on the input image format\n      if (inputformat === 'RGBA') {\n        rTensorPointer = 0;\n        gTensorPointer = offset;\n        bTensorPointer = offset * 2;\n        aTensorPointer = offset * 3;\n      } else if (inputformat === 'RGB') {\n        rTensorPointer = 0;\n        gTensorPointer = offset;\n        bTensorPointer = offset * 2;\n      } else if (inputformat === 'RBG') {\n        rTensorPointer = 0;\n        bTensorPointer = offset;\n        gTensorPointer = offset * 2;\n      }\n\n      image = pixels2DContext.createImageData(width, height);\n\n      for (let i = 0; i < height * width;\n           rImagePointer += step, gImagePointer += step, bImagePointer += step, aImagePointer += step, i++) {\n        image.data[rImagePointer] = ((this.data[rTensorPointer++] as number) - normBias) * normMean;  // R value\n        image.data[gImagePointer] = ((this.data[gTensorPointer++] as number) - normBias) * normMean;  // G value\n        image.data[bImagePointer] = ((this.data[bTensorPointer++] as number) - normBias) * normMean;  // B value\n        image.data[aImagePointer] =\n            aTensorPointer === -1 ? 255 : ((this.data[aTensorPointer++] as number) - normBias) * normMean;  // A value\n      }\n\n    } else {\n      throw new Error('Can not access image data');\n    }\n    return image;\n  }\n\n  // #region fields\n  readonly dims: readonly number[];\n  readonly type: TensorType;\n  readonly data: TensorDataType;\n  readonly size: number;\n  // #endregion\n\n  // #region tensor utilities\n  reshape(dims: readonly number[]): Tensor {\n    return new Tensor(this.type, this.data, dims);\n  }\n  // #endregion\n}\n", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\nimport {Tensor as TensorImpl} from './tensor-impl';\nimport {TypedTensorUtils} from './tensor-utils';\n\n/* eslint-disable @typescript-eslint/no-redeclare */\n\n/**\n * represent a basic tensor with specified dimensions and data type.\n */\ninterface TypedTensorBase<T extends Tensor.Type> {\n  /**\n   * Get the dimensions of the tensor.\n   */\n  readonly dims: readonly number[];\n  /**\n   * Get the data type of the tensor.\n   */\n  readonly type: T;\n  /**\n   * Get the buffer data of the tensor.\n   */\n  readonly data: Tensor.DataTypeMap[T];\n}\n\nexport declare namespace Tensor {\n  interface DataTypeMap {\n    float32: Float32Array;\n    uint8: Uint8Array;\n    int8: Int8Array;\n    uint16: Uint16Array;\n    int16: Int16Array;\n    int32: Int32Array;\n    int64: BigInt64Array;\n    string: string[];\n    bool: Uint8Array;\n    float16: never;  // hold on using Uint16Array before we have a concrete solution for float 16\n    float64: Float64Array;\n    uint32: Uint32Array;\n    uint64: BigUint64Array;\n    // complex64: never;\n    // complex128: never;\n    // bfloat16: never;\n  }\n\n  interface ElementTypeMap {\n    float32: number;\n    uint8: number;\n    int8: number;\n    uint16: number;\n    int16: number;\n    int32: number;\n    int64: bigint;\n    string: string;\n    bool: boolean;\n    float16: never;  // hold on before we have a concret solution for float 16\n    float64: number;\n    uint32: number;\n    uint64: bigint;\n    // complex64: never;\n    // complex128: never;\n    // bfloat16: never;\n  }\n\n  type DataType = DataTypeMap[Type];\n  type ElementType = ElementTypeMap[Type];\n\n  /**\n   * represent the data type of a tensor\n   */\n  export type Type = keyof DataTypeMap;\n}\n\n/**\n * Represent multi-dimensional arrays to feed to or fetch from model inferencing.\n */\nexport interface TypedTensor<T extends Tensor.Type> extends TypedTensorBase<T>, TypedTensorUtils<T> {}\n/**\n * Represent multi-dimensional arrays to feed to or fetch from model inferencing.\n */\nexport interface Tensor extends TypedTensorBase<Tensor.Type>, TypedTensorUtils<Tensor.Type> {}\n\nexport interface TensorConstructor {\n  // #region specify element type\n  /**\n   * Construct a new string tensor object from the given type, data and dims.\n   *\n   * @param type - Specify the element type.\n   * @param data - Specify the tensor data\n   * @param dims - Specify the dimension of the tensor. If omitted, a 1-D tensor is assumed.\n   */\n  new(type: 'string', data: Tensor.DataTypeMap['string']|readonly string[],\n      dims?: readonly number[]): TypedTensor<'string'>;\n\n  /**\n   * Construct a new bool tensor object from the given type, data and dims.\n   *\n   * @param type - Specify the element type.\n   * @param data - Specify the tensor data\n   * @param dims - Specify the dimension of the tensor. If omitted, a 1-D tensor is assumed.\n   */\n  new(type: 'bool', data: Tensor.DataTypeMap['bool']|readonly boolean[], dims?: readonly number[]): TypedTensor<'bool'>;\n\n  /**\n   * Construct a new numeric tensor object from the given type, data and dims.\n   *\n   * @param type - Specify the element type.\n   * @param data - Specify the tensor data\n   * @param dims - Specify the dimension of the tensor. If omitted, a 1-D tensor is assumed.\n   */\n  new<T extends Exclude<Tensor.Type, 'string'|'bool'>>(\n      type: T, data: Tensor.DataTypeMap[T]|readonly number[], dims?: readonly number[]): TypedTensor<T>;\n  // #endregion\n\n  // #region infer element types\n\n  /**\n   * Construct a new float32 tensor object from the given data and dims.\n   *\n   * @param data - Specify the tensor data\n   * @param dims - Specify the dimension of the tensor. If omitted, a 1-D tensor is assumed.\n   */\n  new(data: Float32Array, dims?: readonly number[]): TypedTensor<'float32'>;\n\n  /**\n   * Construct a new int8 tensor object from the given data and dims.\n   *\n   * @param data - Specify the tensor data\n   * @param dims - Specify the dimension of the tensor. If omitted, a 1-D tensor is assumed.\n   */\n  new(data: Int8Array, dims?: readonly number[]): TypedTensor<'int8'>;\n\n  /**\n   * Construct a new uint8 tensor object from the given data and dims.\n   *\n   * @param data - Specify the tensor data\n   * @param dims - Specify the dimension of the tensor. If omitted, a 1-D tensor is assumed.\n   */\n  new(data: Uint8Array, dims?: readonly number[]): TypedTensor<'uint8'>;\n\n  /**\n   * Construct a new uint16 tensor object from the given data and dims.\n   *\n   * @param data - Specify the tensor data\n   * @param dims - Specify the dimension of the tensor. If omitted, a 1-D tensor is assumed.\n   */\n  new(data: Uint16Array, dims?: readonly number[]): TypedTensor<'uint16'>;\n\n  /**\n   * Construct a new int16 tensor object from the given data and dims.\n   *\n   * @param data - Specify the tensor data\n   * @param dims - Specify the dimension of the tensor. If omitted, a 1-D tensor is assumed.\n   */\n  new(data: Int16Array, dims?: readonly number[]): TypedTensor<'int16'>;\n\n  /**\n   * Construct a new int32 tensor object from the given data and dims.\n   *\n   * @param data - Specify the tensor data\n   * @param dims - Specify the dimension of the tensor. If omitted, a 1-D tensor is assumed.\n   */\n  new(data: Int32Array, dims?: readonly number[]): TypedTensor<'int32'>;\n\n  /**\n   * Construct a new int64 tensor object from the given data and dims.\n   *\n   * @param data - Specify the tensor data\n   * @param dims - Specify the dimension of the tensor. If omitted, a 1-D tensor is assumed.\n   */\n  new(data: BigInt64Array, dims?: readonly number[]): TypedTensor<'int64'>;\n\n  /**\n   * Construct a new string tensor object from the given data and dims.\n   *\n   * @param data - Specify the tensor data\n   * @param dims - Specify the dimension of the tensor. If omitted, a 1-D tensor is assumed.\n   */\n  new(data: readonly string[], dims?: readonly number[]): TypedTensor<'string'>;\n\n  /**\n   * Construct a new bool tensor object from the given data and dims.\n   *\n   * @param data - Specify the tensor data\n   * @param dims - Specify the dimension of the tensor. If omitted, a 1-D tensor is assumed.\n   */\n  new(data: readonly boolean[], dims?: readonly number[]): TypedTensor<'bool'>;\n\n  /**\n   * Construct a new float64 tensor object from the given data and dims.\n   *\n   * @param data - Specify the tensor data\n   * @param dims - Specify the dimension of the tensor. If omitted, a 1-D tensor is assumed.\n   */\n  new(data: Float64Array, dims?: readonly number[]): TypedTensor<'float64'>;\n\n  /**\n   * Construct a new uint32 tensor object from the given data and dims.\n   *\n   * @param data - Specify the tensor data\n   * @param dims - Specify the dimension of the tensor. If omitted, a 1-D tensor is assumed.\n   */\n  new(data: Uint32Array, dims?: readonly number[]): TypedTensor<'uint32'>;\n\n  /**\n   * Construct a new uint64 tensor object from the given data and dims.\n   *\n   * @param data - Specify the tensor data\n   * @param dims - Specify the dimension of the tensor. If omitted, a 1-D tensor is assumed.\n   */\n  new(data: BigUint64Array, dims?: readonly number[]): TypedTensor<'uint64'>;\n\n  // #endregion\n\n  // #region fall back to non-generic tensor type declaration\n\n  /**\n   * Construct a new tensor object from the given type, data and dims.\n   *\n   * @param type - Specify the element type.\n   * @param data - Specify the tensor data\n   * @param dims - Specify the dimension of the tensor. If omitted, a 1-D tensor is assumed.\n   */\n  new(type: Tensor.Type, data: Tensor.DataType|readonly number[]|readonly boolean[], dims?: readonly number[]): Tensor;\n\n  /**\n   * Construct a new tensor object from the given data and dims.\n   *\n   * @param data - Specify the tensor data\n   * @param dims - Specify the dimension of the tensor. If omitted, a 1-D tensor is assumed.\n   */\n  new(data: Tensor.DataType, dims?: readonly number[]): Tensor;\n  // #endregion\n}\n\n/**\n * Specify the image format. Assume 'RGBA' if omitted.\n */\nexport type ImageFormat = 'RGB'|'RGBA'|'BGR'|'RBG';\n\n/**\n * Describes Tensor configuration to an image data.\n */\nexport interface TensorToImageDataOptions {\n  /**\n   * Describes Tensor channels order.\n   */\n  format?: ImageFormat;\n  /**\n   * Tensor channel layout - default is 'NHWC'\n   */\n  tensorLayout?: 'NHWC'|'NCHW';\n  /**\n   * Describes Tensor Height - can be accessed via tensor dimensions as well\n   */\n  height?: number;\n  /**\n   * Describes Tensor Width - can be accessed via tensor dimensions as well\n   */\n  width?: number;\n  /**\n   * Describes normalization parameters to ImageData conversion from tensor - default values - Bias: 0, Mean: 255\n   */\n  norm?: {\n    bias?: number;  // Todo add support - |[number,number,number]|[number,number,number,number];\n    mean?: number;  // Todo add support - |[number,number,number]|[number,number,number,number];\n  };\n}\n/**\n * Describes Tensor and Image configuration to an image data.\n */\nexport interface TensorFromImageOptions {\n  /**\n   * Describes image data format - will be used only in the case of ImageBitMap\n   */\n  bitmapFormat?: ImageFormat;\n  /**\n   * Describes Tensor channels order - can differ from original image\n   */\n  tensorFormat?: ImageFormat;\n  /**\n   * Tensor data type - default is 'float32'\n   */\n  dataType?: 'float32'|'uint8';\n  /**\n   * Tensor channel layout - default is 'NHWC'\n   */\n  tensorLayout?: 'NHWC'|'NCHW';\n  /**\n   * Describes Image Height - Required only in the case of ImageBitMap\n   */\n  height?: number;\n  /**\n   * Describes Image Width - Required only in the case of ImageBitMap\n   */\n  width?: number;\n  /**\n   * Describes resized height - can be accessed via tensor dimensions as well\n   */\n  resizedHeight?: number;\n  /**\n   * Describes resized width - can be accessed via tensor dimensions as well\n   */\n  resizedWidth?: number;\n  /**\n   * Describes normalization parameters to tensor conversion from image data - default values - Bias: 0, Mean: 255\n   */\n  norm?: {\n    bias?: number;  // Todo add support - |[number,number,number]|[number,number,number,number];\n    mean?: number;  // Todo add support - |[number,number,number]|[number,number,number,number];\n  };\n}\nexport interface TensorFactory {\n  /**\n   * create a tensor from image object - HTMLImageElement, ImageData, ImageBitmap, URL\n   *\n   * @param imageData - {ImageData} - composed of: Uint8ClampedArray, width. height - uses known pixel format RGBA\n   * @param options - Optional - Interface describing input image & output tensor -\n   * Input Defaults: RGBA, 3 channels, 0-255, NHWC - Output Defaults: same as input parameters\n   * @returns A promise that resolves to a tensor object\n   */\n  fromImage(imageData: ImageData, options?: TensorFromImageOptions): Promise<Tensor>;\n\n  /**\n   * create a tensor from image object - HTMLImageElement, ImageData, ImageBitmap, URL\n   *\n   * @param imageElement - {HTMLImageElement} - since the data is stored as ImageData no need for format parameter\n   * @param options - Optional - Interface describing input image & output tensor -\n   * Input Defaults: RGBA, 3 channels, 0-255, NHWC - Output Defaults: same as input parameters\n   * @returns A promise that resolves to a tensor object\n   */\n  fromImage(imageElement: HTMLImageElement, options?: TensorFromImageOptions): Promise<Tensor>;\n\n  /**\n   * create a tensor from image object - HTMLImageElement, ImageData, ImageBitmap, URL\n   *\n   * @param url - {string} - Assuming the string is a URL to an image\n   * @param options - Optional - Interface describing input image & output tensor -\n   * Input Defaults: RGBA, 3 channels, 0-255, NHWC - Output Defaults: same as input parameters\n   * @returns A promise that resolves to a tensor object\n   */\n  fromImage(url: string, options?: TensorFromImageOptions): Promise<Tensor>;\n\n  /**\n   * create a tensor from image object - HTMLImageElement, ImageData, ImageBitmap, URL\n   *\n   * @param bitMap - {ImageBitmap} - since the data is stored as ImageData no need for format parameter\n   * @param options - NOT Optional - Interface describing input image & output tensor -\n   * Output Defaults: same as input parameters\n   * @returns A promise that resolves to a tensor object\n   */\n  fromImage(bitmap: ImageBitmap, options: TensorFromImageOptions): Promise<Tensor>;\n}\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport const Tensor = TensorImpl as TensorConstructor;\n", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\nimport {<PERSON><PERSON>and<PERSON>} from './backend';\nimport {resolveBackend} from './backend-impl';\nimport {InferenceSession as InferenceSessionInterface} from './inference-session';\nimport {OnnxValue} from './onnx-value';\nimport {Tensor} from './tensor';\n\ntype SessionOptions = InferenceSessionInterface.SessionOptions;\ntype RunOptions = InferenceSessionInterface.RunOptions;\ntype FeedsType = InferenceSessionInterface.FeedsType;\ntype FetchesType = InferenceSessionInterface.FetchesType;\ntype ReturnType = InferenceSessionInterface.ReturnType;\n\nexport class InferenceSession implements InferenceSessionInterface {\n  private constructor(handler: <PERSON><PERSON><PERSON><PERSON>) {\n    this.handler = handler;\n  }\n  run(feeds: FeedsType, options?: RunOptions): Promise<ReturnType>;\n  run(feeds: FeedsType, fetches: FetchesType, options?: RunOptions): Promise<ReturnType>;\n  async run(feeds: FeedsType, arg1?: FetchesType|RunOptions, arg2?: RunOptions): Promise<ReturnType> {\n    const fetches: {[name: string]: OnnxValue|null} = {};\n    let options: RunOptions = {};\n    // check inputs\n    if (typeof feeds !== 'object' || feeds === null || feeds instanceof Tensor || Array.isArray(feeds)) {\n      throw new TypeError(\n          '\\'feeds\\' must be an object that use input names as keys and OnnxValue as corresponding values.');\n    }\n\n    let isFetchesEmpty = true;\n    // determine which override is being used\n    if (typeof arg1 === 'object') {\n      if (arg1 === null) {\n        throw new TypeError('Unexpected argument[1]: cannot be null.');\n      }\n      if (arg1 instanceof Tensor) {\n        throw new TypeError('\\'fetches\\' cannot be a Tensor');\n      }\n\n      if (Array.isArray(arg1)) {\n        if (arg1.length === 0) {\n          throw new TypeError('\\'fetches\\' cannot be an empty array.');\n        }\n        isFetchesEmpty = false;\n        // output names\n        for (const name of arg1) {\n          if (typeof name !== 'string') {\n            throw new TypeError('\\'fetches\\' must be a string array or an object.');\n          }\n          if (this.outputNames.indexOf(name) === -1) {\n            throw new RangeError(`'fetches' contains invalid output name: ${name}.`);\n          }\n          fetches[name] = null;\n        }\n\n        if (typeof arg2 === 'object' && arg2 !== null) {\n          options = arg2;\n        } else if (typeof arg2 !== 'undefined') {\n          throw new TypeError('\\'options\\' must be an object.');\n        }\n      } else {\n        // decide whether arg1 is fetches or options\n        // if any output name is present and its value is valid OnnxValue, we consider it fetches\n        let isFetches = false;\n        const arg1Keys = Object.getOwnPropertyNames(arg1);\n        for (const name of this.outputNames) {\n          if (arg1Keys.indexOf(name) !== -1) {\n            const v = (arg1 as InferenceSessionInterface.NullableOnnxValueMapType)[name];\n            if (v === null || v instanceof Tensor) {\n              isFetches = true;\n              isFetchesEmpty = false;\n              fetches[name] = v;\n            }\n          }\n        }\n\n        if (isFetches) {\n          if (typeof arg2 === 'object' && arg2 !== null) {\n            options = arg2;\n          } else if (typeof arg2 !== 'undefined') {\n            throw new TypeError('\\'options\\' must be an object.');\n          }\n        } else {\n          options = arg1 as RunOptions;\n        }\n      }\n    } else if (typeof arg1 !== 'undefined') {\n      throw new TypeError('Unexpected argument[1]: must be \\'fetches\\' or \\'options\\'.');\n    }\n\n    // check if all inputs are in feed\n    for (const name of this.inputNames) {\n      if (typeof feeds[name] === 'undefined') {\n        throw new Error(`input '${name}' is missing in 'feeds'.`);\n      }\n    }\n\n    // if no fetches is specified, we use the full output names list\n    if (isFetchesEmpty) {\n      for (const name of this.outputNames) {\n        fetches[name] = null;\n      }\n    }\n\n    // feeds, fetches and options are prepared\n\n    const results = await this.handler.run(feeds, fetches, options);\n    const returnValue: {[name: string]: OnnxValue} = {};\n    for (const key in results) {\n      if (Object.hasOwnProperty.call(results, key)) {\n        returnValue[key] = new Tensor(results[key].type, results[key].data, results[key].dims);\n      }\n    }\n    return returnValue;\n  }\n\n  static create(path: string, options?: SessionOptions): Promise<InferenceSessionInterface>;\n  static create(buffer: ArrayBufferLike, options?: SessionOptions): Promise<InferenceSessionInterface>;\n  static create(buffer: ArrayBufferLike, byteOffset: number, byteLength?: number, options?: SessionOptions):\n      Promise<InferenceSessionInterface>;\n  static create(buffer: Uint8Array, options?: SessionOptions): Promise<InferenceSessionInterface>;\n  static async create(\n      arg0: string|ArrayBufferLike|Uint8Array, arg1?: SessionOptions|number, arg2?: number,\n      arg3?: SessionOptions): Promise<InferenceSessionInterface> {\n    // either load from a file or buffer\n    let filePathOrUint8Array: string|Uint8Array;\n    let options: SessionOptions = {};\n\n    if (typeof arg0 === 'string') {\n      filePathOrUint8Array = arg0;\n      if (typeof arg1 === 'object' && arg1 !== null) {\n        options = arg1;\n      } else if (typeof arg1 !== 'undefined') {\n        throw new TypeError('\\'options\\' must be an object.');\n      }\n    } else if (arg0 instanceof Uint8Array) {\n      filePathOrUint8Array = arg0;\n      if (typeof arg1 === 'object' && arg1 !== null) {\n        options = arg1;\n      } else if (typeof arg1 !== 'undefined') {\n        throw new TypeError('\\'options\\' must be an object.');\n      }\n    } else if (\n        arg0 instanceof ArrayBuffer ||\n        (typeof SharedArrayBuffer !== 'undefined' && arg0 instanceof SharedArrayBuffer)) {\n      const buffer = arg0;\n      let byteOffset = 0;\n      let byteLength = arg0.byteLength;\n      if (typeof arg1 === 'object' && arg1 !== null) {\n        options = arg1;\n      } else if (typeof arg1 === 'number') {\n        byteOffset = arg1;\n        if (!Number.isSafeInteger(byteOffset)) {\n          throw new RangeError('\\'byteOffset\\' must be an integer.');\n        }\n        if (byteOffset < 0 || byteOffset >= buffer.byteLength) {\n          throw new RangeError(`'byteOffset' is out of range [0, ${buffer.byteLength}).`);\n        }\n        byteLength = arg0.byteLength - byteOffset;\n        if (typeof arg2 === 'number') {\n          byteLength = arg2;\n          if (!Number.isSafeInteger(byteLength)) {\n            throw new RangeError('\\'byteLength\\' must be an integer.');\n          }\n          if (byteLength <= 0 || byteOffset + byteLength > buffer.byteLength) {\n            throw new RangeError(`'byteLength' is out of range (0, ${buffer.byteLength - byteOffset}].`);\n          }\n          if (typeof arg3 === 'object' && arg3 !== null) {\n            options = arg3;\n          } else if (typeof arg3 !== 'undefined') {\n            throw new TypeError('\\'options\\' must be an object.');\n          }\n        } else if (typeof arg2 !== 'undefined') {\n          throw new TypeError('\\'byteLength\\' must be a number.');\n        }\n      } else if (typeof arg1 !== 'undefined') {\n        throw new TypeError('\\'options\\' must be an object.');\n      }\n      filePathOrUint8Array = new Uint8Array(buffer, byteOffset, byteLength);\n    } else {\n      throw new TypeError('Unexpected argument[0]: must be \\'path\\' or \\'buffer\\'.');\n    }\n\n    // get backend hints\n    const eps = options.executionProviders || [];\n    const backendHints = eps.map(i => typeof i === 'string' ? i : i.name);\n    const backend = await resolveBackend(backendHints);\n    const handler = await backend.createSessionHandler(filePathOrUint8Array, options);\n    return new InferenceSession(handler);\n  }\n\n  startProfiling(): void {\n    this.handler.startProfiling();\n  }\n  endProfiling(): void {\n    this.handler.endProfiling();\n  }\n\n  get inputNames(): readonly string[] {\n    return this.handler.inputNames;\n  }\n  get outputNames(): readonly string[] {\n    return this.handler.outputNames;\n  }\n\n  private handler: SessionHandler;\n}\n", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\nimport {InferenceSession as InferenceSessionImpl} from './inference-session-impl';\nimport {OnnxValue} from './onnx-value';\n\n/* eslint-disable @typescript-eslint/no-redeclare */\n\nexport declare namespace InferenceSession {\n  // #region input/output types\n\n  type OnnxValueMapType = {readonly [name: string]: OnnxValue};\n  type NullableOnnxValueMapType = {readonly [name: string]: OnnxValue | null};\n\n  /**\n   * A feeds (model inputs) is an object that uses input names as keys and OnnxValue as corresponding values.\n   */\n  type FeedsType = OnnxValueMapType;\n\n  /**\n   * A fetches (model outputs) could be one of the following:\n   *\n   * - Omitted. Use model's output names definition.\n   * - An array of string indicating the output names.\n   * - An object that use output names as keys and OnnxValue or null as corresponding values.\n   *\n   * @remark\n   * different from input argument, in output, OnnxValue is optional. If an OnnxValue is present it will be\n   * used as a pre-allocated value by the inference engine; if omitted, inference engine will allocate buffer\n   * internally.\n   */\n  type FetchesType = readonly string[]|NullableOnnxValueMapType;\n\n  /**\n   * A inferencing return type is an object that uses output names as keys and OnnxValue as corresponding values.\n   */\n  type ReturnType = OnnxValueMapType;\n\n  // #endregion\n\n  // #region session options\n\n  /**\n   * A set of configurations for session behavior.\n   */\n  export interface SessionOptions {\n    /**\n     * An array of execution provider options.\n     *\n     * An execution provider option can be a string indicating the name of the execution provider,\n     * or an object of corresponding type.\n     */\n    executionProviders?: readonly ExecutionProviderConfig[];\n\n    /**\n     * The intra OP threads number.\n     *\n     * This setting is available only in ONNXRuntime (Node.js binding and react-native).\n     */\n    intraOpNumThreads?: number;\n\n    /**\n     * The inter OP threads number.\n     *\n     * This setting is available only in ONNXRuntime (Node.js binding and react-native).\n     */\n    interOpNumThreads?: number;\n\n    /**\n     * The optimization level.\n     *\n     * This setting is available only in ONNXRuntime (Node.js binding and react-native) or WebAssembly backend\n     */\n    graphOptimizationLevel?: 'disabled'|'basic'|'extended'|'all';\n\n    /**\n     * Whether enable CPU memory arena.\n     *\n     * This setting is available only in ONNXRuntime (Node.js binding and react-native) or WebAssembly backend\n     */\n    enableCpuMemArena?: boolean;\n\n    /**\n     * Whether enable memory pattern.\n     *\n     * This setting is available only in ONNXRuntime (Node.js binding and react-native) or WebAssembly backend\n     */\n    enableMemPattern?: boolean;\n\n    /**\n     * Execution mode.\n     *\n     * This setting is available only in ONNXRuntime (Node.js binding and react-native) or WebAssembly backend\n     */\n    executionMode?: 'sequential'|'parallel';\n\n    /**\n     * Wether enable profiling.\n     *\n     * This setting is a placeholder for a future use.\n     */\n    enableProfiling?: boolean;\n\n    /**\n     * File prefix for profiling.\n     *\n     * This setting is a placeholder for a future use.\n     */\n    profileFilePrefix?: string;\n\n    /**\n     * Log ID.\n     *\n     * This setting is available only in ONNXRuntime (Node.js binding and react-native) or WebAssembly backend\n     */\n    logId?: string;\n\n    /**\n     * Log severity level. See\n     * https://github.com/microsoft/onnxruntime/blob/main/include/onnxruntime/core/common/logging/severity.h\n     *\n     * This setting is available only in ONNXRuntime (Node.js binding and react-native) or WebAssembly backend\n     */\n    logSeverityLevel?: 0|1|2|3|4;\n\n    /**\n     * Log verbosity level.\n     *\n     * This setting is available only in WebAssembly backend. Will support Node.js binding and react-native later\n     */\n    logVerbosityLevel?: number;\n\n    /**\n     * Store configurations for a session. See\n     * https://github.com/microsoft/onnxruntime/blob/main/include/onnxruntime/core/session/\n     * onnxruntime_session_options_config_keys.h\n     *\n     * This setting is available only in WebAssembly backend. Will support Node.js binding and react-native later\n     *\n     * @example\n     * ```js\n     * extra: {\n     *   session: {\n     *     set_denormal_as_zero: \"1\",\n     *     disable_prepacking: \"1\"\n     *   },\n     *   optimization: {\n     *     enable_gelu_approximation: \"1\"\n     *   }\n     * }\n     * ```\n     */\n    extra?: Record<string, unknown>;\n  }\n\n  // #region execution providers\n\n  // Currently, we have the following backends to support execution providers:\n  // Backend Node.js binding: supports 'cpu' and 'cuda'.\n  // Backend WebAssembly: supports 'cpu', 'wasm' and 'xnnpack'.\n  // Backend ONNX.js: supports 'webgl'.\n  interface ExecutionProviderOptionMap {\n    cpu: CpuExecutionProviderOption;\n    cuda: CudaExecutionProviderOption;\n    wasm: WebAssemblyExecutionProviderOption;\n    webgl: WebGLExecutionProviderOption;\n    xnnpack: XnnpackExecutionProviderOption;\n  }\n\n  type ExecutionProviderName = keyof ExecutionProviderOptionMap;\n  type ExecutionProviderConfig =\n      ExecutionProviderOptionMap[ExecutionProviderName]|ExecutionProviderOption|ExecutionProviderName|string;\n\n  export interface ExecutionProviderOption {\n    readonly name: string;\n  }\n  export interface CpuExecutionProviderOption extends ExecutionProviderOption {\n    readonly name: 'cpu';\n    useArena?: boolean;\n  }\n  export interface CudaExecutionProviderOption extends ExecutionProviderOption {\n    readonly name: 'cuda';\n    deviceId?: number;\n  }\n  export interface WebAssemblyExecutionProviderOption extends ExecutionProviderOption {\n    readonly name: 'wasm';\n  }\n  export interface WebGLExecutionProviderOption extends ExecutionProviderOption {\n    readonly name: 'webgl';\n    // TODO: add flags\n  }\n  export interface XnnpackExecutionProviderOption extends ExecutionProviderOption {\n    readonly name: 'xnnpack';\n  }\n  // #endregion\n\n  // #endregion\n\n  // #region run options\n\n  /**\n   * A set of configurations for inference run behavior\n   */\n  export interface RunOptions {\n    /**\n     * Log severity level. See\n     * https://github.com/microsoft/onnxruntime/blob/main/include/onnxruntime/core/common/logging/severity.h\n     *\n     * This setting is available only in ONNXRuntime (Node.js binding and react-native) or WebAssembly backend\n     */\n    logSeverityLevel?: 0|1|2|3|4;\n\n    /**\n     * Log verbosity level.\n     *\n     * This setting is available only in WebAssembly backend. Will support Node.js binding and react-native later\n     */\n    logVerbosityLevel?: number;\n\n    /**\n     * Terminate all incomplete OrtRun calls as soon as possible if true\n     *\n     * This setting is available only in WebAssembly backend. Will support Node.js binding and react-native later\n     */\n    terminate?: boolean;\n\n    /**\n     * A tag for the Run() calls using this\n     *\n     * This setting is available only in ONNXRuntime (Node.js binding and react-native) or WebAssembly backend\n     */\n    tag?: string;\n\n    /**\n     * Set a single run configuration entry. See\n     * https://github.com/microsoft/onnxruntime/blob/main/include/onnxruntime/core/session/\n     * onnxruntime_run_options_config_keys.h\n     *\n     * This setting is available only in WebAssembly backend. Will support Node.js binding and react-native later\n     *\n     * @example\n     *\n     * ```js\n     * extra: {\n     *   memory: {\n     *     enable_memory_arena_shrinkage: \"1\",\n     *   }\n     * }\n     * ```\n     */\n    extra?: Record<string, unknown>;\n  }\n\n  // #endregion\n\n  // #region value metadata\n\n  // eslint-disable-next-line @typescript-eslint/no-empty-interface\n  interface ValueMetadata {\n    // TBD\n  }\n\n  // #endregion\n}\n\n/**\n * Represent a runtime instance of an ONNX model.\n */\nexport interface InferenceSession {\n  // #region run()\n\n  /**\n   * Execute the model asynchronously with the given feeds and options.\n   *\n   * @param feeds - Representation of the model input. See type description of `InferenceSession.InputType` for detail.\n   * @param options - Optional. A set of options that controls the behavior of model inference.\n   * @returns A promise that resolves to a map, which uses output names as keys and OnnxValue as corresponding values.\n   */\n  run(feeds: InferenceSession.FeedsType, options?: InferenceSession.RunOptions): Promise<InferenceSession.ReturnType>;\n\n  /**\n   * Execute the model asynchronously with the given feeds, fetches and options.\n   *\n   * @param feeds - Representation of the model input. See type description of `InferenceSession.InputType` for detail.\n   * @param fetches - Representation of the model output. See type description of `InferenceSession.OutputType` for\n   * detail.\n   * @param options - Optional. A set of options that controls the behavior of model inference.\n   * @returns A promise that resolves to a map, which uses output names as keys and OnnxValue as corresponding values.\n   */\n  run(feeds: InferenceSession.FeedsType, fetches: InferenceSession.FetchesType,\n      options?: InferenceSession.RunOptions): Promise<InferenceSession.ReturnType>;\n\n  // #endregion\n\n  // #region profiling\n\n  /**\n   * Start profiling.\n   */\n  startProfiling(): void;\n\n  /**\n   * End profiling.\n   */\n  endProfiling(): void;\n\n  // #endregion\n\n  // #region metadata\n\n  /**\n   * Get input names of the loaded model.\n   */\n  readonly inputNames: readonly string[];\n\n  /**\n   * Get output names of the loaded model.\n   */\n  readonly outputNames: readonly string[];\n\n  // /**\n  //  * Get input metadata of the loaded model.\n  //  */\n  // readonly inputMetadata: ReadonlyArray<Readonly<InferenceSession.ValueMetadata>>;\n\n  // /**\n  //  * Get output metadata of the loaded model.\n  //  */\n  // readonly outputMetadata: ReadonlyArray<Readonly<InferenceSession.ValueMetadata>>;\n\n  // #endregion\n}\n\nexport interface InferenceSessionFactory {\n  // #region create()\n\n  /**\n   * Create a new inference session and load model asynchronously from an ONNX model file.\n   *\n   * @param uri - The URI or file path of the model to load.\n   * @param options - specify configuration for creating a new inference session.\n   * @returns A promise that resolves to an InferenceSession object.\n   */\n  create(uri: string, options?: InferenceSession.SessionOptions): Promise<InferenceSession>;\n\n  /**\n   * Create a new inference session and load model asynchronously from an array bufer.\n   *\n   * @param buffer - An ArrayBuffer representation of an ONNX model.\n   * @param options - specify configuration for creating a new inference session.\n   * @returns A promise that resolves to an InferenceSession object.\n   */\n  create(buffer: ArrayBufferLike, options?: InferenceSession.SessionOptions): Promise<InferenceSession>;\n\n  /**\n   * Create a new inference session and load model asynchronously from segment of an array bufer.\n   *\n   * @param buffer - An ArrayBuffer representation of an ONNX model.\n   * @param byteOffset - The beginning of the specified portion of the array buffer.\n   * @param byteLength - The length in bytes of the array buffer.\n   * @param options - specify configuration for creating a new inference session.\n   * @returns A promise that resolves to an InferenceSession object.\n   */\n  create(buffer: ArrayBufferLike, byteOffset: number, byteLength?: number, options?: InferenceSession.SessionOptions):\n      Promise<InferenceSession>;\n\n  /**\n   * Create a new inference session and load model asynchronously from a Uint8Array.\n   *\n   * @param buffer - A Uint8Array representation of an ONNX model.\n   * @param options - specify configuration for creating a new inference session.\n   * @returns A promise that resolves to an InferenceSession object.\n   */\n  create(buffer: Uint8Array, options?: InferenceSession.SessionOptions): Promise<InferenceSession>;\n\n  // #endregion\n}\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport const InferenceSession: InferenceSessionFactory = InferenceSessionImpl;\n"], "names": ["root", "factory", "exports", "module", "define", "amd", "self", "__webpack_require__", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "Symbol", "toStringTag", "value", "backends", "backendsSortedByPriority", "registerBackend", "name", "backend", "priority", "init", "createSessionHandler", "TypeError", "currentBackend", "undefined", "Error", "i", "indexOf", "splice", "length", "push", "env", "constructor", "this", "wasm", "webgl", "logLevelInternal", "logLevel", "isBigInt64ArrayAvailable", "BigInt64Array", "from", "isBigUint64ArrayAvailable", "BigUint64Array", "NUMERIC_TENSOR_TYPE_TO_TYPEDARRAY_MAP", "Map", "Float32Array", "Uint8Array", "Int8Array", "Uint16Array", "Int16Array", "Int32Array", "Float64Array", "Uint32Array", "NUMERIC_TENSOR_TYPEDARRAY_TO_TYPE_MAP", "set", "Tensor", "arg0", "arg1", "arg2", "type", "data", "dims", "Array", "isArray", "typedArrayConstructor", "firstElementType", "mappedType", "size", "dim", "Number", "isSafeInteger", "RangeError", "calculateSize", "static", "buffer", "options", "height", "width", "norm", "norm<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "mean", "bias", "inputformat", "bitmapFormat", "outputformat", "tensorFormat", "offset", "float32Data", "step", "rImagePointer", "gImagePointer", "bImagePointer", "aImagePointer", "rTensorPointer", "gTensorPointer", "b<PERSON>ensor<PERSON>oint<PERSON>", "aTensorPointer", "image", "isHTMLImageEle", "HTMLImageElement", "isImageDataEle", "ImageData", "isImageBitmap", "ImageBitmap", "isURL", "String", "tensorConfig", "canvas", "document", "createElement", "pixels2DContext", "getContext", "naturalHeight", "naturalWidth", "resizedHeight", "resizedWidth", "drawImage", "getImageData", "bufferToTensor", "Promise", "resolve", "reject", "context", "newImage", "Image", "crossOrigin", "src", "onload", "img", "format", "tempCanvas", "putImageData", "toImageData", "channels", "createImageData", "reshape", "InferenceSession", "handler", "async", "feeds", "fetches", "isFetchesEmpty", "outputNames", "isFetches", "arg1Keys", "getOwnPropertyNames", "v", "inputNames", "results", "run", "returnValue", "arg3", "filePathOrUint8Array", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SharedArrayBuffer", "byteOffset", "byteLength", "backendHints", "executionProviders", "map", "backendNames", "errors", "backendName", "backendInfo", "initialized", "aborted", "isInitializing", "initPromise", "e", "err", "join", "resolveBackend", "startProfiling", "endProfiling"], "sourceRoot": ""}