{"version": 3, "file": "matmul.js", "sourceRoot": "", "sources": ["matmul.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAKlC,wCAAuD;AAEvD,oCAAsF;AACtF,oCAA0D;AAE1D,6CAAmH;AACnH,+CAAkE;AAE3D,MAAM,MAAM,GACf,CAAC,gBAAuC,EAAE,MAAgB,EAAE,UAAwC,EAAY,EAAE;IAChH,cAAc,CAAC,MAAM,CAAC,CAAC;IAEvB,IAAI,gBAAgB,CAAC,OAAO,CAAC,IAAI,EAAE;QACjC,OAAO,CAAC,gBAAgB,CAAC,GAAG,CACxB,IAAA,iDAAmC,EAAC,gBAAgB,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;KACzF;SAAM;QACL,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,6BAA6B,CAAC,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;KAC1F;AACH,CAAC,CAAC;AAVO,QAAA,MAAM,UAUb;AAEC,MAAM,qBAAqB,GAC9B,CAAC,IAAgB,EAAgC,EAAE,CAAC,IAAA,8CAAiC,EAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAD9F,QAAA,qBAAqB,yBACyE;AAE3G,MAAM,2BAA2B,GAAG,CAAC,OAAgB,EAAE,SAAiB,EAAE,EAAE,CAAC,CAAC;IAC5E,IAAI,EAAE,QAAQ;IACd,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;IACrD,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,mBAAW,CAAC,QAAQ,EAAE,mBAAW,CAAC,QAAQ,EAAE,mBAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;QACpE,CAAC,mBAAW,CAAC,QAAQ,EAAE,mBAAW,CAAC,QAAQ,CAAC;IAClE,SAAS;CACV,CAAC,CAAC;AAEH,SAAS,uBAAuB,CAC5B,QAAyB,EAAE,MAAgB,EAAE,oBAAkD;IACjG,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC9B,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC9B,MAAM,WAAW,GAAG,oBAAa,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IAClE,IAAI,CAAC,WAAW,EAAE;QAChB,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;KAC3D;IACD,MAAM,cAAc,GAAG,IAAA,yBAAiB,EAAC,WAAW,CAAC,MAAM,CAAC,CAAC;IAC7D,MAAM,aAAa,GAAG,IAAA,qBAAa,GAAE,CAAC;IACtC,MAAM,EAAC,kBAAkB,EAAE,eAAe,EAAC,GAAG,IAAA,iCAAoB,EAAC,oBAAoB,CAAC,CAAC;IAEzF,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IAClC,MAAM,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC,8BAA8B,CAAC,CAAC,CAAC,EAAE,CAAC;IAClE,MAAM,uBAAuB,GACzB,OAAO,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,cAAc,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,WAAW,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAE5G,MAAM,IAAI,GAAG,WAAW,CAAC,MAAM,CAAC;IAChC,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;IAC5B,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;IAC5B,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC5C,MAAM,YAAY,GAAG;MACjB,kBAAkB;MAClB,uBAAuB;gCACG,IAAI;gBACpB,KAAK;gBACL,KAAK;;;;;0BAKK,SAAS;gBACnB,KAAK,GAAG,CAAC;gBACT,KAAK,GAAG,CAAC;;;UAGf,WAAW;UACX,eAAe;;MAEnB,CAAC;IACL,uCACK,QAAQ,KACX,MAAM,EAAE,EAAC,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,WAAW,EAAE,mBAAW,CAAC,QAAQ,EAAC,EACpF,YAAY,IACZ;AACJ,CAAC;AAED,SAAgB,6BAA6B,CACzC,MAAgB,EAAE,oBAAkD;IACtE,MAAM,QAAQ,GAAG,2BAA2B,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;IACzG,uCAAW,QAAQ,KAAE,GAAG,EAAE,GAAG,EAAE,CAAC,uBAAuB,CAAC,QAAQ,EAAE,MAAM,EAAE,oBAAoB,CAAC,IAAE;AACnG,CAAC;AAJD,sEAIC;AAED,MAAM,cAAc,GAAG,CAAC,MAAgB,EAAQ,EAAE;IAChD,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QAClC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;KAC9C;IAED,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;QAC3F,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;KACrD;IAED,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC;QAC9D,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,EAAE;QAClE,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;KAChD;IAED,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;QACrC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;KAC9C;AACH,CAAC,CAAC;AAEF,SAAgB,gBAAgB,CAC5B,cAAsB,EAAE,aAAgC,EAAE,OAA0B,EAAE,QAA2B,EACjH,QAAiB;IACnB,IAAI,qBAAqB,GAAG,EAAE,CAAC;IAC/B,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAC9B,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC;IAChC,MAAM,QAAQ,GAAG,OAAO,GAAG,MAAM,CAAC;IAClC,IAAI,OAAO,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC,EAAE;QAC7B,qBAAqB,GAAG,QAAQ,CAAC;KAClC;SAAM;QACL,qBAAqB,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,UAAU,aAAa,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACnG;IACD,MAAM,aAAa,GAAG,oBAAa,CAAC,gBAAgB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IACxE,MAAM,aAAa,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,UAAU,aAAa,CAAC,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACtG,MAAM,MAAM,GAAG,gBAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACvC,MAAM,aAAa,GAAG,MAAM,KAAK,CAAC,CAAC;IACnC,IAAI,MAAM,GAAG,sCAAsC,CAAC;IACpD,IAAI,aAAa,EAAE;QACjB,MAAM,GAAG,qBAAqB,CAAC;KAChC;IACD,MAAM,sBAAsB,GAAG,QAAQ,CAAC,CAAC,CAAC;;IAExC,cAAc;IACd,aAAa;+BACc,qBAAqB;WACzC,MAAM;EACf,CAAC,CAAC;QACwC;;IAExC,cAAc;IACd,aAAa;;EAEf,CAAC;IAED,OAAO,sBAAsB,CAAC;AAChC,CAAC;AAnCD,4CAmCC"}