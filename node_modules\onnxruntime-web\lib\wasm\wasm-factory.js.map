{"version": 3, "file": "wasm-factory.js", "sourceRoot": "", "sources": ["wasm-factory.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGlC,2CAA6B;AAI7B,wEAAmD;AAEnD,MAAM,sBAAsB;AACxB,iEAAiE;AACjE,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC,CAAC,CAAC,qBAAc,CAAC;AAEjG,IAAI,IAA6B,CAAC;AAClC,IAAI,WAAW,GAAG,KAAK,CAAC;AACxB,IAAI,YAAY,GAAG,KAAK,CAAC;AACzB,IAAI,OAAO,GAAG,KAAK,CAAC;AAEpB,MAAM,sBAAsB,GAAG,GAAY,EAAE;IAC3C,IAAI;QACF,8EAA8E;QAC9E,IAAI,OAAO,iBAAiB,KAAK,WAAW,EAAE;YAC5C,OAAO,KAAK,CAAC;SACd;QAED,sEAAsE;QACtE,sFAAsF;QACtF,IAAI,OAAO,cAAc,KAAK,WAAW,EAAE;YACzC,IAAI,cAAc,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;SAClE;QAED,0EAA0E;QAC1E,8EAA8E;QAC9E,OAAO,WAAW,CAAC,QAAQ,CAAC,IAAI,UAAU,CAAC;YACzC,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAG,CAAC,EAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAG,EAAE,EAAE,CAAC,EAAI,CAAC,EAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAG,CAAC,EAAE,CAAC;YACpE,CAAC,EAAE,CAAC,EAAG,CAAC,EAAI,CAAC,EAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAG,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE;SACnE,CAAC,CAAC,CAAC;KACL;IAAC,OAAO,CAAC,EAAE;QACV,OAAO,KAAK,CAAC;KACd;AACH,CAAC,CAAC;AAEF,MAAM,eAAe,GAAG,GAAY,EAAE;IACpC,IAAI;QACF,uEAAuE;QACvE,0EAA0E;QAE1E,oEAAoE;QACpE,EAAE;QACF,UAAU;QACV,sBAAsB;QACtB,yBAAyB;QACzB,YAAY;QACZ,2BAA2B;QAC3B,uBAAuB;QACvB,2BAA2B;QAC3B,6EAA6E;QAE7E,OAAO,WAAW,CAAC,QAAQ,CAAC,IAAI,UAAU,CAAC;YACzC,CAAC,EAAI,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAI,EAAE,EAAG,CAAC,EAAE,EAAE,EAAE,CAAC;YACxF,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAG,CAAC,EAAG,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE;SAC1F,CAAC,CAAC,CAAC;KACL;IAAC,OAAO,CAAC,EAAE;QACV,OAAO,KAAK,CAAC;KACd;AACH,CAAC,CAAC;AAEF,MAAM,eAAe,GAAG,CAAC,OAAgB,EAAE,UAAmB,EAAE,EAAE;IAChE,IAAI,UAAU,EAAE;QACd,OAAO,OAAO,CAAC,CAAC,CAAC,6BAA6B,CAAC,CAAC,CAAC,wBAAwB,CAAC;KAC3E;SAAM;QACL,OAAO,OAAO,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,eAAe,CAAC;KACzD;AACH,CAAC,CAAC;AAEK,MAAM,qBAAqB,GAAG,KAAK,EAAC,KAA2B,EAAiB,EAAE;IACvF,IAAI,WAAW,EAAE;QACf,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;KAC1B;IACD,IAAI,YAAY,EAAE;QAChB,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;KAC5E;IACD,IAAI,OAAO,EAAE;QACX,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;KACzE;IAED,YAAY,GAAG,IAAI,CAAC;IAEpB,qCAAqC;IACrC,MAAM,OAAO,GAAG,KAAK,CAAC,WAAY,CAAC;IACnC,MAAM,UAAU,GAAG,KAAK,CAAC,UAAW,CAAC;IACrC,MAAM,IAAI,GAAG,KAAK,CAAC,IAAK,CAAC;IAEzB,MAAM,UAAU,GAAG,UAAU,GAAG,CAAC,IAAI,sBAAsB,EAAE,CAAC;IAC9D,MAAM,OAAO,GAAG,IAAI,IAAI,eAAe,EAAE,CAAC;IAE1C,MAAM,kBAAkB,GAAG,OAAO,KAAK,CAAC,SAAS,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC;IAC7F,MAAM,YAAY,GAAG,eAAe,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;IACxD,MAAM,oBAAoB,GAAG,eAAe,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;IAClE,MAAM,gBAAgB,GAAG,OAAO,KAAK,CAAC,SAAS,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IAEjH,IAAI,SAAS,GAAG,KAAK,CAAC;IAEtB,MAAM,KAAK,GAAyB,EAAE,CAAC;IAEvC,sBAAsB;IACtB,IAAI,OAAO,GAAG,CAAC,EAAE;QACf,KAAK,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACjC,UAAU,CAAC,GAAG,EAAE;gBACd,SAAS,GAAG,IAAI,CAAC;gBACjB,OAAO,EAAE,CAAC;YACZ,CAAC,EAAE,OAAO,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC;KACL;IAED,oCAAoC;IACpC,KAAK,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACzC,MAAM,OAAO,GAAG,UAAU,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,qBAAc,CAAC;QACrE,MAAM,MAAM,GAA2B;YACrC,UAAU,EAAE,CAAC,QAAgB,EAAE,eAAuB,EAAE,EAAE;gBACxD,IAAI,CAAC,UAAU,CAAC,mBAAmB,IAAI,UAAU,IAAI,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC;oBAChF,OAAO,IAAI,KAAK,WAAW,EAAE;oBAC/B,OAAO,GAAG,CAAC,eAAe,CAAC,IAAI,IAAI,CAC/B;wBACE,qGAAqG;wBACrG,iEAAiE;wBACjE,OAAO,CAAC,uCAAuC,CAAC;qBACjD,EACD,EAAC,IAAI,EAAE,iBAAiB,EAAC,CAAC,CAAC,CAAC;iBACjC;gBAED,IAAI,QAAQ,KAAK,YAAY,EAAE;oBAC7B,MAAM,MAAM,GAAW,kBAAkB,aAAlB,kBAAkB,cAAlB,kBAAkB,GAAI,eAAe,CAAC;oBAC7D,OAAO,gBAAgB,aAAhB,gBAAgB,cAAhB,gBAAgB,GAAI,MAAM,GAAG,oBAAoB,CAAC;iBAC1D;gBAED,OAAO,eAAe,GAAG,QAAQ,CAAC;YACpC,CAAC;SACF,CAAC;QAEF,IAAI,CAAC,UAAU,CAAC,mBAAmB,IAAI,UAAU,EAAE;YACjD,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;gBAC/B,MAAM,CAAC,mBAAmB,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,sBAAsB,CAAC,CAAC;aAC3E;iBAAM;gBACL,MAAM,gBAAgB,GAAG,yDAAyD,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC;gBAC5G,MAAM,CAAC,mBAAmB,GAAG,IAAI,IAAI,CAAC,CAAC,gBAAgB,CAAC,EAAE,EAAC,IAAI,EAAE,iBAAiB,EAAC,CAAC,CAAC;aACtF;SACF;QAED,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI;QAChB,uCAAuC;QACvC,MAAM,CAAC,EAAE;YACP,YAAY,GAAG,KAAK,CAAC;YACrB,WAAW,GAAG,IAAI,CAAC;YACnB,IAAI,GAAG,MAAM,CAAC;YACd,OAAO,EAAE,CAAC;QACZ,CAAC;QACD,mCAAmC;QACnC,CAAC,IAAI,EAAE,EAAE;YACP,YAAY,GAAG,KAAK,CAAC;YACrB,OAAO,GAAG,IAAI,CAAC;YACf,MAAM,CAAC,IAAI,CAAC,CAAC;QACf,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC;IAEJ,MAAM,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAE1B,IAAI,SAAS,EAAE;QACb,MAAM,IAAI,KAAK,CAAC,2DAA2D,OAAO,IAAI,CAAC,CAAC;KACzF;AACH,CAAC,CAAC;AA/FW,QAAA,qBAAqB,yBA+FhC;AAEK,MAAM,WAAW,GAAG,GAAkB,EAAE;IAC7C,IAAI,WAAW,IAAI,IAAI,EAAE;QACvB,OAAO,IAAI,CAAC;KACb;IAED,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;AACzD,CAAC,CAAC;AANW,QAAA,WAAW,eAMtB;AAEK,MAAM,OAAO,GAAG,GAAS,EAAE;;IAChC,IAAI,WAAW,IAAI,CAAC,YAAY,IAAI,CAAC,OAAO,EAAE;QAC5C,YAAY,GAAG,IAAI,CAAC;QAEpB,MAAC,IAA8B,CAAC,OAAO,0CAAE,mBAAmB,EAAE,CAAC;QAC/D,IAAI,GAAG,SAAS,CAAC;QAEjB,YAAY,GAAG,KAAK,CAAC;QACrB,WAAW,GAAG,KAAK,CAAC;QACpB,OAAO,GAAG,IAAI,CAAC;KAChB;AACH,CAAC,CAAC;AAXW,QAAA,OAAO,WAWlB"}