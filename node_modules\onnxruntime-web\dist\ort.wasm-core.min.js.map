{"version": 3, "file": "ort.wasm-core.min.js", "mappings": ";;;;;CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAa,IAAID,IAEjBD,EAAU,IAAIC,GACf,CATD,CASGK,MAAM,I,4HCPT,MAAMC,EAAW,CAAC,EACZC,EAA2B,GAWpBC,EAAkB,CAACC,EAAMC,EAASC,KAC3C,IAAID,GAAmC,mBAAjBA,EAAQE,MAA+D,mBAAjCF,EAAQG,qBA6BpE,MAAM,IAAIC,UAAU,uBA7BpB,CACI,MAAMC,EAAiBT,EAASG,GAChC,QAAuBO,IAAnBD,EACAT,EAASG,GAAQ,CAAEC,UAASC,gBAE3B,IAAII,EAAeJ,SAAWA,EAE/B,OAEC,GAAII,EAAeJ,WAAaA,GAC7BI,EAAeL,UAAYA,EAC3B,MAAM,IAAIO,MAAM,4BAA4BR,qBAAwBE,IAE5E,CACA,GAAIA,GAAY,EAAG,CACf,MAAMO,EAAIX,EAAyBY,QAAQV,IAChC,IAAPS,GACAX,EAAyBa,OAAOF,EAAG,GAEvC,IAAK,IAAIA,EAAI,EAAGA,EAAIX,EAAyBc,OAAQH,IACjD,GAAIZ,EAASC,EAAyBW,IAAIP,UAAYA,EAElD,YADAJ,EAAyBa,OAAOF,EAAG,EAAGT,GAI9CF,EAAyBe,KAAKb,EAClC,CAEJ,CAC0C,ECtCjCc,EAAM,ICJZ,MACHC,cACIC,KAAKC,KAAO,CAAC,EACbD,KAAKE,MAAQ,CAAC,EACdF,KAAKG,iBAAmB,SAC5B,CAEIC,aAASC,GACT,QAAcd,IAAVc,EAAJ,CAGA,GAAqB,iBAAVA,IAA2F,IAArE,CAAC,UAAW,OAAQ,UAAW,QAAS,SAASX,QAAQW,GACtF,MAAM,IAAIb,MAAM,8BAA8Ba,KAElDL,KAAKG,iBAAmBE,CAJxB,CAKJ,CACID,eACA,OAAOJ,KAAKG,gBAChB,GClBEG,EAAoD,oBAAlBC,eAA+D,mBAAvBA,cAAcC,KACxFC,EAAsD,oBAAnBC,gBAAiE,mBAAxBA,eAAeF,KAE3FG,EAAwC,IAAIC,IAAI,CAClD,CAAC,UAAWC,cACZ,CAAC,QAASC,YACV,CAAC,OAAQC,WACT,CAAC,SAAUC,aACX,CAAC,QAASC,YACV,CAAC,QAASC,YACV,CAAC,OAAQJ,YACT,CAAC,UAAWK,cACZ,CAAC,SAAUC,eAGTC,EAAwC,IAAIT,IAAI,CAClD,CAACC,aAAc,WACf,CAACC,WAAY,SACb,CAACC,UAAW,QACZ,CAACC,YAAa,UACd,CAACC,WAAY,SACb,CAACC,WAAY,SACb,CAACC,aAAc,WACf,CAACC,YAAa,YAEdd,IACAK,EAAsCW,IAAI,QAASf,eACnDc,EAAsCC,IAAIf,cAAe,UAEzDE,IACAE,EAAsCW,IAAI,SAAUZ,gBACpDW,EAAsCC,IAAIZ,eAAgB,WAqBvD,MAAMa,EACTxB,YAAYyB,EAAMC,EAAMC,GACpB,IAAIC,EACAC,EACAC,EAEJ,GAAoB,iBAATL,EAMP,GAFAG,EAAOH,EACPK,EAAOH,EACM,WAATF,EAAmB,CAEnB,IAAKM,MAAMC,QAAQN,GACf,MAAM,IAAIpC,UAAU,kDAIxBuC,EAAOH,CACX,KACK,CAED,MAAMO,EAAwBrB,EAAsCsB,IAAIT,GACxE,QAA8BjC,IAA1ByC,EACA,MAAM,IAAI3C,UAAU,4BAA4BmC,MAEpD,GAAIM,MAAMC,QAAQN,GAKdG,EAAOI,EAAsBxB,KAAKiB,OAEjC,MAAIA,aAAgBO,GAIrB,MAAM,IAAI3C,UAAU,KAAKsC,mCAAsCK,KAH/DJ,EAAOH,CAIX,CACJ,MAOA,GADAI,EAAOJ,EACHK,MAAMC,QAAQP,GAAO,CAErB,GAAoB,IAAhBA,EAAK5B,OACL,MAAM,IAAIP,UAAU,uDAExB,MAAM6C,SAA0BV,EAAK,GACrC,GAAyB,WAArBU,EACAP,EAAO,SACPC,EAAOJ,MAEN,IAAyB,YAArBU,EAQL,MAAM,IAAI7C,UAAU,uCAAuC6C,MAP3DP,EAAO,OAIPC,EAAOd,WAAWN,KAAKgB,EAI3B,CACJ,KACK,CAED,MAAMW,EAAad,EAAsCY,IAAIT,EAAKzB,aAClE,QAAmBR,IAAf4C,EACA,MAAM,IAAI9C,UAAU,qCAAqCmC,EAAKzB,gBAElE4B,EAAOQ,EACPP,EAAOJ,CACX,CAGJ,QAAajC,IAATsC,EAEAA,EAAO,CAACD,EAAKhC,aAEZ,IAAKkC,MAAMC,QAAQF,GACpB,MAAM,IAAIxC,UAAU,0CAGxB,MAAM+C,EArGQ,CAACP,IACnB,IAAIO,EAAO,EACX,IAAK,IAAI3C,EAAI,EAAGA,EAAIoC,EAAKjC,OAAQH,IAAK,CAClC,MAAM4C,EAAMR,EAAKpC,GACjB,GAAmB,iBAAR4C,IAAqBC,OAAOC,cAAcF,GACjD,MAAM,IAAIhD,UAAU,QAAQI,+BAA+B4C,KAE/D,GAAIA,EAAM,EACN,MAAM,IAAIG,WAAW,QAAQ/C,2CAA2C4C,KAE5ED,GAAQC,CACZ,CACA,OAAOD,CAAI,EAyFMK,CAAcZ,GAC3B,GAAIO,IAASR,EAAKhC,OACd,MAAM,IAAIJ,MAAM,iBAAiB4C,iCAAoCR,EAAKhC,YAE9EI,KAAK6B,KAAOA,EACZ7B,KAAK2B,KAAOA,EACZ3B,KAAK4B,KAAOA,EACZ5B,KAAKoC,KAAOA,CAChB,CASAM,sBAAsBC,EAAQC,GAC1B,QAAerD,IAAXoD,EACA,MAAM,IAAInD,MAAM,gCAEpB,QAAuBD,IAAnBqD,EAAQC,aAA0CtD,IAAlBqD,EAAQE,MACxC,MAAM,IAAItD,MAAM,0CAEpB,MAAM,OAAEqD,EAAM,MAAEC,GAAUF,EACpBG,EAAOH,EAAQG,KACrB,IAAIC,EACAC,EAEAD,OADSzD,IAATwD,QAAoCxD,IAAdwD,EAAKG,KAChB,IAGAH,EAAKG,KAGhBD,OADS1D,IAATwD,QAAoCxD,IAAdwD,EAAKI,KAChB,EAGAJ,EAAKI,KAEpB,MAAMC,OAAuC7D,IAAzBqD,EAAQS,aAA6BT,EAAQS,aAAe,OAE1EC,OAAwC/D,IAAzBqD,EAAQW,mBACChE,IAAzBqD,EAAQW,aAA6BX,EAAQW,aAC9C,MACEC,EAASX,EAASC,EAClBW,EAA+B,SAAjBH,EAA0B,IAAIzC,aAAsB,EAAT2C,GAAc,IAAI3C,aAAsB,EAAT2C,GAE9F,IAAIE,EAAO,EAAGC,EAAgB,EAAGC,EAAgB,EAAGC,EAAgB,EAAGC,EAAgB,EACnFC,EAAiB,EAAGC,EAAiBR,EAAQS,EAA0B,EAATT,EAAYU,GAAkB,EAE5E,QAAhBd,IACAM,EAAO,EACPC,EAAgB,EAChBC,EAAgB,EAChBC,EAAgB,EAChBC,GAAiB,GAGA,SAAjBR,EACAY,EAA0B,EAATV,EAEK,QAAjBF,GACLS,EAAiB,EACjBE,EAAiBT,EACjBQ,EAA0B,EAATR,GAEK,QAAjBF,IACLW,EAAiB,EACjBD,EAAiBR,EACjBO,EAA0B,EAATP,GAErB,IAAK,IAAI/D,EAAI,EAAGA,EAAI+D,EAAQ/D,IAAKkE,GAAiBD,EAAMG,GAAiBH,EAAME,GAAiBF,EAAMI,GAAiBJ,EACnHD,EAAYM,MAAqBpB,EAAOgB,GAAiBV,GAAYD,EACrES,EAAYO,MAAqBrB,EAAOiB,GAAiBX,GAAYD,EACrES,EAAYQ,MAAqBtB,EAAOkB,GAAiBZ,GAAYD,GAC7C,IAApBkB,IAA4C,IAAnBJ,IACzBL,EAAYS,MAAqBvB,EAAOmB,GAAiBb,GAAYD,GAM7E,OAF+C,IAAIzB,EAAO,UAAWkC,EAA/B,SAAjBH,EAA6D,CAAC,EAAG,EAAGT,EAAQC,GAC1D,CAAC,EAAG,EAAGD,EAAQC,GAE1D,CACAJ,uBAAuByB,EAAOvB,GAE1B,MAAMwB,EAA+C,oBAAvB,kBAAsCD,aAAiBE,iBAC/EC,EAAwC,oBAAhB,WAA+BH,aAAiBI,UACxEC,EAAyC,oBAAlB,aAAiCL,aAAiBM,YACzEC,EAA4B,oBAAb,SAA6BP,aAAiBQ,QAA2B,iBAAVR,GACpF,IAAIvC,EACAgD,EAAe,CAAC,EAEpB,GAAIR,EAAgB,CAEhB,MAAMS,EAASC,SAASC,cAAc,UAChCC,EAAkBH,EAAOI,WAAW,MAC1C,GAAuB,MAAnBD,EAuCA,MAAM,IAAIxF,MAAM,6BAvCS,CACzB,IAAIqD,EAASsB,EAAMe,cACfpC,EAAQqB,EAAMgB,aAKlB,QAJgB5F,IAAZqD,QAAmDrD,IAA1BqD,EAAQwC,oBAAwD7F,IAAzBqD,EAAQyC,eACxExC,EAASD,EAAQwC,cACjBtC,EAAQF,EAAQyC,mBAEJ9F,IAAZqD,EAAuB,CAEvB,GADAgC,EAAehC,OACcrD,IAAzBqD,EAAQW,aACR,MAAM,IAAI/D,MAAM,+DAKpB,GAFIoF,EAAarB,aAAe,YAEThE,IAAnBqD,EAAQC,QAAwBD,EAAQC,SAAWA,EACnD,MAAM,IAAIrD,MAAM,mEAKpB,GAFIoF,EAAa/B,OAASA,OAEJtD,IAAlBqD,EAAQE,OAAuBF,EAAQE,QAAUA,EACjD,MAAM,IAAItD,MAAM,iEAGhBoF,EAAa9B,MAAQA,CAE7B,MAEI8B,EAAarB,aAAe,OAC5BqB,EAAa/B,OAASA,EACtB+B,EAAa9B,MAAQA,EAEzB+B,EAAO/B,MAAQA,EACf+B,EAAOhC,OAASA,EAChBmC,EAAgBM,UAAUnB,EAAO,EAAG,EAAGrB,EAAOD,GAC9CjB,EAAOoD,EAAgBO,aAAa,EAAG,EAAGzC,EAAOD,GAAQjB,IAC7D,CAIJ,KACK,KAAI0C,EA4CJ,IAAIE,EAAe,CAEpB,QAAgBjF,IAAZqD,EACA,MAAM,IAAIpD,MAAM,2DAEpB,QAA6BD,IAAzBqD,EAAQS,aACR,MAAM,IAAI7D,MAAM,6DAEpB,MAAMwF,EAAkBF,SAASC,cAAc,UAAUE,WAAW,MACpE,GAAuB,MAAnBD,EAAyB,CACzB,MAAMnC,EAASsB,EAAMtB,OACfC,EAAQqB,EAAMrB,MAGpB,GAFAkC,EAAgBM,UAAUnB,EAAO,EAAG,EAAGrB,EAAOD,GAC9CjB,EAAOoD,EAAgBO,aAAa,EAAG,EAAGzC,EAAOD,GAAQjB,UACzCrC,IAAZqD,EAAuB,CAEvB,QAAuBrD,IAAnBqD,EAAQC,QAAwBD,EAAQC,SAAWA,EACnD,MAAM,IAAIrD,MAAM,8DAMpB,GAHIoF,EAAa/B,OAASA,OAGJtD,IAAlBqD,EAAQE,OAAuBF,EAAQE,QAAUA,EACjD,MAAM,IAAItD,MAAM,4DAGhBoF,EAAa9B,MAAQA,CAE7B,MAEI8B,EAAa/B,OAASA,EACtB+B,EAAa9B,MAAQA,EAEzB,OAAOvB,EAAOiE,eAAe5D,EAAMgD,EACvC,CAEI,MAAM,IAAIpF,MAAM,4BAExB,CACK,GAAIkF,EACL,OAAO,IAAIe,SAAQ,CAACC,EAASC,KACzB,MAAMd,EAASC,SAASC,cAAc,UAChCa,EAAUf,EAAOI,WAAW,MAClC,IAAKd,IAAUyB,EACX,OAAOD,IAEX,MAAME,EAAW,IAAIC,MACrBD,EAASE,YAAc,YACvBF,EAASG,IAAM7B,EACf0B,EAASI,OAAS,KACdpB,EAAO/B,MAAQ+C,EAAS/C,MACxB+B,EAAOhC,OAASgD,EAAShD,OACzB+C,EAAQN,UAAUO,EAAU,EAAG,EAAGhB,EAAO/B,MAAO+B,EAAOhC,QACvD,MAAMqD,EAAMN,EAAQL,aAAa,EAAG,EAAGV,EAAO/B,MAAO+B,EAAOhC,QAC5D,QAAgBtD,IAAZqD,EAAuB,CAEvB,QAAuBrD,IAAnBqD,EAAQC,QAAwBD,EAAQC,SAAWgC,EAAOhC,OAC1D,MAAM,IAAIrD,MAAM,8DAMpB,GAHIoF,EAAa/B,OAASgC,EAAOhC,YAGXtD,IAAlBqD,EAAQE,OAAuBF,EAAQE,QAAU+B,EAAO/B,MACxD,MAAM,IAAItD,MAAM,4DAGhBoF,EAAa9B,MAAQ+B,EAAO/B,KAEpC,MAEI8B,EAAa/B,OAASgC,EAAOhC,OAC7B+B,EAAa9B,MAAQ+B,EAAO/B,MAEhC4C,EAAQnE,EAAOiE,eAAeU,EAAItE,KAAMgD,GAAc,CACzD,IAIL,MAAM,IAAIpF,MAAM,iEACpB,CA7HyB,CAErB,MAAM2G,EAAS,OACf,IAAItD,EACAC,EASJ,QARgBvD,IAAZqD,QAAkDrD,IAAzBqD,EAAQyC,mBAAwD9F,IAA1BqD,EAAQwC,eACvEvC,EAASD,EAAQwC,cACjBtC,EAAQF,EAAQyC,eAGhBxC,EAASsB,EAAMtB,OACfC,EAAQqB,EAAMrB,YAEFvD,IAAZqD,EAAuB,CAEvB,GADAgC,EAAehC,OACcrD,IAAzBqD,EAAQS,cAA8BT,EAAQS,eAAiB8C,EAC/D,MAAM,IAAI3G,MAAM,wDAGhBoF,EAAavB,aAAe,MAEpC,MAEIuB,EAAavB,aAAe,OAIhC,GAFAuB,EAAa/B,OAASA,EACtB+B,EAAa9B,MAAQA,OACLvD,IAAZqD,EAAuB,CACvB,MAAMwD,EAAatB,SAASC,cAAc,UAC1CqB,EAAWtD,MAAQA,EACnBsD,EAAWvD,OAASA,EACpB,MAAMmC,EAAkBoB,EAAWnB,WAAW,MAC9C,GAAuB,MAAnBD,EAKA,MAAM,IAAIxF,MAAM,6BAJhBwF,EAAgBqB,aAAalC,EAAO,EAAG,GACvCvC,EAAOoD,EAAgBO,aAAa,EAAG,EAAGzC,EAAOD,GAAQjB,IAKjE,MAEIA,EAAOuC,EAAMvC,IAErB,CAkFA,CACA,QAAarC,IAATqC,EACA,OAAOL,EAAOiE,eAAe5D,EAAMgD,GAGnC,MAAM,IAAIpF,MAAM,iEAExB,CACA8G,YAAY1D,GACR,IAAI2D,EAAIC,EACR,MAAMxB,EAAkBF,SAASC,cAAc,UAAUE,WAAW,MACpE,IAAId,EACJ,GAAuB,MAAnBa,EAoDA,MAAM,IAAIxF,MAAM,6BApDS,CAEzB,MAAMsD,EAAQ9C,KAAK6B,KAAK,GAClBgB,EAAS7C,KAAK6B,KAAK,GACnB4E,EAAWzG,KAAK6B,KAAK,GACrBuB,OAA0B7D,IAAZqD,QAA4CrD,IAAnBqD,EAAQuD,OAAuBvD,EAAQuD,OAAkB,MAChGnD,OAAuBzD,IAAZqD,QAAgGrD,KAA9C,QAAvBgH,EAAK3D,EAAQG,YAAyB,IAAPwD,OAAgB,EAASA,EAAGrD,MAAsBN,EAAQG,KAAKG,KAAc,IAClJD,OAAuB1D,IAAZqD,QAAgGrD,KAA9C,QAAvBiH,EAAK5D,EAAQG,YAAyB,IAAPyD,OAAgB,EAASA,EAAGrD,MAAsBP,EAAQG,KAAKI,KAAY,EAChJK,EAASX,EAASC,EACxB,QAAgBvD,IAAZqD,EAAuB,CACvB,QAAuBrD,IAAnBqD,EAAQC,QAAwBD,EAAQC,SAAWA,EACnD,MAAM,IAAIrD,MAAM,0DAEpB,QAAsBD,IAAlBqD,EAAQE,OAAuBF,EAAQE,QAAUA,EACjD,MAAM,IAAItD,MAAM,wDAEpB,QAAuBD,IAAnBqD,EAAQuD,QAAsC,IAAbM,GAAqC,SAAnB7D,EAAQuD,QAC7C,IAAbM,GAAsC,QAAnB7D,EAAQuD,QAAuC,QAAnBvD,EAAQuD,OACxD,MAAM,IAAI3G,MAAM,gDAExB,CAEA,MAAMkE,EAAO,EACb,IAAIC,EAAgB,EAAGC,EAAgB,EAAGC,EAAgB,EAAGC,EAAgB,EACzEC,EAAiB,EAAGC,EAAiBR,EAAQS,EAA0B,EAATT,EAAYU,GAAkB,EAE5E,SAAhBd,GACAW,EAAiB,EACjBC,EAAiBR,EACjBS,EAA0B,EAATT,EACjBU,EAA0B,EAATV,GAEI,QAAhBJ,GACLW,EAAiB,EACjBC,EAAiBR,EACjBS,EAA0B,EAATT,GAEI,QAAhBJ,IACLW,EAAiB,EACjBE,EAAiBT,EACjBQ,EAA0B,EAATR,GAErBW,EAAQa,EAAgB0B,gBAAgB5D,EAAOD,GAC/C,IAAK,IAAIpD,EAAI,EAAGA,EAAIoD,EAASC,EAAOa,GAAiBD,EAAME,GAAiBF,EAAMG,GAAiBH,EAAMI,GAAiBJ,EAAMjE,IAC5H0E,EAAMvC,KAAK+B,IAAkB3D,KAAK4B,KAAKmC,KAAoBd,GAAYD,EACvEmB,EAAMvC,KAAKgC,IAAkB5D,KAAK4B,KAAKoC,KAAoBf,GAAYD,EACvEmB,EAAMvC,KAAKiC,IAAkB7D,KAAK4B,KAAKqC,KAAoBhB,GAAYD,EACvEmB,EAAMvC,KAAKkC,IACa,IAApBI,EAAwB,KAAOlE,KAAK4B,KAAKsC,KAAoBjB,GAAYD,CAErF,CAIA,OAAOmB,CACX,CAGAwC,QAAQ9E,GACJ,OAAO,IAAIN,EAAOvB,KAAK2B,KAAM3B,KAAK4B,KAAMC,EAC5C,EC1dG,MAAM,EAASN,ECAf,MAAMqF,EACT7G,YAAY8G,GACR7G,KAAK6G,QAAUA,CACnB,CACAC,UAAUC,EAAOtF,EAAMC,GACnB,MAAMsF,EAAU,CAAC,EACjB,IAAIpE,EAAU,CAAC,EAEf,GAAqB,iBAAVmE,GAAgC,OAAVA,GAAkBA,aAAiB,GAAUjF,MAAMC,QAAQgF,GACxF,MAAM,IAAI1H,UAAU,iGAExB,IAAI4H,GAAiB,EAErB,GAAoB,iBAATxF,EAAmB,CAC1B,GAAa,OAATA,EACA,MAAM,IAAIpC,UAAU,2CAExB,GAAIoC,aAAgB,EAChB,MAAM,IAAIpC,UAAU,gCAExB,GAAIyC,MAAMC,QAAQN,GAAO,CACrB,GAAoB,IAAhBA,EAAK7B,OACL,MAAM,IAAIP,UAAU,uCAExB4H,GAAiB,EAEjB,IAAK,MAAMjI,KAAQyC,EAAM,CACrB,GAAoB,iBAATzC,EACP,MAAM,IAAIK,UAAU,kDAExB,IAAwC,IAApCW,KAAKkH,YAAYxH,QAAQV,GACzB,MAAM,IAAIwD,WAAW,2CAA2CxD,MAEpEgI,EAAQhI,GAAQ,IACpB,CACA,GAAoB,iBAAT0C,GAA8B,OAATA,EAC5BkB,EAAUlB,OAET,QAAoB,IAATA,EACZ,MAAM,IAAIrC,UAAU,+BAE5B,KACK,CAGD,IAAI8H,GAAY,EAChB,MAAMC,EAAWC,OAAOC,oBAAoB7F,GAC5C,IAAK,MAAMzC,KAAQgB,KAAKkH,YACpB,IAAgC,IAA5BE,EAAS1H,QAAQV,GAAc,CAC/B,MAAMuI,EAAI9F,EAAKzC,IACL,OAANuI,GAAcA,aAAa,KAC3BJ,GAAY,EACZF,GAAiB,EACjBD,EAAQhI,GAAQuI,EAExB,CAEJ,GAAIJ,GACA,GAAoB,iBAATzF,GAA8B,OAATA,EAC5BkB,EAAUlB,OAET,QAAoB,IAATA,EACZ,MAAM,IAAIrC,UAAU,qCAIxBuD,EAAUnB,CAElB,CACJ,MACK,QAAoB,IAATA,EACZ,MAAM,IAAIpC,UAAU,2DAGxB,IAAK,MAAML,KAAQgB,KAAKwH,WACpB,QAA2B,IAAhBT,EAAM/H,GACb,MAAM,IAAIQ,MAAM,UAAUR,6BAIlC,GAAIiI,EACA,IAAK,MAAMjI,KAAQgB,KAAKkH,YACpBF,EAAQhI,GAAQ,KAIxB,MAAMyI,QAAgBzH,KAAK6G,QAAQa,IAAIX,EAAOC,EAASpE,GACjD+E,EAAc,CAAC,EACrB,IAAK,MAAMC,KAAOH,EACVJ,OAAOQ,eAAeC,KAAKL,EAASG,KACpCD,EAAYC,GAAO,IAAI,EAAOH,EAAQG,GAAKjG,KAAM8F,EAAQG,GAAKhG,KAAM6F,EAAQG,GAAK/F,OAGzF,OAAO8F,CACX,CACAjF,oBAAoBlB,EAAMC,EAAMC,EAAMqG,GAElC,IAAIC,EACApF,EAAU,CAAC,EACf,GAAoB,iBAATpB,GAEP,GADAwG,EAAuBxG,EACH,iBAATC,GAA8B,OAATA,EAC5BmB,EAAUnB,OAET,QAAoB,IAATA,EACZ,MAAM,IAAIpC,UAAU,qCAGvB,GAAImC,aAAgBV,YAErB,GADAkH,EAAuBxG,EACH,iBAATC,GAA8B,OAATA,EAC5BmB,EAAUnB,OAET,QAAoB,IAATA,EACZ,MAAM,IAAIpC,UAAU,oCAGvB,MAAImC,aAAgByG,aACS,oBAAtBC,mBAAqC1G,aAAgB0G,mBAyC7D,MAAM,IAAI7I,UAAU,uDAzC6D,CACjF,MAAMsD,EAASnB,EACf,IAAI2G,EAAa,EACbC,EAAa5G,EAAK4G,WACtB,GAAoB,iBAAT3G,GAA8B,OAATA,EAC5BmB,EAAUnB,OAET,GAAoB,iBAATA,EAAmB,CAE/B,GADA0G,EAAa1G,GACRa,OAAOC,cAAc4F,GACtB,MAAM,IAAI3F,WAAW,oCAEzB,GAAI2F,EAAa,GAAKA,GAAcxF,EAAOyF,WACvC,MAAM,IAAI5F,WAAW,oCAAoCG,EAAOyF,gBAGpE,GADAA,EAAa5G,EAAK4G,WAAaD,EACX,iBAATzG,EAAmB,CAE1B,GADA0G,EAAa1G,GACRY,OAAOC,cAAc6F,GACtB,MAAM,IAAI5F,WAAW,oCAEzB,GAAI4F,GAAc,GAAKD,EAAaC,EAAazF,EAAOyF,WACpD,MAAM,IAAI5F,WAAW,oCAAoCG,EAAOyF,WAAaD,OAEjF,GAAoB,iBAATJ,GAA8B,OAATA,EAC5BnF,EAAUmF,OAET,QAAoB,IAATA,EACZ,MAAM,IAAI1I,UAAU,+BAE5B,MACK,QAAoB,IAATqC,EACZ,MAAM,IAAIrC,UAAU,iCAE5B,MACK,QAAoB,IAAToC,EACZ,MAAM,IAAIpC,UAAU,gCAExB2I,EAAuB,IAAIlH,WAAW6B,EAAQwF,EAAYC,EAC9D,CAGA,CAEA,MACMC,GADMzF,EAAQ0F,oBAAsB,IACjBC,KAAI9I,GAAkB,iBAANA,EAAiBA,EAAIA,EAAET,OAC1DC,OLlHgB6H,OAAOuB,IACjC,MAAMG,EAAuC,IAAxBH,EAAazI,OAAed,EAA2BuJ,EACtEI,EAAS,GACf,IAAK,MAAMC,KAAeF,EAAc,CACpC,MAAMG,EAAc9J,EAAS6J,GAC7B,GAAIC,EAAa,CACb,GAAIA,EAAYC,YACZ,OAAOD,EAAY1J,QAElB,GAAI0J,EAAYE,QACjB,SAEJ,MAAMC,IAAmBH,EAAYI,YACrC,IAMI,OALKD,IACDH,EAAYI,YAAcJ,EAAY1J,QAAQE,cAE5CwJ,EAAYI,YAClBJ,EAAYC,aAAc,EACnBD,EAAY1J,OAUvB,CARA,MAAO+J,GACEF,GACDL,EAAO5I,KAAK,CAAEb,KAAM0J,EAAaO,IAAKD,IAE1CL,EAAYE,SAAU,CAC1B,CACA,eACWF,EAAYI,WACvB,CACJ,CACJ,CACA,MAAM,IAAIvJ,MAAM,oCAAoCiJ,EAAOF,KAAIS,GAAK,IAAIA,EAAEhK,SAASgK,EAAEC,QAAOC,KAAK,QAAQ,EKkF/EC,CAAed,GAC/BxB,QAAgB5H,EAAQG,qBAAqB4I,EAAsBpF,GACzE,OAAO,IAAIgE,EAAiBC,EAChC,CACAuC,iBACIpJ,KAAK6G,QAAQuC,gBACjB,CACAC,eACIrJ,KAAK6G,QAAQwC,cACjB,CACI7B,iBACA,OAAOxH,KAAK6G,QAAQW,UACxB,CACIN,kBACA,OAAOlH,KAAK6G,QAAQK,WACxB,ECnLG,MAAM,EAAmBN,C,oBCF1B0C,WADFC,GAEqCD,YADnCA,WAAiC,oBAAbxE,UAA4BA,SAAS0E,cAAgB1E,SAAS0E,cAAcxD,SAAMzG,I,YAEnG,SACAgK,GAIT,IAAIE,EAA2DC,EAAGC,EAHhEJ,EAAUA,GAAW,CAAC,EAGlBE,IAAIA,OAAqB,IAAZF,EAA0BA,EAAU,CAAC,GAAaE,EAAEG,MAAM,IAAInE,SAAQ,SAASoE,EAAEC,GAAGJ,EAAGG,EAAEF,EAAGG,CAAC,IAAG,IAA6OC,EAAGC,EAAEzC,EAAE0C,EAAGC,EAAEC,EAArPC,EAAG/C,OAAOgD,OAAO,CAAC,EAAEZ,GAAGa,EAAG,iBAAiBC,EAAG,CAACV,EAAEC,KAAK,MAAMA,CAAC,EAAGU,EAAG,iBAAiBC,OAAOC,EAAE,mBAAmBC,cAAcC,EAAE,iBAAiBC,SAAS,iBAAiBA,QAAQC,UAAU,iBAAiBD,QAAQC,SAASC,KAAKC,EAAE,GAC1VJ,GAAEI,EAAEN,EAAE,eAAwBM,GAAG,IAAIC,KAAcd,EAAG,KAAKD,IAAID,EAAG,EAAQ,KAAMC,EAAE,EAAQ,KAAO,EAAGH,EAAG,SAASF,EAAEC,GAAyB,OAAtBK,IAAKN,EAAEK,EAAEgB,UAAUrB,GAAUI,EAAGkB,aAAatB,EAAEC,OAAE,EAAO,OAAO,EAAEvC,EAAEsC,KAAIA,EAAEE,EAAGF,GAAE,IAAMlH,SAASkH,EAAE,IAAI/I,WAAW+I,IAAWA,GAAGG,EAAE,CAACH,EAAEC,EAAEsB,KAAKjB,IAAKN,EAAEK,EAAEgB,UAAUrB,GAAGI,EAAGoB,SAASxB,GAAE,SAASb,EAAEsC,GAAGtC,EAAEoC,EAAEpC,GAAGc,EAAEwB,EAAE3I,OAAO,GAAC,EAAG,EAAEkI,QAAQU,KAAK3L,SAAS0K,EAAGO,QAAQU,KAAK,GAAGC,QAAQ,MAAM,MAAMX,QAAQU,KAAKE,MAAM,GAAGZ,QAAQa,GAAG,qBAAoB,SAAS7B,GAAG,KAAKA,aAAa8B,GAAI,MAAM9B,CAAE,IAAGgB,QAAQa,GAAG,sBACpf,SAAS7B,GAAG,MAAMA,CAAE,IAAGU,EAAG,CAACV,EAAEC,KAAK,GAAG8B,GAAe,EAAEC,EAAG,MAAMhB,QAAQiB,SAASjC,EAAEC,EAAEA,aAAa6B,GAAII,EAAE,6BAA6BjC,GAAGe,QAAQmB,KAAKnC,EAAC,EAAGJ,EAAEwC,QAAQ,WAAW,MAAM,4BAA4B,IAAUzB,GAAIE,KAAEA,EAAEM,EAAEpM,KAAKsN,SAASC,KAAK,oBAAoBrH,UAAUA,SAAS0E,gBAAgBwB,EAAElG,SAAS0E,cAAcxD,KAAKsD,aAAa0B,EAAE1B,YAAmC0B,EAAvB,IAAIA,EAAEtL,QAAQ,SAAWsL,EAAEoB,OAAO,EAAEpB,EAAEQ,QAAQ,SAAS,IAAIa,YAAY,KAAK,GAAK,GAAGtC,EAAGF,IAAI,IAAIC,EAAE,IAAIwC,eAC3c,OAD0dxC,EAAEyC,KAAK,MAAM1C,GAAE,GAAIC,EAAE0C,KAAK,MAC7e1C,EAAE2C,cAAc/B,IAAInD,EAAEsC,IAAI,IAAIC,EAAE,IAAIwC,eAA4E,OAA7DxC,EAAEyC,KAAK,MAAM1C,GAAE,GAAIC,EAAE4C,aAAa,cAAc5C,EAAE0C,KAAK,MAAa,IAAI1L,WAAWgJ,EAAE6C,SAAQ,GAAI3C,EAAE,CAACH,EAAEC,EAAEsB,KAAK,IAAIpC,EAAE,IAAIsD,eAAetD,EAAEuD,KAAK,MAAM1C,GAAE,GAAIb,EAAE0D,aAAa,cAAc1D,EAAE/C,OAAO,KAAK,KAAK+C,EAAE4D,QAAQ,GAAG5D,EAAE4D,QAAQ5D,EAAE2D,SAAS7C,EAAEd,EAAE2D,UAAUvB,GAAE,EAAGpC,EAAE6D,QAAQzB,EAAEpC,EAAEwD,KAAK,KAAI,GAAG,IAC9UM,EADkVC,EAAGtD,EAAEuD,OAAOC,QAAQC,IAAIC,KAAKF,SAASlB,EAAEtC,EAAE2D,UAAUH,QAAQI,KAAKF,KAAKF,SAAS5F,OAAOgD,OAAOZ,EAAEW,GAAIA,EAAG,KAAKX,EAAE6D,cAAchD,EAAGb,EAAE6D,aAAa7D,EAAE8D,OAAOhD,EAAGd,EAAE8D,MAC3e9D,EAAE+D,aAAaV,EAAErD,EAAE+D,YAAY,IAAI5B,EAAcnC,EAAEmC,gBAAe,EAAG,iBAAiB6B,aAAaC,EAAE,mCAAmC,IAAIC,EAGLC,EAAGC,EAAEC,EAAEC,EAAEC,EAHDC,GAAE,EAAGC,EAAG,oBAAoBC,YAAY,IAAIA,YAAY,aAAQ,EACrN,SAASC,EAAGvE,EAAEC,EAAEsB,GAAU,IAAIpC,GAAXc,KAAK,GAAUsB,EAAE,IAAIA,EAAEtB,EAAED,EAAEuB,MAAMA,GAAGpC,MAAMoC,EAAE,GAAG,GAAGA,EAAEtB,GAAGD,EAAElH,QAAQuL,EAAG,OAAOA,EAAGG,OAAOxE,EAAEyE,SAASxE,EAAEsB,IAAI,IAAIpC,EAAE,GAAGc,EAAEsB,GAAG,CAAC,IAAIE,EAAEzB,EAAEC,KAAK,GAAK,IAAFwB,EAAM,CAAC,IAAIiD,EAAS,GAAP1E,EAAEC,KAAQ,GAAG,MAAQ,IAAFwB,GAAOtC,GAAGrE,OAAO6J,cAAgB,GAAFlD,IAAO,EAAEiD,OAAO,CAAC,IAAIE,EAAS,GAAP5E,EAAEC,KAAwE,OAAhEwB,EAAE,MAAQ,IAAFA,IAAU,GAAFA,IAAO,GAAGiD,GAAG,EAAEE,GAAK,EAAFnD,IAAM,GAAGiD,GAAG,GAAGE,GAAG,EAAS,GAAP5E,EAAEC,MAAgBd,GAAGrE,OAAO6J,aAAalD,IAAIA,GAAG,MAAMtC,GAAGrE,OAAO6J,aAAa,MAAMlD,GAAG,GAAG,MAAQ,KAAFA,GAAQ,CAAC,MAAMtC,GAAGrE,OAAO6J,aAAalD,EAAE,CAAC,OAAOtC,CAAC,CAAC,SAAS0F,EAAG7E,EAAEC,GAAG,OAAOD,KAAK,GAAGuE,EAAGN,EAAEjE,EAAEC,GAAG,EAAE,CAC3e,SAAS6E,EAAG9E,EAAEC,EAAEsB,EAAEpC,GAAU,KAAK,EAAEA,GAAG,OAAO,EAAE,IAAIsC,EAA9BF,KAAK,EAA6BpC,EAAEoC,EAAEpC,EAAE,EAAE,IAAI,IAAIuF,EAAE,EAAEA,EAAE1E,EAAEjK,SAAS2O,EAAE,CAAC,IAAIE,EAAE5E,EAAE+E,WAAWL,GAAgF,GAA1E,OAAOE,GAAG,OAAOA,IAA2BA,EAAE,QAAU,KAAFA,IAAS,IAAM,KAA3C5E,EAAE+E,aAAaL,IAAoC,KAAKE,EAAE,CAAC,GAAGrD,GAAGpC,EAAE,MAAMc,EAAEsB,MAAM,GAAGqD,CAAC,KAAK,CAAC,GAAG,MAAMA,EAAE,CAAC,GAAGrD,EAAE,GAAGpC,EAAE,MAAMc,EAAEsB,MAAM,GAAG,IAAIqD,GAAG,CAAC,KAAK,CAAC,GAAG,OAAOA,EAAE,CAAC,GAAGrD,EAAE,GAAGpC,EAAE,MAAMc,EAAEsB,MAAM,GAAG,IAAIqD,GAAG,EAAE,KAAK,CAAC,GAAGrD,EAAE,GAAGpC,EAAE,MAAMc,EAAEsB,MAAM,GAAG,IAAIqD,GAAG,GAAG3E,EAAEsB,MAAM,GAAG,IAAIqD,GAAG,GAAG,EAAE,CAAC3E,EAAEsB,MAAM,GAAG,IAAIqD,GAAG,EAAE,EAAE,CAAC3E,EAAEsB,MAAM,GAAG,IAAM,GAAFqD,CAAI,CAAC,CAAY,OAAX3E,EAAEsB,IAAI,GAAG,EAASA,EAAEE,CAAC,CACnd,SAASuD,EAAGhF,GAAG,IAAI,IAAIC,EAAE,EAAEsB,EAAE,EAAEA,EAAEvB,EAAEjK,SAASwL,EAAE,CAAC,IAAIpC,EAAEa,EAAE+E,WAAWxD,GAAG,KAAKpC,EAAEc,IAAI,MAAMd,EAAEc,GAAG,EAAE,OAAOd,GAAG,OAAOA,GAAGc,GAAG,IAAIsB,GAAGtB,GAAG,CAAC,CAAC,OAAOA,CAAC,CAAgB,SAASgF,IAAK,IAAIjF,EAAE8D,EAAGhL,OAAOiL,EAAG/D,EAAEJ,EAAEsF,MAAMlB,EAAE,IAAI9M,UAAU8I,GAAGJ,EAAEuF,OAAO,IAAI/N,WAAW4I,GAAGJ,EAAEwF,OAAOlB,EAAE,IAAI7M,WAAW2I,GAAGJ,EAAEyF,OAAOpB,EAAE,IAAIhN,WAAW+I,GAAGJ,EAAE0F,QAAQ,IAAInO,YAAY6I,GAAGJ,EAAE2F,QAAQpB,EAAE,IAAI5M,YAAYyI,GAAGJ,EAAE4F,QAAQ,IAAIxO,aAAagJ,GAAGJ,EAAE6F,QAAQ,IAAInO,aAAa0I,EAAE,CAAC,IAAI0F,EAAGC,EAAG,GAAGC,EAAG,GAAGC,EAAG,GAAGC,EAAG,GAAG9D,EAAG,EACrc,SAAS+D,IAAK,IAAI/F,EAAEJ,EAAEoG,OAAOC,QAAQN,EAAGO,QAAQlG,EAAE,CAAC,IAAuQmG,EAAnQC,EAAE,EAAEC,EAAG,KAAKC,EAAE,KAAK,SAASzC,EAAE7D,GAA6I,MAAvIJ,EAAE2G,SAAQ3G,EAAE2G,QAAQvG,GAAsBkC,EAAnBlC,EAAE,WAAWA,EAAE,KAASoE,GAAE,EAAGpE,EAAE,IAAI4D,YAAY4C,aAAaxG,EAAE,4CAA4CF,EAAGE,GAASA,CAAE,CAAC,SAASyG,IAAK,OAAON,EAAEO,WAAW,wCAAwC,CAAyB,GAAlBP,EAAE,iBAAoBM,IAAK,CAAC,IAAIE,EAAGR,EAAEA,EAAEvG,EAAEgH,WAAWhH,EAAEgH,WAAWD,EAAGxF,GAAGA,EAAEwF,CAAE,CACvY,SAASE,IAAK,IAAI7G,EAAEmG,EAAE,IAAI,GAAGnG,GAAGmG,GAAGlD,EAAE,OAAO,IAAIhM,WAAWgM,GAAG,GAAGvF,EAAE,OAAOA,EAAEsC,GAAG,KAAK,iDAAgE,CAAb,MAAMC,GAAG4D,EAAE5D,EAAE,CAAC,CACuP,SAAS6B,EAAG9B,GAAG7J,KAAKhB,KAAK,aAAagB,KAAK2Q,QAAQ,gCAAgC9G,EAAE,IAAI7J,KAAK4M,OAAO/C,CAAC,CAClf,SAAS+G,EAAE/G,GAAG,KAAK,EAAEA,EAAEjK,QAAQiK,EAAEiG,OAAFjG,CAAUJ,EAAE,CAAC,IAAIoH,EAAE,GAAGC,EAAE,EAAEC,GAAE,EAC3D,SAASC,GAAEnH,GAAG7J,KAAKiR,GAAGpH,EAAE7J,KAAKkR,GAAGrH,EAAE,GAAG7J,KAAKmR,GAAG,SAASrH,GAAGkE,EAAEhO,KAAKkR,GAAG,GAAG,IAAI,GAAGpH,CAAC,EAAE9J,KAAKoR,GAAG,WAAW,OAAOpD,EAAEhO,KAAKkR,GAAG,GAAG,IAAI,EAAE,EAAElR,KAAKqR,GAAG,SAASvH,GAAGkE,EAAEhO,KAAKkR,GAAG,GAAG,IAAI,GAAGpH,CAAC,EAAE9J,KAAKsR,GAAG,WAAW,OAAOtD,EAAEhO,KAAKkR,GAAG,GAAG,IAAI,EAAE,EAAElR,KAAKuR,GAAG,WAAWxD,EAAE/N,KAAKkR,IAAI,IAAI,GAAG,CAAC,EAAElR,KAAKwR,GAAG,SAAS1H,GAAG+D,EAAE7N,KAAKkR,GAAG,IAAI,IAAI,GAAGpH,EAAE,EAAE,CAAC,EAAE9J,KAAKyR,GAAG,WAAW,OAAO,GAAG5D,EAAE7N,KAAKkR,GAAG,IAAI,IAAI,EAAE,EAAElR,KAAK0R,GAAG,SAAS5H,GAAG+D,EAAE7N,KAAKkR,GAAG,IAAI,IAAI,GAAGpH,EAAE,EAAE,CAAC,EAAE9J,KAAK2R,GAAG,WAAW,OAAO,GAAG9D,EAAE7N,KAAKkR,GAAG,IAAI,IAAI,EAAE,EAAElR,KAAK4R,GAAG,SAAS9H,EAAEsB,GAAGpL,KAAK6R,GAAG,GAAG7R,KAAKmR,GAAGrH,GAAG9J,KAAKqR,GAAGjG,GAC3fpL,KAAKuR,KAAKvR,KAAKwR,IAAG,GAAIxR,KAAK0R,IAAG,EAAG,EAAE1R,KAAK8R,GAAG,WAAW/D,EAAE/N,KAAKkR,IAAI,IAAI,IAAI,CAAC,EAAElR,KAAK+R,GAAG,WAAW,IAAIjI,EAAEiE,EAAE/N,KAAKkR,IAAI,IAAI,GAAyB,OAAtBnD,EAAE/N,KAAKkR,IAAI,IAAI,GAAGpH,EAAE,EAAS,IAAIA,CAAC,EAAE9J,KAAK6R,GAAG,SAAS/H,GAAGkE,EAAEhO,KAAKkR,GAAG,IAAI,IAAI,GAAGpH,CAAC,EAAE9J,KAAKgS,GAAG,WAAW,OAAOhE,EAAEhO,KAAKkR,GAAG,IAAI,IAAI,EAAE,EAAElR,KAAKiS,GAAG,WAAW,GAAGC,GAAGlS,KAAKoR,MAAM,OAAOpD,EAAEhO,KAAKiR,IAAI,IAAI,GAAG,IAAInH,EAAE9J,KAAKgS,KAAK,OAAO,IAAIlI,EAAEA,EAAE9J,KAAKiR,EAAE,CAAC,CAAC,SAASkB,GAAGtI,GAAG,OAAOuI,GAAG,IAAKpB,GAAEnH,GAAIqH,GAAG,CAAC,IAAImB,GAAE,GAAG,SAASC,GAAEzI,GAAG,IAAIC,EAAEuI,GAAExI,GAAqD,OAAlDC,IAAID,GAAGwI,GAAEzS,SAASyS,GAAEzS,OAAOiK,EAAE,GAAGwI,GAAExI,GAAGC,EAAEyF,EAAGtN,IAAI4H,IAAWC,CAAC,CAChe,SAASyI,GAAG1I,GAAG,IAAIC,EAAE+E,EAAGhF,GAAG,EAAEuB,EAAEoH,GAAG1I,GAAkB,OAAfsB,GAAGuD,EAAG9E,EAAEgE,EAAEzC,EAAEtB,GAAUsB,CAAC,CAAia,IAAIqH,GAAG,CAAC,EACre,SAASC,KAAK,IAAIC,GAAG,CAAC,IAAuN7I,EAAnND,EAAE,CAAC+I,KAAK,WAAWC,QAAQ,WAAWC,KAAK,IAAIC,IAAI,IAAIC,KAAK,iBAAiBC,MAAM,iBAAiBC,WAAWA,UAAUC,WAAWD,UAAUC,UAAU,IAAI,KAAK3H,QAAQ,IAAI,KAAK,SAAS4H,EAAE9I,GAAI,kBAAoB,IAAIR,KAAK2I,QAAG,IAASA,GAAG3I,UAAUD,EAAEC,GAAGD,EAAEC,GAAG2I,GAAG3I,GAAG,IAAIsB,EAAE,GAAG,IAAItB,KAAKD,EAAEuB,EAAEvL,KAAKiK,EAAE,IAAID,EAAEC,IAAI6I,GAAGvH,CAAC,CAAC,OAAOuH,EAAE,CAAC,IAAIA,GAAGU,GAAG,CAAC,KAAK,GAAG,IAAI,SAASC,GAAGzJ,EAAEC,GAAG,IAAIsB,EAAEiI,GAAGxJ,GAAG,IAAIC,GAAG,KAAKA,IAAI,IAAID,EAAEkD,EAAGhB,GAAGqC,EAAGhD,EAAE,IAAIA,EAAExL,OAAO,GAAGwL,EAAEvL,KAAKiK,EAAE,CAAC,IAAIyJ,GAAE,EACpH,SAASC,GAAG3J,GAAG,OAAO,GAAIA,EAAE,IAAI,GAAIA,EAAE,KAAK,GAAIA,EAAE,IAAI,CAAC,IAAI4J,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAIC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAEhe,SAASC,GAAG9J,EAAEC,EAAEsB,EAAEpC,GAAG,SAASsC,EAAEsI,EAAEC,EAAEC,GAAG,IAAIF,EAAE,iBAAiBA,EAAEA,EAAEG,WAAWH,GAAG,GAAGA,EAAEhU,OAAOiU,GAAGD,EAAEE,EAAE,GAAGF,EAAE,OAAOA,CAAC,CAAC,SAASrF,EAAEqF,EAAEC,GAAG,OAAOvI,EAAEsI,EAAEC,EAAE,IAAI,CAAC,SAASpF,EAAEmF,EAAEC,GAAG,SAASC,EAAEE,GAAG,OAAO,EAAEA,GAAG,EAAE,EAAEA,EAAE,EAAE,CAAC,CAAC,IAAIC,EAAmH,OAAjH,KAAKA,EAAEH,EAAEF,EAAEM,cAAcL,EAAEK,iBAAiB,KAAKD,EAAEH,EAAEF,EAAEO,WAAWN,EAAEM,eAAeF,EAAEH,EAAEF,EAAEQ,UAAUP,EAAEO,YAAmBH,CAAC,CAAC,SAASI,EAAET,GAAG,OAAOA,EAAEU,UAAU,KAAK,EAAE,OAAO,IAAIC,KAAKX,EAAEM,cAAc,EAAE,GAAG,IAAI,KAAK,EAAE,OAAON,EAAE,KAAK,EAAE,OAAO,IAAIW,KAAKX,EAAEM,cAAc,EAAE,GAAG,KAAK,EAAE,OAAO,IAAIK,KAAKX,EAAEM,cAC7e,EAAE,GAAG,KAAK,EAAE,OAAO,IAAIK,KAAKX,EAAEM,cAAc,EAAE,GAAG,KAAK,EAAE,OAAO,IAAIK,KAAKX,EAAEM,cAAc,EAAE,GAAG,IAAI,KAAK,EAAE,OAAO,IAAIK,KAAKX,EAAEM,cAAc,EAAE,GAAG,IAAI,CAAC,SAASM,EAAEZ,GAAG,IAAIC,EAAED,EAAEa,GAAG,IAAIb,EAAE,IAAIW,KAAK,IAAKA,KAAKX,EAAEc,GAAG,KAAK,EAAE,GAAIC,WAAW,EAAEd,GAAG,CAAC,IAAIC,EAAEF,EAAEO,WAAWF,GAAGT,GAAGI,EAAEM,eAAeT,GAAGC,IAAII,GAAG,KAAGD,EAAEI,EAAEL,EAAEQ,WAAoH,CAACR,EAAEgB,QAAQhB,EAAEQ,UAAUP,GAAG,KAAK,CAAzIA,GAAGI,EAAEL,EAAEQ,UAAU,EAAER,EAAEgB,QAAQ,GAAG,GAAGd,EAAEF,EAAEiB,SAASf,EAAE,IAAIF,EAAEiB,SAAS,GAAGjB,EAAEkB,YAAYlB,EAAEM,cAAc,GAAwC,CACza,OAD0aJ,EAAE,IAAIS,KAAKX,EAAEM,cAAc,EAAE,EAAE,GAAGL,EAAEQ,EAAE,IAAIE,KAAKX,EAAEM,cACxe,EAAE,IAAIJ,EAAEO,EAAEP,GAAU,GAAGrF,EAAEoF,EAAED,GAAG,GAAGnF,EAAEqF,EAAEF,GAAGA,EAAEM,cAAc,EAAEN,EAAEM,cAAcN,EAAEM,cAAc,CAAC,CAAC,IAAIa,EAAEhH,EAAE/E,EAAE,IAAI,IAAI,GACyE,IAAI,IAAIgM,KAD9EhM,EAAE,CAACiM,GAAGlH,EAAE/E,GAAG,IAAI,GAAGkM,GAAGnH,EAAE/E,EAAE,GAAG,IAAI,GAAGmM,GAAGpH,EAAE/E,EAAE,GAAG,IAAI,GAAGoM,GAAGrH,EAAE/E,EAAE,IAAI,IAAI,GAAGqM,GAAGtH,EAAE/E,EAAE,IAAI,IAAI,GAAG0L,GAAG3G,EAAE/E,EAAE,IAAI,IAAI,GAAGsM,GAAGvH,EAAE/E,EAAE,IAAI,IAAI,GAAGyL,GAAG1G,EAAE/E,EAAE,IAAI,IAAI,GAAGuM,GAAGxH,EAAE/E,EAAE,IAAI,IAAI,GAAGwM,GAAGzH,EAAE/E,EAAE,IAAI,IAAI,GAAGyM,GAAGV,EAAErG,EAAGqG,GAAG,IAAI3J,EAAEsD,EAAGtD,GAAG2J,EAAE,CAAC,KAAK,uBAAuB,KAAK,WAAW,KAAK,WAAW,KAAK,KAAK,KAAK,cAAc,KAAK,QAAQ,KAAK,WAAW,KAAK,WAAW,KAAK,WAAW,MAAM,KAAK,MAAM,KAAK,MAAM,WAC/e,MAAM,WAAW,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,MAAqB3J,EAAEA,EAAEI,QAAQ,IAAIkK,OAAOV,EAAE,KAAKD,EAAEC,IAAI,IAAIW,EAAE,2DAA2DC,MAAM,KAAKC,EAAE,wFAAwFD,MAAM,KAG1F,IAAIZ,KAH2FD,EAAE,CAAC,KAAK,SAASnB,GAAG,OAAO+B,EAAE/B,EAAE0B,IAAIQ,UAAU,EAAE,EAAE,EAAE,KAAK,SAASlC,GAAG,OAAO+B,EAAE/B,EAAE0B,GAAG,EAAE,KAAK,SAAS1B,GAAG,OAAOiC,EAAEjC,EAAEyB,IAAIS,UAAU,EACzhB,EAAE,EAAE,KAAK,SAASlC,GAAG,OAAOiC,EAAEjC,EAAEyB,GAAG,EAAE,KAAK,SAASzB,GAAG,OAAOrF,GAAGqF,EAAEc,GAAG,MAAM,IAAI,EAAE,EAAE,EAAE,KAAK,SAASd,GAAG,OAAOrF,EAAEqF,EAAEwB,GAAG,EAAE,EAAE,KAAK,SAASxB,GAAG,OAAOtI,EAAEsI,EAAEwB,GAAG,EAAE,IAAI,EAAE,KAAK,SAASxB,GAAG,OAAOY,EAAEZ,GAAGG,WAAW+B,UAAU,EAAE,EAAE,KAAK,SAASlC,GAAG,OAAOY,EAAEZ,EAAE,EAAE,KAAK,SAASA,GAAG,OAAOrF,EAAEqF,EAAEuB,GAAG,EAAE,EAAE,KAAK,SAASvB,GAAkC,OAAxB,IAAPA,EAAEA,EAAEuB,IAAQvB,EAAE,GAAG,GAAGA,IAAIA,GAAG,IAAWrF,EAAEqF,EAAE,EAAE,EAAE,KAAK,SAASA,GAAG,IAAI,IAAIC,EAAE,EAAEC,EAAE,EAAEA,GAAGF,EAAEyB,GAAG,EAAExB,IAAIL,GAAGI,EAAEc,GAAG,MAAMjB,GAAGC,IAAII,MAAM,OAAOvF,EAAEqF,EAAEwB,GAAGvB,EAAE,EAAE,EAAE,KAAK,SAASD,GAAG,OAAOrF,EAAEqF,EAAEyB,GAAG,EAAE,EAAE,EAAE,KAAK,SAASzB,GAAG,OAAOrF,EAAEqF,EAAEsB,GACpf,EAAE,EAAE,KAAK,WAAW,MAAM,IAAI,EAAE,KAAK,SAAStB,GAAG,OAAO,GAAGA,EAAEuB,IAAI,GAAGvB,EAAEuB,GAAG,KAAK,IAAI,EAAE,KAAK,SAASvB,GAAG,OAAOrF,EAAEqF,EAAEqB,GAAG,EAAE,EAAE,KAAK,WAAW,MAAM,IAAI,EAAE,KAAK,SAASrB,GAAG,OAAOA,EAAE0B,IAAI,CAAC,EAAE,KAAK,SAAS1B,GAAG,OAAOrF,EAAEwH,KAAKC,OAAOpC,EAAEa,GAAG,EAAEb,EAAE0B,IAAI,GAAG,EAAE,EAAE,KAAK,SAAS1B,GAAG,IAAIC,EAAEkC,KAAKC,OAAOpC,EAAEa,GAAG,GAAGb,EAAE0B,GAAG,GAAG,GAAG,GAA+B,GAA5B,IAAI1B,EAAE0B,GAAG,IAAI1B,EAAEa,GAAG,GAAG,GAAGZ,IAAOA,EAAE,IAAIA,IAAwB,IAApBC,GAAGF,EAAE0B,GAAG,IAAI1B,EAAEa,IAAI,IAAQ,GAAGX,GAAGN,GAAGI,EAAEc,MAAMb,EAAE,QAAQ,CAACA,EAAE,GAAG,IAAIC,GAAGF,EAAE0B,GAAG,EAAE1B,EAAEa,GAAG,GAAG,GAAG,GAAGX,GAAG,GAAGA,GAAGN,GAAGI,EAAEc,GAAG,IAAI,KAAKb,GAAG,CAAC,OAAOtF,EAAEsF,EAAE,EAAE,EAAE,KAAK,SAASD,GAAG,OAAOA,EAAE0B,EAAE,EAAE,KAAK,SAAS1B,GAAG,OAAOrF,EAAEwH,KAAKC,OAAOpC,EAAEa,GAC1hB,GAAGb,EAAE0B,GAAG,GAAG,GAAG,GAAG,EAAE,EAAE,KAAK,SAAS1B,GAAG,OAAOA,EAAEc,GAAG,MAAMX,WAAW+B,UAAU,EAAE,EAAE,KAAK,SAASlC,GAAG,OAAOA,EAAEc,GAAG,IAAI,EAAE,KAAK,SAASd,GAAU,IAAIC,EAAE,IAAbD,EAAEA,EAAE4B,IAA+B,OAAjB5B,EAAEmC,KAAKE,IAAIrC,GAAG,IAAUC,EAAE,IAAI,KAAKlP,OAAO,QAAQiP,EAAE,GAAG,IAAIA,EAAE,KAAKnI,OAAO,EAAE,EAAE,KAAK,SAASmI,GAAG,OAAOA,EAAE6B,EAAE,EAAE,KAAK,WAAW,MAAM,GAAG,GAAGrK,EAAEA,EAAEI,QAAQ,MAAM,QAAqBuJ,EAAE3J,EAAE8K,SAASlB,KAAK5J,EAAEA,EAAEI,QAAQ,IAAIkK,OAAOV,EAAE,KAAKD,EAAEC,GAAGhM,KAAsC,OAARgM,EAPxZ,SAAYnL,GAAG,IAAIC,EAAEhI,MAAM+M,EAAGhF,GAAG,GAAsB,OAAnB8E,EAAG9E,EAAEC,EAAE,EAAEA,EAAElK,QAAekK,CAAC,CAO2VqM,CAA3B/K,EAAEA,EAAEI,QAAQ,QAAQ,MAAgBwJ,EAAEpV,OAAOkK,EAAS,GAAE+D,EAAEvM,IAAI0T,EAAEnL,IAAI,GAAUmL,EAAEpV,OAAO,EAAC,CACvd,IAAI8R,GAAG,CAAC7H,EAAE,SAASA,GAAG,OAAO2I,GAAG3I,EAAE,IAAI,EAAE,EAAEa,EAAE,SAASb,GAA+D,OAA5DA,EAAE,IAAImH,GAAEnH,IAAK4H,OAAO5H,EAAE2H,IAAG,GAAIV,KAAKjH,EAAE6H,IAAG,GAAIb,EAAEhR,KAAKgK,GAAGA,EAAEiI,KAAYjI,EAAEoI,IAAI,EAAE9H,GAAG,SAASN,GAAoF,MAAjFkC,EAAE,0EAA0EkC,GAAE,EAASpE,CAAE,EAAEiK,EAAE,WAAWsC,GAAE,GAAG,IAAIvM,EAAEgH,EAAEwF,MAAM,GAAGxM,EAAEkI,OAAOlI,EAAE8H,KAAK,CAAC,IAAI7H,EAAED,EAAEyH,KAAKxH,GAAGwI,GAAExI,EAAFwI,CAAKzI,EAAEoH,IAAIkB,GAAGtI,EAAEoH,GAAG,CAACF,GAAE,CAAC,EAAEtH,EAAE,WAAW,IAAII,EAAEkH,GAAE,IAAIlH,EAAE,OAAO0J,GAAE,EAAE,IAAIzJ,EAAE,IAAIkH,GAAEnH,GAAGC,EAAE+H,GAAGhI,GAAG,IAAIuB,EAAEtB,EAAEsH,KAAK,IAAIhG,EAAE,OAAOmI,GAAE,EAAE1J,EAAE,IAAI,IAAIb,EAAElH,MAAMwU,UAAU7K,MAAM3D,KAAKyO,WAAWjL,EAAE,EAAEA,EAAEtC,EAAEpJ,OAAO0L,IAAI,CAAC,IAAIiD,EAAEvF,EAAEsC,GACnf,GAAG,IAAIiD,GAAGA,IAAInD,EAAE,MAAM,GAAGoL,GAAGjI,EAAEnD,EAAEtB,EAAEoH,GAAG,IAAI,OAAOqC,GAAEhF,EAAE1E,CAAC,CAAK,OAAJ0J,GAAEnI,EAASvB,CAAC,EAAE4E,EAAE,WAAW,IAAI5E,EAAEkH,GAAE,IAAIlH,EAAE,OAAO0J,GAAE,EAAE,IAAIzJ,EAAE,IAAIkH,GAAEnH,GAAGC,EAAE+H,GAAGhI,GAAG,IAAIuB,EAAEtB,EAAEsH,KAAK,IAAIhG,EAAE,OAAOmI,GAAE,EAAE1J,EAAE,IAAI,IAAIb,EAAElH,MAAMwU,UAAU7K,MAAM3D,KAAKyO,WAAWjL,EAAE,EAAEA,EAAEtC,EAAEpJ,OAAO0L,IAAI,CAAC,IAAIiD,EAAEvF,EAAEsC,GAAG,GAAG,IAAIiD,GAAGA,IAAInD,EAAE,MAAM,GAAGoL,GAAGjI,EAAEnD,EAAEtB,EAAEoH,GAAG,IAAI,OAAOqC,GAAEhF,EAAE1E,CAAC,CAAK,OAAJ0J,GAAEnI,EAASvB,CAAC,EAAE+J,EAAE,WAAW,IAAI/J,EAAEkH,GAAE,IAAIlH,EAAE,OAAO0J,GAAE,EAAE,IAAIzJ,EAAE,IAAIkH,GAAEnH,GAAGC,EAAE+H,GAAGhI,GAAG,IAAIuB,EAAEtB,EAAEsH,KAAK,IAAIhG,EAAE,OAAOmI,GAAE,EAAE1J,EAAE,IAAI,IAAIb,EAAElH,MAAMwU,UAAU7K,MAAM3D,KAAKyO,WAAWjL,EAAE,EAAEA,EAAEtC,EAAEpJ,OAAO0L,IAAI,CAAC,IAAIiD,EAAEvF,EAAEsC,GAAG,GAAG,IAAIiD,GAAGA,IAAInD,EAAE,MAChf,GAAGoL,GAAGjI,EAAEnD,EAAEtB,EAAEoH,GAAG,IAAI,OAAOqC,GAAEhF,EAAE1E,CAAC,CAAK,OAAJ0J,GAAEnI,EAASvB,CAAC,EAAE4M,EAAEtE,GAAGhC,EAAE,WAAW,IAAItG,EAAEgH,EAAEwF,MAAMxM,GAAG6D,EAAE,yBAAyB,IAAI5D,EAAED,EAAEoH,GAAiD,MAA9CpH,EAAE8H,OAAOd,EAAEhR,KAAKgK,GAAGA,EAAE6H,IAAG,GAAI7H,EAAE2H,IAAG,GAAIV,KAAKC,GAAEjH,EAAQA,CAAE,EAAEA,EAAE,SAASD,EAAEC,EAAEsB,GAA8B,MAA3B,IAAK4F,GAAEnH,GAAI+H,GAAG9H,EAAEsB,GAAG2F,GAAElH,EAAEiH,IAAUjH,CAAE,EAAEkD,GAAG,WAAW,OAAO+D,CAAC,EAAErR,EAAE,SAASoK,GAAY,MAATkH,KAAIA,GAAElH,GAASA,CAAE,EAAEgE,EAAE,WAAW,OAAO,CAAC,EAAEyC,GAAG,WAAW,EAAE5B,GAAG,WAAW,EAAEG,GAAG,WAAW,EAAEhD,GAAG,WAAW,OAAO,CAAC,EAAE+D,GAAG,WAAW,EAAEL,GAAG,WAAW,EAAEI,GAAG,WAAW,EAAEoB,EAAE,WAAW,EAAEpC,GAAG,WAAW,EAAET,GAAG,WAAW,EAAEgC,GAAG,WAAW,EAAE9B,GAAG,WAAW,EAC3fgE,GAAG,WAAW,EAAEI,GAAG,WAAW9E,EAAE,iHAAiH,EAAE6E,GAAG,WAAW7E,EAAE,iHAAiH,EAAEsD,EAAE,WAAW,OAAOuD,KAAKmC,KAAK,EAAElG,GAAG,WAAW,OAAM,CAAE,EAAEE,GAAG,SAAS7G,EAAEC,GAAGD,EAAE,IAAI0K,KAAK,KAAKvG,EAAEnE,IAAI,GAAG,WAAWkE,EAAElE,EAAE,IAAI,KAAKkE,EAAEjE,GAAG,IAAI,GAAGD,EAAE8M,gBAAgB5I,EAAEjE,EAAE,GAAG,IAAI,GAAGD,EAAE+M,gBAAgB7I,EAAEjE,EAAE,GAAG,IAAI,GAAGD,EAAEgN,cAAc9I,EAAEjE,EAAE,IAAI,IACpf,GAAGD,EAAEiN,aAAa/I,EAAEjE,EAAE,IAAI,IAAI,GAAGD,EAAEkN,cAAchJ,EAAEjE,EAAE,IAAI,IAAI,GAAGD,EAAEmN,iBAAiB,KAAKjJ,EAAEjE,EAAE,IAAI,IAAI,GAAGD,EAAEoN,YAAYlJ,EAAEjE,EAAE,IAAI,IAAI,IAAID,EAAE8K,UAAUJ,KAAK2C,IAAIrN,EAAEmN,iBAAiB,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,MAAM,CAAC,EAAEG,GAAG,SAAStN,EAAEC,GAAGD,EAAE,IAAI0K,KAAK,KAAKvG,EAAEnE,IAAI,GAAG,WAAWkE,EAAElE,EAAE,IAAI,KAAKkE,EAAEjE,GAAG,IAAI,GAAGD,EAAEuN,aAAarJ,EAAEjE,EAAE,GAAG,IAAI,GAAGD,EAAEwN,aAAatJ,EAAEjE,EAAE,GAAG,IAAI,GAAGD,EAAEyN,WAAWvJ,EAAEjE,EAAE,IAAI,IAAI,GAAGD,EAAEuK,UAAUrG,EAAEjE,EAAE,IAAI,IAAI,GAAGD,EAAEsK,WAAWpG,EAAEjE,EAAE,IAAI,IAAI,GAAGD,EAAEqK,cAAc,KAAKnG,EAAEjE,EAAE,IAAI,IAAI,GAAGD,EAAEyK,SAAS,IAAIlJ,EAAE,IAAImJ,KAAK1K,EAAEqK,cAAc,EAAE,GAAGnG,EAAEjE,EACpf,IAAI,IAAI,IAAID,EAAE8K,UAAUvJ,EAAEuJ,WAAW,MAAM,EAAE5G,EAAEjE,EAAE,IAAI,IAAI,IAAK,GAAGD,EAAE0N,oBAAqB,IAAIvO,EAAE,IAAKuL,KAAK1K,EAAEqK,cAAc,EAAE,GAAIqD,oBAAoBnM,EAAEA,EAAEmM,oBAAoBxJ,EAAEjE,EAAE,IAAI,IAAI,GAAgD,GAA5Cd,GAAGoC,GAAGvB,EAAE0N,qBAAqBxB,KAAKyB,IAAIpM,EAAEpC,GAAK,EAAEkJ,GAAG,SAASrI,GAAG,IAAIC,EAAE,IAAIyK,KAAKxG,EAAElE,EAAE,IAAI,IAAI,GAAG,KAAKkE,EAAElE,EAAE,IAAI,IAAI,GAAGkE,EAAElE,EAAE,IAAI,IAAI,GAAGkE,EAAElE,EAAE,GAAG,IAAI,GAAGkE,EAAElE,EAAE,GAAG,IAAI,GAAGkE,EAAElE,GAAG,IAAI,GAAG,GAAGuB,EAAE2C,EAAElE,EAAE,IAAI,IAAI,GAAGb,EAAEc,EAAEyN,oBAAoBjM,EAAE,IAAIiJ,KAAKzK,EAAEoK,cAAc,EAAE,GAAG3F,EAAE,IAAKgG,KAAKzK,EAAEoK,cAAc,EAAE,GAAIqD,oBAAoB9I,EAAEnD,EAAEiM,oBACvelD,EAAE0B,KAAKyB,IAAI/I,EAAEF,GAAsU,OAAnU,EAAEnD,EAAE2C,EAAElE,EAAE,IAAI,IAAI,GAAGvH,OAAOiM,GAAGE,GAAG4F,GAAGrL,GAAG,EAAEoC,IAAIiJ,GAAGrL,KAAKuF,EAAEwH,KAAK0B,IAAIhJ,EAAEF,GAAGzE,EAAE4N,QAAQ5N,EAAE6K,UAAU,MAAM,EAAEvJ,EAAEiJ,EAAE9F,GAAGvF,KAAK+E,EAAElE,EAAE,IAAI,IAAI,GAAGC,EAAEwK,SAASvG,EAAElE,EAAE,IAAI,IAAI,IAAIC,EAAE6K,UAAUrJ,EAAEqJ,WAAW,MAAM,EAAE5G,EAAElE,GAAG,IAAI,GAAGC,EAAEsN,aAAarJ,EAAElE,EAAE,GAAG,IAAI,GAAGC,EAAEuN,aAAatJ,EAAElE,EAAE,GAAG,IAAI,GAAGC,EAAEwN,WAAWvJ,EAAElE,EAAE,IAAI,IAAI,GAAGC,EAAEsK,UAAUrG,EAAElE,EAAE,IAAI,IAAI,GAAGC,EAAEqK,WAAkBrK,EAAE6K,UAAU,IAAI,CAAC,EAAE/G,GAAG,WAAW,OAAO,EAAE,EAAEkB,GAAG,WAAW,EAAEqD,GAjBwB,SAASwF,EAAG9N,EAAEC,EAAEsB,GAAGuM,EAAGC,KAAKD,EAAGC,IAAG,EAAlZ,SAAY/N,EAAEC,EAAEsB,GAAG,SAASpC,EAAEwL,GAAG,OAAOA,EAAEA,EAAEqD,eAAeC,MAAM,sBAAsBtD,EAAE,GAAG,KAAK,CAAC,IAAIlJ,GAAE,IAAKiJ,MAAML,cAAc3F,EAAE,IAAIgG,KAAKjJ,EAAE,EAAE,GAAGmD,EAAE,IAAI8F,KAAKjJ,EAAE,EAAE,GAAGA,EAAEiD,EAAEgJ,oBAAoB,IAAIlD,EAAE5F,EAAE8I,oBAAoBxJ,EAAElE,GAAG,IAAI,GAAG,GAAGkM,KAAK0B,IAAInM,EAAE+I,GAAGtG,EAAEjE,GAAG,IAAI,GAAGxH,OAAOgJ,GAAG+I,GAAGxK,EAAEb,EAAEuF,GAAGzE,EAAEd,EAAEyF,GAAG5E,EAAE0I,GAAG1I,GAAGC,EAAEyI,GAAGzI,GAAGuK,EAAE/I,GAAG0C,EAAE5C,GAAG,IAAI,GAAGvB,EAAEmE,EAAE5C,EAAE,GAAG,IAAI,GAAGtB,IAAIkE,EAAE5C,GAAG,IAAI,GAAGtB,EAAEkE,EAAE5C,EAAE,GAAG,IAAI,GAAGvB,EAAE,CAAqCkO,CAAGlO,EAAEC,EAAEsB,GAAG,EAiBhEsC,EAAE,WAAWA,EAAE,GAAG,EAAEC,GAAG,WAAW,OAAO,UAAU,EAAEI,EAAEnD,EAAE,KAAK,IAAIf,EAAEgB,QAAQmN,SAAS,OAAO,IACxfnO,EAAE,GAAGA,EAAE,GAAG,KAAK,IAAIoO,YAAYvB,MAAMhH,GAAG,SAAS7F,EAAEC,EAAEsB,GAAG0C,EAAEoK,WAAWrO,IAAI,EAAEC,IAAI,EAAEA,EAAEsB,IAAI,EAAE,EAAE0C,EAAE,SAASjE,GAAG,IAAIC,EAAEgE,EAAElO,OAAc,GAAG,YAAViK,KAAK,GAAkB,OAAM,EAAG,IAAI,IAAIuB,EAAE,EAAE,GAAGA,EAAEA,GAAG,EAAE,CAAC,IAAIpC,EAAEc,GAAG,EAAE,GAAGsB,GAAGpC,EAAE+M,KAAKyB,IAAIxO,EAAEa,EAAE,WAAW,IAAIyB,EAAEyK,KAAK/M,EAAE+M,KAAK0B,IAAI5N,EAAEb,GAAGsC,EAAEA,EAAEkM,IAAI1P,KAAKwD,EAAE,WAAWtC,GAAG,MAAMA,EAAE,OAAO,OAAOa,EAAE,CAAC,IAAI8D,EAAGwK,KAAK7M,EAAEsC,EAAGxF,WAAW,QAAQ,IAAI0G,IAAK,IAAIP,EAAE,EAAE,MAAM1E,CAAW,CAAT,MAAM4E,GAAG,CAACF,OAAE,CAAM,CAAC,GAAGA,EAAE,OAAM,CAAE,CAAC,OAAM,CAAE,EAAEiB,GAAG,SAAS3F,EAAEC,GAAG,IAAIsB,EAAE,EACrX,OADuXsH,KAAK0F,SAAQ,SAASpP,EAAEsC,GAAG,IAAIiD,EAAEzE,EAAEsB,EAAsB,IAApBE,EAAE0C,EAAEnE,EAAE,EAAEyB,GAAG,IAAI,GAAGiD,EAAMA,EAAE,EAAEA,EAAEvF,EAAEpJ,SAAS2O,EAAEV,EAAEvC,KAC9f,IAAI,GAAGtC,EAAE4F,WAAWL,GAAGV,EAAEvC,GAAG,IAAI,GAAG,EAAEF,GAAGpC,EAAEpJ,OAAO,CAAC,IAAU,CAAC,EAAE6P,GAAG,SAAS5F,EAAEC,GAAG,IAAIsB,EAAEsH,KAAK1E,EAAEnE,GAAG,IAAI,GAAGuB,EAAExL,OAAO,IAAIoJ,EAAE,EAAsD,OAApDoC,EAAEgN,SAAQ,SAAS9M,GAAGtC,GAAGsC,EAAE1L,OAAO,CAAC,IAAGoO,EAAElE,GAAG,IAAI,GAAGd,EAAS,CAAC,EAAEW,GAAG,SAASE,GAAG+B,GAAe,EAAEC,IAAKwM,KAAKzH,EAAElB,GAAI4I,GAAG,GAAGjF,GAAG,GAAGzT,QAAQ0T,GAAG,EAAE,IAAID,GAAG,GAAGzT,QAAQ0T,GAAG,EAAE,KAAU1H,GAAe,EAAEC,IAAQpC,EAAE8O,QAAO9O,EAAE8O,OAAO1O,GAAGoE,GAAE,GAAG1D,EAAGV,EAAE,IAAI8B,EAAG9B,GAAG,EAAE8L,EAAE,WAAW,OAAO,EAAE,EAAE7E,EAAE,WAAW,OAAO,EAAE,EAAE1G,GAAG,WAAW,OAAO,EAAE,EAAEyG,EAAE,SAAShH,EAAEC,EAAEsB,EAAEpC,GAAG,IAAI,IAAIsC,EAAE,EAAEiD,EAAE,EAAEA,EAAEnD,EAAEmD,IAAI,CAAC,IAAIE,EAAET,EAAElE,GAAG,IAAI,GAAGuK,EAAErG,EAAElE,EAAE,GAClf,IAAI,GAAGA,GAAG,EAAE,IAAI,IAAI0K,EAAE,EAAEA,EAAEH,EAAEG,IAAIlB,GAAGzJ,EAAEiE,EAAEW,EAAE+F,IAAI,IAAIlJ,GAAG+I,CAAC,CAAe,OAAdrG,EAAEhF,GAAG,IAAI,GAAGsC,EAAS,CAAC,EAAEF,EAAE,WAAW,OAAOmI,EAAC,EAAE5H,GAlB+J,SAAS6M,EAAE3O,EAAEC,GAAG0O,EAAEC,KAAKD,EAAEC,GAA7R,WAAc,GAAG,iBAAiBC,QAAQ,mBAAmBA,OAAOC,gBAAgB,CAAC,IAAI9O,EAAE,IAAI/I,WAAW,GAAG,MAAM,KAAK4X,OAAOC,gBAAgB9O,GAAUA,EAAE,GAAG,CAAC,GAAGe,EAAE,IAAI,IAAId,EAAE,EAAQ,wGAAU,MAAM,IAAIA,EAAE8O,YAAY,GAAG,EAAY,CAAT,MAAMxN,GAAG,CAAC,MAAM,IAAIsC,EAAE,eAAe,CAA6BmL,IAAM,IAAI,IAAIzN,EAAE,EAAEA,EAAEtB,EAAEsB,IAAIyC,EAAEhE,EAAEuB,GAAG,IAAI,GAAGoN,EAAEC,KAAK,OAAO,CAAC,EAkB7OlO,GAgBqE,SAAYV,EAAEC,EAAEsB,GAAG,IAAIpC,EAAE8P,KAAI,IAAI,OAAOxG,GAAEzI,EAAFyI,CAAKxI,EAAEsB,EAA0C,CAAvC,MAAME,GAAQ,GAALyN,GAAE/P,GAAMsC,IAAIA,EAAE,EAAE,MAAMA,EAAE8K,GAAE,EAAE,EAAE,CAAC,EAhBzJ5L,GAgB5B,SAAYX,EAAEC,EAAEsB,GAAG,IAAIpC,EAAE8P,KAAI,IAAI,OAAOxG,GAAEzI,EAAFyI,CAAKxI,EAAEsB,EAA0C,CAAvC,MAAME,GAAQ,GAALyN,GAAE/P,GAAMsC,IAAIA,EAAE,EAAE,MAAMA,EAAE8K,GAAE,EAAE,EAAE,CAAC,EAhBxDpI,EAgBtH,SAAYnE,GAAG,IAAIC,EAAEgP,KAAI,IAAI,OAAOxG,GAAEzI,EAAFyI,EAA8C,CAAvC,MAAMlH,GAAQ,GAAL2N,GAAEjP,GAAMsB,IAAIA,EAAE,EAAE,MAAMA,EAAEgL,GAAE,EAAE,EAAE,CAAC,EAhBwCpN,EAW0F,SAAYa,EAAEC,GAAG,IAAIsB,EAAE0N,KAAI,IAAI,OAAOxG,GAAEzI,EAAFyI,CAAKxI,EAA0C,CAAvC,MAAMd,GAAQ,GAAL+P,GAAE3N,GAAMpC,IAAIA,EAAE,EAAE,MAAMA,EAAEoN,GAAE,EAAE,EAAE,CAAC,EAX3KpG,EAcmE,SAAYnG,EAAEC,EAAEsB,GAAG,IAAIpC,EAAE8P,KAAI,IAAI,OAAOxG,GAAEzI,EAAFyI,CAAKxI,EAAEsB,EAA0C,CAAvC,MAAME,GAAQ,GAALyN,GAAE/P,GAAMsC,IAAIA,EAAE,EAAE,MAAMA,EAAE8K,GAAE,EAAE,EAAE,CAAC,EAdxJxF,EAc7B,SAAY/G,EAAEC,EAAEsB,GAAG,IAAIpC,EAAE8P,KAAI,IAAI,OAAOxG,GAAEzI,EAAFyI,CAAKxI,EAAEsB,EAA0C,CAAvC,MAAME,GAAQ,GAALyN,GAAE/P,GAAMsC,IAAIA,EAAE,EAAE,MAAMA,EAAE8K,GAAE,EAAE,EAAE,CAAC,EAdxD4C,EAY1I,SAAYnP,EAAEC,EAAEsB,GAAG,IAAIpC,EAAE8P,KAAI,IAAI,OAAOxG,GAAEzI,EAAFyI,CAAKxI,EAAEsB,EAA0C,CAAvC,MAAME,GAAQ,GAALyN,GAAE/P,GAAMsC,IAAIA,EAAE,EAAE,MAAMA,EAAE8K,GAAE,EAAE,EAAE,CAAC,EAZqD6C,EAYpD,SAAYpP,EAAEC,EAAEsB,EAAEpC,GAAG,IAAIsC,EAAEwN,KAAI,IAAI,OAAOxG,GAAEzI,EAAFyI,CAAKxI,EAAEsB,EAAEpC,EAA0C,CAAvC,MAAMuF,GAAQ,GAALwK,GAAEzN,GAAMiD,IAAIA,EAAE,EAAE,MAAMA,EAAE6H,GAAE,EAAE,EAAE,CAAC,EAZrCxL,EAapJ,SAAYf,EAAEC,EAAEsB,EAAEpC,EAAEsC,GAAG,IAAIiD,EAAEuK,KAAI,IAAI,OAAOxG,GAAEzI,EAAFyI,CAAKxI,EAAEsB,EAAEpC,EAAEsC,EAA0C,CAAvC,MAAMmD,GAAQ,GAALsK,GAAExK,GAAME,IAAIA,EAAE,EAAE,MAAMA,EAAE2H,GAAE,EAAE,EAAE,CAAC,EAbuDpC,EAciP,SAAYnK,EAAEC,EAAEsB,EAAEpC,EAAEsC,EAAEiD,GAAG,IAAIE,EAAEqK,KAAI,IAAI,OAAOxG,GAAEzI,EAAFyI,CAAKxI,EAAEsB,EAAEpC,EAAEsC,EAAEiD,EAA0C,CAAvC,MAAM8F,GAAQ,GAAL0E,GAAEtK,GAAM4F,IAAIA,EAAE,EAAE,MAAMA,EAAE+B,GAAE,EAAE,EAAE,CAAC,EAdlVpM,EAYoN,SAAYH,EAAEC,EAAEsB,EAAEpC,EAAEsC,EAAEiD,GAAG,IAAIE,EAAEqK,KAAI,IAAI,OAAOxG,GAAEzI,EAAFyI,CAAKxI,EAAEsB,EAAEpC,EAAEsC,EAAEiD,EAA0C,CAAvC,MAAM8F,GAAQ,GAAL0E,GAAEtK,GAAM4F,IAAIA,EAAE,EAAE,MAAMA,EAAE+B,GAAE,EAAE,EAAE,CAAC,EAZrT7O,EAYuB,SAAYsC,EAAEC,EAAEsB,EAAEpC,EAAEsC,EAAEiD,EAAEE,GAAG,IAAI4F,EAAEyE,KAAI,IAAI,OAAOxG,GAAEzI,EAAFyI,CAAKxI,EAAEsB,EAAEpC,EAAEsC,EAAEiD,EAAEE,EAA0C,CAAvC,MAAM+F,GAAQ,GAALuE,GAAE1E,GAAMG,IAAIA,EAAE,EAAE,MAAMA,EAAE4B,GAAE,EAAE,EAAE,CAAC,EAZ5HnG,EAexK,SAAYpG,EAAEC,EAAEsB,EAAEpC,EAAEsC,EAAEiD,EAAEE,EAAE4F,GAAG,IAAIG,EAAEsE,KAAI,IAAI,OAAOxG,GAAEzI,EAAFyI,CAAKxI,EAAEsB,EAAEpC,EAAEsC,EAAEiD,EAAEE,EAAE4F,EAA0C,CAAvC,MAAMU,GAAQ,GAALgE,GAAEvE,GAAMO,IAAIA,EAAE,EAAE,MAAMA,EAAEqB,GAAE,EAAE,EAAE,CAAC,EAf+DnI,EAe9D,SAAYpE,EAAEC,EAAEsB,EAAEpC,EAAEsC,EAAEiD,EAAEE,EAAE4F,EAAEG,EAAEO,EAAEC,EAAEW,GAAG,IAAIE,EAAEiD,KAAI,IAAI,OAAOxG,GAAEzI,EAAFyI,CAAKxI,EAAEsB,EAAEpC,EAAEsC,EAAEiD,EAAEE,EAAE4F,EAAEG,EAAEO,EAAEC,EAAEW,EAA0C,CAAvC,MAAM/B,GAAQ,GAALmF,GAAElD,GAAMjC,IAAIA,EAAE,EAAE,MAAMA,EAAEwC,GAAE,EAAE,EAAE,CAAC,EAf3DA,EAkBlL,SAAYvM,EAAEC,EAAEsB,EAAEpC,EAAEsC,EAAEiD,EAAEE,EAAE4F,GAAG,IAAIG,EAAEsE,KAAI,IAAI,OAAOvH,GAAG1H,EAAEC,EAAEsB,EAAEpC,EAAEsC,EAAEiD,EAAEE,EAAE4F,EAA0C,CAAvC,MAAMU,GAAQ,GAALgE,GAAEvE,GAAMO,IAAIA,EAAE,EAAE,MAAMA,EAAEqB,GAAE,EAAE,EAAE,CAAC,EAlByE7C,EAkBW,SAAY1J,EAAEC,EAAEsB,EAAEpC,EAAEsC,EAAEiD,EAAEE,GAAG,IAAI4F,EAAEyE,KAAI,IAAI,OAAOL,GAAG5O,EAAEC,EAAEsB,EAAEpC,EAAEsC,EAAEiD,EAAEE,EAA0C,CAAvC,MAAM+F,GAAQ,GAALuE,GAAE1E,GAAMG,IAAIA,EAAE,EAAE,MAAMA,EAAE4B,GAAE,EAAE,EAAE,CAAC,EAlBhH9D,EAkBiH,SAAYzI,EAAEC,EAAEsB,EAAEpC,EAAEsC,GAAG,IAAIiD,EAAEuK,KAAI,IAAI,OAAO3H,GAAGtH,EAAEC,EAAEsB,EAAEpC,EAAEsC,EAA0C,CAAvC,MAAMmD,GAAQ,GAALsK,GAAExK,GAAME,IAAIA,EAAE,EAAE,MAAMA,EAAE2H,GAAE,EAAE,EAAE,CAAC,EAlB9M2C,EAiBuB,SAAYlP,EAAEC,EAAEsB,EAAEpC,GAAG,IAAIsC,EAAEwN,KAAI,IAAI,OAAOlH,GAAG/H,EAAEC,EAAEsB,EAAEpC,EAA0C,CAAvC,MAAMuF,GAAQ,GAALwK,GAAEzN,GAAMiD,IAAIA,EAAE,EAAE,MAAMA,EAAE6H,GAAE,EAAE,EAAE,CAAC,EAjBhHoC,EAkBvF,SAAY3O,GAAG,IAAIC,EAAEgP,KAAI,IAAI,OAAOnH,GAAG9H,EAA0C,CAAvC,MAAMuB,GAAQ,GAAL2N,GAAEjP,GAAMsB,IAAIA,EAAE,EAAE,MAAMA,EAAEgL,GAAE,EAAE,EAAE,CAAC,EAlBU0C,EAiB4G,SAAYjP,EAAEC,GAAG,IAAIsB,EAAE0N,KAAI,IAAI,OAAOzH,GAAGxH,EAAEC,EAA0C,CAAvC,MAAMd,GAAQ,GAAL+P,GAAE3N,GAAMpC,IAAIA,EAAE,EAAE,MAAMA,EAAEoN,GAAE,EAAE,EAAE,CAAC,EAjB7L/D,EAkBgM,SAAYxI,EAAEC,EAAEsB,GAAG,IAAIpC,EAAE8P,KAAI,IAAI,OAAOhH,GAAGjI,EAAEC,EAAEsB,EAA0C,CAAvC,MAAME,GAAQ,GAALyN,GAAE/P,GAAMsC,IAAIA,EAAE,EAAE,MAAMA,EAAE8K,GAAE,EAAE,EAAE,CAAC,EAlBrR9K,EAYgF,SAAYzB,GAAG,IAAIC,EAAEgP,KAAI,IAAIxG,GAAEzI,EAAFyI,EAA8C,CAAvC,MAAMlH,GAAQ,GAAL2N,GAAEjP,GAAMsB,IAAIA,EAAE,EAAE,MAAMA,EAAEgL,GAAE,EAAE,EAAE,CAAC,EAZvJpL,EAWkF,SAAYnB,EAAEC,GAAG,IAAIsB,EAAE0N,KAAI,IAAIxG,GAAEzI,EAAFyI,CAAKxI,EAA0C,CAAvC,MAAMd,GAAQ,GAAL+P,GAAE3N,GAAMpC,IAAIA,EAAE,EAAE,MAAMA,EAAEoN,GAAE,EAAE,EAAE,CAAC,EAX5J7H,EAW6J,SAAY1E,EAAEC,EAAEsB,GAAG,IAAIpC,EAAE8P,KAAI,IAAIxG,GAAEzI,EAAFyI,CAAKxI,EAAEsB,EAA0C,CAAvC,MAAME,GAAQ,GAALyN,GAAE/P,GAAMsC,IAAIA,EAAE,EAAE,MAAMA,EAAE8K,GAAE,EAAE,EAAE,CAAC,EAX3O9L,GAgBsC,SAAYT,EAAEC,EAAEsB,EAAEpC,GAAG,IAAIsC,EAAEwN,KAAI,IAAIxG,GAAEzI,EAAFyI,CAAKxI,EAAEsB,EAAEpC,EAA0C,CAAvC,MAAMuF,GAAQ,GAALwK,GAAEzN,GAAMiD,IAAIA,EAAE,EAAE,MAAMA,EAAE6H,GAAE,EAAE,EAAE,CAAC,EAhBvH/B,EAavI,SAAYxK,EAAEC,EAAEsB,EAAEpC,GAAG,IAAIsC,EAAEwN,KAAI,IAAIxG,GAAEzI,EAAFyI,CAAKxI,EAAEsB,EAAEpC,EAA0C,CAAvC,MAAMuF,GAAQ,GAALwK,GAAEzN,GAAMiD,IAAIA,EAAE,EAAE,MAAMA,EAAE6H,GAAE,EAAE,EAAE,CAAC,EAbqDrB,EAa4C,SAAYlL,EAAEC,EAAEsB,EAAEpC,EAAEsC,GAAG,IAAIiD,EAAEuK,KAAI,IAAIxG,GAAEzI,EAAFyI,CAAKxI,EAAEsB,EAAEpC,EAAEsC,EAA0C,CAAvC,MAAMmD,GAAQ,GAALsK,GAAExK,GAAME,IAAIA,EAAE,EAAE,MAAMA,EAAE2H,GAAE,EAAE,EAAE,CAAC,EAblIvC,EAazD,SAAYhK,EAAEC,EAAEsB,EAAEpC,EAAEsC,EAAEiD,GAAG,IAAIE,EAAEqK,KAAI,IAAIxG,GAAEzI,EAAFyI,CAAKxI,EAAEsB,EAAEpC,EAAEsC,EAAEiD,EAA0C,CAAvC,MAAM8F,GAAQ,GAAL0E,GAAEtK,GAAM4F,IAAIA,EAAE,EAAE,MAAMA,EAAE+B,GAAE,EAAE,EAAE,CAAC,EAbjCpB,EAa8H,SAAYnL,EAAEC,EAAEsB,EAAEpC,EAAEsC,EAAEiD,EAAEE,GAAG,IAAI4F,EAAEyE,KAAI,IAAIxG,GAAEzI,EAAFyI,CAAKxI,EAAEsB,EAAEpC,EAAEsC,EAAEiD,EAAEE,EAA0C,CAAvC,MAAM+F,GAAQ,GAALuE,GAAE1E,GAAMG,IAAIA,EAAE,EAAE,MAAMA,EAAE4B,GAAE,EAAE,EAAE,CAAC,EAb5NrK,EAc9P,SAAYlC,EAAEC,EAAEsB,EAAEpC,EAAEsC,EAAEiD,EAAEE,EAAE4F,GAAG,IAAIG,EAAEsE,KAAI,IAAIxG,GAAEzI,EAAFyI,CAAKxI,EAAEsB,EAAEpC,EAAEsC,EAAEiD,EAAEE,EAAE4F,EAA0C,CAAvC,MAAMU,GAAQ,GAALgE,GAAEvE,GAAMO,IAAIA,EAAE,EAAE,MAAMA,EAAEqB,GAAE,EAAE,EAAE,CAAC,EAd4J8C,GAc2B,SAAYrP,EAAEC,EAAEsB,EAAEpC,EAAEsC,EAAEiD,EAAEE,EAAE4F,EAAEG,GAAG,IAAIO,EAAE+D,KAAI,IAAIxG,GAAEzI,EAAFyI,CAAKxI,EAAEsB,EAAEpC,EAAEsC,EAAEiD,EAAEE,EAAE4F,EAAEG,EAA0C,CAAvC,MAAMQ,GAAQ,GAAL+D,GAAEhE,GAAMC,IAAIA,EAAE,EAAE,MAAMA,EAAEoB,GAAE,EAAE,EAAE,CAAC,EAdhItJ,EAe3B,SAAYjD,EAAEC,EAAEsB,EAAEpC,EAAEsC,EAAEiD,EAAEE,EAAE4F,EAAEG,EAAEO,EAAEC,GAAG,IAAIW,EAAEmD,KAAI,IAAIxG,GAAEzI,EAAFyI,CAAKxI,EAAEsB,EAAEpC,EAAEsC,EAAEiD,EAAEE,EAAE4F,EAAEG,EAAEO,EAAEC,EAA0C,CAAvC,MAAMa,GAAQ,GAALkD,GAAEpD,GAAME,IAAIA,EAAE,EAAE,MAAMA,EAAEO,GAAE,EAAE,EAAE,CAAC,EAfnFP,EAeoF,SAAYhM,EAAEC,EAAEsB,EAAEpC,EAAEsC,EAAEiD,EAAEE,EAAE4F,EAAEG,EAAEO,EAAEC,EAAEW,EAAEE,EAAEjC,EAAEC,EAAEC,GAAG,IAAIG,EAAE6E,KAAI,IAAIxG,GAAEzI,EAAFyI,CAAKxI,EAAEsB,EAAEpC,EAAEsC,EAAEiD,EAAEE,EAAE4F,EAAEG,EAAEO,EAAEC,EAAEW,EAAEE,EAAEjC,EAAEC,EAAEC,EAA0C,CAAvC,MAAME,GAAQ,GAAL+E,GAAE9E,GAAMD,IAAIA,EAAE,EAAE,MAAMA,EAAEoC,GAAE,EAAE,EAAE,CAAC,EAftN1M,GAgB+E,SAAYG,EAAEC,EAAEsB,EAAEpC,EAAEsC,EAAEiD,EAAEE,EAAE4F,GAAG,IAAIG,EAAEsE,KAAI,IAAI9G,GAAGnI,EAAEC,EAAEsB,EAAEpC,EAAEsC,EAAEiD,EAAEE,EAAE4F,EAA0C,CAAvC,MAAMU,GAAQ,GAALgE,GAAEvE,GAAMO,IAAIA,EAAE,EAAE,MAAMA,EAAEqB,GAAE,EAAE,EAAE,CAAC,EAhBhLhD,EAiBzL,SAAYvJ,EAAEC,EAAEsB,EAAEpC,EAAEsC,EAAEiD,EAAEE,EAAE4F,EAAEG,EAAEO,EAAEC,EAAEW,GAAG,IAAIE,EAAEiD,KAAI,IAAI7G,GAAGpI,EAAEC,EAAEsB,EAAEpC,EAAEsC,EAAEiD,EAAEE,EAAE4F,EAAEG,EAAEO,EAAEC,EAAEW,EAA0C,CAAvC,MAAM/B,GAAQ,GAALmF,GAAElD,GAAMjC,IAAIA,EAAE,EAAE,MAAMA,EAAEwC,GAAE,EAAE,EAAE,CAAC,EAjBuE+C,EAiB9R,SAAYtP,EAAEC,EAAEsB,EAAEpC,EAAEsC,EAAEiD,GAAG,IAAIE,EAAEqK,KAAI,IAAIrH,GAAG5H,EAAEC,EAAEsB,EAAEpC,EAAEsC,EAAEiD,EAA0C,CAAvC,MAAM8F,GAAQ,GAAL0E,GAAEtK,GAAM4F,IAAIA,EAAE,EAAE,MAAMA,EAAE+B,GAAE,EAAE,EAAE,CAAC,EAjBoM5B,EAAE,SAAS3K,GAAG,OAAOA,CAAC,EAAEoK,EAAE,SAASpK,GAAG0J,GAAE1J,CAAC,EAAEE,GAAG4J,GAAGzJ,EAAE,SAASL,EAAEC,EAAEsB,EAAEpC,GAAG,OAAO2K,GAAG9J,EAAEC,EAAEsB,EAAEpC,EAAE,IAC1X,WAAY,SAASa,EAAEyB,GAAG7B,EAAE2P,IAAI9N,EAAE9M,QAAQmP,EAAGlE,EAAE2P,IAAIrB,GAAGjJ,IAAKS,EAAG9F,EAAE2P,IAAIC,GAAG5J,EAAGM,QAAQtG,EAAE2P,IAAIzB,IAAI1H,IAAIxG,EAAE6P,wBAAwB7P,EAAE6P,uBAAuBrJ,GAAG,GAAGA,IAAI,OAAOC,IAAKqJ,cAAcrJ,GAAIA,EAAG,MAAMC,IAAI7E,EAAE6E,EAAEA,EAAE,KAAK7E,KAAK,CAAC,SAASxB,EAAEwB,GAAGzB,EAAEyB,EAAEkO,SAAS,CAAC,SAASpO,EAAEE,GAAG,OAzBnQ,WAAc,IAAIwB,IAAItC,GAAIE,GAAG,CAAC,GAAG,mBAAmB+O,QAAQzJ,EAAEO,WAAW,WAAW,OAAOkJ,MAAMzJ,EAAE,CAAC0J,YAAY,gBAAgBC,MAAK,SAAS9P,GAAG,IAAIA,EAAE+P,GAAG,KAAK,uCAAuC5J,EAAE,IAAI,OAAOnG,EAAEgQ,aAAa,IAAGC,OAAM,WAAW,OAAOpJ,GAAI,IAAG,GAAG1G,EAAE,OAAO,IAAIvE,SAAQ,SAASoE,EAAEC,GAAGE,EAAEgG,GAAE,SAAS5E,GAAGvB,EAAE,IAAI/I,WAAWsK,GAAG,GAAEtB,EAAE,GAAE,CAAC,OAAOrE,QAAQC,UAAUiU,MAAK,WAAW,OAAOjJ,GAAI,GAAE,CAyBjIyG,GAAKwC,MAAK,SAASpL,GAAG,OAAOd,YAAYsM,YAAYxL,EAAEvF,EAAE,IAAG2Q,MAAK,SAASpL,GAAG,OAAOA,CAAC,IAAGoL,KAAKrO,GAAE,SAASiD,GAAGxC,EAAE,0CAA0CwC,GAAGb,EAAEa,EAAE,GAAE,CAAC,IAAIvF,EAAE,CAACa,EAAE6H,IAA8D,GAA1DzB,IAAIxG,EAAE6P,wBAAwB7P,EAAE6P,uBAAuBrJ,GAAMxG,EAAEuQ,gBAAgB,IAAI,OAAOvQ,EAAEuQ,gBAAgBhR,EACpiBa,EAAgF,CAA7E,MAAMyB,GAAG,OAAOS,EAAE,sDAAsDT,IAAG,CAAE,EAAoBwB,GAAG,mBAAmBW,YAAYwM,sBAAsB3J,KAAMN,EAAEO,WAAW,YAAY3F,GAAG,mBAAmB6O,MAAMrO,EAAEtB,GAAG2P,MAAMzJ,EAAE,CAAC0J,YAAY,gBAAgBC,MAAK,SAASrO,GAAG,OAAOmC,YAAYwM,qBAAqB3O,EAAEtC,GAAG2Q,KAAK7P,GAAE,SAASyE,GAAyF,OAAtFxC,EAAE,kCAAkCwC,GAAGxC,EAAE,6CAAoDX,EAAEtB,EAAE,GAAE,KAAOgQ,MAAMnQ,EAAa,CAD/c,GAEAF,EAAEyQ,mBAAmB,WAAW,OAAOzQ,EAAEyQ,mBAAmBzQ,EAAE2P,IAAIzB,IAAIwC,MAAM,KAAK5D,UAAU,EAAE9M,EAAE2Q,SAAS,WAAW,OAAO3Q,EAAE2Q,SAAS3Q,EAAE2P,IAAI3G,IAAI0H,MAAM,KAAK5D,UAAU,EAAE9M,EAAE4Q,yBAAyB,WAAW,OAAO5Q,EAAE4Q,yBAAyB5Q,EAAE2P,IAAI1G,IAAIyH,MAAM,KAAK5D,UAAU,EAAE9M,EAAE6Q,4BAA4B,WAAW,OAAO7Q,EAAE6Q,4BAA4B7Q,EAAE2P,IAAIzG,IAAIwH,MAAM,KAAK5D,UAAU,EAAE9M,EAAE8Q,0BAA0B,WAAW,OAAO9Q,EAAE8Q,0BAA0B9Q,EAAE2P,IAAI/F,IAAI8G,MAAM,KAAK5D,UAAU,EACve9M,EAAE+Q,0BAA0B,WAAW,OAAO/Q,EAAE+Q,0BAA0B/Q,EAAE2P,IAAI9F,IAAI6G,MAAM,KAAK5D,UAAU,EAAE9M,EAAEgR,kBAAkB,WAAW,OAAOhR,EAAEgR,kBAAkBhR,EAAE2P,IAAIP,IAAIsB,MAAM,KAAK5D,UAAU,EAAE9M,EAAEiR,mBAAmB,WAAW,OAAOjR,EAAEiR,mBAAmBjR,EAAE2P,IAAI5F,IAAI2G,MAAM,KAAK5D,UAAU,EAAE9M,EAAEkR,kBAAkB,WAAW,OAAOlR,EAAEkR,kBAAkBlR,EAAE2P,IAAI3F,IAAI0G,MAAM,KAAK5D,UAAU,EAAE9M,EAAEmR,mBAAmB,WAAW,OAAOnR,EAAEmR,mBAAmBnR,EAAE2P,IAAI1F,IAAIyG,MAAM,KAAK5D,UAAU,EACzd9M,EAAEoR,iBAAiB,WAAW,OAAOpR,EAAEoR,iBAAiBpR,EAAE2P,IAAIjD,IAAIgE,MAAM,KAAK5D,UAAU,EAAE9M,EAAEqR,kBAAkB,WAAW,OAAOrR,EAAEqR,kBAAkBrR,EAAE2P,IAAIzF,IAAIwG,MAAM,KAAK5D,UAAU,EAAE9M,EAAEsR,SAAS,WAAW,OAAOtR,EAAEsR,SAAStR,EAAE2P,IAAI5C,IAAI2D,MAAM,KAAK5D,UAAU,EAAE9M,EAAEuR,iBAAiB,WAAW,OAAOvR,EAAEuR,iBAAiBvR,EAAE2P,IAAIf,IAAI8B,MAAM,KAAK5D,UAAU,EAAE9M,EAAEwR,kBAAkB,WAAW,OAAOxR,EAAEwR,kBAAkBxR,EAAE2P,IAAId,IAAI6B,MAAM,KAAK5D,UAAU,EAC/a9M,EAAEyR,kBAAkB,WAAW,OAAOzR,EAAEyR,kBAAkBzR,EAAE2P,IAAI7S,IAAI4T,MAAM,KAAK5D,UAAU,EAAE9M,EAAE0R,qBAAqB,WAAW,OAAO1R,EAAE0R,qBAAqB1R,EAAE2P,IAAIgC,IAAIjB,MAAM,KAAK5D,UAAU,EAAE9M,EAAE4R,sBAAsB,WAAW,OAAO5R,EAAE4R,sBAAsB5R,EAAE2P,IAAIkC,IAAInB,MAAM,KAAK5D,UAAU,EAAE9M,EAAE8R,sBAAsB,WAAW,OAAO9R,EAAE8R,sBAAsB9R,EAAE2P,IAAIoC,IAAIrB,MAAM,KAAK5D,UAAU,EAAE9M,EAAEgS,QAAQ,WAAW,OAAOhS,EAAEgS,QAAQhS,EAAE2P,IAAIsC,IAAIvB,MAAM,KAAK5D,UAAU,EACvc9M,EAAEkS,iBAAiB,WAAW,OAAOlS,EAAEkS,iBAAiBlS,EAAE2P,IAAIwC,IAAIzB,MAAM,KAAK5D,UAAU,EACvF,IAW6IqB,GAXzIpF,GAAG/I,EAAEoS,QAAQ,WAAW,OAAOrJ,GAAG/I,EAAEoS,QAAQpS,EAAE2P,IAAI0C,IAAI3B,MAAM,KAAK5D,UAAU,EAAEnE,GAAG3I,EAAEsS,MAAM,WAAW,OAAO3J,GAAG3I,EAAEsS,MAAMtS,EAAE2P,IAAI4C,IAAI7B,MAAM,KAAK5D,UAAU,EAAE+B,GAAG7O,EAAEwS,QAAQ,WAAW,OAAO3D,GAAG7O,EAAEwS,QAAQxS,EAAE2P,IAAI8C,IAAI/B,MAAM,KAAK5D,UAAU,EAAE8B,GAAG5O,EAAE0S,iBAAiB,WAAW,OAAO9D,GAAG5O,EAAE0S,iBAAiB1S,EAAE2P,IAAIgD,IAAIjC,MAAM,KAAK5D,UAAU,EAAEH,GAAE3M,EAAE4S,UAAU,WAAW,OAAOjG,GAAE3M,EAAE4S,UAAU5S,EAAE2P,IAAIkD,IAAInC,MAAM,KAAK5D,UAAU,EAAEuC,GAAErP,EAAE8S,UAAU,WAAW,OAAOzD,GAAErP,EAAE8S,UAAU9S,EAAE2P,IAAIoD,IAAIrC,MAAM,KAAK5D,UAAU,EAAEwC,GAAEtP,EAAEgT,aACxe,WAAW,OAAO1D,GAAEtP,EAAEgT,aAAahT,EAAE2P,IAAIsD,IAAIvC,MAAM,KAAK5D,UAAU,EAAEnB,GAAG3L,EAAEkT,WAAW,WAAW,OAAOvH,GAAG3L,EAAEkT,WAAWlT,EAAE2P,IAAIwD,IAAIzC,MAAM,KAAK5D,UAAU,EAAEC,GAAG/M,EAAEoT,iBAAiB,WAAW,OAAOrG,GAAG/M,EAAEoT,iBAAiBpT,EAAE2P,IAAI0D,IAAI3C,MAAM,KAAK5D,UAAU,EAAErE,GAAGzI,EAAEsT,uBAAuB,WAAW,OAAO7K,GAAGzI,EAAEsT,uBAAuBtT,EAAE2P,IAAI4D,IAAI7C,MAAM,KAAK5D,UAAU,EAAE5E,GAAGlI,EAAEwT,UAAU,WAAW,OAAOtL,GAAGlI,EAAEwT,UAAUxT,EAAE2P,IAAI8D,IAAI/C,MAAM,KAAK5D,UAAU,EAAEkC,GAAGhP,EAAE0T,eAAe,WAAW,OAAO1E,GAAGhP,EAAE0T,eAAe1T,EAAE2P,IAAIgE,IAAIjD,MAAM,KAC5f5D,UAAU,EAAEzE,GAAGrI,EAAE4T,YAAY,WAAW,OAAOvL,GAAGrI,EAAE4T,YAAY5T,EAAE2P,IAAIkE,IAAInD,MAAM,KAAK5D,UAAU,EAAEvE,GAAGvI,EAAE8T,gBAAgB,WAAW,OAAOvL,GAAGvI,EAAE8T,gBAAgB9T,EAAE2P,IAAIoE,IAAIrD,MAAM,KAAK5D,UAAU,EAAE9E,GAAGhI,EAAEgU,aAAa,WAAW,OAAOhM,GAAGhI,EAAEgU,aAAahU,EAAE2P,IAAIsE,IAAIvD,MAAM,KAAK5D,UAAU,EAAEtE,GAAGxI,EAAEkU,kBAAkB,WAAW,OAAO1L,GAAGxI,EAAEkU,kBAAkBlU,EAAE2P,IAAIwE,IAAIzD,MAAM,KAAK5D,UAAU,EAAE3E,GAAGnI,EAAEoU,YAAY,WAAW,OAAOjM,GAAGnI,EAAEoU,YAAYpU,EAAE2P,IAAI0E,IAAI3D,MAAM,KAAK5D,UAAU,EAAElF,GAAG5H,EAAEsU,WAAW,WAAW,OAAO1M,GAAG5H,EAAEsU,WAClftU,EAAE2P,IAAI4E,IAAI7D,MAAM,KAAK5D,UAAU,EAAEhF,GAAG9H,EAAEwU,gBAAgB,WAAW,OAAO1M,GAAG9H,EAAEwU,gBAAgBxU,EAAE2P,IAAI8E,IAAI/D,MAAM,KAAK5D,UAAU,EAAEpF,GAAG1H,EAAE0U,aAAa,WAAW,OAAOhN,GAAG1H,EAAE0U,aAAa1U,EAAE2P,IAAIgF,IAAIjE,MAAM,KAAK5D,UAAU,EASnN,SAASxE,KAAK,SAASlI,IAAI,IAAI+N,KAAKA,IAAG,EAAGnO,EAAE4U,WAAU,GAAIpQ,GAAG,CAAgE,GAA/D2C,EAAEnB,GAAI/F,EAAGD,GAAMA,EAAE6U,sBAAqB7U,EAAE6U,uBAA0B7U,EAAE8U,QAAQ,IAAI,mBAAmB9U,EAAE8U,UAAU9U,EAAE8U,QAAQ,CAAC9U,EAAE8U,UAAU9U,EAAE8U,QAAQ3e,QAAQ,CAAC,IAAIkK,EAAEL,EAAE8U,QAAQzO,QAAQH,EAAGI,QAAQjG,EAAE,CAAC8G,EAAEjB,EAAG,CAAC,CAAC,KAAK,EAAEM,GAAG,CAAC,GAAGxG,EAAEoG,OAAO,IAAI,mBAAmBpG,EAAEoG,SAASpG,EAAEoG,OAAO,CAACpG,EAAEoG,SAASpG,EAAEoG,OAAOjQ,QAAQgQ,IAAKgB,EAAEpB,GAAI,EAAES,IAAIxG,EAAE+U,WAAW/U,EAAE+U,UAAU,cAAcC,YAAW,WAAWA,YAAW,WAAWhV,EAAE+U,UAAU,GAAG,GAAE,GAAG3U,GAAG,GAAE,IAAIA,IAAI,CAAC,CACze,GAFAJ,EAAEiV,aAAahQ,EAAGjF,EAAEkV,aAAa,SAAS9U,EAAEC,EAAEsB,GAAG,OAAOuD,EAAG9E,EAAEiE,EAAEhE,EAAEsB,EAAE,EAAE3B,EAAEmV,gBAAgB/P,EAAGpF,EAAE8S,UAAUzD,GAAErP,EAAEgT,aAAa1D,GAAEtP,EAAEkT,WAAWvH,GAAUjF,EAAE,SAASmB,IAAKsG,IAAI7F,KAAK6F,KAAKzH,EAAEmB,EAAG,EAEhL7H,EAAEoV,QAAQ,IAAI,mBAAmBpV,EAAEoV,UAAUpV,EAAEoV,QAAQ,CAACpV,EAAEoV,UAAU,EAAEpV,EAAEoV,QAAQjf,QAAQ6J,EAAEoV,QAAQxI,KAAV5M,GAGzF,OAH2GsI,KAGpGxI,EAAQK,KAEjB,GAGEnL,EAAOD,QAAU+K,C,8CCrEnB,eACA,SAEA,SACA,SAQa,KAAkB,KAa7B,IAZoC,iBAAzB,EAAAzJ,IAAIG,KAAK6e,aAA4B,EAAAhf,IAAIG,KAAK6e,YAAc,KACrE,EAAAhf,IAAIG,KAAK6e,YAAc,GAGI,kBAAlB,EAAAhf,IAAIG,KAAK8e,OAClB,EAAAjf,IAAIG,KAAK8e,MAAO,GAGY,kBAAnB,EAAAjf,IAAIG,KAAK+e,QAClB,EAAAlf,IAAIG,KAAK+e,OAAQ,GAGgB,iBAAxB,EAAAlf,IAAIG,KAAKgf,aAA4B3c,OAAO4c,UAAU,EAAApf,IAAIG,KAAKgf,aAAe,EAAAnf,IAAIG,KAAKgf,YAAc,EAAG,CACjH,MAAME,EAA0C,oBAAdjM,WAA4B,IAAAkM,QAAOxf,OAASsT,UAAUmM,oBACxF,EAAAvf,IAAIG,KAAKgf,WAAalJ,KAAKyB,IAAI,EAAGzB,KAAKuJ,MAAMH,GAAsB,GAAK,G,GAsB/D,KAAc,IAlB3B,MACErY,cAEE,gBAGM,IAAAyY,WACR,CAGAzY,2BAA2B0Y,EAAiC5c,GAE1D,MAAMiE,EAAU,IAAI,EAAA4Y,qCAEpB,aADM5Y,EAAQ6Y,UAAUF,EAAc5c,GAC/B6C,QAAQC,QAAQmB,EACzB,E,ugBCzCF,YACA,eAM8B,CAC5B,MAAM8Y,EAAc,WACpB,IAAA5gB,iBAAgB,MAAO4gB,EAAa,KACpC,IAAA5gB,iBAAgB,OAAQ4gB,EAAa,KACrC,IAAA5gB,iBAAgB,UAAW4gB,EAAa,E,0GCZ7B,EAAAC,oBACT,CAAChd,EAAkCid,EAAgBC,EAClDjZ,KACC,GAAsB,iBAAXjE,GAAmC,OAAZA,EAAkB,CAClD,GAAIkd,EAAKC,IAAInd,GACX,MAAM,IAAIpD,MAAM,iCAEhBsgB,EAAKE,IAAIpd,E,CAIbyE,OAAO4Y,QAAQrd,GAASwV,SAAQ,EAAExQ,EAAKvH,MACrC,MAAMrB,EAAO,EAAW6gB,EAASjY,EAAMA,EACvC,GAAqB,iBAAVvH,GACT,IAAAuf,qBAAoBvf,EAAkCrB,EAAO,IAAK8gB,EAAMjZ,QACnE,GAAqB,iBAAVxG,GAAuC,iBAAVA,EAC7CwG,EAAQ7H,EAAMqB,EAAM0T,gBACf,IAAqB,kBAAV1T,EAGhB,MAAM,IAAIb,MAAM,0CAA0Ca,GAF1DwG,EAAQ7H,EAAM,EAAU,IAAM,I,IAIhC,C,g2BC1BR,eAGA,YACA,SA2FsC,oBAAb8F,WAAyE,QAA7C,EAAQ,OAARA,eAAQ,IAARA,cAAQ,EAARA,SAAU0E,qBAAmC,SAAExD,KAEvF,EAAAuZ,SAAWzY,UAgCb,IAAAoZ,uBAAsB,EAAApgB,IAAIG,MAIxB,EAAAkgB,QAAUrZ,MAAMmY,EAAoBmB,KAS7CC,EAAKF,QAAQlB,EAAYmB,E,EAIhB,EAAAE,sBAAwBxZ,MAAMyZ,GAShCF,EAAKC,sBAAsBC,GAIzB,EAAAC,sBAAwB1Z,MAAM2Z,EAAkC7d,IAU9Dyd,EAAKG,sBAAsBC,EAAW7d,GAIxC,EAAA8d,cACT5Z,MAAMyZ,EAAmB3d,IASlByd,EAAKK,cAAcH,EAAO3d,GAIxB,EAAA+d,eAAiB7Z,MAAM8Z,IAShCP,EAAKM,eAAeC,E,EAIX,EAAAlZ,IAAMZ,MACf8Z,EAAmBC,EAAwBC,EAA8BC,EACzEne,IASOyd,EAAK3Y,IAAIkZ,EAAWC,EAAcC,EAAQC,EAAene,GAIvD,EAAAyG,aAAevC,MAAM8Z,IAS9BP,EAAKhX,aAAauX,E,sGC9NtB,eACA,SACA,SAEa,EAAAI,cAAiBpe,IAC5B,MAAM3C,GAAO,IAAAghB,eACb,IAAIC,EAAmB,EACvB,MAAMC,EAAmB,GAEnBC,EAA0Cxe,GAAW,CAAC,EAE5D,IACE,QAAkCrD,KAA9BqD,aAAO,EAAPA,EAASye,kBACXD,EAAWC,iBAAmB,OACzB,GACiC,iBAA7Bze,EAAQye,mBAAkC/e,OAAO4c,UAAUtc,EAAQye,mBAC1Eze,EAAQye,iBAAmB,GAAKze,EAAQye,iBAAmB,EAC7D,MAAM,IAAI7hB,MAAM,qCAAqCoD,EAAQye,oBAG/D,QAAmC9hB,KAA/BqD,aAAO,EAAPA,EAAS0e,mBACXF,EAAWE,kBAAoB,OAC1B,GAAyC,iBAA9B1e,EAAQ0e,oBAAmChf,OAAO4c,UAAUtc,EAAQ0e,mBACpF,MAAM,IAAI9hB,MAAM,qCAAqCoD,EAAQ0e,0BAGpC/hB,KAAvBqD,aAAO,EAAPA,EAAS2e,aACXH,EAAWG,WAAY,GAGzB,IAAIC,EAAgB,EAOpB,QANqBjiB,KAAjBqD,aAAO,EAAPA,EAAS6e,OACXD,GAAgB,IAAAE,iBAAgB9e,EAAQ6e,IAAKN,IAG/CD,EAAmBjhB,EAAKkb,qBACpBiG,EAAWC,iBAAmBD,EAAWE,oBAAsBF,EAAWG,UAAYC,GACjE,IAArBN,EACF,MAAM,IAAI1hB,MAAM,4BAclB,YAXuBD,KAAnBqD,aAAO,EAAPA,EAAS+e,SACX,IAAA/B,qBAAoBhd,EAAQ+e,MAAO,GAAI,IAAIC,SAAoC,CAACha,EAAKvH,KACnF,MAAMwhB,GAAgB,IAAAH,iBAAgB9Z,EAAKuZ,GACrCW,GAAkB,IAAAJ,iBAAgBrhB,EAAO8gB,GAE/C,GAAqF,IAAjFlhB,EAAKob,sBAAsB6F,EAAkBW,EAAeC,GAC9D,MAAM,IAAItiB,MAAM,iCAAiCoI,OAASvH,I,IAKzD,CAAC6gB,EAAkBC,E,CAC1B,MAAOnY,GAKP,MAJyB,IAArBkY,GACFjhB,EAAKsb,sBAAsB2F,GAE7BC,EAAO/I,QAAQnY,EAAK8b,OACd/S,C,8HC5DV,eACA,SACA,SAGA,SAEA,IAAI+Y,EAqBJ,6CAMEjb,4BAA4Bkb,GAG1B,MAAMrV,QAAiB8M,MAAMuI,GACvBnI,QAAoBlN,EAASkN,cACnC,OAAO,IAAAyG,uBAAsB,IAAIxf,WAAW+Y,GAC9C,CAEA/S,gBAAgB0Y,EAAiC5c,GAM/C,GALKmf,UACG,IAAA5B,SAAQ,EAAArgB,IAAIG,KAAKgf,WAlCT,CAAC7e,IACnB,OAAQA,GACN,IAAK,UACH,OAAO,EACT,IAAK,OACH,OAAO,EACT,IAAK,UACH,OAAO,EACT,IAAK,QACH,OAAO,EACT,IAAK,QACH,OAAO,EACT,QACE,MAAM,IAAIZ,MAAM,8BAA8BY,K,EAqBV6hB,CAAY,EAAAniB,IAAIM,WACpD2hB,GAAU,GAGgB,iBAAjBvC,EACT,GAAqB,oBAAV/F,MAAuB,CAEhC,MAAM8G,QAAc,IAAA2B,WAAU,EAAA7W,SAAV,CAAoBmU,IACvCxf,KAAK4gB,UAAW5gB,KAAKwH,WAAYxH,KAAKkH,mBAAqB,IAAAwZ,eAAcH,EAAO3d,E,KAC5E,CAGL,MAAMuf,QAAyCniB,KAAKsgB,sBAAsBd,IAEzExf,KAAK4gB,UAAW5gB,KAAKwH,WAAYxH,KAAKkH,mBAAqB,IAAAsZ,uBAAsB2B,EAAWvf,E,MAG9F5C,KAAK4gB,UAAW5gB,KAAKwH,WAAYxH,KAAKkH,mBAAqB,IAAAwZ,eAAclB,EAAc5c,EAE5F,CAEAkE,gBACE,OAAO,IAAA6Z,gBAAe3gB,KAAK4gB,UAC7B,CAEA9Z,UAAUC,EAAiCC,EAAqCpE,GAE9E,MAAMwf,EAAuB,GACvBvB,EAAyB,GAC/BxZ,OAAO4Y,QAAQlZ,GAAOqR,SAAQiK,IAC5B,MAAMrjB,EAAOqjB,EAAI,GACXC,EAASD,EAAI,GACbE,EAAQviB,KAAKwH,WAAW9H,QAAQV,GACtC,IAAe,IAAXujB,EACF,MAAM,IAAI/iB,MAAM,kBAAkBR,MAEpCojB,EAAWviB,KAAKyiB,GAChBzB,EAAahhB,KAAK0iB,EAAM,IAG1B,MAAMxB,EAA0B,GAChC1Z,OAAO4Y,QAAQjZ,GAASoR,SAAQiK,IAC9B,MAAMrjB,EAAOqjB,EAAI,GAEXE,EAAQviB,KAAKkH,YAAYxH,QAAQV,GACvC,IAAe,IAAXujB,EACF,MAAM,IAAI/iB,MAAM,mBAAmBR,MAErC+hB,EAAclhB,KAAK0iB,EAAM,IAG3B,MAAMC,QACI,IAAA9a,KAAI1H,KAAK4gB,UAAWC,EAAcuB,EAAW7Z,KAAIwM,GAAK,CAACA,EAAEpT,KAAMoT,EAAElT,KAAMkT,EAAEnT,QAAQmf,EAAene,GAEpG6f,EAAoC,CAAC,EAC3C,IAAK,IAAIhjB,EAAI,EAAGA,EAAI+iB,EAAQ5iB,OAAQH,IAClCgjB,EAAOziB,KAAKkH,YAAY6Z,EAActhB,KAAO,IAAI,EAAA8B,OAAOihB,EAAQ/iB,GAAG,GAAI+iB,EAAQ/iB,GAAG,GAAI+iB,EAAQ/iB,GAAG,IAEnG,OAAOgjB,CACT,CAEArZ,iBAEA,CAEAC,gBACO,IAAAA,cAAarJ,KAAK4gB,UACzB,E,yGC7GF,eACA,SACA,SAmEa,EAAA8B,kBAAqB9f,IAChC,MAAM3C,GAAO,IAAAghB,eACb,IAAI0B,EAAuB,EAC3B,MAAMxB,EAAmB,GAEnByB,EAAkDhgB,GAAW,CAAC,EA5CzC,CAACA,IACvBA,EAAQ+e,QACX/e,EAAQ+e,MAAQ,CAAC,GAEd/e,EAAQ+e,MAAMkB,UACjBjgB,EAAQ+e,MAAMkB,QAAU,CAAC,GAE3B,MAAMA,EAAUjgB,EAAQ+e,MAAMkB,QACzBA,EAAQC,+BAEXD,EAAQC,6BAA+B,I,EAmCzCC,CAAqBH,GAErB,SAC0CrjB,KAApCqD,aAAO,EAAPA,EAASogB,0BACXJ,EAAeI,uBAAyB,OAE1C,MAAMA,EA7EuB,CAACA,IAChC,OAAQA,GACN,IAAK,WACH,OAAO,EACT,IAAK,QACH,OAAO,EACT,IAAK,WACH,OAAO,EACT,IAAK,MACH,OAAO,GACT,QACE,MAAM,IAAIxjB,MAAM,yCAAyCwjB,K,EAkE5BC,CAAyBL,EAAeI,6BAEpCzjB,KAA/BqD,aAAO,EAAPA,EAASsgB,qBACXN,EAAeM,mBAAoB,QAGH3jB,KAA9BqD,aAAO,EAAPA,EAASugB,oBACXP,EAAeO,kBAAmB,QAGL5jB,KAA3BqD,aAAO,EAAPA,EAASwgB,iBACXR,EAAeQ,cAAgB,cAEjC,MAAMA,EA3Ee,CAACA,IACxB,OAAQA,GACN,IAAK,aACH,OAAO,EACT,IAAK,WACH,OAAO,EACT,QACE,MAAM,IAAI5jB,MAAM,+BAA+B4jB,K,EAoE3BC,CAAiBT,EAAeQ,eAEtD,IAAIE,EAAkB,EAKtB,QAJuB/jB,KAAnBqD,aAAO,EAAPA,EAAS2gB,SACXD,GAAkB,IAAA5B,iBAAgB9e,EAAQ2gB,MAAOpC,SAGjB5hB,KAA9BqD,aAAO,EAAPA,EAASye,kBACXuB,EAAevB,iBAAmB,OAC7B,GACiC,iBAA7Bze,EAAQye,mBAAkC/e,OAAO4c,UAAUtc,EAAQye,mBAC1Eze,EAAQye,iBAAmB,GAAKze,EAAQye,iBAAmB,EAC7D,MAAM,IAAI7hB,MAAM,qCAAqCoD,EAAQye,oBAG/D,QAAmC9hB,KAA/BqD,aAAO,EAAPA,EAAS0e,mBACXsB,EAAetB,kBAAoB,OAC9B,GAAyC,iBAA9B1e,EAAQ0e,oBAAmChf,OAAO4c,UAAUtc,EAAQ0e,mBACpF,MAAM,IAAI9hB,MAAM,qCAAqCoD,EAAQ0e,qBAW/D,QARiC/hB,KAA7BqD,aAAO,EAAPA,EAAS4gB,mBACXZ,EAAeY,iBAAkB,GAGnCb,EAAuB1iB,EAAKoa,yBACxB2I,IAA0BJ,EAAeM,oBAAsBN,EAAeO,iBAAmBC,IAC/FR,EAAeY,gBAAkB,EAAGF,EAAiBV,EAAevB,iBACtEuB,EAAetB,mBACU,IAAzBqB,EACF,MAAM,IAAInjB,MAAM,gCAkBlB,OAfIoD,aAAO,EAAPA,EAAS0F,qBAlFb,EAACqa,EAA8Bra,EAC9B6Y,KACC,IAAK,MAAMsC,KAAMnb,EAAoB,CACnC,IAAIob,EAAuB,iBAAPD,EAAkBA,EAAKA,EAAGzkB,KAG9C,OAAQ0kB,GACN,IAAK,UACHA,EAAS,UACT,MACF,IAAK,OACL,IAAK,MACH,SACF,QACE,MAAM,IAAIlkB,MAAM,qBAAqBkkB,KAGzC,MAAMC,GAAmB,IAAAjC,iBAAgBgC,EAAQvC,GACjD,GAA0F,KAAtF,IAAAF,eAAc3G,4BAA4BqI,EAAsBgB,GAClE,MAAM,IAAInkB,MAAM,oCAAoCkkB,I,GAgExDE,CAAsBjB,EAAsB/f,EAAQ0F,mBAAoB6Y,QAGnD5hB,KAAnBqD,aAAO,EAAPA,EAAS+e,SACX,IAAA/B,qBAAoBhd,EAAQ+e,MAAO,GAAI,IAAIC,SAAoC,CAACha,EAAKvH,KACnF,MAAMwhB,GAAgB,IAAAH,iBAAgB9Z,EAAKuZ,GACrCW,GAAkB,IAAAJ,iBAAgBrhB,EAAO8gB,GAE/C,GAA6F,IAAzFlhB,EAAKsa,0BAA0BoI,EAAsBd,EAAeC,GACtE,MAAM,IAAItiB,MAAM,qCAAqCoI,OAASvH,I,IAK7D,CAACsiB,EAAsBxB,E,CAC9B,MAAOnY,GAKP,MAJ6B,IAAzB2Z,GACF1iB,EAAKua,0BAA0BmI,GAEjCxB,EAAO/I,QAAQnY,EAAK8b,OACd/S,C,yGCtJV,eAEa,EAAA0Y,gBAAkB,CAAC9f,EAAcuf,KAC5C,MAAMlhB,GAAO,IAAAghB,eAEP4C,EAAa5jB,EAAK2e,gBAAgBhd,GAAQ,EAC1CkiB,EAAa7jB,EAAK4b,QAAQgI,GAIhC,OAHA5jB,EAAK0e,aAAa/c,EAAMkiB,EAAYD,GACpC1C,EAAOthB,KAAKikB,GAELA,CAAU,C,kOCPnB,eACA,SACA,SACA,SAOa,EAAA3D,QAAU,CAAClB,EAAoBmB,KAC1C,MAAM2D,GAAY,IAAA9C,eAAc7G,SAAS6E,EAAYmB,GACrD,GAAkB,IAAd2D,EACF,MAAM,IAAIvkB,MAAM,8CAA8CukB,I,EASlE,MAAMC,EAAiB,IAAIpjB,IAMd,EAAA0f,sBAAyBC,IACpC,MAAMtgB,GAAO,IAAAghB,eACPgD,EAAkBhkB,EAAK4b,QAAQ0E,EAAMnY,YAE3C,OADAnI,EAAKiP,OAAO5N,IAAIif,EAAO0D,GAChB,CAACA,EAAiB1D,EAAMnY,WAAW,EAG/B,EAAAoY,sBACT,CAAC2B,EAAkCvf,KACjC,MAAM3C,GAAO,IAAAghB,eAEb,IAAIiD,EAAgB,EAChBvB,EAAuB,EACvBxB,EAAmB,GAEvB,IAIE,IAHCwB,EAAsBxB,IAAU,IAAAuB,mBAAkB9f,GAEnDshB,EAAgBjkB,EAAKwa,kBAAkB0H,EAAU,GAAIA,EAAU,GAAIQ,GAC7C,IAAlBuB,EACF,MAAM,IAAI1kB,MAAM,yB,SAGlBS,EAAK8b,MAAMoG,EAAU,IACrBliB,EAAKua,0BAA0BmI,GAC/BxB,EAAO/I,QAAQnY,EAAK8b,M,CAGtB,MAAMoI,EAAalkB,EAAK0a,kBAAkBuJ,GACpCE,EAAcnkB,EAAK2a,mBAAmBsJ,GAEtC1c,EAAa,GACb6c,EAAwB,GACxBnd,EAAc,GACdod,EAAyB,GAC/B,IAAK,IAAI7kB,EAAI,EAAGA,EAAI0kB,EAAY1kB,IAAK,CACnC,MAAMT,EAAOiB,EAAK4a,iBAAiBqJ,EAAezkB,GAClD,GAAa,IAATT,EACF,MAAM,IAAIQ,MAAM,2BAElB6kB,EAAsBxkB,KAAKb,GAC3BwI,EAAW3H,KAAKI,EAAKye,aAAa1f,G,CAEpC,IAAK,IAAIS,EAAI,EAAGA,EAAI2kB,EAAa3kB,IAAK,CACpC,MAAMT,EAAOiB,EAAK6a,kBAAkBoJ,EAAezkB,GACnD,GAAa,IAATT,EACF,MAAM,IAAIQ,MAAM,4BAElB8kB,EAAuBzkB,KAAKb,GAC5BkI,EAAYrH,KAAKI,EAAKye,aAAa1f,G,CAIrC,OADAglB,EAAe1iB,IAAI4iB,EAAe,CAACA,EAAeG,EAAuBC,IAClE,CAACJ,EAAe1c,EAAYN,EAAY,EAQxC,EAAAwZ,cACT,CAACH,EAAmB3d,KAClB,MAAMuf,GAAmC,IAAA7B,uBAAsBC,GAC/D,OAAO,IAAAC,uBAAsB2B,EAAWvf,EAAQ,EAGzC,EAAA+d,eAAkBC,IAC7B,MAAM3gB,GAAO,IAAAghB,eACP4B,EAAUmB,EAAe/hB,IAAI2e,GACnC,IAAKiC,EACH,MAAM,IAAIrjB,MAAM,sBAElB,MAAM0kB,EAAgBrB,EAAQ,GACxBwB,EAAwBxB,EAAQ,GAChCyB,EAAyBzB,EAAQ,GAEvCwB,EAAsBjM,QAAQnY,EAAK8a,UACnCuJ,EAAuBlM,QAAQnY,EAAK8a,UACpC9a,EAAKya,mBAAmBwJ,GACxBF,EAAeO,OAAO3D,EAAU,EA2BlC,MAAM4D,EAA8B7iB,IAClC,OAAQA,GACN,IAAK,OACH,OAAO,EACT,IAAK,QACH,OAAO,EACT,IAAK,OACH,OAAO,EACT,IAAK,QACH,OAAO,EACT,IAAK,SACH,OAAO,EACT,IAAK,QACH,OAAO,EACT,IAAK,SACH,OAAO,GACT,IAAK,UACH,OAAO,EACT,IAAK,UACH,OAAO,GACT,IAAK,SACH,OAAO,EACT,IAAK,QACH,OAAO,EACT,IAAK,SACH,OAAO,GAET,QACE,MAAM,IAAInC,MAAM,0BAA0BmC,K,EAI1C8iB,EAA8BC,IAClC,OAAQA,GACN,KAAK,EACH,MAAO,OACT,KAAK,EACH,MAAO,QACT,KAAK,EACH,MAAO,OACT,KAAK,EACH,MAAO,QACT,KAAK,EACH,MAAO,SACT,KAAK,EACH,MAAO,QACT,KAAK,GACH,MAAO,SACT,KAAK,EACH,MAAO,UACT,KAAK,GACH,MAAO,UACT,KAAK,EACH,MAAO,SACT,KAAK,EACH,MAAO,QACT,KAAK,GACH,MAAO,SAET,QACE,MAAM,IAAIllB,MAAM,0BAA0BklB,K,EAI1CC,EAAiChjB,IAGjC,OAAQA,GACN,IAAK,UACH,OAAOd,aACT,IAAK,QAUL,IAAK,OACH,OAAOC,WATT,IAAK,OACH,OAAOC,UACT,IAAK,SACH,OAAOC,YACT,IAAK,QACH,OAAOC,WACT,IAAK,QACH,OAAOC,WAGT,IAAK,UACH,OAAOC,aACT,IAAK,SACH,OAAOC,YACT,IAAK,QACH,OAAOb,cACT,IAAK,SACH,OAAOG,eACT,QACE,MAAM,IAAIlB,MAAM,qBAAqBmC,K,EAOlC,EAAA+F,IACT,CAACkZ,EAAmBC,EAAwBC,EAA8BC,EACzEne,KACC,MAAM3C,GAAO,IAAAghB,eACP4B,EAAUmB,EAAe/hB,IAAI2e,GACnC,IAAKiC,EACH,MAAM,IAAIrjB,MAAM,sBAElB,MAAM0kB,EAAgBrB,EAAQ,GACxBwB,EAAwBxB,EAAQ,GAChCyB,EAAyBzB,EAAQ,GAEjCsB,EAAatD,EAAajhB,OAC1BwkB,EAAcrD,EAAcnhB,OAElC,IAAIshB,EAAmB,EACnB0D,EAA6B,GAEjC,MAAMC,EAAwB,GACxBC,EAAwB,GAE9B,KACG5D,EAAkB0D,IAAoB,IAAA5D,eAAcpe,GAGrD,IAAK,IAAInD,EAAI,EAAGA,EAAI0kB,EAAY1kB,IAAK,CACnC,MAAMslB,EAAWjE,EAAOrhB,GAAG,GACrBoC,EAAOif,EAAOrhB,GAAG,GACjBmC,EAAOkf,EAAOrhB,GAAG,GAEvB,IAAIqkB,EACAkB,EAEJ,GAAIljB,MAAMC,QAAQH,GAAO,CAEvBojB,EAAiB,EAAIpjB,EAAKhC,OAC1BkkB,EAAa7jB,EAAK4b,QAAQmJ,GAC1BF,EAAYjlB,KAAKikB,GACjB,IAAImB,EAAYnB,EAAa,EAC7B,IAAK,IAAIrkB,EAAI,EAAGA,EAAImC,EAAKhC,OAAQH,IAAK,CACpC,GAAuB,iBAAZmC,EAAKnC,GACd,MAAM,IAAIJ,UAAU,wBAAwBI,qBAE9CQ,EAAKmP,QAAQ6V,MAAe,IAAAvD,iBAAgB9f,EAAKnC,GAAIqlB,E,OAGvDE,EAAiBpjB,EAAKwG,WACtB0b,EAAa7jB,EAAK4b,QAAQmJ,GAC1BF,EAAYjlB,KAAKikB,GACjB7jB,EAAKiP,OAAO5N,IAAI,IAAIR,WAAWc,EAAKe,OAAQf,EAAKuG,WAAY6c,GAAiBlB,GAGhF,MAAMoB,EAAQjlB,EAAKsc,YACb4I,EAAallB,EAAK0c,WAAW,EAAI9a,EAAKjC,QAC5C,IACE,IAAIwlB,EAAWD,EAAa,EAC5BtjB,EAAKuW,SAAQ3O,GAAKxJ,EAAKgP,OAAOmW,KAAc3b,IAC5C,MAAM6Y,EAASriB,EAAK+a,iBAChBwJ,EAA2BO,GAAWjB,EAAYkB,EAAgBG,EAAYtjB,EAAKjC,QACvF,GAAe,IAAX0iB,EACF,MAAM,IAAI9iB,MAAM,yBAElBqlB,EAAYhlB,KAAKyiB,E,SAEjBriB,EAAKwc,aAAayI,E,EAItB,MAAMG,EAAiBplB,EAAKsc,YACtB+I,EAAoBrlB,EAAK0c,WAAwB,EAAbwH,GACpCoB,EAAmBtlB,EAAK0c,WAAwB,EAAbwH,GACnCqB,EAAqBvlB,EAAK0c,WAAyB,EAAdyH,GACrCqB,EAAoBxlB,EAAK0c,WAAyB,EAAdyH,GAE1C,IACE,IAAIsB,EAAmBJ,EAAoB,EACvCK,EAAkBJ,EAAmB,EACrCK,EAAoBJ,EAAqB,EACzCK,EAAmBJ,EAAoB,EAC3C,IAAK,IAAIhmB,EAAI,EAAGA,EAAI0kB,EAAY1kB,IAC9BQ,EAAKmP,QAAQsW,KAAsBb,EAAYplB,GAC/CQ,EAAKmP,QAAQuW,KAAqBtB,EAAsBxD,EAAaphB,IAEvE,IAAK,IAAIA,EAAI,EAAGA,EAAI2kB,EAAa3kB,IAC/BQ,EAAKmP,QAAQwW,KAAuB,EACpC3lB,EAAKmP,QAAQyW,KAAsBvB,EAAuBvD,EAActhB,IAI1E,IAAIskB,EAAY9jB,EAAKwb,QACjByI,EAAeqB,EAAkBD,EAAmBnB,EAAYsB,EAAmBrB,EACnFoB,EAAoBtE,GAExB,MAAM4E,EAA+B,GAErC,GAAkB,IAAd/B,EACF,IAAK,IAAItkB,EAAI,EAAGA,EAAI2kB,EAAa3kB,IAAK,CACpC,MAAM6iB,EAASriB,EAAKmP,QAAQoW,EAAqB,EAAI/lB,GAE/CsmB,EAA2B9lB,EAAKsc,YAEhCyJ,EAAmB/lB,EAAK0c,WAAW,IAEzC,IAAIhb,EAA6BmiB,EAAa,EAC9C,IAGE,GAFAC,EAAY9jB,EAAKgb,kBACbqH,EAAQ0D,EAAkBA,EAAmB,EAAGA,EAAmB,EAAGA,EAAmB,IAC3E,IAAdjC,EACF,MAAM,IAAIvkB,MAAM,iDAAiDukB,KAEnE,IAAIkC,EAAkBD,EAAmB,EACzC,MAAMjB,EAAW9kB,EAAKmP,QAAQ6W,KAC9BnC,EAAa7jB,EAAKmP,QAAQ6W,KAC1B,MAAMd,EAAallB,EAAKmP,QAAQ6W,KAC1BC,EAAajmB,EAAKmP,QAAQ6W,KAC1BpkB,EAAO,GACb,IAAK,IAAIpC,EAAI,EAAGA,EAAIymB,EAAYzmB,IAC9BoC,EAAKhC,KAAKI,EAAKmP,QAAQ+V,EAAa,EAAI1lB,IAE1CQ,EAAK8a,SAASoK,GAEd,MAAM/iB,EAAuB,IAAhBP,EAAKjC,OAAe,EAAIiC,EAAKskB,QAAO,CAACtc,EAAGC,IAAMD,EAAIC,IAE/D,GADAnI,EAAO8iB,EAA2BM,GACrB,WAATpjB,EAAmB,CACrB,MAAMykB,EAAuB,GAC7B,IAAInB,EAAYnB,EAAa,EAC7B,IAAK,IAAIrkB,EAAI,EAAGA,EAAI2C,EAAM3C,IAAK,CAC7B,MAAM+D,EAASvD,EAAKmP,QAAQ6V,KACtBoB,EAAiB5mB,IAAM2C,EAAO,OAAI7C,EAAYU,EAAKmP,QAAQ6V,GAAazhB,EAC9E4iB,EAAWvmB,KAAKI,EAAKye,aAAalb,EAAQ6iB,G,CAE5CP,EAAOjmB,KAAK,CAAC8B,EAAME,EAAMukB,G,KACpB,CACL,MACMxkB,EAAO,IADiB+iB,EAA8BhjB,GAC/C,CAA0BS,GACvC,IAAItB,WAAWc,EAAKe,OAAQf,EAAKuG,WAAYvG,EAAKwG,YAC7C9G,IAAIrB,EAAKiP,OAAOZ,SAASwV,EAAYA,EAAaliB,EAAKwG,aAC5D0d,EAAOjmB,KAAK,CAAC8B,EAAME,EAAMD,G,UAG3B3B,EAAKwc,aAAasJ,GACL,WAATpkB,GAAqBmiB,GACvB7jB,EAAK8b,MAAM+H,GAEb7jB,EAAKib,kBAAkBoH,E,EAK7B,GAAkB,IAAdyB,EACF,OAAO+B,EAEP,MAAM,IAAItmB,MAAM,yCAAyCukB,K,SAG3D9jB,EAAKwc,aAAa4I,E,UAGpBR,EAAYzM,QAAQnY,EAAKib,mBACzB4J,EAAY1M,QAAQnY,EAAK8b,OAEzB9b,EAAKsb,sBAAsB2F,GAC3B0D,EAAiBxM,QAAQnY,EAAK8b,M,GAOzB,EAAA1S,aAAgBuX,IAC3B,MAAM3gB,GAAO,IAAAghB,eACP4B,EAAUmB,EAAe/hB,IAAI2e,GACnC,IAAKiC,EACH,MAAM,IAAIrjB,MAAM,sBAElB,MAAM0kB,EAAgBrB,EAAQ,GAGxByD,EAAkBrmB,EAAK0b,iBAAiBuI,GAC9C,GAAwB,IAApBoC,EACF,MAAM,IAAI9mB,MAAM,kCAElBS,EAAK8a,SAASuL,EAAgB,EAGnB,EAAAC,2BAA8BC,IACzC,MAAMC,EAA6B,GACnC,IAAK,MAAMnE,KAAUkE,EAAS,CAC5B,MAAM5kB,EAAO0gB,EAAO,IACfxgB,MAAMC,QAAQH,IAASA,EAAKe,QAC/B8jB,EAAQ5mB,KAAK+B,EAAKe,O,CAGtB,OAAO8jB,CAAO,C,m2BC5ahB,gBAIA,YAEMC,EAE4E,UAElF,IAAIzmB,EACA2I,GAAc,EACd+d,GAAe,EACf9d,GAAU,EAEd,MAiDM+d,EAAkB,CAACC,EAAkBC,IACrCA,EACKD,EAAU,8BAAgC,yBAE1CA,EAAU,qBAAuB,gBAI/B,EAAA3G,sBAAwBpZ,MAAMigB,IACzC,GAAIne,EACF,OAAOnD,QAAQC,UAEjB,GAAIihB,EACF,MAAM,IAAInnB,MAAM,yDAElB,GAAIqJ,EACF,MAAM,IAAIrJ,MAAM,sDAGlBmnB,GAAe,EAGf,MAAMK,EAAUD,EAAMjI,YAChBG,EAAa8H,EAAM9H,WACnBF,EAAOgI,EAAMhI,KAEb+H,EAAa7H,EAAa,GA3EH,MAC7B,IAEE,MAAiC,oBAAtB/W,oBAMmB,oBAAnB+e,iBACT,IAAIA,gBAAiBC,MAAMC,YAAY,IAAIjf,kBAAkB,IAKxDuF,YAAY2Z,SAAS,IAAItmB,WAAW,CACzC,EAAG,GAAI,IAAK,IAAK,EAAG,EAAI,EAAI,EAAG,EAAG,EAAG,EAAI,GAAI,EAAK,EAAI,EAAG,EAAG,EAAI,EAAG,EACnE,EAAG,EAAI,EAAK,EAAK,EAAG,GAAI,GAAI,EAAG,EAAG,EAAG,GAAI,EAAI,IAAK,GAAI,EAAG,EAAG,GAAI,M,CAElE,MAAOkI,GACP,OAAO,C,GAuD4Bqe,GAC/BR,EAAU9H,GApDM,MACtB,IAeE,OAAOtR,YAAY2Z,SAAS,IAAItmB,WAAW,CACzC,EAAK,GAAI,IAAK,IAAK,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,EAAK,GAAK,EAAG,GAAI,EACvF,IAAK,GAAI,IAAK,GAAK,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAI,EAAI,IAAK,IAAK,EAAG,GAAI,K,CAEzF,MAAOkI,GACP,OAAO,C,GA+Bese,GAElBC,EAAgD,iBAApBR,EAAMS,UAAyBT,EAAMS,eAAYjoB,EAC7EkoB,EAAeb,GAAgB,EAAOE,GACtCY,EAAuBd,EAAgBC,EAASC,GAChDa,EAA8C,iBAApBZ,EAAMS,UAAyBT,EAAMS,UAAUE,QAAwBnoB,EAEvG,IAAIqoB,GAAY,EAEhB,MAAMC,EAA8B,GAgEpC,GA7DIb,EAAU,GACZa,EAAMhoB,KAAK,IAAI4F,SAASC,IACtB+Y,YAAW,KACTmJ,GAAY,EACZliB,GAAS,GACRshB,EAAQ,KAKfa,EAAMhoB,KAAK,IAAI4F,SAAQ,CAACC,EAASC,MACfmhB,EAAaJ,EAAyB,WACf,CACrCjW,WAAY,CAACqX,EAAkBC,IAYzBD,IAAaL,EAERE,QAAAA,GADgBJ,QAAAA,EAAsBQ,GACTL,EAG/BK,EAAkBD,IAabnO,MAEZlb,IACEkoB,GAAe,EACf/d,GAAc,EACd3I,EAAOxB,EACPiH,GAAS,IAGVsiB,IACCrB,GAAe,EACf9d,GAAU,EACVlD,EAAOqiB,EAAK,GACZ,WAGFviB,QAAQwiB,KAAKJ,GAEfD,EACF,MAAM,IAAIpoB,MAAM,2DAA2DwnB,M,EAIlE,EAAA/F,YAAc,KACzB,GAAIrY,GAAe3I,EACjB,OAAOA,EAGT,MAAM,IAAIT,MAAM,sCAAsC,EAG3C,EAAA0oB,QAAU,K,OACjBtf,GAAgB+d,GAAiB9d,IACnC8d,GAAe,EAEwB,QAAtC,EAAA1mB,EAA+BkoB,eAAO,SAAEC,sBACzCnoB,OAAOV,EAEPonB,GAAe,EACf/d,GAAc,EACdC,GAAU,E,sEC7LVwf,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBhpB,IAAjBipB,EACH,OAAOA,EAAahqB,QAGrB,IAAIC,EAAS4pB,EAAyBE,GAAY,CAGjD/pB,QAAS,CAAC,GAOX,OAHAiqB,EAAoBF,GAAUzgB,KAAKrJ,EAAOD,QAASC,EAAQA,EAAOD,QAAS8pB,GAGpE7pB,EAAOD,OACf,C,OCrBA8pB,EAAoB7e,EAAI,CAACjL,EAASkqB,KACjC,IAAI,IAAI9gB,KAAO8gB,EACXJ,EAAoBrP,EAAEyP,EAAY9gB,KAAS0gB,EAAoBrP,EAAEza,EAASoJ,IAC5EP,OAAOshB,eAAenqB,EAASoJ,EAAK,CAAEghB,YAAY,EAAM3mB,IAAKymB,EAAW9gB,IAE1E,ECND0gB,EAAoBrP,EAAI,CAAC4P,EAAKC,IAAUzhB,OAAOiP,UAAUzO,eAAeC,KAAK+gB,EAAKC,GCClFR,EAAoBte,EAAKxL,IACH,oBAAXuqB,QAA0BA,OAAOC,aAC1C3hB,OAAOshB,eAAenqB,EAASuqB,OAAOC,YAAa,CAAE3oB,MAAO,WAE7DgH,OAAOshB,eAAenqB,EAAS,aAAc,CAAE6B,OAAO,GAAO,ECFpCioB,EAAoB,G", "sources": ["webpack://ort/webpack/universalModuleDefinition", "webpack://ort/../common/dist/lib/backend-impl.js", "webpack://ort/../common/dist/lib/env.js", "webpack://ort/../common/dist/lib/env-impl.js", "webpack://ort/../common/dist/lib/tensor-impl.js", "webpack://ort/../common/dist/lib/tensor.js", "webpack://ort/../common/dist/lib/inference-session-impl.js", "webpack://ort/../common/dist/lib/inference-session.js", "webpack://ort/./lib/wasm/binding/ort-wasm.js", "webpack://ort/./lib/backend-wasm.ts", "webpack://ort/./lib/index.ts", "webpack://ort/./lib/wasm/options-utils.ts", "webpack://ort/./lib/wasm/proxy-wrapper.ts", "webpack://ort/./lib/wasm/run-options.ts", "webpack://ort/./lib/wasm/session-handler.ts", "webpack://ort/./lib/wasm/session-options.ts", "webpack://ort/./lib/wasm/string-utils.ts", "webpack://ort/./lib/wasm/wasm-core-impl.ts", "webpack://ort/./lib/wasm/wasm-factory.ts", "webpack://ort/webpack/bootstrap", "webpack://ort/webpack/runtime/define property getters", "webpack://ort/webpack/runtime/hasOwnProperty shorthand", "webpack://ort/webpack/runtime/make namespace object", "webpack://ort/webpack/startup"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ort\"] = factory();\n\telse\n\t\troot[\"ort\"] = factory();\n})(self, () => {\nreturn ", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\nconst backends = {};\nconst backendsSortedByPriority = [];\n/**\n * Register a backend.\n *\n * @param name - the name as a key to lookup as an execution provider.\n * @param backend - the backend object.\n * @param priority - an integer indicating the priority of the backend. Higher number means higher priority. if priority\n * < 0, it will be considered as a 'beta' version and will not be used as a fallback backend by default.\n *\n * @internal\n */\nexport const registerBackend = (name, backend, priority) => {\n    if (backend && typeof backend.init === 'function' && typeof backend.createSessionHandler === 'function') {\n        const currentBackend = backends[name];\n        if (currentBackend === undefined) {\n            backends[name] = { backend, priority };\n        }\n        else if (currentBackend.priority > priority) {\n            // same name is already registered with a higher priority. skip registeration.\n            return;\n        }\n        else if (currentBackend.priority === priority) {\n            if (currentBackend.backend !== backend) {\n                throw new Error(`cannot register backend \"${name}\" using priority ${priority}`);\n            }\n        }\n        if (priority >= 0) {\n            const i = backendsSortedByPriority.indexOf(name);\n            if (i !== -1) {\n                backendsSortedByPriority.splice(i, 1);\n            }\n            for (let i = 0; i < backendsSortedByPriority.length; i++) {\n                if (backends[backendsSortedByPriority[i]].priority <= priority) {\n                    backendsSortedByPriority.splice(i, 0, name);\n                    return;\n                }\n            }\n            backendsSortedByPriority.push(name);\n        }\n        return;\n    }\n    throw new TypeError('not a valid backend');\n};\n/**\n * Resolve backend by specified hints.\n *\n * @param backendHints - a list of execution provider names to lookup. If omitted use registered backends as list.\n * @returns a promise that resolves to the backend.\n *\n * @internal\n */\nexport const resolveBackend = async (backendHints) => {\n    const backendNames = backendHints.length === 0 ? backendsSortedByPriority : backendHints;\n    const errors = [];\n    for (const backendName of backendNames) {\n        const backendInfo = backends[backendName];\n        if (backendInfo) {\n            if (backendInfo.initialized) {\n                return backendInfo.backend;\n            }\n            else if (backendInfo.aborted) {\n                continue; // current backend is unavailable; try next\n            }\n            const isInitializing = !!backendInfo.initPromise;\n            try {\n                if (!isInitializing) {\n                    backendInfo.initPromise = backendInfo.backend.init();\n                }\n                await backendInfo.initPromise;\n                backendInfo.initialized = true;\n                return backendInfo.backend;\n            }\n            catch (e) {\n                if (!isInitializing) {\n                    errors.push({ name: backendName, err: e });\n                }\n                backendInfo.aborted = true;\n            }\n            finally {\n                delete backendInfo.initPromise;\n            }\n        }\n    }\n    throw new Error(`no available backend found. ERR: ${errors.map(e => `[${e.name}] ${e.err}`).join(', ')}`);\n};\n//# sourceMappingURL=backend-impl.js.map", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\nimport { EnvImpl } from './env-impl';\n/**\n * Represent a set of flags as a global singleton.\n */\nexport const env = new EnvImpl();\n//# sourceMappingURL=env.js.map", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\nexport class EnvImpl {\n    constructor() {\n        this.wasm = {};\n        this.webgl = {};\n        this.logLevelInternal = 'warning';\n    }\n    // TODO standadize the getter and setter convention in env for other fields.\n    set logLevel(value) {\n        if (value === undefined) {\n            return;\n        }\n        if (typeof value !== 'string' || ['verbose', 'info', 'warning', 'error', 'fatal'].indexOf(value) === -1) {\n            throw new Error(`Unsupported logging level: ${value}`);\n        }\n        this.logLevelInternal = value;\n    }\n    get logLevel() {\n        return this.logLevelInternal;\n    }\n}\n//# sourceMappingURL=env-impl.js.map", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\nconst isBigInt64ArrayAvailable = typeof BigInt64Array !== 'undefined' && typeof BigInt64Array.from === 'function';\nconst isBigUint64ArrayAvailable = typeof BigUint64Array !== 'undefined' && typeof BigUint64Array.from === 'function';\n// a runtime map that maps type string to TypedArray constructor. Should match Tensor.DataTypeMap.\nconst NUMERIC_TENSOR_TYPE_TO_TYPEDARRAY_MAP = new Map([\n    ['float32', Float32Array],\n    ['uint8', Uint8Array],\n    ['int8', Int8Array],\n    ['uint16', Uint16Array],\n    ['int16', Int16Array],\n    ['int32', Int32Array],\n    ['bool', Uint8Array],\n    ['float64', Float64Array],\n    ['uint32', Uint32Array],\n]);\n// a runtime map that maps type string to TypedArray constructor. Should match Tensor.DataTypeMap.\nconst NUMERIC_TENSOR_TYPEDARRAY_TO_TYPE_MAP = new Map([\n    [Float32Array, 'float32'],\n    [Uint8Array, 'uint8'],\n    [Int8Array, 'int8'],\n    [Uint16Array, 'uint16'],\n    [Int16Array, 'int16'],\n    [Int32Array, 'int32'],\n    [Float64Array, 'float64'],\n    [Uint32Array, 'uint32'],\n]);\nif (isBigInt64ArrayAvailable) {\n    NUMERIC_TENSOR_TYPE_TO_TYPEDARRAY_MAP.set('int64', BigInt64Array);\n    NUMERIC_TENSOR_TYPEDARRAY_TO_TYPE_MAP.set(BigInt64Array, 'int64');\n}\nif (isBigUint64ArrayAvailable) {\n    NUMERIC_TENSOR_TYPE_TO_TYPEDARRAY_MAP.set('uint64', BigUint64Array);\n    NUMERIC_TENSOR_TYPEDARRAY_TO_TYPE_MAP.set(BigUint64Array, 'uint64');\n}\n/**\n * calculate size from dims.\n *\n * @param dims the dims array. May be an illegal input.\n */\nconst calculateSize = (dims) => {\n    let size = 1;\n    for (let i = 0; i < dims.length; i++) {\n        const dim = dims[i];\n        if (typeof dim !== 'number' || !Number.isSafeInteger(dim)) {\n            throw new TypeError(`dims[${i}] must be an integer, got: ${dim}`);\n        }\n        if (dim < 0) {\n            throw new RangeError(`dims[${i}] must be a non-negative integer, got: ${dim}`);\n        }\n        size *= dim;\n    }\n    return size;\n};\nexport class Tensor {\n    constructor(arg0, arg1, arg2) {\n        let type;\n        let data;\n        let dims;\n        // check whether arg0 is type or data\n        if (typeof arg0 === 'string') {\n            //\n            // Override: constructor(type, data, ...)\n            //\n            type = arg0;\n            dims = arg2;\n            if (arg0 === 'string') {\n                // string tensor\n                if (!Array.isArray(arg1)) {\n                    throw new TypeError('A string tensor\\'s data must be a string array.');\n                }\n                // we don't check whether every element in the array is string; this is too slow. we assume it's correct and\n                // error will be populated at inference\n                data = arg1;\n            }\n            else {\n                // numeric tensor\n                const typedArrayConstructor = NUMERIC_TENSOR_TYPE_TO_TYPEDARRAY_MAP.get(arg0);\n                if (typedArrayConstructor === undefined) {\n                    throw new TypeError(`Unsupported tensor type: ${arg0}.`);\n                }\n                if (Array.isArray(arg1)) {\n                    // use 'as any' here because TypeScript's check on type of 'SupportedTypedArrayConstructors.from()' produces\n                    // incorrect results.\n                    // 'typedArrayConstructor' should be one of the typed array prototype objects.\n                    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                    data = typedArrayConstructor.from(arg1);\n                }\n                else if (arg1 instanceof typedArrayConstructor) {\n                    data = arg1;\n                }\n                else {\n                    throw new TypeError(`A ${type} tensor's data must be type of ${typedArrayConstructor}`);\n                }\n            }\n        }\n        else {\n            //\n            // Override: constructor(data, ...)\n            //\n            dims = arg1;\n            if (Array.isArray(arg0)) {\n                // only boolean[] and string[] is supported\n                if (arg0.length === 0) {\n                    throw new TypeError('Tensor type cannot be inferred from an empty array.');\n                }\n                const firstElementType = typeof arg0[0];\n                if (firstElementType === 'string') {\n                    type = 'string';\n                    data = arg0;\n                }\n                else if (firstElementType === 'boolean') {\n                    type = 'bool';\n                    // 'arg0' is of type 'boolean[]'. Uint8Array.from(boolean[]) actually works, but typescript thinks this is\n                    // wrong type. We use 'as any' to make it happy.\n                    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                    data = Uint8Array.from(arg0);\n                }\n                else {\n                    throw new TypeError(`Invalid element type of data array: ${firstElementType}.`);\n                }\n            }\n            else {\n                // get tensor type from TypedArray\n                const mappedType = NUMERIC_TENSOR_TYPEDARRAY_TO_TYPE_MAP.get(arg0.constructor);\n                if (mappedType === undefined) {\n                    throw new TypeError(`Unsupported type for tensor data: ${arg0.constructor}.`);\n                }\n                type = mappedType;\n                data = arg0;\n            }\n        }\n        // type and data is processed, now processing dims\n        if (dims === undefined) {\n            // assume 1-D tensor if dims omitted\n            dims = [data.length];\n        }\n        else if (!Array.isArray(dims)) {\n            throw new TypeError('A tensor\\'s dims must be a number array');\n        }\n        // perform check\n        const size = calculateSize(dims);\n        if (size !== data.length) {\n            throw new Error(`Tensor's size(${size}) does not match data length(${data.length}).`);\n        }\n        this.dims = dims;\n        this.type = type;\n        this.data = data;\n        this.size = size;\n    }\n    // #endregion\n    /**\n     * Create a new tensor object from image object\n     *\n     * @param buffer - Extracted image buffer data - assuming RGBA format\n     * @param imageFormat - input image configuration - required configurations height, width, format\n     * @param tensorFormat - output tensor configuration - Default is RGB format\n     */\n    static bufferToTensor(buffer, options) {\n        if (buffer === undefined) {\n            throw new Error('Image buffer must be defined');\n        }\n        if (options.height === undefined || options.width === undefined) {\n            throw new Error('Image height and width must be defined');\n        }\n        const { height, width } = options;\n        const norm = options.norm;\n        let normMean;\n        let normBias;\n        if (norm === undefined || norm.mean === undefined) {\n            normMean = 255;\n        }\n        else {\n            normMean = norm.mean;\n        }\n        if (norm === undefined || norm.bias === undefined) {\n            normBias = 0;\n        }\n        else {\n            normBias = norm.bias;\n        }\n        const inputformat = options.bitmapFormat !== undefined ? options.bitmapFormat : 'RGBA';\n        // default value is RGBA since imagedata and HTMLImageElement uses it\n        const outputformat = options.tensorFormat !== undefined ?\n            (options.tensorFormat !== undefined ? options.tensorFormat : 'RGB') :\n            'RGB';\n        const offset = height * width;\n        const float32Data = outputformat === 'RGBA' ? new Float32Array(offset * 4) : new Float32Array(offset * 3);\n        // Default pointer assignments\n        let step = 4, rImagePointer = 0, gImagePointer = 1, bImagePointer = 2, aImagePointer = 3;\n        let rTensorPointer = 0, gTensorPointer = offset, bTensorPointer = offset * 2, aTensorPointer = -1;\n        // Updating the pointer assignments based on the input image format\n        if (inputformat === 'RGB') {\n            step = 3;\n            rImagePointer = 0;\n            gImagePointer = 1;\n            bImagePointer = 2;\n            aImagePointer = -1;\n        }\n        // Updating the pointer assignments based on the output tensor format\n        if (outputformat === 'RGBA') {\n            aTensorPointer = offset * 3;\n        }\n        else if (outputformat === 'RBG') {\n            rTensorPointer = 0;\n            bTensorPointer = offset;\n            gTensorPointer = offset * 2;\n        }\n        else if (outputformat === 'BGR') {\n            bTensorPointer = 0;\n            gTensorPointer = offset;\n            rTensorPointer = offset * 2;\n        }\n        for (let i = 0; i < offset; i++, rImagePointer += step, bImagePointer += step, gImagePointer += step, aImagePointer += step) {\n            float32Data[rTensorPointer++] = (buffer[rImagePointer] + normBias) / normMean;\n            float32Data[gTensorPointer++] = (buffer[gImagePointer] + normBias) / normMean;\n            float32Data[bTensorPointer++] = (buffer[bImagePointer] + normBias) / normMean;\n            if (aTensorPointer !== -1 && aImagePointer !== -1) {\n                float32Data[aTensorPointer++] = (buffer[aImagePointer] + normBias) / normMean;\n            }\n        }\n        // Float32Array -> ort.Tensor\n        const outputTensor = outputformat === 'RGBA' ? new Tensor('float32', float32Data, [1, 4, height, width]) :\n            new Tensor('float32', float32Data, [1, 3, height, width]);\n        return outputTensor;\n    }\n    static async fromImage(image, options) {\n        // checking the type of image object\n        const isHTMLImageEle = typeof (HTMLImageElement) !== 'undefined' && image instanceof HTMLImageElement;\n        const isImageDataEle = typeof (ImageData) !== 'undefined' && image instanceof ImageData;\n        const isImageBitmap = typeof (ImageBitmap) !== 'undefined' && image instanceof ImageBitmap;\n        const isURL = typeof (String) !== 'undefined' && (image instanceof String || typeof image === 'string');\n        let data;\n        let tensorConfig = {};\n        // filling and checking image configuration options\n        if (isHTMLImageEle) {\n            // HTMLImageElement - image object - format is RGBA by default\n            const canvas = document.createElement('canvas');\n            const pixels2DContext = canvas.getContext('2d');\n            if (pixels2DContext != null) {\n                let height = image.naturalHeight;\n                let width = image.naturalWidth;\n                if (options !== undefined && options.resizedHeight !== undefined && options.resizedWidth !== undefined) {\n                    height = options.resizedHeight;\n                    width = options.resizedWidth;\n                }\n                if (options !== undefined) {\n                    tensorConfig = options;\n                    if (options.tensorFormat !== undefined) {\n                        throw new Error('Image input config format must be RGBA for HTMLImageElement');\n                    }\n                    else {\n                        tensorConfig.tensorFormat = 'RGBA';\n                    }\n                    if (options.height !== undefined && options.height !== height) {\n                        throw new Error('Image input config height doesn\\'t match HTMLImageElement height');\n                    }\n                    else {\n                        tensorConfig.height = height;\n                    }\n                    if (options.width !== undefined && options.width !== width) {\n                        throw new Error('Image input config width doesn\\'t match HTMLImageElement width');\n                    }\n                    else {\n                        tensorConfig.width = width;\n                    }\n                }\n                else {\n                    tensorConfig.tensorFormat = 'RGBA';\n                    tensorConfig.height = height;\n                    tensorConfig.width = width;\n                }\n                canvas.width = width;\n                canvas.height = height;\n                pixels2DContext.drawImage(image, 0, 0, width, height);\n                data = pixels2DContext.getImageData(0, 0, width, height).data;\n            }\n            else {\n                throw new Error('Can not access image data');\n            }\n        }\n        else if (isImageDataEle) {\n            // ImageData - image object - format is RGBA by default\n            const format = 'RGBA';\n            let height;\n            let width;\n            if (options !== undefined && options.resizedWidth !== undefined && options.resizedHeight !== undefined) {\n                height = options.resizedHeight;\n                width = options.resizedWidth;\n            }\n            else {\n                height = image.height;\n                width = image.width;\n            }\n            if (options !== undefined) {\n                tensorConfig = options;\n                if (options.bitmapFormat !== undefined && options.bitmapFormat !== format) {\n                    throw new Error('Image input config format must be RGBA for ImageData');\n                }\n                else {\n                    tensorConfig.bitmapFormat = 'RGBA';\n                }\n            }\n            else {\n                tensorConfig.bitmapFormat = 'RGBA';\n            }\n            tensorConfig.height = height;\n            tensorConfig.width = width;\n            if (options !== undefined) {\n                const tempCanvas = document.createElement('canvas');\n                tempCanvas.width = width;\n                tempCanvas.height = height;\n                const pixels2DContext = tempCanvas.getContext('2d');\n                if (pixels2DContext != null) {\n                    pixels2DContext.putImageData(image, 0, 0);\n                    data = pixels2DContext.getImageData(0, 0, width, height).data;\n                }\n                else {\n                    throw new Error('Can not access image data');\n                }\n            }\n            else {\n                data = image.data;\n            }\n        }\n        else if (isImageBitmap) {\n            // ImageBitmap - image object - format must be provided by user\n            if (options === undefined) {\n                throw new Error('Please provide image config with format for Imagebitmap');\n            }\n            if (options.bitmapFormat !== undefined) {\n                throw new Error('Image input config format must be defined for ImageBitmap');\n            }\n            const pixels2DContext = document.createElement('canvas').getContext('2d');\n            if (pixels2DContext != null) {\n                const height = image.height;\n                const width = image.width;\n                pixels2DContext.drawImage(image, 0, 0, width, height);\n                data = pixels2DContext.getImageData(0, 0, width, height).data;\n                if (options !== undefined) {\n                    // using square brackets to avoid TS error - type 'never'\n                    if (options.height !== undefined && options.height !== height) {\n                        throw new Error('Image input config height doesn\\'t match ImageBitmap height');\n                    }\n                    else {\n                        tensorConfig.height = height;\n                    }\n                    // using square brackets to avoid TS error - type 'never'\n                    if (options.width !== undefined && options.width !== width) {\n                        throw new Error('Image input config width doesn\\'t match ImageBitmap width');\n                    }\n                    else {\n                        tensorConfig.width = width;\n                    }\n                }\n                else {\n                    tensorConfig.height = height;\n                    tensorConfig.width = width;\n                }\n                return Tensor.bufferToTensor(data, tensorConfig);\n            }\n            else {\n                throw new Error('Can not access image data');\n            }\n        }\n        else if (isURL) {\n            return new Promise((resolve, reject) => {\n                const canvas = document.createElement('canvas');\n                const context = canvas.getContext('2d');\n                if (!image || !context) {\n                    return reject();\n                }\n                const newImage = new Image();\n                newImage.crossOrigin = 'Anonymous';\n                newImage.src = image;\n                newImage.onload = () => {\n                    canvas.width = newImage.width;\n                    canvas.height = newImage.height;\n                    context.drawImage(newImage, 0, 0, canvas.width, canvas.height);\n                    const img = context.getImageData(0, 0, canvas.width, canvas.height);\n                    if (options !== undefined) {\n                        // using square brackets to avoid TS error - type 'never'\n                        if (options.height !== undefined && options.height !== canvas.height) {\n                            throw new Error('Image input config height doesn\\'t match ImageBitmap height');\n                        }\n                        else {\n                            tensorConfig.height = canvas.height;\n                        }\n                        // using square brackets to avoid TS error - type 'never'\n                        if (options.width !== undefined && options.width !== canvas.width) {\n                            throw new Error('Image input config width doesn\\'t match ImageBitmap width');\n                        }\n                        else {\n                            tensorConfig.width = canvas.width;\n                        }\n                    }\n                    else {\n                        tensorConfig.height = canvas.height;\n                        tensorConfig.width = canvas.width;\n                    }\n                    resolve(Tensor.bufferToTensor(img.data, tensorConfig));\n                };\n            });\n        }\n        else {\n            throw new Error('Input data provided is not supported - aborted tensor creation');\n        }\n        if (data !== undefined) {\n            return Tensor.bufferToTensor(data, tensorConfig);\n        }\n        else {\n            throw new Error('Input data provided is not supported - aborted tensor creation');\n        }\n    }\n    toImageData(options) {\n        var _a, _b;\n        const pixels2DContext = document.createElement('canvas').getContext('2d');\n        let image;\n        if (pixels2DContext != null) {\n            // Default values for height and width & format\n            const width = this.dims[3];\n            const height = this.dims[2];\n            const channels = this.dims[1];\n            const inputformat = options !== undefined ? (options.format !== undefined ? options.format : 'RGB') : 'RGB';\n            const normMean = options !== undefined ? (((_a = options.norm) === null || _a === void 0 ? void 0 : _a.mean) !== undefined ? options.norm.mean : 255) : 255;\n            const normBias = options !== undefined ? (((_b = options.norm) === null || _b === void 0 ? void 0 : _b.bias) !== undefined ? options.norm.bias : 0) : 0;\n            const offset = height * width;\n            if (options !== undefined) {\n                if (options.height !== undefined && options.height !== height) {\n                    throw new Error('Image output config height doesn\\'t match tensor height');\n                }\n                if (options.width !== undefined && options.width !== width) {\n                    throw new Error('Image output config width doesn\\'t match tensor width');\n                }\n                if (options.format !== undefined && (channels === 4 && options.format !== 'RGBA') ||\n                    (channels === 3 && (options.format !== 'RGB' && options.format !== 'BGR'))) {\n                    throw new Error('Tensor format doesn\\'t match input tensor dims');\n                }\n            }\n            // Default pointer assignments\n            const step = 4;\n            let rImagePointer = 0, gImagePointer = 1, bImagePointer = 2, aImagePointer = 3;\n            let rTensorPointer = 0, gTensorPointer = offset, bTensorPointer = offset * 2, aTensorPointer = -1;\n            // Updating the pointer assignments based on the input image format\n            if (inputformat === 'RGBA') {\n                rTensorPointer = 0;\n                gTensorPointer = offset;\n                bTensorPointer = offset * 2;\n                aTensorPointer = offset * 3;\n            }\n            else if (inputformat === 'RGB') {\n                rTensorPointer = 0;\n                gTensorPointer = offset;\n                bTensorPointer = offset * 2;\n            }\n            else if (inputformat === 'RBG') {\n                rTensorPointer = 0;\n                bTensorPointer = offset;\n                gTensorPointer = offset * 2;\n            }\n            image = pixels2DContext.createImageData(width, height);\n            for (let i = 0; i < height * width; rImagePointer += step, gImagePointer += step, bImagePointer += step, aImagePointer += step, i++) {\n                image.data[rImagePointer] = (this.data[rTensorPointer++] - normBias) * normMean; // R value\n                image.data[gImagePointer] = (this.data[gTensorPointer++] - normBias) * normMean; // G value\n                image.data[bImagePointer] = (this.data[bTensorPointer++] - normBias) * normMean; // B value\n                image.data[aImagePointer] =\n                    aTensorPointer === -1 ? 255 : (this.data[aTensorPointer++] - normBias) * normMean; // A value\n            }\n        }\n        else {\n            throw new Error('Can not access image data');\n        }\n        return image;\n    }\n    // #endregion\n    // #region tensor utilities\n    reshape(dims) {\n        return new Tensor(this.type, this.data, dims);\n    }\n}\n//# sourceMappingURL=tensor-impl.js.map", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\nimport { Tensor as TensorImpl } from './tensor-impl';\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport const Tensor = TensorImpl;\n//# sourceMappingURL=tensor.js.map", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\nimport { resolveBackend } from './backend-impl';\nimport { Tensor } from './tensor';\nexport class InferenceSession {\n    constructor(handler) {\n        this.handler = handler;\n    }\n    async run(feeds, arg1, arg2) {\n        const fetches = {};\n        let options = {};\n        // check inputs\n        if (typeof feeds !== 'object' || feeds === null || feeds instanceof Tensor || Array.isArray(feeds)) {\n            throw new TypeError('\\'feeds\\' must be an object that use input names as keys and OnnxValue as corresponding values.');\n        }\n        let isFetchesEmpty = true;\n        // determine which override is being used\n        if (typeof arg1 === 'object') {\n            if (arg1 === null) {\n                throw new TypeError('Unexpected argument[1]: cannot be null.');\n            }\n            if (arg1 instanceof Tensor) {\n                throw new TypeError('\\'fetches\\' cannot be a Tensor');\n            }\n            if (Array.isArray(arg1)) {\n                if (arg1.length === 0) {\n                    throw new TypeError('\\'fetches\\' cannot be an empty array.');\n                }\n                isFetchesEmpty = false;\n                // output names\n                for (const name of arg1) {\n                    if (typeof name !== 'string') {\n                        throw new TypeError('\\'fetches\\' must be a string array or an object.');\n                    }\n                    if (this.outputNames.indexOf(name) === -1) {\n                        throw new RangeError(`'fetches' contains invalid output name: ${name}.`);\n                    }\n                    fetches[name] = null;\n                }\n                if (typeof arg2 === 'object' && arg2 !== null) {\n                    options = arg2;\n                }\n                else if (typeof arg2 !== 'undefined') {\n                    throw new TypeError('\\'options\\' must be an object.');\n                }\n            }\n            else {\n                // decide whether arg1 is fetches or options\n                // if any output name is present and its value is valid OnnxValue, we consider it fetches\n                let isFetches = false;\n                const arg1Keys = Object.getOwnPropertyNames(arg1);\n                for (const name of this.outputNames) {\n                    if (arg1Keys.indexOf(name) !== -1) {\n                        const v = arg1[name];\n                        if (v === null || v instanceof Tensor) {\n                            isFetches = true;\n                            isFetchesEmpty = false;\n                            fetches[name] = v;\n                        }\n                    }\n                }\n                if (isFetches) {\n                    if (typeof arg2 === 'object' && arg2 !== null) {\n                        options = arg2;\n                    }\n                    else if (typeof arg2 !== 'undefined') {\n                        throw new TypeError('\\'options\\' must be an object.');\n                    }\n                }\n                else {\n                    options = arg1;\n                }\n            }\n        }\n        else if (typeof arg1 !== 'undefined') {\n            throw new TypeError('Unexpected argument[1]: must be \\'fetches\\' or \\'options\\'.');\n        }\n        // check if all inputs are in feed\n        for (const name of this.inputNames) {\n            if (typeof feeds[name] === 'undefined') {\n                throw new Error(`input '${name}' is missing in 'feeds'.`);\n            }\n        }\n        // if no fetches is specified, we use the full output names list\n        if (isFetchesEmpty) {\n            for (const name of this.outputNames) {\n                fetches[name] = null;\n            }\n        }\n        // feeds, fetches and options are prepared\n        const results = await this.handler.run(feeds, fetches, options);\n        const returnValue = {};\n        for (const key in results) {\n            if (Object.hasOwnProperty.call(results, key)) {\n                returnValue[key] = new Tensor(results[key].type, results[key].data, results[key].dims);\n            }\n        }\n        return returnValue;\n    }\n    static async create(arg0, arg1, arg2, arg3) {\n        // either load from a file or buffer\n        let filePathOrUint8Array;\n        let options = {};\n        if (typeof arg0 === 'string') {\n            filePathOrUint8Array = arg0;\n            if (typeof arg1 === 'object' && arg1 !== null) {\n                options = arg1;\n            }\n            else if (typeof arg1 !== 'undefined') {\n                throw new TypeError('\\'options\\' must be an object.');\n            }\n        }\n        else if (arg0 instanceof Uint8Array) {\n            filePathOrUint8Array = arg0;\n            if (typeof arg1 === 'object' && arg1 !== null) {\n                options = arg1;\n            }\n            else if (typeof arg1 !== 'undefined') {\n                throw new TypeError('\\'options\\' must be an object.');\n            }\n        }\n        else if (arg0 instanceof ArrayBuffer ||\n            (typeof SharedArrayBuffer !== 'undefined' && arg0 instanceof SharedArrayBuffer)) {\n            const buffer = arg0;\n            let byteOffset = 0;\n            let byteLength = arg0.byteLength;\n            if (typeof arg1 === 'object' && arg1 !== null) {\n                options = arg1;\n            }\n            else if (typeof arg1 === 'number') {\n                byteOffset = arg1;\n                if (!Number.isSafeInteger(byteOffset)) {\n                    throw new RangeError('\\'byteOffset\\' must be an integer.');\n                }\n                if (byteOffset < 0 || byteOffset >= buffer.byteLength) {\n                    throw new RangeError(`'byteOffset' is out of range [0, ${buffer.byteLength}).`);\n                }\n                byteLength = arg0.byteLength - byteOffset;\n                if (typeof arg2 === 'number') {\n                    byteLength = arg2;\n                    if (!Number.isSafeInteger(byteLength)) {\n                        throw new RangeError('\\'byteLength\\' must be an integer.');\n                    }\n                    if (byteLength <= 0 || byteOffset + byteLength > buffer.byteLength) {\n                        throw new RangeError(`'byteLength' is out of range (0, ${buffer.byteLength - byteOffset}].`);\n                    }\n                    if (typeof arg3 === 'object' && arg3 !== null) {\n                        options = arg3;\n                    }\n                    else if (typeof arg3 !== 'undefined') {\n                        throw new TypeError('\\'options\\' must be an object.');\n                    }\n                }\n                else if (typeof arg2 !== 'undefined') {\n                    throw new TypeError('\\'byteLength\\' must be a number.');\n                }\n            }\n            else if (typeof arg1 !== 'undefined') {\n                throw new TypeError('\\'options\\' must be an object.');\n            }\n            filePathOrUint8Array = new Uint8Array(buffer, byteOffset, byteLength);\n        }\n        else {\n            throw new TypeError('Unexpected argument[0]: must be \\'path\\' or \\'buffer\\'.');\n        }\n        // get backend hints\n        const eps = options.executionProviders || [];\n        const backendHints = eps.map(i => typeof i === 'string' ? i : i.name);\n        const backend = await resolveBackend(backendHints);\n        const handler = await backend.createSessionHandler(filePathOrUint8Array, options);\n        return new InferenceSession(handler);\n    }\n    startProfiling() {\n        this.handler.startProfiling();\n    }\n    endProfiling() {\n        this.handler.endProfiling();\n    }\n    get inputNames() {\n        return this.handler.inputNames;\n    }\n    get outputNames() {\n        return this.handler.outputNames;\n    }\n}\n//# sourceMappingURL=inference-session-impl.js.map", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\nimport { InferenceSession as InferenceSessionImpl } from './inference-session-impl';\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport const InferenceSession = InferenceSessionImpl;\n//# sourceMappingURL=inference-session.js.map", "\r\nvar ortWasm = (() => {\r\n  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;\r\n  if (typeof __filename !== 'undefined') _scriptDir = _scriptDir || __filename;\r\n  return (\r\nfunction(ortWasm) {\r\n  ortWasm = ortWasm || {};\r\n\r\n\r\nvar d;d||(d=typeof ortWasm !== 'undefined' ? ortWasm : {});var aa,ba;d.ready=new Promise(function(a,b){aa=a;ba=b});var ca=Object.assign({},d),da=\"./this.program\",ea=(a,b)=>{throw b;},fa=\"object\"==typeof window,m=\"function\"==typeof importScripts,p=\"object\"==typeof process&&\"object\"==typeof process.versions&&\"string\"==typeof process.versions.node,q=\"\",ha,r,v,fs,y,ia;\r\nif(p)q=m?require(\"path\").dirname(q)+\"/\":__dirname+\"/\",ia=()=>{y||(fs=require(\"fs\"),y=require(\"path\"))},ha=function(a,b){ia();a=y.normalize(a);return fs.readFileSync(a,b?void 0:\"utf8\")},v=a=>{a=ha(a,!0);a.buffer||(a=new Uint8Array(a));return a},r=(a,b,c)=>{ia();a=y.normalize(a);fs.readFile(a,function(e,f){e?c(e):b(f.buffer)})},1<process.argv.length&&(da=process.argv[1].replace(/\\\\/g,\"/\")),process.argv.slice(2),process.on(\"uncaughtException\",function(a){if(!(a instanceof ja))throw a;}),process.on(\"unhandledRejection\",\r\nfunction(a){throw a;}),ea=(a,b)=>{if(noExitRuntime||0<ka)throw process.exitCode=a,b;b instanceof ja||z(\"exiting due to exception: \"+b);process.exit(a)},d.inspect=function(){return\"[Emscripten Module object]\"};else if(fa||m)m?q=self.location.href:\"undefined\"!=typeof document&&document.currentScript&&(q=document.currentScript.src),_scriptDir&&(q=_scriptDir),0!==q.indexOf(\"blob:\")?q=q.substr(0,q.replace(/[?#].*/,\"\").lastIndexOf(\"/\")+1):q=\"\",ha=a=>{var b=new XMLHttpRequest;b.open(\"GET\",a,!1);b.send(null);\r\nreturn b.responseText},m&&(v=a=>{var b=new XMLHttpRequest;b.open(\"GET\",a,!1);b.responseType=\"arraybuffer\";b.send(null);return new Uint8Array(b.response)}),r=(a,b,c)=>{var e=new XMLHttpRequest;e.open(\"GET\",a,!0);e.responseType=\"arraybuffer\";e.onload=()=>{200==e.status||0==e.status&&e.response?b(e.response):c()};e.onerror=c;e.send(null)};var la=d.print||console.log.bind(console),z=d.printErr||console.warn.bind(console);Object.assign(d,ca);ca=null;d.thisProgram&&(da=d.thisProgram);d.quit&&(ea=d.quit);\r\nvar A;d.wasmBinary&&(A=d.wasmBinary);var noExitRuntime=d.noExitRuntime||!1;\"object\"!=typeof WebAssembly&&B(\"no native wasm support detected\");var ma,D=!1,na=\"undefined\"!=typeof TextDecoder?new TextDecoder(\"utf8\"):void 0;\r\nfunction oa(a,b,c){b>>>=0;var e=b+c;for(c=b;a[c]&&!(c>=e);)++c;if(16<c-b&&a.buffer&&na)return na.decode(a.subarray(b,c));for(e=\"\";b<c;){var f=a[b++];if(f&128){var h=a[b++]&63;if(192==(f&224))e+=String.fromCharCode((f&31)<<6|h);else{var k=a[b++]&63;f=224==(f&240)?(f&15)<<12|h<<6|k:(f&7)<<18|h<<12|k<<6|a[b++]&63;65536>f?e+=String.fromCharCode(f):(f-=65536,e+=String.fromCharCode(55296|f>>10,56320|f&1023))}}else e+=String.fromCharCode(f)}return e}function pa(a,b){return(a>>>=0)?oa(G,a,b):\"\"}\r\nfunction qa(a,b,c,e){c>>>=0;if(!(0<e))return 0;var f=c;e=c+e-1;for(var h=0;h<a.length;++h){var k=a.charCodeAt(h);if(55296<=k&&57343>=k){var l=a.charCodeAt(++h);k=65536+((k&1023)<<10)|l&1023}if(127>=k){if(c>=e)break;b[c++>>>0]=k}else{if(2047>=k){if(c+1>=e)break;b[c++>>>0]=192|k>>6}else{if(65535>=k){if(c+2>=e)break;b[c++>>>0]=224|k>>12}else{if(c+3>=e)break;b[c++>>>0]=240|k>>18;b[c++>>>0]=128|k>>12&63}b[c++>>>0]=128|k>>6&63}b[c++>>>0]=128|k&63}}b[c>>>0]=0;return c-f}\r\nfunction ra(a){for(var b=0,c=0;c<a.length;++c){var e=a.charCodeAt(c);127>=e?b++:2047>=e?b+=2:55296<=e&&57343>=e?(b+=4,++c):b+=3}return b}var sa,H,G,I,J;function ta(){var a=ma.buffer;sa=a;d.HEAP8=H=new Int8Array(a);d.HEAP16=new Int16Array(a);d.HEAP32=I=new Int32Array(a);d.HEAPU8=G=new Uint8Array(a);d.HEAPU16=new Uint16Array(a);d.HEAPU32=J=new Uint32Array(a);d.HEAPF32=new Float32Array(a);d.HEAPF64=new Float64Array(a)}var ua,va=[],wa=[],xa=[],ya=[],ka=0;\r\nfunction za(){var a=d.preRun.shift();va.unshift(a)}var K=0,Aa=null,L=null;function B(a){if(d.onAbort)d.onAbort(a);a=\"Aborted(\"+a+\")\";z(a);D=!0;a=new WebAssembly.RuntimeError(a+\". Build with -sASSERTIONS for more info.\");ba(a);throw a;}function Ba(){return N.startsWith(\"data:application/octet-stream;base64,\")}var N;N=\"ort-wasm.wasm\";if(!Ba()){var Ca=N;N=d.locateFile?d.locateFile(Ca,q):q+Ca}\r\nfunction Da(){var a=N;try{if(a==N&&A)return new Uint8Array(A);if(v)return v(a);throw\"both async and sync fetching of the wasm failed\";}catch(b){B(b)}}\r\nfunction Ea(){if(!A&&(fa||m)){if(\"function\"==typeof fetch&&!N.startsWith(\"file://\"))return fetch(N,{credentials:\"same-origin\"}).then(function(a){if(!a.ok)throw\"failed to load wasm binary file at '\"+N+\"'\";return a.arrayBuffer()}).catch(function(){return Da()});if(r)return new Promise(function(a,b){r(N,function(c){a(new Uint8Array(c))},b)})}return Promise.resolve().then(function(){return Da()})}function ja(a){this.name=\"ExitStatus\";this.message=\"Program terminated with exit(\"+a+\")\";this.status=a}\r\nfunction O(a){for(;0<a.length;)a.shift()(d)}var P=[],Q=0,R=0;\r\nfunction S(a){this.Db=a;this.zb=a-24;this.Ub=function(b){J[this.zb+4>>2>>>0]=b};this.Eb=function(){return J[this.zb+4>>2>>>0]};this.Sb=function(b){J[this.zb+8>>2>>>0]=b};this.Wb=function(){return J[this.zb+8>>2>>>0]};this.Tb=function(){I[this.zb>>2>>>0]=0};this.Ib=function(b){H[this.zb+12>>0>>>0]=b?1:0};this.Pb=function(){return 0!=H[this.zb+12>>0>>>0]};this.Jb=function(b){H[this.zb+13>>0>>>0]=b?1:0};this.Lb=function(){return 0!=H[this.zb+13>>0>>>0]};this.Rb=function(b,c){this.Fb(0);this.Ub(b);this.Sb(c);\r\nthis.Tb();this.Ib(!1);this.Jb(!1)};this.Nb=function(){I[this.zb>>2>>>0]+=1};this.Xb=function(){var b=I[this.zb>>2>>>0];I[this.zb>>2>>>0]=b-1;return 1===b};this.Fb=function(b){J[this.zb+16>>2>>>0]=b};this.Ob=function(){return J[this.zb+16>>2>>>0]};this.Qb=function(){if(Fa(this.Eb()))return J[this.Db>>2>>>0];var b=this.Ob();return 0!==b?b:this.Db}}function Ga(a){return Ha((new S(a)).zb)}var T=[];function U(a){var b=T[a];b||(a>=T.length&&(T.length=a+1),T[a]=b=ua.get(a));return b}\r\nfunction Ia(a){var b=ra(a)+1,c=Ja(b);c&&qa(a,H,c,b);return c}function Ka(a,b,c){function e(n){return(n=n.toTimeString().match(/\\(([A-Za-z ]+)\\)$/))?n[1]:\"GMT\"}var f=(new Date).getFullYear(),h=new Date(f,0,1),k=new Date(f,6,1);f=h.getTimezoneOffset();var l=k.getTimezoneOffset();I[a>>2>>>0]=60*Math.max(f,l);I[b>>2>>>0]=Number(f!=l);a=e(h);b=e(k);a=Ia(a);b=Ia(b);l<f?(J[c>>2>>>0]=a,J[c+4>>2>>>0]=b):(J[c>>2>>>0]=b,J[c+4>>2>>>0]=a)}function La(a,b,c){La.Vb||(La.Vb=!0,Ka(a,b,c))}var Ma={};\r\nfunction Na(){if(!Oa){var a={USER:\"web_user\",LOGNAME:\"web_user\",PATH:\"/\",PWD:\"/\",HOME:\"/home/<USER>\",LANG:(\"object\"==typeof navigator&&navigator.languages&&navigator.languages[0]||\"C\").replace(\"-\",\"_\")+\".UTF-8\",_:da||\"./this.program\"},b;for(b in Ma)void 0===Ma[b]?delete a[b]:a[b]=Ma[b];var c=[];for(b in a)c.push(b+\"=\"+a[b]);Oa=c}return Oa}var Oa,Pa=[null,[],[]];function Qa(a,b){var c=Pa[a];0===b||10===b?((1===a?la:z)(oa(c,0)),c.length=0):c.push(b)}var V=0;\r\nfunction Ra(){if(\"object\"==typeof crypto&&\"function\"==typeof crypto.getRandomValues){var a=new Uint8Array(1);return()=>{crypto.getRandomValues(a);return a[0]}}if(p)try{var b=require(\"crypto\");return()=>b.randomBytes(1)[0]}catch(c){}return()=>B(\"randomDevice\")}function W(a,b){W.Mb||(W.Mb=Ra());for(var c=0;c<b;c++)H[a+c>>0>>>0]=W.Mb();return 0}function Sa(a){return 0===a%4&&(0!==a%100||0===a%400)}var Ta=[31,29,31,30,31,30,31,31,30,31,30,31],Ua=[31,28,31,30,31,30,31,31,30,31,30,31];\r\nfunction Va(a){var b=Array(ra(a)+1);qa(a,b,0,b.length);return b}\r\nfunction Wa(a,b,c,e){function f(g,u,w){for(g=\"number\"==typeof g?g.toString():g||\"\";g.length<u;)g=w[0]+g;return g}function h(g,u){return f(g,u,\"0\")}function k(g,u){function w(M){return 0>M?-1:0<M?1:0}var F;0===(F=w(g.getFullYear()-u.getFullYear()))&&0===(F=w(g.getMonth()-u.getMonth()))&&(F=w(g.getDate()-u.getDate()));return F}function l(g){switch(g.getDay()){case 0:return new Date(g.getFullYear()-1,11,29);case 1:return g;case 2:return new Date(g.getFullYear(),0,3);case 3:return new Date(g.getFullYear(),\r\n0,2);case 4:return new Date(g.getFullYear(),0,1);case 5:return new Date(g.getFullYear()-1,11,31);case 6:return new Date(g.getFullYear()-1,11,30)}}function n(g){var u=g.Bb;for(g=new Date((new Date(g.Cb+1900,0,1)).getTime());0<u;){var w=g.getMonth(),F=(Sa(g.getFullYear())?Ta:Ua)[w];if(u>F-g.getDate())u-=F-g.getDate()+1,g.setDate(1),11>w?g.setMonth(w+1):(g.setMonth(0),g.setFullYear(g.getFullYear()+1));else{g.setDate(g.getDate()+u);break}}w=new Date(g.getFullYear()+1,0,4);u=l(new Date(g.getFullYear(),\r\n0,4));w=l(w);return 0>=k(u,g)?0>=k(w,g)?g.getFullYear()+1:g.getFullYear():g.getFullYear()-1}var t=I[e+40>>2>>>0];e={$b:I[e>>2>>>0],Zb:I[e+4>>2>>>0],Gb:I[e+8>>2>>>0],Kb:I[e+12>>2>>>0],Hb:I[e+16>>2>>>0],Cb:I[e+20>>2>>>0],Ab:I[e+24>>2>>>0],Bb:I[e+28>>2>>>0],bc:I[e+32>>2>>>0],Yb:I[e+36>>2>>>0],ac:t?pa(t):\"\"};c=pa(c);t={\"%c\":\"%a %b %d %H:%M:%S %Y\",\"%D\":\"%m/%d/%y\",\"%F\":\"%Y-%m-%d\",\"%h\":\"%b\",\"%r\":\"%I:%M:%S %p\",\"%R\":\"%H:%M\",\"%T\":\"%H:%M:%S\",\"%x\":\"%m/%d/%y\",\"%X\":\"%H:%M:%S\",\"%Ec\":\"%c\",\"%EC\":\"%C\",\"%Ex\":\"%m/%d/%y\",\r\n\"%EX\":\"%H:%M:%S\",\"%Ey\":\"%y\",\"%EY\":\"%Y\",\"%Od\":\"%d\",\"%Oe\":\"%e\",\"%OH\":\"%H\",\"%OI\":\"%I\",\"%Om\":\"%m\",\"%OM\":\"%M\",\"%OS\":\"%S\",\"%Ou\":\"%u\",\"%OU\":\"%U\",\"%OV\":\"%V\",\"%Ow\":\"%w\",\"%OW\":\"%W\",\"%Oy\":\"%y\"};for(var x in t)c=c.replace(new RegExp(x,\"g\"),t[x]);var E=\"Sunday Monday Tuesday Wednesday Thursday Friday Saturday\".split(\" \"),C=\"January February March April May June July August September October November December\".split(\" \");t={\"%a\":function(g){return E[g.Ab].substring(0,3)},\"%A\":function(g){return E[g.Ab]},\"%b\":function(g){return C[g.Hb].substring(0,\r\n3)},\"%B\":function(g){return C[g.Hb]},\"%C\":function(g){return h((g.Cb+1900)/100|0,2)},\"%d\":function(g){return h(g.Kb,2)},\"%e\":function(g){return f(g.Kb,2,\" \")},\"%g\":function(g){return n(g).toString().substring(2)},\"%G\":function(g){return n(g)},\"%H\":function(g){return h(g.Gb,2)},\"%I\":function(g){g=g.Gb;0==g?g=12:12<g&&(g-=12);return h(g,2)},\"%j\":function(g){for(var u=0,w=0;w<=g.Hb-1;u+=(Sa(g.Cb+1900)?Ta:Ua)[w++]);return h(g.Kb+u,3)},\"%m\":function(g){return h(g.Hb+1,2)},\"%M\":function(g){return h(g.Zb,\r\n2)},\"%n\":function(){return\"\\n\"},\"%p\":function(g){return 0<=g.Gb&&12>g.Gb?\"AM\":\"PM\"},\"%S\":function(g){return h(g.$b,2)},\"%t\":function(){return\"\\t\"},\"%u\":function(g){return g.Ab||7},\"%U\":function(g){return h(Math.floor((g.Bb+7-g.Ab)/7),2)},\"%V\":function(g){var u=Math.floor((g.Bb+7-(g.Ab+6)%7)/7);2>=(g.Ab+371-g.Bb-2)%7&&u++;if(u)53==u&&(w=(g.Ab+371-g.Bb)%7,4==w||3==w&&Sa(g.Cb)||(u=1));else{u=52;var w=(g.Ab+7-g.Bb-1)%7;(4==w||5==w&&Sa(g.Cb%400-1))&&u++}return h(u,2)},\"%w\":function(g){return g.Ab},\"%W\":function(g){return h(Math.floor((g.Bb+\r\n7-(g.Ab+6)%7)/7),2)},\"%y\":function(g){return(g.Cb+1900).toString().substring(2)},\"%Y\":function(g){return g.Cb+1900},\"%z\":function(g){g=g.Yb;var u=0<=g;g=Math.abs(g)/60;return(u?\"+\":\"-\")+String(\"0000\"+(g/60*100+g%60)).slice(-4)},\"%Z\":function(g){return g.ac},\"%%\":function(){return\"%\"}};c=c.replace(/%%/g,\"\\x00\\x00\");for(x in t)c.includes(x)&&(c=c.replace(new RegExp(x,\"g\"),t[x](e)));c=c.replace(/\\0\\0/g,\"%\");x=Va(c);if(x.length>b)return 0;H.set(x,a>>>0);return x.length-1}\r\nvar Jb={a:function(a){return Ja(a+24)+24},m:function(a){a=new S(a);a.Pb()||(a.Ib(!0),Q--);a.Jb(!1);P.push(a);a.Nb();return a.Qb()},ia:function(a){z(\"Unexpected exception thrown, this is not properly supported - aborting\");D=!0;throw a;},w:function(){X(0);var a=P.pop();if(a.Xb()&&!a.Lb()){var b=a.Wb();b&&U(b)(a.Db);Ga(a.Db)}R=0},d:function(){var a=R;if(!a)return V=0;var b=new S(a);b.Fb(a);var c=b.Eb();if(!c)return V=0,a;for(var e=Array.prototype.slice.call(arguments),f=0;f<e.length;f++){var h=e[f];\r\nif(0===h||h===c)break;if(Xa(h,c,b.zb+16))return V=h,a}V=c;return a},k:function(){var a=R;if(!a)return V=0;var b=new S(a);b.Fb(a);var c=b.Eb();if(!c)return V=0,a;for(var e=Array.prototype.slice.call(arguments),f=0;f<e.length;f++){var h=e[f];if(0===h||h===c)break;if(Xa(h,c,b.zb+16))return V=h,a}V=c;return a},g:function(){var a=R;if(!a)return V=0;var b=new S(a);b.Fb(a);var c=b.Eb();if(!c)return V=0,a;for(var e=Array.prototype.slice.call(arguments),f=0;f<e.length;f++){var h=e[f];if(0===h||h===c)break;\r\nif(Xa(h,c,b.zb+16))return V=h,a}V=c;return a},s:Ga,L:function(){var a=P.pop();a||B(\"no exception to throw\");var b=a.Db;a.Lb()||(P.push(a),a.Jb(!0),a.Ib(!1),Q++);R=b;throw b;},b:function(a,b,c){(new S(a)).Rb(b,c);R=a;Q++;throw a;},la:function(){return Q},i:function(a){R||(R=a);throw a;},H:function(){return 0},Ba:function(){},pa:function(){},ra:function(){},ka:function(){return 0},za:function(){},ua:function(){},ya:function(){},R:function(){},qa:function(){},na:function(){},Aa:function(){},oa:function(){},\r\nHa:function(){},Ja:function(){B(\"To use dlopen, you need enable dynamic linking, see https://github.com/emscripten-core/emscripten/wiki/Linking\")},Ia:function(){B(\"To use dlopen, you need enable dynamic linking, see https://github.com/emscripten-core/emscripten/wiki/Linking\")},S:function(){return Date.now()},Ca:function(){return!0},Da:function(a,b){a=new Date(1E3*(J[a>>>2]+4294967296*I[a+4>>>2]));I[b>>2>>>0]=a.getUTCSeconds();I[b+4>>2>>>0]=a.getUTCMinutes();I[b+8>>2>>>0]=a.getUTCHours();I[b+12>>2>>>\r\n0]=a.getUTCDate();I[b+16>>2>>>0]=a.getUTCMonth();I[b+20>>2>>>0]=a.getUTCFullYear()-1900;I[b+24>>2>>>0]=a.getUTCDay();I[b+28>>2>>>0]=(a.getTime()-Date.UTC(a.getUTCFullYear(),0,1,0,0,0,0))/864E5|0},Ea:function(a,b){a=new Date(1E3*(J[a>>>2]+4294967296*I[a+4>>>2]));I[b>>2>>>0]=a.getSeconds();I[b+4>>2>>>0]=a.getMinutes();I[b+8>>2>>>0]=a.getHours();I[b+12>>2>>>0]=a.getDate();I[b+16>>2>>>0]=a.getMonth();I[b+20>>2>>>0]=a.getFullYear()-1900;I[b+24>>2>>>0]=a.getDay();var c=new Date(a.getFullYear(),0,1);I[b+\r\n28>>2>>>0]=(a.getTime()-c.getTime())/864E5|0;I[b+36>>2>>>0]=-(60*a.getTimezoneOffset());var e=(new Date(a.getFullYear(),6,1)).getTimezoneOffset();c=c.getTimezoneOffset();I[b+32>>2>>>0]=(e!=c&&a.getTimezoneOffset()==Math.min(c,e))|0},Fa:function(a){var b=new Date(I[a+20>>2>>>0]+1900,I[a+16>>2>>>0],I[a+12>>2>>>0],I[a+8>>2>>>0],I[a+4>>2>>>0],I[a>>2>>>0],0),c=I[a+32>>2>>>0],e=b.getTimezoneOffset(),f=new Date(b.getFullYear(),0,1),h=(new Date(b.getFullYear(),6,1)).getTimezoneOffset(),k=f.getTimezoneOffset(),\r\nl=Math.min(k,h);0>c?I[a+32>>2>>>0]=Number(h!=k&&l==e):0<c!=(l==e)&&(h=Math.max(k,h),b.setTime(b.getTime()+6E4*((0<c?l:h)-e)));I[a+24>>2>>>0]=b.getDay();I[a+28>>2>>>0]=(b.getTime()-f.getTime())/864E5|0;I[a>>2>>>0]=b.getSeconds();I[a+4>>2>>>0]=b.getMinutes();I[a+8>>2>>>0]=b.getHours();I[a+12>>2>>>0]=b.getDate();I[a+16>>2>>>0]=b.getMonth();return b.getTime()/1E3|0},sa:function(){return-52},ta:function(){},Ga:La,B:function(){B(\"\")},ma:function(){return 4294901760},I:p?()=>{var a=process.hrtime();return 1E3*\r\na[0]+a[1]/1E6}:()=>performance.now(),xa:function(a,b,c){G.copyWithin(a>>>0,b>>>0,b+c>>>0)},G:function(a){var b=G.length;a>>>=0;if(4294901760<a)return!1;for(var c=1;4>=c;c*=2){var e=b*(1+.2/c);e=Math.min(e,a+100663296);var f=Math;e=Math.max(a,e);f=f.min.call(f,4294901760,e+(65536-e%65536)%65536);a:{try{ma.grow(f-sa.byteLength+65535>>>16);ta();var h=1;break a}catch(k){}h=void 0}if(h)return!0}return!1},va:function(a,b){var c=0;Na().forEach(function(e,f){var h=b+c;f=J[a+4*f>>2>>>0]=h;for(h=0;h<e.length;++h)H[f++>>\r\n0>>>0]=e.charCodeAt(h);H[f>>0>>>0]=0;c+=e.length+1});return 0},wa:function(a,b){var c=Na();J[a>>2>>>0]=c.length;var e=0;c.forEach(function(f){e+=f.length+1});J[b>>2>>>0]=e;return 0},ba:function(a){noExitRuntime||0<ka||(Ya(),O(xa),Za(0),Pa[1].length&&Qa(1,10),Pa[2].length&&Qa(2,10));if(!(noExitRuntime||0<ka)){if(d.onExit)d.onExit(a);D=!0}ea(a,new ja(a))},E:function(){return 52},Q:function(){return 52},ca:function(){return 70},P:function(a,b,c,e){for(var f=0,h=0;h<c;h++){var k=J[b>>2>>>0],l=J[b+4>>\r\n2>>>0];b+=8;for(var n=0;n<l;n++)Qa(a,G[k+n>>>0]);f+=l}J[e>>2>>>0]=f;return 0},c:function(){return V},ja:W,ea:$a,fa:ab,J:bb,e:cb,N:db,O:eb,j:fb,o:gb,p:hb,M:ib,r:jb,v:kb,K:lb,D:mb,X:nb,V:ob,U:pb,Z:qb,W:rb,Y:sb,T:tb,f:ub,q:vb,h:wb,da:xb,l:yb,t:zb,u:Ab,x:Bb,z:Cb,ga:Db,A:Eb,C:Fb,aa:Gb,_:Hb,$:Ib,n:function(a){return a},F:function(a){V=a},ha:Wa,y:function(a,b,c,e){return Wa(a,b,c,e)}};\r\n(function(){function a(f){d.asm=f.exports;ma=d.asm.Ka;ta();ua=d.asm.ib;wa.unshift(d.asm.La);K--;d.monitorRunDependencies&&d.monitorRunDependencies(K);0==K&&(null!==Aa&&(clearInterval(Aa),Aa=null),L&&(f=L,L=null,f()))}function b(f){a(f.instance)}function c(f){return Ea().then(function(h){return WebAssembly.instantiate(h,e)}).then(function(h){return h}).then(f,function(h){z(\"failed to asynchronously prepare wasm: \"+h);B(h)})}var e={a:Jb};K++;d.monitorRunDependencies&&d.monitorRunDependencies(K);if(d.instantiateWasm)try{return d.instantiateWasm(e,\r\na)}catch(f){return z(\"Module.instantiateWasm callback failed with error: \"+f),!1}(function(){return A||\"function\"!=typeof WebAssembly.instantiateStreaming||Ba()||N.startsWith(\"file://\")||p||\"function\"!=typeof fetch?c(b):fetch(N,{credentials:\"same-origin\"}).then(function(f){return WebAssembly.instantiateStreaming(f,e).then(b,function(h){z(\"wasm streaming compile failed: \"+h);z(\"falling back to ArrayBuffer instantiation\");return c(b)})})})().catch(ba);return{}})();\r\nd.___wasm_call_ctors=function(){return(d.___wasm_call_ctors=d.asm.La).apply(null,arguments)};d._OrtInit=function(){return(d._OrtInit=d.asm.Ma).apply(null,arguments)};d._OrtCreateSessionOptions=function(){return(d._OrtCreateSessionOptions=d.asm.Na).apply(null,arguments)};d._OrtAppendExecutionProvider=function(){return(d._OrtAppendExecutionProvider=d.asm.Oa).apply(null,arguments)};d._OrtAddSessionConfigEntry=function(){return(d._OrtAddSessionConfigEntry=d.asm.Pa).apply(null,arguments)};\r\nd._OrtReleaseSessionOptions=function(){return(d._OrtReleaseSessionOptions=d.asm.Qa).apply(null,arguments)};d._OrtCreateSession=function(){return(d._OrtCreateSession=d.asm.Ra).apply(null,arguments)};d._OrtReleaseSession=function(){return(d._OrtReleaseSession=d.asm.Sa).apply(null,arguments)};d._OrtGetInputCount=function(){return(d._OrtGetInputCount=d.asm.Ta).apply(null,arguments)};d._OrtGetOutputCount=function(){return(d._OrtGetOutputCount=d.asm.Ua).apply(null,arguments)};\r\nd._OrtGetInputName=function(){return(d._OrtGetInputName=d.asm.Va).apply(null,arguments)};d._OrtGetOutputName=function(){return(d._OrtGetOutputName=d.asm.Wa).apply(null,arguments)};d._OrtFree=function(){return(d._OrtFree=d.asm.Xa).apply(null,arguments)};d._OrtCreateTensor=function(){return(d._OrtCreateTensor=d.asm.Ya).apply(null,arguments)};d._OrtGetTensorData=function(){return(d._OrtGetTensorData=d.asm.Za).apply(null,arguments)};\r\nd._OrtReleaseTensor=function(){return(d._OrtReleaseTensor=d.asm._a).apply(null,arguments)};d._OrtCreateRunOptions=function(){return(d._OrtCreateRunOptions=d.asm.$a).apply(null,arguments)};d._OrtAddRunConfigEntry=function(){return(d._OrtAddRunConfigEntry=d.asm.ab).apply(null,arguments)};d._OrtReleaseRunOptions=function(){return(d._OrtReleaseRunOptions=d.asm.bb).apply(null,arguments)};d._OrtRun=function(){return(d._OrtRun=d.asm.cb).apply(null,arguments)};\r\nd._OrtEndProfiling=function(){return(d._OrtEndProfiling=d.asm.db).apply(null,arguments)};\r\nvar Ja=d._malloc=function(){return(Ja=d._malloc=d.asm.eb).apply(null,arguments)},Ha=d._free=function(){return(Ha=d._free=d.asm.fb).apply(null,arguments)},Za=d._fflush=function(){return(Za=d._fflush=d.asm.gb).apply(null,arguments)},Ya=d.___funcs_on_exit=function(){return(Ya=d.___funcs_on_exit=d.asm.hb).apply(null,arguments)},X=d._setThrew=function(){return(X=d._setThrew=d.asm.jb).apply(null,arguments)},Y=d.stackSave=function(){return(Y=d.stackSave=d.asm.kb).apply(null,arguments)},Z=d.stackRestore=\r\nfunction(){return(Z=d.stackRestore=d.asm.lb).apply(null,arguments)},Kb=d.stackAlloc=function(){return(Kb=d.stackAlloc=d.asm.mb).apply(null,arguments)},Xa=d.___cxa_can_catch=function(){return(Xa=d.___cxa_can_catch=d.asm.nb).apply(null,arguments)},Fa=d.___cxa_is_pointer_type=function(){return(Fa=d.___cxa_is_pointer_type=d.asm.ob).apply(null,arguments)},Lb=d.dynCall_j=function(){return(Lb=d.dynCall_j=d.asm.pb).apply(null,arguments)},Mb=d.dynCall_iiiiij=function(){return(Mb=d.dynCall_iiiiij=d.asm.qb).apply(null,\r\narguments)},Nb=d.dynCall_jii=function(){return(Nb=d.dynCall_jii=d.asm.rb).apply(null,arguments)},Ob=d.dynCall_viiiiij=function(){return(Ob=d.dynCall_viiiiij=d.asm.sb).apply(null,arguments)},Pb=d.dynCall_vjji=function(){return(Pb=d.dynCall_vjji=d.asm.tb).apply(null,arguments)},Qb=d.dynCall_viiijjjii=function(){return(Qb=d.dynCall_viiijjjii=d.asm.ub).apply(null,arguments)},Rb=d.dynCall_iij=function(){return(Rb=d.dynCall_iij=d.asm.vb).apply(null,arguments)},Sb=d.dynCall_ji=function(){return(Sb=d.dynCall_ji=\r\nd.asm.wb).apply(null,arguments)},Tb=d.dynCall_iiiiiij=function(){return(Tb=d.dynCall_iiiiiij=d.asm.xb).apply(null,arguments)},Ub=d.dynCall_iiij=function(){return(Ub=d.dynCall_iiij=d.asm.yb).apply(null,arguments)};function cb(a,b){var c=Y();try{return U(a)(b)}catch(e){Z(c);if(e!==e+0)throw e;X(1,0)}}function vb(a,b){var c=Y();try{U(a)(b)}catch(e){Z(c);if(e!==e+0)throw e;X(1,0)}}function wb(a,b,c){var e=Y();try{U(a)(b,c)}catch(f){Z(e);if(f!==f+0)throw f;X(1,0)}}\r\nfunction fb(a,b,c){var e=Y();try{return U(a)(b,c)}catch(f){Z(e);if(f!==f+0)throw f;X(1,0)}}function gb(a,b,c,e){var f=Y();try{return U(a)(b,c,e)}catch(h){Z(f);if(h!==h+0)throw h;X(1,0)}}function kb(a,b,c,e,f,h,k){var l=Y();try{return U(a)(b,c,e,f,h,k)}catch(n){Z(l);if(n!==n+0)throw n;X(1,0)}}function ub(a){var b=Y();try{U(a)()}catch(c){Z(b);if(c!==c+0)throw c;X(1,0)}}function jb(a,b,c,e,f,h){var k=Y();try{return U(a)(b,c,e,f,h)}catch(l){Z(k);if(l!==l+0)throw l;X(1,0)}}\r\nfunction hb(a,b,c,e,f){var h=Y();try{return U(a)(b,c,e,f)}catch(k){Z(h);if(k!==k+0)throw k;X(1,0)}}function yb(a,b,c,e){var f=Y();try{U(a)(b,c,e)}catch(h){Z(f);if(h!==h+0)throw h;X(1,0)}}function Ab(a,b,c,e,f,h){var k=Y();try{U(a)(b,c,e,f,h)}catch(l){Z(k);if(l!==l+0)throw l;X(1,0)}}function zb(a,b,c,e,f){var h=Y();try{U(a)(b,c,e,f)}catch(k){Z(h);if(k!==k+0)throw k;X(1,0)}}function Bb(a,b,c,e,f,h,k){var l=Y();try{U(a)(b,c,e,f,h,k)}catch(n){Z(l);if(n!==n+0)throw n;X(1,0)}}\r\nfunction Cb(a,b,c,e,f,h,k,l){var n=Y();try{U(a)(b,c,e,f,h,k,l)}catch(t){Z(n);if(t!==t+0)throw t;X(1,0)}}function eb(a,b,c){var e=Y();try{return U(a)(b,c)}catch(f){Z(e);if(f!==f+0)throw f;X(1,0)}}function db(a,b,c){var e=Y();try{return U(a)(b,c)}catch(f){Z(e);if(f!==f+0)throw f;X(1,0)}}function Db(a,b,c,e,f,h,k,l,n){var t=Y();try{U(a)(b,c,e,f,h,k,l,n)}catch(x){Z(t);if(x!==x+0)throw x;X(1,0)}}function ib(a,b,c,e,f,h){var k=Y();try{return U(a)(b,c,e,f,h)}catch(l){Z(k);if(l!==l+0)throw l;X(1,0)}}\r\nfunction lb(a,b,c,e,f,h,k,l){var n=Y();try{return U(a)(b,c,e,f,h,k,l)}catch(t){Z(n);if(t!==t+0)throw t;X(1,0)}}function mb(a,b,c,e,f,h,k,l,n,t,x,E){var C=Y();try{return U(a)(b,c,e,f,h,k,l,n,t,x,E)}catch(g){Z(C);if(g!==g+0)throw g;X(1,0)}}function Eb(a,b,c,e,f,h,k,l,n,t,x){var E=Y();try{U(a)(b,c,e,f,h,k,l,n,t,x)}catch(C){Z(E);if(C!==C+0)throw C;X(1,0)}}function Fb(a,b,c,e,f,h,k,l,n,t,x,E,C,g,u,w){var F=Y();try{U(a)(b,c,e,f,h,k,l,n,t,x,E,C,g,u,w)}catch(M){Z(F);if(M!==M+0)throw M;X(1,0)}}\r\nfunction bb(a){var b=Y();try{return U(a)()}catch(c){Z(b);if(c!==c+0)throw c;X(1,0)}}function ab(a,b,c){var e=Y();try{return U(a)(b,c)}catch(f){Z(e);if(f!==f+0)throw f;X(1,0)}}function $a(a,b,c){var e=Y();try{return U(a)(b,c)}catch(f){Z(e);if(f!==f+0)throw f;X(1,0)}}function xb(a,b,c,e){var f=Y();try{U(a)(b,c,e)}catch(h){Z(f);if(h!==h+0)throw h;X(1,0)}}function Gb(a,b,c,e,f,h,k,l){var n=Y();try{Ob(a,b,c,e,f,h,k,l)}catch(t){Z(n);if(t!==t+0)throw t;X(1,0)}}\r\nfunction Ib(a,b,c,e,f,h){var k=Y();try{Pb(a,b,c,e,f,h)}catch(l){Z(k);if(l!==l+0)throw l;X(1,0)}}function Hb(a,b,c,e,f,h,k,l,n,t,x,E){var C=Y();try{Qb(a,b,c,e,f,h,k,l,n,t,x,E)}catch(g){Z(C);if(g!==g+0)throw g;X(1,0)}}function qb(a,b,c,e){var f=Y();try{return Rb(a,b,c,e)}catch(h){Z(f);if(h!==h+0)throw h;X(1,0)}}function sb(a,b){var c=Y();try{return Sb(a,b)}catch(e){Z(c);if(e!==e+0)throw e;X(1,0)}}\r\nfunction nb(a,b,c,e,f,h,k,l){var n=Y();try{return Tb(a,b,c,e,f,h,k,l)}catch(t){Z(n);if(t!==t+0)throw t;X(1,0)}}function rb(a){var b=Y();try{return Lb(a)}catch(c){Z(b);if(c!==c+0)throw c;X(1,0)}}function ob(a,b,c,e,f,h,k){var l=Y();try{return Mb(a,b,c,e,f,h,k)}catch(n){Z(l);if(n!==n+0)throw n;X(1,0)}}function pb(a,b,c,e,f){var h=Y();try{return Ub(a,b,c,e,f)}catch(k){Z(h);if(k!==k+0)throw k;X(1,0)}}function tb(a,b,c){var e=Y();try{return Nb(a,b,c)}catch(f){Z(e);if(f!==f+0)throw f;X(1,0)}}\r\nd.UTF8ToString=pa;d.stringToUTF8=function(a,b,c){return qa(a,G,b,c)};d.lengthBytesUTF8=ra;d.stackSave=Y;d.stackRestore=Z;d.stackAlloc=Kb;var Vb;L=function Wb(){Vb||Xb();Vb||(L=Wb)};\r\nfunction Xb(){function a(){if(!Vb&&(Vb=!0,d.calledRun=!0,!D)){O(wa);aa(d);if(d.onRuntimeInitialized)d.onRuntimeInitialized();if(d.postRun)for(\"function\"==typeof d.postRun&&(d.postRun=[d.postRun]);d.postRun.length;){var b=d.postRun.shift();ya.unshift(b)}O(ya)}}if(!(0<K)){if(d.preRun)for(\"function\"==typeof d.preRun&&(d.preRun=[d.preRun]);d.preRun.length;)za();O(va);0<K||(d.setStatus?(d.setStatus(\"Running...\"),setTimeout(function(){setTimeout(function(){d.setStatus(\"\")},1);a()},1)):a())}}\r\nif(d.preInit)for(\"function\"==typeof d.preInit&&(d.preInit=[d.preInit]);0<d.preInit.length;)d.preInit.pop()();Xb();\r\n\r\n\r\n  return ortWasm.ready\r\n}\r\n);\r\n})();\r\nif (typeof exports === 'object' && typeof module === 'object')\r\n  module.exports = ortWasm;\r\nelse if (typeof define === 'function' && define['amd'])\r\n  define([], function() { return ortWasm; });\r\nelse if (typeof exports === 'object')\r\n  exports[\"ortWasm\"] = ortWasm;\r\n", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\nimport {Backend, env, InferenceSession, SessionHandler} from 'onnxruntime-common';\nimport {cpus} from 'os';\n\nimport {initWasm} from './wasm/proxy-wrapper';\nimport {OnnxruntimeWebAssemblySessionHandler} from './wasm/session-handler';\n\n/**\n * This function initializes all flags for WebAssembly.\n *\n * Those flags are accessible from `ort.env.wasm`. Users are allow to set those flags before the first inference session\n * being created, to override default value.\n */\nexport const initializeFlags = (): void => {\n  if (typeof env.wasm.initTimeout !== 'number' || env.wasm.initTimeout < 0) {\n    env.wasm.initTimeout = 0;\n  }\n\n  if (typeof env.wasm.simd !== 'boolean') {\n    env.wasm.simd = true;\n  }\n\n  if (typeof env.wasm.proxy !== 'boolean') {\n    env.wasm.proxy = false;\n  }\n\n  if (typeof env.wasm.numThreads !== 'number' || !Number.isInteger(env.wasm.numThreads) || env.wasm.numThreads <= 0) {\n    const numCpuLogicalCores = typeof navigator === 'undefined' ? cpus().length : navigator.hardwareConcurrency;\n    env.wasm.numThreads = Math.min(4, Math.ceil((numCpuLogicalCores || 1) / 2));\n  }\n};\n\nclass OnnxruntimeWebAssemblyBackend implements Backend {\n  async init(): Promise<void> {\n    // populate wasm flags\n    initializeFlags();\n\n    // init wasm\n    await initWasm();\n  }\n  createSessionHandler(path: string, options?: InferenceSession.SessionOptions): Promise<SessionHandler>;\n  createSessionHandler(buffer: Uint8Array, options?: InferenceSession.SessionOptions): Promise<SessionHandler>;\n  async createSessionHandler(pathOrBuffer: string|Uint8Array, options?: InferenceSession.SessionOptions):\n      Promise<SessionHandler> {\n    const handler = new OnnxruntimeWebAssemblySessionHandler();\n    await handler.loadModel(pathOrBuffer, options);\n    return Promise.resolve(handler);\n  }\n}\n\nexport const wasmBackend = new OnnxruntimeWebAssemblyBackend();\n", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\n/* eslint-disable @typescript-eslint/no-var-requires, @typescript-eslint/no-require-imports */\n// We use \"require\" instead of \"import\" here because import statement must be put in top level. Our current code does\n// not allow terser to tree-shaking code as expected because some codes are treated as having side effects.\n// So we import code inside the if-clause to allow terser remove the code safely.\n\nexport * from 'onnxruntime-common';\nimport {registerBackend} from 'onnxruntime-common';\n\nif (!BUILD_DEFS.DISABLE_WEBGL) {\n  const onnxjsBackend = require('./backend-onnxjs').onnxjsBackend;\n  registerBackend('webgl', onnxjsBackend, -10);\n}\nif (!BUILD_DEFS.DISABLE_WASM) {\n  const wasmBackend = require('./backend-wasm').wasmBackend;\n  registerBackend('cpu', wasmBackend, 10);\n  registerBackend('wasm', wasmBackend, 10);\n  registerBackend('xnnpack', wasmBackend, 9);\n}\n", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\ninterface ExtraOptionsHandler {\n  (name: string, value: string): void;\n}\n\nexport const iterateExtraOptions =\n    (options: Record<string, unknown>, prefix: string, seen: WeakSet<Record<string, unknown>>,\n     handler: ExtraOptionsHandler): void => {\n      if (typeof options == 'object' && options !== null) {\n        if (seen.has(options)) {\n          throw new Error('Circular reference in options');\n        } else {\n          seen.add(options);\n        }\n      }\n\n      Object.entries(options).forEach(([key, value]) => {\n        const name = (prefix) ? prefix + key : key;\n        if (typeof value === 'object') {\n          iterateExtraOptions(value as Record<string, unknown>, name + '.', seen, handler);\n        } else if (typeof value === 'string' || typeof value === 'number') {\n          handler(name, value.toString());\n        } else if (typeof value === 'boolean') {\n          handler(name, (value) ? '1' : '0');\n        } else {\n          throw new Error(`Can't handle extra config type: ${typeof value}`);\n        }\n      });\n    };\n", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\nimport {env, InferenceSession} from 'onnxruntime-common';\n\nimport {OrtWasmMessage, SerializableModeldata, SerializableSessionMetadata, SerializableTensor} from './proxy-messages';\nimport * as core from './wasm-core-impl';\nimport {initializeWebAssembly} from './wasm-factory';\n\nconst isProxy = (): boolean => !!env.wasm.proxy && typeof document !== 'undefined';\nlet proxyWorker: Worker|undefined;\nlet initializing = false;\nlet initialized = false;\nlet aborted = false;\n\n// resolve; reject\ntype PromiseCallbacks<T = void> = [(result: T) => void, (reason: unknown) => void];\n\nlet initWasmCallbacks: PromiseCallbacks;\nlet initOrtCallbacks: PromiseCallbacks;\nconst createSessionAllocateCallbacks: Array<PromiseCallbacks<SerializableModeldata>> = [];\nconst createSessionFinalizeCallbacks: Array<PromiseCallbacks<SerializableSessionMetadata>> = [];\nconst createSessionCallbacks: Array<PromiseCallbacks<SerializableSessionMetadata>> = [];\nconst releaseSessionCallbacks: Array<PromiseCallbacks<void>> = [];\nconst runCallbacks: Array<PromiseCallbacks<SerializableTensor[]>> = [];\nconst endProfilingCallbacks: Array<PromiseCallbacks<void>> = [];\n\nconst ensureWorker = (): void => {\n  if (initializing || !initialized || aborted || !proxyWorker) {\n    throw new Error('worker not ready');\n  }\n};\n\nconst onProxyWorkerMessage = (ev: MessageEvent<OrtWasmMessage>): void => {\n  switch (ev.data.type) {\n    case 'init-wasm':\n      initializing = false;\n      if (ev.data.err) {\n        aborted = true;\n        initWasmCallbacks[1](ev.data.err);\n      } else {\n        initialized = true;\n        initWasmCallbacks[0]();\n      }\n      break;\n    case 'init-ort':\n      if (ev.data.err) {\n        initOrtCallbacks[1](ev.data.err);\n      } else {\n        initOrtCallbacks[0]();\n      }\n      break;\n    case 'create_allocate':\n      if (ev.data.err) {\n        createSessionAllocateCallbacks.shift()![1](ev.data.err);\n      } else {\n        createSessionAllocateCallbacks.shift()![0](ev.data.out!);\n      }\n      break;\n    case 'create_finalize':\n      if (ev.data.err) {\n        createSessionFinalizeCallbacks.shift()![1](ev.data.err);\n      } else {\n        createSessionFinalizeCallbacks.shift()![0](ev.data.out!);\n      }\n      break;\n    case 'create':\n      if (ev.data.err) {\n        createSessionCallbacks.shift()![1](ev.data.err);\n      } else {\n        createSessionCallbacks.shift()![0](ev.data.out!);\n      }\n      break;\n    case 'release':\n      if (ev.data.err) {\n        releaseSessionCallbacks.shift()![1](ev.data.err);\n      } else {\n        releaseSessionCallbacks.shift()![0]();\n      }\n      break;\n    case 'run':\n      if (ev.data.err) {\n        runCallbacks.shift()![1](ev.data.err);\n      } else {\n        runCallbacks.shift()![0](ev.data.out!);\n      }\n      break;\n    case 'end-profiling':\n      if (ev.data.err) {\n        endProfilingCallbacks.shift()![1](ev.data.err);\n      } else {\n        endProfilingCallbacks.shift()![0]();\n      }\n      break;\n    default:\n  }\n};\n\nconst scriptSrc = typeof document !== 'undefined' ? (document?.currentScript as HTMLScriptElement)?.src : undefined;\n\nexport const initWasm = async(): Promise<void> => {\n  if (!BUILD_DEFS.DISABLE_WASM_PROXY && isProxy()) {\n    if (initialized) {\n      return;\n    }\n    if (initializing) {\n      throw new Error('multiple calls to \\'initWasm()\\' detected.');\n    }\n    if (aborted) {\n      throw new Error('previous call to \\'initWasm()\\' failed.');\n    }\n\n    initializing = true;\n\n    // overwrite wasm filepaths\n    if (env.wasm.wasmPaths === undefined) {\n      if (scriptSrc && scriptSrc.indexOf('blob:') !== 0) {\n        env.wasm.wasmPaths = scriptSrc.substr(0, +(scriptSrc).lastIndexOf('/') + 1);\n      }\n    }\n\n    return new Promise<void>((resolve, reject) => {\n      proxyWorker?.terminate();\n      // eslint-disable-next-line @typescript-eslint/no-var-requires, @typescript-eslint/no-require-imports\n      proxyWorker = require('worker-loader?inline=no-fallback!./proxy-worker/main').default() as Worker;\n      proxyWorker.onmessage = onProxyWorkerMessage;\n      initWasmCallbacks = [resolve, reject];\n      const message: OrtWasmMessage = {type: 'init-wasm', in : env.wasm};\n      proxyWorker.postMessage(message);\n    });\n\n  } else {\n    return initializeWebAssembly(env.wasm);\n  }\n};\n\nexport const initOrt = async(numThreads: number, loggingLevel: number): Promise<void> => {\n  if (!BUILD_DEFS.DISABLE_WASM_PROXY && isProxy()) {\n    ensureWorker();\n    return new Promise<void>((resolve, reject) => {\n      initOrtCallbacks = [resolve, reject];\n      const message: OrtWasmMessage = {type: 'init-ort', in : {numThreads, loggingLevel}};\n      proxyWorker!.postMessage(message);\n    });\n  } else {\n    core.initOrt(numThreads, loggingLevel);\n  }\n};\n\nexport const createSessionAllocate = async(model: Uint8Array): Promise<SerializableModeldata> => {\n  if (!BUILD_DEFS.DISABLE_WASM_PROXY && isProxy()) {\n    ensureWorker();\n    return new Promise<SerializableModeldata>((resolve, reject) => {\n      createSessionAllocateCallbacks.push([resolve, reject]);\n      const message: OrtWasmMessage = {type: 'create_allocate', in : {model}};\n      proxyWorker!.postMessage(message, [model.buffer]);\n    });\n  } else {\n    return core.createSessionAllocate(model);\n  }\n};\n\nexport const createSessionFinalize = async(modeldata: SerializableModeldata, options?: InferenceSession.SessionOptions):\n    Promise<SerializableSessionMetadata> => {\n      if (!BUILD_DEFS.DISABLE_WASM_PROXY && isProxy()) {\n        ensureWorker();\n        return new Promise<SerializableSessionMetadata>((resolve, reject) => {\n          createSessionFinalizeCallbacks.push([resolve, reject]);\n          const message: OrtWasmMessage = {type: 'create_finalize', in : {modeldata, options}};\n          proxyWorker!.postMessage(message);\n        });\n      } else {\n        return core.createSessionFinalize(modeldata, options);\n      }\n    };\n\nexport const createSession =\n    async(model: Uint8Array, options?: InferenceSession.SessionOptions): Promise<SerializableSessionMetadata> => {\n  if (!BUILD_DEFS.DISABLE_WASM_PROXY && isProxy()) {\n    ensureWorker();\n    return new Promise<SerializableSessionMetadata>((resolve, reject) => {\n      createSessionCallbacks.push([resolve, reject]);\n      const message: OrtWasmMessage = {type: 'create', in : {model, options}};\n      proxyWorker!.postMessage(message, [model.buffer]);\n    });\n  } else {\n    return core.createSession(model, options);\n  }\n};\n\nexport const releaseSession = async(sessionId: number): Promise<void> => {\n  if (!BUILD_DEFS.DISABLE_WASM_PROXY && isProxy()) {\n    ensureWorker();\n    return new Promise<void>((resolve, reject) => {\n      releaseSessionCallbacks.push([resolve, reject]);\n      const message: OrtWasmMessage = {type: 'release', in : sessionId};\n      proxyWorker!.postMessage(message);\n    });\n  } else {\n    core.releaseSession(sessionId);\n  }\n};\n\nexport const run = async(\n    sessionId: number, inputIndices: number[], inputs: SerializableTensor[], outputIndices: number[],\n    options: InferenceSession.RunOptions): Promise<SerializableTensor[]> => {\n  if (!BUILD_DEFS.DISABLE_WASM_PROXY && isProxy()) {\n    ensureWorker();\n    return new Promise<SerializableTensor[]>((resolve, reject) => {\n      runCallbacks.push([resolve, reject]);\n      const message: OrtWasmMessage = {type: 'run', in : {sessionId, inputIndices, inputs, outputIndices, options}};\n      proxyWorker!.postMessage(message, core.extractTransferableBuffers(inputs));\n    });\n  } else {\n    return core.run(sessionId, inputIndices, inputs, outputIndices, options);\n  }\n};\n\nexport const endProfiling = async(sessionId: number): Promise<void> => {\n  if (!BUILD_DEFS.DISABLE_WASM_PROXY && isProxy()) {\n    ensureWorker();\n    return new Promise<void>((resolve, reject) => {\n      endProfilingCallbacks.push([resolve, reject]);\n      const message: OrtWasmMessage = {type: 'end-profiling', in : sessionId};\n      proxyWorker!.postMessage(message);\n    });\n  } else {\n    core.endProfiling(sessionId);\n  }\n};\n", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\nimport {InferenceSession} from 'onnxruntime-common';\n\nimport {iterateExtraOptions} from './options-utils';\nimport {allocWasmString} from './string-utils';\nimport {getInstance} from './wasm-factory';\n\nexport const setRunOptions = (options: InferenceSession.RunOptions): [number, number[]] => {\n  const wasm = getInstance();\n  let runOptionsHandle = 0;\n  const allocs: number[] = [];\n\n  const runOptions: InferenceSession.RunOptions = options || {};\n\n  try {\n    if (options?.logSeverityLevel === undefined) {\n      runOptions.logSeverityLevel = 2;  // Default to warning\n    } else if (\n        typeof options.logSeverityLevel !== 'number' || !Number.isInteger(options.logSeverityLevel) ||\n        options.logSeverityLevel < 0 || options.logSeverityLevel > 4) {\n      throw new Error(`log serverity level is not valid: ${options.logSeverityLevel}`);\n    }\n\n    if (options?.logVerbosityLevel === undefined) {\n      runOptions.logVerbosityLevel = 0;  // Default to 0\n    } else if (typeof options.logVerbosityLevel !== 'number' || !Number.isInteger(options.logVerbosityLevel)) {\n      throw new Error(`log verbosity level is not valid: ${options.logVerbosityLevel}`);\n    }\n\n    if (options?.terminate === undefined) {\n      runOptions.terminate = false;\n    }\n\n    let tagDataOffset = 0;\n    if (options?.tag !== undefined) {\n      tagDataOffset = allocWasmString(options.tag, allocs);\n    }\n\n    runOptionsHandle = wasm._OrtCreateRunOptions(\n        runOptions.logSeverityLevel!, runOptions.logVerbosityLevel!, !!runOptions.terminate!, tagDataOffset);\n    if (runOptionsHandle === 0) {\n      throw new Error('Can\\'t create run options');\n    }\n\n    if (options?.extra !== undefined) {\n      iterateExtraOptions(options.extra, '', new WeakSet<Record<string, unknown>>(), (key, value) => {\n        const keyDataOffset = allocWasmString(key, allocs);\n        const valueDataOffset = allocWasmString(value, allocs);\n\n        if (wasm._OrtAddRunConfigEntry(runOptionsHandle, keyDataOffset, valueDataOffset) !== 0) {\n          throw new Error(`Can't set a run config entry: ${key} - ${value}`);\n        }\n      });\n    }\n\n    return [runOptionsHandle, allocs];\n  } catch (e) {\n    if (runOptionsHandle !== 0) {\n      wasm._OrtReleaseRunOptions(runOptionsHandle);\n    }\n    allocs.forEach(wasm._free);\n    throw e;\n  }\n};\n", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\nimport {readFile} from 'fs';\nimport {env, InferenceSession, SessionHandler, Tensor} from 'onnxruntime-common';\nimport {promisify} from 'util';\n\nimport {SerializableModeldata} from './proxy-messages';\nimport {createSession, createSessionAllocate, createSessionFinalize, endProfiling, initOrt, releaseSession, run} from './proxy-wrapper';\n\nlet ortInit: boolean;\n\n\nconst getLogLevel = (logLevel: 'verbose'|'info'|'warning'|'error'|'fatal'): number => {\n  switch (logLevel) {\n    case 'verbose':\n      return 0;\n    case 'info':\n      return 1;\n    case 'warning':\n      return 2;\n    case 'error':\n      return 3;\n    case 'fatal':\n      return 4;\n    default:\n      throw new Error(`unsupported logging level: ${logLevel}`);\n  }\n};\n\n\nexport class OnnxruntimeWebAssemblySessionHandler implements SessionHandler {\n  private sessionId: number;\n\n  inputNames: string[];\n  outputNames: string[];\n\n  async createSessionAllocate(path: string): Promise<SerializableModeldata> {\n    // fetch model from url and move to wasm heap. The arraybufffer that held the http\n    // response is freed once we return\n    const response = await fetch(path);\n    const arrayBuffer = await response.arrayBuffer();\n    return createSessionAllocate(new Uint8Array(arrayBuffer));\n  }\n\n  async loadModel(pathOrBuffer: string|Uint8Array, options?: InferenceSession.SessionOptions): Promise<void> {\n    if (!ortInit) {\n      await initOrt(env.wasm.numThreads!, getLogLevel(env.logLevel!));\n      ortInit = true;\n    }\n\n    if (typeof pathOrBuffer === 'string') {\n      if (typeof fetch === 'undefined') {\n        // node\n        const model = await promisify(readFile)(pathOrBuffer);\n        [this.sessionId, this.inputNames, this.outputNames] = await createSession(model, options);\n      } else {\n        // browser\n        // fetch model and move to wasm heap.\n        const modelData: SerializableModeldata = await this.createSessionAllocate(pathOrBuffer);\n        // create the session\n        [this.sessionId, this.inputNames, this.outputNames] = await createSessionFinalize(modelData, options);\n      }\n    } else {\n      [this.sessionId, this.inputNames, this.outputNames] = await createSession(pathOrBuffer, options);\n    }\n  }\n\n  async dispose(): Promise<void> {\n    return releaseSession(this.sessionId);\n  }\n\n  async run(feeds: SessionHandler.FeedsType, fetches: SessionHandler.FetchesType, options: InferenceSession.RunOptions):\n      Promise<SessionHandler.ReturnType> {\n    const inputArray: Tensor[] = [];\n    const inputIndices: number[] = [];\n    Object.entries(feeds).forEach(kvp => {\n      const name = kvp[0];\n      const tensor = kvp[1];\n      const index = this.inputNames.indexOf(name);\n      if (index === -1) {\n        throw new Error(`invalid input '${name}'`);\n      }\n      inputArray.push(tensor);\n      inputIndices.push(index);\n    });\n\n    const outputIndices: number[] = [];\n    Object.entries(fetches).forEach(kvp => {\n      const name = kvp[0];\n      // TODO: support pre-allocated output\n      const index = this.outputNames.indexOf(name);\n      if (index === -1) {\n        throw new Error(`invalid output '${name}'`);\n      }\n      outputIndices.push(index);\n    });\n\n    const outputs =\n        await run(this.sessionId, inputIndices, inputArray.map(t => [t.type, t.dims, t.data]), outputIndices, options);\n\n    const result: SessionHandler.ReturnType = {};\n    for (let i = 0; i < outputs.length; i++) {\n      result[this.outputNames[outputIndices[i]]] = new Tensor(outputs[i][0], outputs[i][2], outputs[i][1]);\n    }\n    return result;\n  }\n\n  startProfiling(): void {\n    // TODO: implement profiling\n  }\n\n  endProfiling(): void {\n    void endProfiling(this.sessionId);\n  }\n}\n", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\nimport {InferenceSession} from 'onnxruntime-common';\n\nimport {iterateExtraOptions} from './options-utils';\nimport {allocWasmString} from './string-utils';\nimport {getInstance} from './wasm-factory';\n\nconst getGraphOptimzationLevel = (graphOptimizationLevel: string|unknown): number => {\n  switch (graphOptimizationLevel) {\n    case 'disabled':\n      return 0;\n    case 'basic':\n      return 1;\n    case 'extended':\n      return 2;\n    case 'all':\n      return 99;\n    default:\n      throw new Error(`unsupported graph optimization level: ${graphOptimizationLevel}`);\n  }\n};\n\nconst getExecutionMode = (executionMode: 'sequential'|'parallel'): number => {\n  switch (executionMode) {\n    case 'sequential':\n      return 0;\n    case 'parallel':\n      return 1;\n    default:\n      throw new Error(`unsupported execution mode: ${executionMode}`);\n  }\n};\n\nconst appendDefaultOptions = (options: InferenceSession.SessionOptions): void => {\n  if (!options.extra) {\n    options.extra = {};\n  }\n  if (!options.extra.session) {\n    options.extra.session = {};\n  }\n  const session = options.extra.session as Record<string, string>;\n  if (!session.use_ort_model_bytes_directly) {\n    // eslint-disable-next-line camelcase\n    session.use_ort_model_bytes_directly = '1';\n  }\n};\n\nconst setExecutionProviders =\n    (sessionOptionsHandle: number, executionProviders: readonly InferenceSession.ExecutionProviderConfig[],\n     allocs: number[]): void => {\n      for (const ep of executionProviders) {\n        let epName = typeof ep === 'string' ? ep : ep.name;\n\n        // check EP name\n        switch (epName) {\n          case 'xnnpack':\n            epName = 'XNNPACK';\n            break;\n          case 'wasm':\n          case 'cpu':\n            continue;\n          default:\n            throw new Error(`not supported EP: ${epName}`);\n        }\n\n        const epNameDataOffset = allocWasmString(epName, allocs);\n        if (getInstance()._OrtAppendExecutionProvider(sessionOptionsHandle, epNameDataOffset) !== 0) {\n          throw new Error(`Can't append execution provider: ${epName}`);\n        }\n      }\n    };\n\nexport const setSessionOptions = (options?: InferenceSession.SessionOptions): [number, number[]] => {\n  const wasm = getInstance();\n  let sessionOptionsHandle = 0;\n  const allocs: number[] = [];\n\n  const sessionOptions: InferenceSession.SessionOptions = options || {};\n  appendDefaultOptions(sessionOptions);\n\n  try {\n    if (options?.graphOptimizationLevel === undefined) {\n      sessionOptions.graphOptimizationLevel = 'all';\n    }\n    const graphOptimizationLevel = getGraphOptimzationLevel(sessionOptions.graphOptimizationLevel!);\n\n    if (options?.enableCpuMemArena === undefined) {\n      sessionOptions.enableCpuMemArena = true;\n    }\n\n    if (options?.enableMemPattern === undefined) {\n      sessionOptions.enableMemPattern = true;\n    }\n\n    if (options?.executionMode === undefined) {\n      sessionOptions.executionMode = 'sequential';\n    }\n    const executionMode = getExecutionMode(sessionOptions.executionMode!);\n\n    let logIdDataOffset = 0;\n    if (options?.logId !== undefined) {\n      logIdDataOffset = allocWasmString(options.logId, allocs);\n    }\n\n    if (options?.logSeverityLevel === undefined) {\n      sessionOptions.logSeverityLevel = 2;  // Default to warning\n    } else if (\n        typeof options.logSeverityLevel !== 'number' || !Number.isInteger(options.logSeverityLevel) ||\n        options.logSeverityLevel < 0 || options.logSeverityLevel > 4) {\n      throw new Error(`log serverity level is not valid: ${options.logSeverityLevel}`);\n    }\n\n    if (options?.logVerbosityLevel === undefined) {\n      sessionOptions.logVerbosityLevel = 0;  // Default to 0\n    } else if (typeof options.logVerbosityLevel !== 'number' || !Number.isInteger(options.logVerbosityLevel)) {\n      throw new Error(`log verbosity level is not valid: ${options.logVerbosityLevel}`);\n    }\n\n    if (options?.enableProfiling === undefined) {\n      sessionOptions.enableProfiling = false;\n    }\n\n    sessionOptionsHandle = wasm._OrtCreateSessionOptions(\n        graphOptimizationLevel, !!sessionOptions.enableCpuMemArena!, !!sessionOptions.enableMemPattern!, executionMode,\n        !!sessionOptions.enableProfiling!, 0, logIdDataOffset, sessionOptions.logSeverityLevel!,\n        sessionOptions.logVerbosityLevel!);\n    if (sessionOptionsHandle === 0) {\n      throw new Error('Can\\'t create session options');\n    }\n\n    if (options?.executionProviders) {\n      setExecutionProviders(sessionOptionsHandle, options.executionProviders, allocs);\n    }\n\n    if (options?.extra !== undefined) {\n      iterateExtraOptions(options.extra, '', new WeakSet<Record<string, unknown>>(), (key, value) => {\n        const keyDataOffset = allocWasmString(key, allocs);\n        const valueDataOffset = allocWasmString(value, allocs);\n\n        if (wasm._OrtAddSessionConfigEntry(sessionOptionsHandle, keyDataOffset, valueDataOffset) !== 0) {\n          throw new Error(`Can't set a session config entry: ${key} - ${value}`);\n        }\n      });\n    }\n\n    return [sessionOptionsHandle, allocs];\n  } catch (e) {\n    if (sessionOptionsHandle !== 0) {\n      wasm._OrtReleaseSessionOptions(sessionOptionsHandle);\n    }\n    allocs.forEach(wasm._free);\n    throw e;\n  }\n};\n", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\nimport {getInstance} from './wasm-factory';\n\nexport const allocWasmString = (data: string, allocs: number[]): number => {\n  const wasm = getInstance();\n\n  const dataLength = wasm.lengthBytesUTF8(data) + 1;\n  const dataOffset = wasm._malloc(dataLength);\n  wasm.stringToUTF8(data, dataOffset, dataLength);\n  allocs.push(dataOffset);\n\n  return dataOffset;\n};\n", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\nimport {InferenceSession, Tensor} from 'onnxruntime-common';\n\nimport {SerializableModeldata, SerializableSessionMetadata, SerializableTensor} from './proxy-messages';\nimport {setRunOptions} from './run-options';\nimport {setSessionOptions} from './session-options';\nimport {allocWasmString} from './string-utils';\nimport {getInstance} from './wasm-factory';\n\n/**\n * initialize ORT environment.\n * @param numThreads SetGlobalIntraOpNumThreads(numThreads)\n * @param loggingLevel CreateEnv(static_cast<OrtLoggingLevel>(logging_level))\n */\nexport const initOrt = (numThreads: number, loggingLevel: number): void => {\n  const errorCode = getInstance()._OrtInit(numThreads, loggingLevel);\n  if (errorCode !== 0) {\n    throw new Error(`Can't initialize onnxruntime. error code = ${errorCode}`);\n  }\n};\n\n/**\n *  tuple elements are: InferenceSession ID; inputNamesUTF8Encoded; outputNamesUTF8Encoded\n */\ntype SessionMetadata = [number, number[], number[]];\n\nconst activeSessions = new Map<number, SessionMetadata>();\n\n/**\n * create an instance of InferenceSession.\n * @returns the metadata of InferenceSession. 0-value handle for failure.\n */\nexport const createSessionAllocate = (model: Uint8Array): [number, number] => {\n  const wasm = getInstance();\n  const modelDataOffset = wasm._malloc(model.byteLength);\n  wasm.HEAPU8.set(model, modelDataOffset);\n  return [modelDataOffset, model.byteLength];\n};\n\nexport const createSessionFinalize =\n    (modelData: SerializableModeldata, options?: InferenceSession.SessionOptions): SerializableSessionMetadata => {\n      const wasm = getInstance();\n\n      let sessionHandle = 0;\n      let sessionOptionsHandle = 0;\n      let allocs: number[] = [];\n\n      try {\n        [sessionOptionsHandle, allocs] = setSessionOptions(options);\n\n        sessionHandle = wasm._OrtCreateSession(modelData[0], modelData[1], sessionOptionsHandle);\n        if (sessionHandle === 0) {\n          throw new Error('Can\\'t create a session');\n        }\n      } finally {\n        wasm._free(modelData[0]);\n        wasm._OrtReleaseSessionOptions(sessionOptionsHandle);\n        allocs.forEach(wasm._free);\n      }\n\n      const inputCount = wasm._OrtGetInputCount(sessionHandle);\n      const outputCount = wasm._OrtGetOutputCount(sessionHandle);\n\n      const inputNames = [];\n      const inputNamesUTF8Encoded = [];\n      const outputNames = [];\n      const outputNamesUTF8Encoded = [];\n      for (let i = 0; i < inputCount; i++) {\n        const name = wasm._OrtGetInputName(sessionHandle, i);\n        if (name === 0) {\n          throw new Error('Can\\'t get an input name');\n        }\n        inputNamesUTF8Encoded.push(name);\n        inputNames.push(wasm.UTF8ToString(name));\n      }\n      for (let i = 0; i < outputCount; i++) {\n        const name = wasm._OrtGetOutputName(sessionHandle, i);\n        if (name === 0) {\n          throw new Error('Can\\'t get an output name');\n        }\n        outputNamesUTF8Encoded.push(name);\n        outputNames.push(wasm.UTF8ToString(name));\n      }\n\n      activeSessions.set(sessionHandle, [sessionHandle, inputNamesUTF8Encoded, outputNamesUTF8Encoded]);\n      return [sessionHandle, inputNames, outputNames];\n    };\n\n\n/**\n * create an instance of InferenceSession.\n * @returns the metadata of InferenceSession. 0-value handle for failure.\n */\nexport const createSession =\n    (model: Uint8Array, options?: InferenceSession.SessionOptions): SerializableSessionMetadata => {\n      const modelData: SerializableModeldata = createSessionAllocate(model);\n      return createSessionFinalize(modelData, options);\n    };\n\nexport const releaseSession = (sessionId: number): void => {\n  const wasm = getInstance();\n  const session = activeSessions.get(sessionId);\n  if (!session) {\n    throw new Error('invalid session id');\n  }\n  const sessionHandle = session[0];\n  const inputNamesUTF8Encoded = session[1];\n  const outputNamesUTF8Encoded = session[2];\n\n  inputNamesUTF8Encoded.forEach(wasm._OrtFree);\n  outputNamesUTF8Encoded.forEach(wasm._OrtFree);\n  wasm._OrtReleaseSession(sessionHandle);\n  activeSessions.delete(sessionId);\n};\n\n/**\n * Copied from ONNX definition. Use this to drop dependency 'onnx_proto' to decrease compiled .js file size.\n */\nconst enum DataType {\n  undefined = 0,\n  float = 1,\n  uint8 = 2,\n  int8 = 3,\n  uint16 = 4,\n  int16 = 5,\n  int32 = 6,\n  int64 = 7,\n  string = 8,\n  bool = 9,\n  float16 = 10,\n  double = 11,\n  uint32 = 12,\n  uint64 = 13,\n  complex64 = 14,\n  complex128 = 15,\n  bfloat16 = 16\n}\n\n\nconst tensorDataTypeStringToEnum = (type: string): DataType => {\n  switch (type) {\n    case 'int8':\n      return DataType.int8;\n    case 'uint8':\n      return DataType.uint8;\n    case 'bool':\n      return DataType.bool;\n    case 'int16':\n      return DataType.int16;\n    case 'uint16':\n      return DataType.uint16;\n    case 'int32':\n      return DataType.int32;\n    case 'uint32':\n      return DataType.uint32;\n    case 'float32':\n      return DataType.float;\n    case 'float64':\n      return DataType.double;\n    case 'string':\n      return DataType.string;\n    case 'int64':\n      return DataType.int64;\n    case 'uint64':\n      return DataType.uint64;\n\n    default:\n      throw new Error(`unsupported data type: ${type}`);\n  }\n};\n\nconst tensorDataTypeEnumToString = (typeProto: DataType): Tensor.Type => {\n  switch (typeProto) {\n    case DataType.int8:\n      return 'int8';\n    case DataType.uint8:\n      return 'uint8';\n    case DataType.bool:\n      return 'bool';\n    case DataType.int16:\n      return 'int16';\n    case DataType.uint16:\n      return 'uint16';\n    case DataType.int32:\n      return 'int32';\n    case DataType.uint32:\n      return 'uint32';\n    case DataType.float:\n      return 'float32';\n    case DataType.double:\n      return 'float64';\n    case DataType.string:\n      return 'string';\n    case DataType.int64:\n      return 'int64';\n    case DataType.uint64:\n      return 'uint64';\n\n    default:\n      throw new Error(`unsupported data type: ${typeProto}`);\n  }\n};\n\nconst numericTensorTypeToTypedArray = (type: Tensor.Type): Float32ArrayConstructor|Uint8ArrayConstructor|\n    Int8ArrayConstructor|Uint16ArrayConstructor|Int16ArrayConstructor|Int32ArrayConstructor|BigInt64ArrayConstructor|\n    Uint8ArrayConstructor|Float64ArrayConstructor|Uint32ArrayConstructor|BigUint64ArrayConstructor => {\n      switch (type) {\n        case 'float32':\n          return Float32Array;\n        case 'uint8':\n          return Uint8Array;\n        case 'int8':\n          return Int8Array;\n        case 'uint16':\n          return Uint16Array;\n        case 'int16':\n          return Int16Array;\n        case 'int32':\n          return Int32Array;\n        case 'bool':\n          return Uint8Array;\n        case 'float64':\n          return Float64Array;\n        case 'uint32':\n          return Uint32Array;\n        case 'int64':\n          return BigInt64Array;\n        case 'uint64':\n          return BigUint64Array;\n        default:\n          throw new Error(`unsupported type: ${type}`);\n      }\n    };\n\n/**\n * perform inference run\n */\nexport const run =\n    (sessionId: number, inputIndices: number[], inputs: SerializableTensor[], outputIndices: number[],\n     options: InferenceSession.RunOptions): SerializableTensor[] => {\n      const wasm = getInstance();\n      const session = activeSessions.get(sessionId);\n      if (!session) {\n        throw new Error('invalid session id');\n      }\n      const sessionHandle = session[0];\n      const inputNamesUTF8Encoded = session[1];\n      const outputNamesUTF8Encoded = session[2];\n\n      const inputCount = inputIndices.length;\n      const outputCount = outputIndices.length;\n\n      let runOptionsHandle = 0;\n      let runOptionsAllocs: number[] = [];\n\n      const inputValues: number[] = [];\n      const inputAllocs: number[] = [];\n\n      try {\n        [runOptionsHandle, runOptionsAllocs] = setRunOptions(options);\n\n        // create input tensors\n        for (let i = 0; i < inputCount; i++) {\n          const dataType = inputs[i][0];\n          const dims = inputs[i][1];\n          const data = inputs[i][2];\n\n          let dataOffset: number;\n          let dataByteLength: number;\n\n          if (Array.isArray(data)) {\n            // string tensor\n            dataByteLength = 4 * data.length;\n            dataOffset = wasm._malloc(dataByteLength);\n            inputAllocs.push(dataOffset);\n            let dataIndex = dataOffset / 4;\n            for (let i = 0; i < data.length; i++) {\n              if (typeof data[i] !== 'string') {\n                throw new TypeError(`tensor data at index ${i} is not a string`);\n              }\n              wasm.HEAPU32[dataIndex++] = allocWasmString(data[i], inputAllocs);\n            }\n          } else {\n            dataByteLength = data.byteLength;\n            dataOffset = wasm._malloc(dataByteLength);\n            inputAllocs.push(dataOffset);\n            wasm.HEAPU8.set(new Uint8Array(data.buffer, data.byteOffset, dataByteLength), dataOffset);\n          }\n\n          const stack = wasm.stackSave();\n          const dimsOffset = wasm.stackAlloc(4 * dims.length);\n          try {\n            let dimIndex = dimsOffset / 4;\n            dims.forEach(d => wasm.HEAP32[dimIndex++] = d);\n            const tensor = wasm._OrtCreateTensor(\n                tensorDataTypeStringToEnum(dataType), dataOffset, dataByteLength, dimsOffset, dims.length);\n            if (tensor === 0) {\n              throw new Error('Can\\'t create a tensor');\n            }\n            inputValues.push(tensor);\n          } finally {\n            wasm.stackRestore(stack);\n          }\n        }\n\n        const beforeRunStack = wasm.stackSave();\n        const inputValuesOffset = wasm.stackAlloc(inputCount * 4);\n        const inputNamesOffset = wasm.stackAlloc(inputCount * 4);\n        const outputValuesOffset = wasm.stackAlloc(outputCount * 4);\n        const outputNamesOffset = wasm.stackAlloc(outputCount * 4);\n\n        try {\n          let inputValuesIndex = inputValuesOffset / 4;\n          let inputNamesIndex = inputNamesOffset / 4;\n          let outputValuesIndex = outputValuesOffset / 4;\n          let outputNamesIndex = outputNamesOffset / 4;\n          for (let i = 0; i < inputCount; i++) {\n            wasm.HEAPU32[inputValuesIndex++] = inputValues[i];\n            wasm.HEAPU32[inputNamesIndex++] = inputNamesUTF8Encoded[inputIndices[i]];\n          }\n          for (let i = 0; i < outputCount; i++) {\n            wasm.HEAPU32[outputValuesIndex++] = 0;\n            wasm.HEAPU32[outputNamesIndex++] = outputNamesUTF8Encoded[outputIndices[i]];\n          }\n\n          // support RunOptions\n          let errorCode = wasm._OrtRun(\n              sessionHandle, inputNamesOffset, inputValuesOffset, inputCount, outputNamesOffset, outputCount,\n              outputValuesOffset, runOptionsHandle);\n\n          const output: SerializableTensor[] = [];\n\n          if (errorCode === 0) {\n            for (let i = 0; i < outputCount; i++) {\n              const tensor = wasm.HEAPU32[outputValuesOffset / 4 + i];\n\n              const beforeGetTensorDataStack = wasm.stackSave();\n              // stack allocate 4 pointer value\n              const tensorDataOffset = wasm.stackAlloc(4 * 4);\n\n              let type: Tensor.Type|undefined, dataOffset = 0;\n              try {\n                errorCode = wasm._OrtGetTensorData(\n                    tensor, tensorDataOffset, tensorDataOffset + 4, tensorDataOffset + 8, tensorDataOffset + 12);\n                if (errorCode !== 0) {\n                  throw new Error(`Can't access output tensor data. error code = ${errorCode}`);\n                }\n                let tensorDataIndex = tensorDataOffset / 4;\n                const dataType = wasm.HEAPU32[tensorDataIndex++];\n                dataOffset = wasm.HEAPU32[tensorDataIndex++];\n                const dimsOffset = wasm.HEAPU32[tensorDataIndex++];\n                const dimsLength = wasm.HEAPU32[tensorDataIndex++];\n                const dims = [];\n                for (let i = 0; i < dimsLength; i++) {\n                  dims.push(wasm.HEAPU32[dimsOffset / 4 + i]);\n                }\n                wasm._OrtFree(dimsOffset);\n\n                const size = dims.length === 0 ? 1 : dims.reduce((a, b) => a * b);\n                type = tensorDataTypeEnumToString(dataType);\n                if (type === 'string') {\n                  const stringData: string[] = [];\n                  let dataIndex = dataOffset / 4;\n                  for (let i = 0; i < size; i++) {\n                    const offset = wasm.HEAPU32[dataIndex++];\n                    const maxBytesToRead = i === size - 1 ? undefined : wasm.HEAPU32[dataIndex] - offset;\n                    stringData.push(wasm.UTF8ToString(offset, maxBytesToRead));\n                  }\n                  output.push([type, dims, stringData]);\n                } else {\n                  const typedArrayConstructor = numericTensorTypeToTypedArray(type);\n                  const data = new typedArrayConstructor(size);\n                  new Uint8Array(data.buffer, data.byteOffset, data.byteLength)\n                      .set(wasm.HEAPU8.subarray(dataOffset, dataOffset + data.byteLength));\n                  output.push([type, dims, data]);\n                }\n              } finally {\n                wasm.stackRestore(beforeGetTensorDataStack);\n                if (type === 'string' && dataOffset) {\n                  wasm._free(dataOffset);\n                }\n                wasm._OrtReleaseTensor(tensor);\n              }\n            }\n          }\n\n          if (errorCode === 0) {\n            return output;\n          } else {\n            throw new Error(`failed to call OrtRun(). error code = ${errorCode}.`);\n          }\n        } finally {\n          wasm.stackRestore(beforeRunStack);\n        }\n      } finally {\n        inputValues.forEach(wasm._OrtReleaseTensor);\n        inputAllocs.forEach(wasm._free);\n\n        wasm._OrtReleaseRunOptions(runOptionsHandle);\n        runOptionsAllocs.forEach(wasm._free);\n      }\n    };\n\n/**\n * end profiling\n */\nexport const endProfiling = (sessionId: number): void => {\n  const wasm = getInstance();\n  const session = activeSessions.get(sessionId);\n  if (!session) {\n    throw new Error('invalid session id');\n  }\n  const sessionHandle = session[0];\n\n  // profile file name is not used yet, but it must be freed.\n  const profileFileName = wasm._OrtEndProfiling(sessionHandle);\n  if (profileFileName === 0) {\n    throw new Error('Can\\'t get an profile file name');\n  }\n  wasm._OrtFree(profileFileName);\n};\n\nexport const extractTransferableBuffers = (tensors: readonly SerializableTensor[]): ArrayBufferLike[] => {\n  const buffers: ArrayBufferLike[] = [];\n  for (const tensor of tensors) {\n    const data = tensor[2];\n    if (!Array.isArray(data) && data.buffer) {\n      buffers.push(data.buffer);\n    }\n  }\n  return buffers;\n};\n", "// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\nimport {Env} from 'onnxruntime-common';\nimport * as path from 'path';\n\nimport {OrtWasmModule} from './binding/ort-wasm';\nimport {OrtWasmThreadedModule} from './binding/ort-wasm-threaded';\nimport ortWasmFactory from './binding/ort-wasm.js';\n\nconst ortWasmFactoryThreaded: EmscriptenModuleFactory<OrtWasmModule> =\n    // eslint-disable-next-line @typescript-eslint/no-require-imports\n    !BUILD_DEFS.DISABLE_WASM_THREAD ? require('./binding/ort-wasm-threaded.js') : ortWasmFactory;\n\nlet wasm: OrtWasmModule|undefined;\nlet initialized = false;\nlet initializing = false;\nlet aborted = false;\n\nconst isMultiThreadSupported = (): boolean => {\n  try {\n    // If 'SharedArrayBuffer' is not available, WebAssembly threads will not work.\n    if (typeof SharedArrayBuffer === 'undefined') {\n      return false;\n    }\n\n    // Test for transferability of SABs (for browsers. needed for Firefox)\n    // https://groups.google.com/forum/#!msg/mozilla.dev.platform/IHkBZlHETpA/dwsMNchWEQAJ\n    if (typeof MessageChannel !== 'undefined') {\n      new MessageChannel().port1.postMessage(new SharedArrayBuffer(1));\n    }\n\n    // Test for WebAssembly threads capability (for both browsers and Node.js)\n    // This typed array is a WebAssembly program containing threaded instructions.\n    return WebAssembly.validate(new Uint8Array([\n      0, 97, 115, 109, 1, 0,  0,  0, 1, 4, 1,  96, 0,   0,  3, 2, 1,  0, 5,\n      4, 1,  3,   1,   1, 10, 11, 1, 9, 0, 65, 0,  254, 16, 2, 0, 26, 11\n    ]));\n  } catch (e) {\n    return false;\n  }\n};\n\nconst isSimdSupported = (): boolean => {\n  try {\n    // Test for WebAssembly SIMD capability (for both browsers and Node.js)\n    // This typed array is a WebAssembly program containing SIMD instructions.\n\n    // The binary data is generated from the following code by wat2wasm:\n    //\n    // (module\n    //   (type $t0 (func))\n    //   (func $f0 (type $t0)\n    //     (drop\n    //       (i32x4.dot_i16x8_s\n    //         (i8x16.splat\n    //           (i32.const 0))\n    //         (v128.const i32x4 0x00000000 0x00000000 0x00000000 0x00000000)))))\n\n    return WebAssembly.validate(new Uint8Array([\n      0,   97, 115, 109, 1, 0, 0, 0, 1, 4, 1, 96, 0, 0, 3, 2, 1, 0, 10, 30, 1,   28,  0, 65, 0,\n      253, 15, 253, 12,  0, 0, 0, 0, 0, 0, 0, 0,  0, 0, 0, 0, 0, 0, 0,  0,  253, 186, 1, 26, 11\n    ]));\n  } catch (e) {\n    return false;\n  }\n};\n\nconst getWasmFileName = (useSimd: boolean, useThreads: boolean) => {\n  if (useThreads) {\n    return useSimd ? 'ort-wasm-simd-threaded.wasm' : 'ort-wasm-threaded.wasm';\n  } else {\n    return useSimd ? 'ort-wasm-simd.wasm' : 'ort-wasm.wasm';\n  }\n};\n\nexport const initializeWebAssembly = async(flags: Env.WebAssemblyFlags): Promise<void> => {\n  if (initialized) {\n    return Promise.resolve();\n  }\n  if (initializing) {\n    throw new Error('multiple calls to \\'initializeWebAssembly()\\' detected.');\n  }\n  if (aborted) {\n    throw new Error('previous call to \\'initializeWebAssembly()\\' failed.');\n  }\n\n  initializing = true;\n\n  // wasm flags are already initialized\n  const timeout = flags.initTimeout!;\n  const numThreads = flags.numThreads!;\n  const simd = flags.simd!;\n\n  const useThreads = numThreads > 1 && isMultiThreadSupported();\n  const useSimd = simd && isSimdSupported();\n\n  const wasmPrefixOverride = typeof flags.wasmPaths === 'string' ? flags.wasmPaths : undefined;\n  const wasmFileName = getWasmFileName(false, useThreads);\n  const wasmOverrideFileName = getWasmFileName(useSimd, useThreads);\n  const wasmPathOverride = typeof flags.wasmPaths === 'object' ? flags.wasmPaths[wasmOverrideFileName] : undefined;\n\n  let isTimeout = false;\n\n  const tasks: Array<Promise<void>> = [];\n\n  // promise for timeout\n  if (timeout > 0) {\n    tasks.push(new Promise((resolve) => {\n      setTimeout(() => {\n        isTimeout = true;\n        resolve();\n      }, timeout);\n    }));\n  }\n\n  // promise for module initialization\n  tasks.push(new Promise((resolve, reject) => {\n    const factory = useThreads ? ortWasmFactoryThreaded : ortWasmFactory;\n    const config: Partial<OrtWasmModule> = {\n      locateFile: (fileName: string, scriptDirectory: string) => {\n        if (!BUILD_DEFS.DISABLE_WASM_THREAD && useThreads && fileName.endsWith('.worker.js') &&\n            typeof Blob !== 'undefined') {\n          return URL.createObjectURL(new Blob(\n              [\n                // This require() function is handled by webpack to load file content of the corresponding .worker.js\n                // eslint-disable-next-line @typescript-eslint/no-require-imports\n                require('./binding/ort-wasm-threaded.worker.js')\n              ],\n              {type: 'text/javascript'}));\n        }\n\n        if (fileName === wasmFileName) {\n          const prefix: string = wasmPrefixOverride ?? scriptDirectory;\n          return wasmPathOverride ?? prefix + wasmOverrideFileName;\n        }\n\n        return scriptDirectory + fileName;\n      }\n    };\n\n    if (!BUILD_DEFS.DISABLE_WASM_THREAD && useThreads) {\n      if (typeof Blob === 'undefined') {\n        config.mainScriptUrlOrBlob = path.join(__dirname, 'ort-wasm-threaded.js');\n      } else {\n        const scriptSourceCode = `var ortWasmThreaded=(function(){var _scriptDir;return ${factory.toString()}})();`;\n        config.mainScriptUrlOrBlob = new Blob([scriptSourceCode], {type: 'text/javascript'});\n      }\n    }\n\n    factory(config).then(\n        // wasm module initialized successfully\n        module => {\n          initializing = false;\n          initialized = true;\n          wasm = module;\n          resolve();\n        },\n        // wasm module failed to initialize\n        (what) => {\n          initializing = false;\n          aborted = true;\n          reject(what);\n        });\n  }));\n\n  await Promise.race(tasks);\n\n  if (isTimeout) {\n    throw new Error(`WebAssembly backend initializing failed due to timeout: ${timeout}ms`);\n  }\n};\n\nexport const getInstance = (): OrtWasmModule => {\n  if (initialized && wasm) {\n    return wasm;\n  }\n\n  throw new Error('WebAssembly is not initialized yet.');\n};\n\nexport const dispose = (): void => {\n  if (initialized && !initializing && !aborted) {\n    initializing = true;\n\n    (wasm as OrtWasmThreadedModule).PThread?.terminateAllThreads();\n    wasm = undefined;\n\n    initializing = false;\n    initialized = false;\n    aborted = true;\n  }\n};\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// startup\n// Load entry module and return exports\n// This entry module is referenced by other modules so it can't be inlined\nvar __webpack_exports__ = __webpack_require__(18);\n"], "names": ["root", "factory", "exports", "module", "define", "amd", "self", "backends", "backendsSortedByPriority", "registerBackend", "name", "backend", "priority", "init", "createSessionHandler", "TypeError", "currentBackend", "undefined", "Error", "i", "indexOf", "splice", "length", "push", "env", "constructor", "this", "wasm", "webgl", "logLevelInternal", "logLevel", "value", "isBigInt64ArrayAvailable", "BigInt64Array", "from", "isBigUint64ArrayAvailable", "BigUint64Array", "NUMERIC_TENSOR_TYPE_TO_TYPEDARRAY_MAP", "Map", "Float32Array", "Uint8Array", "Int8Array", "Uint16Array", "Int16Array", "Int32Array", "Float64Array", "Uint32Array", "NUMERIC_TENSOR_TYPEDARRAY_TO_TYPE_MAP", "set", "Tensor", "arg0", "arg1", "arg2", "type", "data", "dims", "Array", "isArray", "typedArrayConstructor", "get", "firstElementType", "mappedType", "size", "dim", "Number", "isSafeInteger", "RangeError", "calculateSize", "static", "buffer", "options", "height", "width", "norm", "norm<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "mean", "bias", "inputformat", "bitmapFormat", "outputformat", "tensorFormat", "offset", "float32Data", "step", "rImagePointer", "gImagePointer", "bImagePointer", "aImagePointer", "rTensorPointer", "gTensorPointer", "b<PERSON>ensor<PERSON>oint<PERSON>", "aTensorPointer", "image", "isHTMLImageEle", "HTMLImageElement", "isImageDataEle", "ImageData", "isImageBitmap", "ImageBitmap", "isURL", "String", "tensorConfig", "canvas", "document", "createElement", "pixels2DContext", "getContext", "naturalHeight", "naturalWidth", "resizedHeight", "resizedWidth", "drawImage", "getImageData", "bufferToTensor", "Promise", "resolve", "reject", "context", "newImage", "Image", "crossOrigin", "src", "onload", "img", "format", "tempCanvas", "putImageData", "toImageData", "_a", "_b", "channels", "createImageData", "reshape", "InferenceSession", "handler", "async", "feeds", "fetches", "isFetchesEmpty", "outputNames", "isFetches", "arg1Keys", "Object", "getOwnPropertyNames", "v", "inputNames", "results", "run", "returnValue", "key", "hasOwnProperty", "call", "arg3", "filePathOrUint8Array", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SharedArrayBuffer", "byteOffset", "byteLength", "backendHints", "executionProviders", "map", "backendNames", "errors", "backendName", "backendInfo", "initialized", "aborted", "isInitializing", "initPromise", "e", "err", "join", "resolveBackend", "startProfiling", "endProfiling", "_scriptDir", "ortWasm", "currentScript", "d", "aa", "ba", "ready", "a", "b", "ha", "r", "fs", "y", "ia", "ca", "assign", "da", "ea", "fa", "window", "m", "importScripts", "p", "process", "versions", "node", "q", "__dirname", "normalize", "readFileSync", "c", "readFile", "f", "argv", "replace", "slice", "on", "ja", "noExitRuntime", "ka", "exitCode", "z", "exit", "inspect", "location", "href", "substr", "lastIndexOf", "XMLHttpRequest", "open", "send", "responseText", "responseType", "response", "status", "onerror", "A", "la", "print", "console", "log", "bind", "printErr", "warn", "thisProgram", "quit", "wasmBinary", "WebAssembly", "B", "ma", "sa", "H", "G", "I", "J", "D", "na", "TextDecoder", "oa", "decode", "subarray", "h", "fromCharCode", "k", "pa", "qa", "charCodeAt", "ra", "ta", "HEAP8", "HEAP16", "HEAP32", "HEAPU8", "HEAPU16", "HEAPU32", "HEAPF32", "HEAPF64", "ua", "va", "wa", "xa", "ya", "za", "preRun", "shift", "unshift", "N", "K", "Aa", "L", "onAbort", "RuntimeError", "Ba", "startsWith", "Ca", "locateFile", "Da", "message", "O", "P", "Q", "R", "S", "Db", "zb", "Ub", "Eb", "Sb", "Wb", "Tb", "Ib", "Pb", "Jb", "Lb", "Rb", "Fb", "Nb", "Xb", "Ob", "Qb", "Fa", "Ga", "Ha", "T", "U", "Ia", "<PERSON>a", "Ma", "Na", "Oa", "USER", "LOGNAME", "PATH", "PWD", "HOME", "LANG", "navigator", "languages", "_", "Pa", "Qa", "V", "Sa", "Ta", "Ua", "Wa", "g", "u", "w", "toString", "M", "F", "getFullYear", "getMonth", "getDate", "l", "getDay", "Date", "n", "Bb", "Cb", "getTime", "setDate", "setMonth", "setFullYear", "t", "x", "$b", "Zb", "Gb", "Kb", "Hb", "Ab", "bc", "Yb", "ac", "RegExp", "E", "split", "C", "substring", "Math", "floor", "abs", "includes", "Va", "X", "pop", "prototype", "arguments", "Xa", "s", "now", "getUTCSeconds", "getUTCMinutes", "getUTCHours", "getUTCDate", "getUTCMonth", "getUTCFullYear", "getUTCDay", "UTC", "Ea", "getSeconds", "getMinutes", "getHours", "getTimezoneOffset", "min", "max", "setTime", "La", "Vb", "toTimeString", "match", "<PERSON>", "hrtime", "performance", "copyWithin", "grow", "for<PERSON>ach", "Ya", "<PERSON>a", "onExit", "W", "Mb", "crypto", "getRandomValues", "randomBytes", "Ra", "Y", "Z", "j", "o", "ga", "$", "asm", "ib", "monitorRunDependencies", "clearInterval", "instance", "fetch", "credentials", "then", "ok", "arrayBuffer", "catch", "instantiate", "instantiateWasm", "instantiateStreaming", "___wasm_call_ctors", "apply", "_OrtInit", "_OrtCreateSessionOptions", "_OrtAppendExecutionProvider", "_OrtAddSessionConfigEntry", "_OrtReleaseSessionOptions", "_OrtCreateSession", "_OrtReleaseSession", "_OrtGetInputCount", "_OrtGetOutputCount", "_OrtGetInputName", "_OrtGetOutputName", "_OrtFree", "_OrtCreateTensor", "_OrtGetTensorData", "_OrtReleaseTensor", "_OrtCreateRunOptions", "$a", "_OrtAddRunConfigEntry", "ab", "_OrtReleaseRunOptions", "bb", "_OrtRun", "cb", "_OrtEndProfiling", "db", "_malloc", "eb", "_free", "fb", "_fflush", "gb", "___funcs_on_exit", "hb", "_setThrew", "jb", "stackSave", "kb", "stackRestore", "lb", "stackAlloc", "mb", "___cxa_can_catch", "nb", "___cxa_is_pointer_type", "ob", "dynCall_j", "pb", "dynCall_iiiiij", "qb", "dynCall_jii", "rb", "dynCall_viiiiij", "sb", "dynCall_vjji", "tb", "dynCall_viiijjjii", "ub", "dynCall_iij", "vb", "dyn<PERSON>all_ji", "wb", "dynCall_iiiiiij", "xb", "dynCall_iiij", "yb", "calledRun", "onRuntimeInitialized", "postRun", "setStatus", "setTimeout", "UTF8ToString", "stringToUTF8", "lengthBytesUTF8", "preInit", "initTimeout", "simd", "proxy", "numThreads", "isInteger", "numCpuLogicalCores", "cpus", "hardwareConcurrency", "ceil", "initWasm", "pathOr<PERSON><PERSON>er", "OnnxruntimeWebAssemblySessionHandler", "loadModel", "wasmBackend", "iterateExtraOptions", "prefix", "seen", "has", "add", "entries", "initializeWebAssembly", "initOrt", "loggingLevel", "core", "createSessionAllocate", "model", "createSessionFinalize", "modeldata", "createSession", "releaseSession", "sessionId", "inputIndices", "inputs", "outputIndices", "setRunOptions", "getInstance", "runOptionsHandle", "allocs", "runOptions", "logSeverityLevel", "logVerbosityLevel", "terminate", "tagDataOffset", "tag", "allocWasmString", "extra", "WeakSet", "keyDataOffset", "valueDataOffset", "ortInit", "path", "getLogLevel", "promisify", "modelData", "inputArray", "kvp", "tensor", "index", "outputs", "result", "setSessionOptions", "sessionOptionsHandle", "sessionOptions", "session", "use_ort_model_bytes_directly", "appendDefaultOptions", "graphOptimizationLevel", "getGraphOptimzationLevel", "enableCpuMemArena", "enableMemPattern", "executionMode", "getExecutionMode", "logIdDataOffset", "logId", "enableProfiling", "ep", "epName", "epNameDataOffset", "setExecutionProviders", "dataLength", "dataOffset", "errorCode", "activeSessions", "modelDataOffset", "<PERSON><PERSON><PERSON><PERSON>", "inputCount", "outputCount", "inputNamesUTF8Encoded", "outputNamesUTF8Encoded", "delete", "tensorDataTypeStringToEnum", "tensorDataTypeEnumToString", "typeProto", "numericTensorTypeToTypedArray", "runOptionsAllocs", "inputValues", "inputAllocs", "dataType", "dataByteLength", "dataIndex", "stack", "dimsOffset", "dimIndex", "beforeRunStack", "inputValuesOffset", "inputNamesOffset", "outputValuesOffset", "outputNamesOffset", "inputValuesIndex", "inputNamesIndex", "outputValuesIndex", "outputNamesIndex", "output", "beforeGetTensorDataStack", "tensorDataOffset", "tensorDataIndex", "dimsLength", "reduce", "stringData", "maxBytesToRead", "profileFileName", "extractTransferableBuffers", "tensors", "buffers", "ortWasmFactoryThreaded", "initializing", "getWasmFileName", "useSimd", "useThreads", "flags", "timeout", "MessageChannel", "port1", "postMessage", "validate", "isMultiThreadSupported", "isSimdSupported", "wasmPrefixOverride", "<PERSON>m<PERSON><PERSON><PERSON>", "wasmFileName", "wasmOverrideFileName", "wasmPathOverride", "isTimeout", "tasks", "fileName", "scriptDirectory", "what", "race", "dispose", "PThread", "terminateAllThreads", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "definition", "defineProperty", "enumerable", "obj", "prop", "Symbol", "toStringTag"], "sourceRoot": ""}