{"version": 3, "file": "opset.js", "sourceRoot": "", "sources": ["opset.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAyBlC,SAAgB,eAAe,CAAC,IAAgB,EAAE,MAAwB,EAAE,KAAmC;IAC7G,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;QACxB,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACvB,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACvB,MAAM,eAAe,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QAChC,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACvB,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QAEvB,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,EAAE,EAAG,wBAAwB;YACrD,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;gBAC1B,kDAAkD;gBAClD,IAAI,KAAK,CAAC,MAAM,KAAK,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,SAAS,IAAI,MAAM,KAAK,EAAE,CAAC,EAAE,EAAG,qBAAqB;oBACpG,IAAI,aAAa,CAAC,KAAK,CAAC,OAAO,EAAE,eAAe,CAAC,EAAE;wBACjD,OAAO,EAAC,MAAM,EAAE,MAAM,EAAC,CAAC;qBACzB;iBACF;aACF;SACF;KACF;IAED,MAAM,IAAI,SAAS,CAAC,4BAA4B,IAAI,CAAC,MAAM,kBACvD,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,IAAI,SAAS,KAAK,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACpF,CAAC;AAtBD,0CAsBC;AAED,SAAS,aAAa,CAAC,OAAe,EAAE,QAAgB;IACtD,IAAI,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;QAC1B,kDAAkD;QAClD,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACnF,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,UAAU,IAAI,OAAO,CAAC;KACpD;SAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;QAC3C,4CAA4C;QAC5C,MAAM,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACjC,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAChD,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC9C,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,UAAU,IAAI,OAAO,IAAI,OAAO,IAAI,QAAQ,CAAC;KAC/F;SAAM;QACL,wCAAwC;QACxC,OAAO,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,CAAC,KAAK,OAAO,CAAC;KAClD;AACH,CAAC"}