import { Tensor } from '../../../tensor';
import { WebGLInferenceHandler } from '../inference-handler';
import { ProgramInfoLoader } from '../types';
import { ConvAttributes } from './conv';
export declare const createIm2ColProgramInfoLoader: (inferenceHandler: WebGLInferenceHandler, x: Tensor, w: Tensor, outputShape: readonly number[], attributes: ConvAttributes) => ProgramInfoLoader;
export declare const calculateIm2ColDims: (inputShape: readonly number[], kernelShape: readonly number[], outputShape: readonly number[], channels?: number) => number[];
