{"version": 3, "file": "gather.js", "sourceRoot": "", "sources": ["gather.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAElC,gFAAqG;AAErG,kDAAgG;AAEhG,wCAAwC;AAExC,oCAAsF;AAM/E,MAAM,MAAM,GACf,CAAC,gBAAuC,EAAE,MAAgB,EAAE,UAA4B,EAAY,EAAE;IACpG,cAAc,CAAC,MAAM,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC;IACxC,MAAM,MAAM,GAAG,gBAAgB,CAAC,GAAG,CAAC,6BAA6B,CAAC,gBAAgB,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM,CAAC,CAAC;IACjH,OAAO,CAAC,MAAM,CAAC,CAAC;AAClB,CAAC,CAAC;AALO,QAAA,MAAM,UAKb;AAEC,MAAM,qBAAqB,GAA6C,CAAC,IAAgB,EAAoB,EAAE,CAClH,IAAA,sDAA2B,EAAC,EAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,EAAC,CAAC,CAAC;AAD9D,QAAA,qBAAqB,yBACyC;AAE3E,MAAM,qBAAqB,GAAG;IAC5B,IAAI,EAAE,QAAQ;IACd,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;IACtB,UAAU,EAAE,CAAC,mBAAW,CAAC,QAAQ,EAAE,mBAAW,CAAC,QAAQ,CAAC;CACzD,CAAC;AAEF,MAAM,uBAAuB,GACzB,CAAC,OAA8B,EAAE,QAAyB,EAAE,MAAgB,EAAE,IAAY,EAAe,EAAE;IACzG,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;IAC1C,MAAM,cAAc,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;IAC9C,MAAM,WAAW,GAAG,IAAI,KAAK,CAAC,UAAU,CAAC,MAAM,GAAG,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAE7E,IAAI,GAAG,gBAAS,CAAC,aAAa,CAAC,IAAI,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC;IACxD,MAAM,YAAY,GAAa,EAAE,CAAC;IAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC3C,mDAAmD;QACnD,gEAAgE;QAChE,gEAAgE;QAChE,EAAE;QACF,iCAAiC;QACjC,IAAI,CAAC,GAAG,IAAI,EAAE,EAAG,IAAI;YACnB,WAAW,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YAC/B,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;SACxD;aAAM;YACL,IAAI,CAAC,GAAG,IAAI,GAAG,cAAc,CAAC,MAAM,EAAE,EAAG,IAAI;gBAC3C,WAAW,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;gBAC1C,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,IAAI,iBAAiB,CAAC,IAAI,CAAC,CAAC;aACnE;iBAAM,EAAwD,IAAI;gBACjE,WAAW,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAE,kBAAkB;gBAC/E,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,cAAc,CAAC,MAAM,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;aACpF;SACF;KACF;IAED,MAAM,KAAK,GAAG,WAAW,CAAC,MAAM,IAAI,CAAC,CAAC;IACtC,MAAM,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC;IAChC,MAAM,MAAM,GAAG,cAAc,CAAC,MAAM,IAAI,CAAC,CAAC;IAC1C,MAAM,YAAY,GAAG;oCACS,KAAK;uBAClB,KAAK;2BACD,MAAM;;UAEvB,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC;;mBAEtB,IAAI,uBAAuB,UAAU,CAAC,IAAI,CAAC;;QAEtD,CAAC;IACH,uCACK,QAAQ,KACX,MAAM,EAAE,EAAC,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,WAAW,EAAE,mBAAW,CAAC,QAAQ,EAAC,EACpF,YAAY,IACZ;AACJ,CAAC,CAAC;AAEN,MAAM,6BAA6B,GAC/B,CAAC,OAA8B,EAAE,MAAgB,EAAE,UAA4B,EAAqB,EAAE;IACpG,MAAM,QAAQ,mCAAO,qBAAqB,KAAE,SAAS,EAAE,UAAU,CAAC,QAAQ,GAAC,CAAC;IAC5E,uCAAW,QAAQ,KAAE,GAAG,EAAE,GAAG,EAAE,CAAC,uBAAuB,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC,IAAI,CAAC,IAAE;AACvG,CAAC,CAAC;AAEN,MAAM,cAAc,GAAG,CAAC,MAAgB,EAAE,IAAY,EAAQ,EAAE;IAC9D,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QAClC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;KAC9C;IACD,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;IACzC,IAAI,UAAU,GAAG,CAAC,EAAE;QAClB,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;KACzC;IACD,IAAI,IAAI,GAAG,CAAC,UAAU,IAAI,IAAI,GAAG,UAAU,GAAG,CAAC,EAAE;QAC/C,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;KAClC;IACD,IAAI,wBAAY,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;QAC/C,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;KACvC;IACD,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,EAAE;QAC5D,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;KACvC;AACH,CAAC,CAAC"}