{"version": 3, "file": "conv-transpose.js", "sourceRoot": "", "sources": ["conv-transpose.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAElC,gFAA8E;AAK9E,gDAAuC;AAEvC,oCAAsF;AAGtF,6CAAqF;AAErF,MAAM,eAAe,GACjB,CAAC,KAAa,EAAE,MAAc,EAAE,GAAW,EAAE,MAAc,EAAE,QAAgB,EAAE,OAAe,EAAE,EAAE,CAC9F,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,QAAQ,GAAG,CAAC,GAAG,OAAO,CAAC;AAE3E,MAAM,iBAAiB,GAAG,CAAC,QAAgB,EAAE,OAAe,EAAE,IAAc,EAAE,IAAY,EAAE,IAAY,EAAE,EAAE;IAC1G,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;IAC1C,IAAI,OAAO,KAAK,YAAY,EAAE;QAC5B,IAAI,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC;QACtB,IAAI,CAAC,IAAI,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC;KAClC;SAAM,IAAI,OAAO,KAAK,YAAY,EAAE;QACnC,IAAI,CAAC,IAAI,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC;QACjC,IAAI,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC;KACvB;AACH,CAAC,CAAC;AAEF,MAAM,2BAA2B,GAC7B,CAAC,UAA6B,EAAE,WAA8B,EAAE,SAA4B,EAAE,OAAe,EAC5G,IAAc,EAAE,OAA0B,EAAE,aAAgC,EAAE,WAAqB,EAAE,EAAE;IACtG,MAAM,WAAW,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;IAC1C,MAAM,WAAW,GAAG,WAAW,CAAC,MAAM,KAAK,CAAC,CAAC;IAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,EAAE,CAAC,EAAE;QACpC,MAAM,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAC9E,MAAM,QAAQ,GAAG,eAAe,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;QAChH,iBAAiB,CAAC,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,CAAC;QAC/D,IAAI,WAAW,EAAE;YACf,WAAW,CAAC,IAAI,CACZ,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;gBACjG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC;SACtC;KACF;AACH,CAAC,CAAC;AAOC,MAAM,aAAa,GACtB,CAAC,gBAAkC,EAAE,MAAgB,EAAE,UAAmC,EAAY,EAAE;IACtG,cAAc,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,CAAE,6CAA6C;IAClF,OAAO,eAAe,CAAC,gBAAgB,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;AAC/D,CAAC,CAAC;AAJO,QAAA,aAAa,iBAIpB;AAEN,MAAM,eAAe,GACjB,CAAC,gBAAuC,EAAE,MAAgB,EAAE,UAAmC,EAAY,EAAE;IAC3G,MAAM,kBAAkB,GAAG,kCAAkC,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;IAClF,OAAO,CAAC,uBAAuB,CAAC,gBAAgB,EAAE,MAAM,EAAE,kBAAkB,CAAC,CAAC,CAAC;AACjF,CAAC,CAAC;AAEN,MAAM,kCAAkC,GAAG,CAAC,OAAgB,EAAE,SAAiB,EAAE,EAAE,CAAC,CAAC;IACnF,IAAI,EAAE,eAAe;IACrB,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;IAClD,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,mBAAW,CAAC,QAAQ,EAAE,mBAAW,CAAC,QAAQ,EAAE,mBAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;QACpE,CAAC,mBAAW,CAAC,QAAQ,EAAE,mBAAW,CAAC,QAAQ,CAAC;IAClE,SAAS;CACV,CAAC,CAAC;AAEH,MAAM,sCAAsC,GACxC,CAAC,gBAAuC,EAAE,MAAyB,EAAE,QAAyB,EAC7F,UAAmC,EAAe,EAAE;IACnD,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IAClC,MAAM,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,KAAK,CAAC;IAC3D,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC9B,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC9B,MAAM,sBAAsB,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACzC,MAAM,qBAAqB,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC;IAC3D,MAAM,WAAW,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,KAAK,EAAE,GAAG,UAAU,CAAC,WAAW,CAAC,CAAC;IACzG,MAAM,IAAI,GAAG,IAAA,qBAAO,EAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;IACzE,MAAM,EAAC,kBAAkB,EAAE,eAAe,EAAC,GAAG,IAAA,iCAAoB,EAAC,UAAU,CAAC,CAAC;IAE/E,MAAM,YAAY,GAAG;gCACK,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC;6BAClD,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IAClE,kBAAkB;;;;;;;;sCAQgB,sBAAsB;oDACR,sBAAsB;;oBAEtD,SAAS;sDACyB,qBAAqB;uCACpC,qBAAqB;oCACxB,MAAM,CAAC,CAAC,CAAC;sCACP,MAAM,CAAC,CAAC,CAAC;uCACR,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,aAAa,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC;;;;;0CAKxD,MAAM,CAAC,CAAC,CAAC;0CACT,MAAM,CAAC,CAAC,CAAC;;;;;;;;;MAS7C,eAAe;MACf,IAAI,CAAC,MAAM;;CAEhB,CAAC;IACI,uCACK,QAAQ,KACX,MAAM,EAAE,EAAC,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,WAAW,EAAE,mBAAW,CAAC,QAAQ,EAAC,EACpF,YAAY,EACZ,OAAO,EAAE,IAAI,IACb;AACJ,CAAC,CAAC;AAEN,MAAM,4CAA4C,GAC9C,CAAC,gBAAuC,EAAE,MAAyB,EAAE,UAAmC,EAClF,EAAE;IAClB,MAAM,QAAQ,GAAG,kCAAkC,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAC;IAC5F,uCACK,QAAQ,KACX,GAAG,EAAE,GAAG,EAAE,CAAC,sCAAsC,CAAC,gBAAgB,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,CAAC,IACjG;AACJ,CAAC,CAAC;AAGV,MAAM,uBAAuB,GACzB,CAAC,gBAAuC,EAAE,MAAyB,EAAE,UAAmC,EAC7F,EAAE;IACP,MAAM,MAAM,GAAG,gBAAgB,CAAC,GAAG,CAC/B,4CAA4C,CAAC,gBAAgB,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM,CAAC,CAAC;IAChG,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAEV,MAAM,kCAAkC,GAAG,CAAoC,UAAa,EAAE,MAAgB,EAAK,EAAE;IACnH,MAAM,WAAW,GAAG,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;IACnD,qGAAqG;IACrG,IAAI,UAAU,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;QACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YAC9C,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;SACrC;KACF;IAED,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;IACrC,MAAM,WAAW,GAAG,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;IACnD,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAClC,6FAA6F;IAC7F,uDAAuD;IACvD,2BAA2B,CACvB,UAAU,EAAE,WAAW,EAAE,UAAU,CAAC,SAAS,EAAE,UAAU,CAAC,OAAO,EAAE,IAAI,EAAE,UAAU,CAAC,OAAO,EAC3F,UAAU,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;IAE3C,wEAAwE;IACxE,MAAM,aAAa,GAAM,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;IACvD,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,EAAC,WAAW,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU,CAAC,QAAQ,EAAC,CAAC,CAAC;IAC9F,OAAO,aAAa,CAAC;AACvB,CAAC,CAAC;AAEK,MAAM,4BAA4B,GACrC,CAAC,IAAgB,EAA2B,EAAE;IAC5C,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;IACnC,MAAM,oBAAoB,GAAG,IAAA,8CAAiC,EAAC,UAAU,CAAC,CAAC;IAC3E,2FAA2F;IAC3F,MAAM,OAAO,GAAG,UAAU,CAAC,SAAS,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IAC3D,MAAM,SAAS,GAAG,UAAU,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC1D,MAAM,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;IAC5C,MAAM,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;IAC3D,MAAM,aAAa,GAAG,UAAU,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnE,MAAM,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;IAC3D,MAAM,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACtD,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAEtD,OAAO,IAAA,sDAA2B,kBAC7B,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,EAAE,aAAa,EAAE,WAAW,EAAE,IAAI,EAAE,OAAO,IAAK,oBAAoB,EAAE,CAAC;AACpH,CAAC,CAAC;AAhBO,QAAA,4BAA4B,gCAgBnC;AAEN,MAAM,cAAc,GAAG,CAAC,MAAgB,EAAE,UAAmC,EAAQ,EAAE;IACrF,+CAA+C;IAC/C,gEAAgE;IAChE,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE;QAC3D,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;KAChD;IAED,wDAAwD;IACxD,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QAC9D,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;KAC9D;IAED,oDAAoD;IACpD,MAAM,WAAW,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACtC,MAAM,eAAe,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC1C,IAAI,WAAW,KAAK,eAAe,EAAE;QACnC,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;KACtE;IAED,MAAM,WAAW,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC;IAEzD,+GAA+G;IAC/G,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,WAAW,CAAC,EAAE;QAC7F,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;KACjC;IAED,MAAM,WAAW,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;IAC9C,4BAA4B;IAC5B,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,KAAK,WAAW,EAAE;QAC/C,MAAM,IAAI,KAAK,CAAC,uBAAuB,WAAW,GAAG,CAAC,CAAC;KACxD;IAED,0BAA0B;IAC1B,IAAI,UAAU,CAAC,OAAO,CAAC,MAAM,KAAK,WAAW,EAAE;QAC7C,MAAM,IAAI,KAAK,CAAC,qBAAqB,WAAW,GAAG,CAAC,CAAC;KACtD;IAED,uBAAuB;IACvB,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,KAAK,WAAW,GAAG,CAAC,EAAE;QAC9C,MAAM,IAAI,KAAK,CAAC,kBAAkB,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;KACvD;IAED,iCAAiC;IACjC,IAAI,UAAU,CAAC,aAAa,CAAC,MAAM,KAAK,WAAW,EAAE;QACnD,MAAM,IAAI,KAAK,CAAC,4BAA4B,WAAW,GAAG,CAAC,CAAC;KAC7D;IAED,sGAAsG;IACtG,iDAAiD;IACjD,IAAI,UAAU,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;QACtG,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;KACzC;IAED,sEAAsE;IACtE,IAAI,UAAU,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;QACtG,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;KACzC;IAED,yCAAyC;IACzC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,EAAE;QAChE,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;KACpE;IAED,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,EAAE;QACvD,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;KACrE;AACH,CAAC,CAAC"}