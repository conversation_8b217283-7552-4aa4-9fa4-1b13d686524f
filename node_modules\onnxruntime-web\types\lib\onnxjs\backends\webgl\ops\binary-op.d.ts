import { Tensor } from '../../../tensor';
import { GlslValueFunction } from '../glsl-definitions';
import { WebGLInferenceHandler } from '../inference-handler';
export declare function glslAdd(): GlslValueFunction;
export declare function glslDiv(): GlslValueFunction;
export declare function glslMul(): GlslValueFunction;
export declare function glslSub(): GlslValueFunction;
export declare function glslEqual(): GlslValueFunction;
export declare function glslGreater(): GlslValueFunction;
export declare function glslLess(): GlslValueFunction;
export declare function glslAnd(): GlslValueFunction;
export declare function glslOr(): GlslValueFunction;
export declare function glslXor(): GlslValueFunction;
export declare function glslPow(): GlslValueFunction;
export declare function glslPRelu(): GlslValueFunction;
export declare const add: (handler: WebGLInferenceHandler, inputs: Tensor[]) => Tensor[];
export declare const and: (handler: WebGLInferenceHandler, inputs: Tensor[]) => Tensor[];
export declare const div: (handler: WebGLInferenceHandler, inputs: Tensor[]) => Tensor[];
export declare const equal: (handler: WebGLInferenceHandler, inputs: Tensor[]) => Tensor[];
export declare const greater: (handler: WebGLInferenceHandler, inputs: Tensor[]) => Tensor[];
export declare const less: (handler: WebGLInferenceHandler, inputs: Tensor[]) => Tensor[];
export declare const mul: (handler: WebGLInferenceHandler, inputs: Tensor[]) => Tensor[];
export declare const or: (handler: WebGLInferenceHandler, inputs: Tensor[]) => Tensor[];
export declare const pow: (handler: WebGLInferenceHandler, inputs: Tensor[]) => Tensor[];
export declare const pRelu: (handler: WebGLInferenceHandler, inputs: Tensor[]) => Tensor[];
export declare const sub: (handler: WebGLInferenceHandler, inputs: Tensor[]) => Tensor[];
export declare const xor: (handler: WebGLInferenceHandler, inputs: Tensor[]) => Tensor[];
