{"version": 3, "file": "glsl-definitions.js", "sourceRoot": "", "sources": ["glsl-definitions.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAKlC,yDAAyD;AACzD,IAAY,YAGX;AAHD,WAAY,YAAY;IACtB,2DAAU,CAAA;IACV,2DAAU,CAAA;AACZ,CAAC,EAHW,YAAY,GAAZ,oBAAY,KAAZ,oBAAY,QAGvB;AAYD,MAAa,WAAW;IACtB,YACW,SAAuB,EAAS,WAAwB,EAAS,mBAAoC,EACrG,mBAAkC;QADlC,cAAS,GAAT,SAAS,CAAc;QAAS,gBAAW,GAAX,WAAW,CAAa;QAAS,wBAAmB,GAAnB,mBAAmB,CAAiB;QACrG,wBAAmB,GAAnB,mBAAmB,CAAe;IAAG,CAAC;CAClD;AAJD,kCAIC;AACD,MAAsB,OAAO;IAC3B,YAAmB,OAAoB;QAApB,YAAO,GAAP,OAAO,CAAa;IAAG,CAAC;CAG5C;AAJD,0BAIC;AAED,wEAAwE;AACxE,MAAa,cAAc;IACzB,YAAmB,WAAmB,EAAS,YAAuB;QAAnD,gBAAW,GAAX,WAAW,CAAQ;QAAS,iBAAY,GAAZ,YAAY,CAAW;IAAG,CAAC;CAC3E;AAFD,wCAEC;AAED,uFAAuF;AACvF,mGAAmG;AACnG,MAAa,kBAAkB;IAG7B,YAAmB,IAAY,EAAE,WAAoB,EAAE,YAAmC;QAAvE,SAAI,GAAJ,IAAI,CAAQ;QAC7B,IAAI,YAAY,EAAE;YAChB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;SAClC;aAAM;YACL,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;SACxB;QAED,IAAI,WAAW,EAAE;YACf,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;SAChC;IACH,CAAC;IACD,aAAa,CAAC,IAAwB;QACpC,IAAI,IAAI,EAAE;YACR,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC9B;IACH,CAAC;CACF;AAnBD,gDAmBC;AAED,oGAAoG;AACpG,MAAa,2BAA2B;IACtC,MAAM,CAAC,kBAAkB,CAAC,KAA2B;QACnD,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YAChC,OAAO,EAAE,CAAC;SACX;QAED,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,OAAO,KAAK,CAAC;SACd;QAED,MAAM,UAAU,GAAG,IAAI,GAAG,EAAU,CAAC;QACrC,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAU,CAAC;QAC3C,MAAM,MAAM,GAAG,IAAI,KAAK,EAAsB,CAAC;QAE/C,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,UAAU,EAAE,gBAAgB,EAAE,MAAM,CAAC,CAAC;QACrE,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,MAAM,CAAC,kBAAkB,CAC7B,UAAgC,EAAE,UAAuB,EAAE,gBAA6B,EACxF,MAA4B;QAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YAC1C,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,gBAAgB,EAAE,MAAM,CAAC,CAAC;SACvE;IACH,CAAC;IAEO,MAAM,CAAC,WAAW,CACtB,IAAwB,EAAE,UAAuB,EAAE,gBAA6B,EAAE,MAA4B;QAChH,iDAAiD;QACjD,IAAI,CAAC,IAAI,IAAI,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YAC5C,OAAO;SACR;QAED,sCAAsC;QACtC,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YAC7B,MAAM,IAAI,KAAK,CAAC,mFAAmF,CAAC,CAAC;SACtG;QAED,yCAAyC;QACzC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE1B,qCAAqC;QACrC,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACvC,IAAI,YAAY,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBAC5C,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,gBAAgB,EAAE,MAAM,CAAC,CAAC;aACzE;SACF;QAED,uBAAuB;QACvB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAElB,wEAAwE;QACxE,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEhC,mBAAmB;QACnB,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;CACF;AA1DD,kEA0DC"}