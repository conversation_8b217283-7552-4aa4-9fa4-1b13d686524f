{"version": 3, "file": "execution-plan.js", "sourceRoot": "", "sources": ["execution-plan.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAIlC,6CAA8C;AAI9C,MAAM,QAAQ;IACZ,YAAmB,EAAY,EAAS,IAAgB;QAArC,OAAE,GAAF,EAAE,CAAU;QAAS,SAAI,GAAJ,IAAI,CAAY;IAAG,CAAC;CAC7D;AAED,MAAa,aAAa;IACxB,YAAoB,KAAY,EAAE,GAAe,EAAU,QAA4B;QAAnE,UAAK,GAAL,KAAK,CAAO;QAA2B,aAAQ,GAAR,QAAQ,CAAoB;QACrF,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;IACvB,CAAC;IAED,UAAU,CAAC,GAAe;QACxB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,EAAE,0BAA0B,EAAE,GAAG,EAAE;YAC9D,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;YACzC,IAAI,UAAU,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,EAAE;gBACpC,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;aAC5D;YAED,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,QAAQ,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChE,IAAI,CAAC,KAAK,EAAE,CAAC;YAEb,2BAA2B;YAC3B,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;YACnB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE;gBAC1B,IAAI,QAAQ,GAAG,IAAI,CAAC;gBACpB,KAAK,MAAM,KAAK,IAAI,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE;oBAClC,IACI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAmC,2BAA2B;2BAC/E,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAE,kBAAkB;sBAC3E;wBACA,QAAQ,GAAG,KAAK,CAAC;wBACjB,MAAM;qBACP;iBACF;gBACD,IAAI,QAAQ,EAAE;oBACZ,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBACvB;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK;QACH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;IAC3D,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,cAA8B,EAAE,WAAqB;QACjE,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,EAAE,uBAAuB,EAAE,KAAK,IAAI,EAAE;YACxE,sBAAsB;YACtB,IAAI,CAAC,KAAK,EAAE,CAAC;YAEb,2BAA2B;YAC3B,MAAM,gBAAgB,GAAG,cAAc,CAAC,sBAAsB,EAAE,CAAC;YAEjE,wBAAwB;YACxB,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;YACjD,IAAI,WAAW,CAAC,MAAM,KAAK,WAAW,CAAC,MAAM,EAAE;gBAC7C,MAAM,IAAI,KAAK,CAAC,kFACZ,WAAW,CAAC,MAAM,cAAc,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;aAC3D;YAED,WAAW,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE;gBAC/B,MAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;gBAC7B,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;YAC9B,CAAC,CAAC,CAAC;YAEH,2BAA2B;YAC3B,MAAM,QAAQ,GAAa,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAElD,uBAAuB;YACvB,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;YAC3C,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;YAEzC,IAAI,IAAI,GAAG,CAAC,CAAC;YACb,OAAO,IAAI,GAAG,QAAQ,CAAC,MAAM,EAAE;gBAC7B,MAAM,WAAW,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;gBACrC,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAEtC,cAAc;gBACd,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC/D,IAAI,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;oBACvC,MAAM,IAAI,KAAK,CAAC,kCAAkC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;iBAClE;gBAED,MAAM;gBACN,MAAM,YAAY,GAAG,SAAqB,CAAC;gBAC3C,mBAAM,CAAC,OAAO,CACV,UAAU,EACV,aAAa,MAAM,CAAC,IAAI,CAAC,IAAI,KACzB,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAEhH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,CACxC,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,EAAE,YAAY,EAAE,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;gBAE7G,eAAe;gBACf,IAAI,UAAU,CAAC,MAAM,KAAK,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;oBACpD,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;iBACxE;gBAED,aAAa;gBACb,UAAU,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC/B,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;oBACjC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;wBACnB,MAAM,IAAI,KAAK,CAAC,WAAW,CAAC,2BAA2B,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;qBAC5E;oBACD,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;gBAC3B,CAAC,CAAC,CAAC;gBAEH,2BAA2B;gBAC3B,MAAM,eAAe,GAAG,IAAI,GAAG,EAAU,CAAC;gBAC1C,UAAU,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC/B,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;oBACjC,KAAK,MAAM,0BAA0B,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;wBAC1D,MAAM,qBAAqB,GAAG,UAAU,CAAC,0BAA0B,CAAC,CAAC;wBACrE,IAAI,QAAQ,GAAG,IAAI,CAAC;wBACpB,KAAK,MAAM,CAAC,IAAI,qBAAqB,CAAC,MAAM,EAAE;4BAC5C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;gCACpB,QAAQ,GAAG,KAAK,CAAC;gCACjB,MAAM;6BACP;yBACF;wBACD,IAAI,QAAQ,EAAE;4BACZ,eAAe,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;yBACjD;qBACF;gBACH,CAAC,CAAC,CAAC;gBACH,QAAQ,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,CAAC;aACnC;YAED,MAAM,MAAM,GAAa,EAAE,CAAC;YAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC7D,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;gBACrD,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;gBAC/C,IAAI,YAAY,KAAK,SAAS,EAAE;oBAC9B,MAAM,IAAI,KAAK,CAAC,oBAAoB,WAAW,uBAAuB,CAAC,CAAC;iBACzE;gBACD,IAAI,WAAW,KAAK,CAAC,EAAE;oBACrB,MAAM,YAAY,CAAC,OAAO,EAAE,CAAC;iBAC9B;qBAAM;oBACL,iDAAiD;oBACjD,YAAY,CAAC,IAAI,CAAC;iBACnB;gBACD,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;aAC3B;YACD,mBAAM,CAAC,OAAO,CAAC,UAAU,EAAE,+BAA+B,CAAC,CAAC;YAC5D,gBAAgB,CAAC,OAAO,EAAE,CAAC;YAC3B,OAAO,MAAM,CAAC;QAChB,CAAC,CAAC,CAAC;IACL,CAAC;CAKF;AAlJD,sCAkJC"}