/*!
* ONNX Runtime Web v1.14.0
* Copyright (c) Microsoft Corporation. All rights reserved.
* Licensed under the MIT License.
*/
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.ort=e():t.ort=e()}(self,(()=>(()=>{var __webpack_modules__={8453:(t,e,n)=>{"use strict";n.r(e),n.d(e,{InferenceSession:()=>f,Tensor:()=>p,env:()=>s,registerBackend:()=>o});const r={},i=[],o=(t,e,n)=>{if(!e||"function"!=typeof e.init||"function"!=typeof e.createSessionHandler)throw new TypeError("not a valid backend");{const o=r[t];if(void 0===o)r[t]={backend:e,priority:n};else{if(o.priority>n)return;if(o.priority===n&&o.backend!==e)throw new Error(`cannot register backend "${t}" using priority ${n}`)}if(n>=0){const e=i.indexOf(t);-1!==e&&i.splice(e,1);for(let e=0;e<i.length;e++)if(r[i[e]].priority<=n)return void i.splice(e,0,t);i.push(t)}}},s=new class{constructor(){this.wasm={},this.webgl={},this.logLevelInternal="warning"}set logLevel(t){if(void 0!==t){if("string"!=typeof t||-1===["verbose","info","warning","error","fatal"].indexOf(t))throw new Error(`Unsupported logging level: ${t}`);this.logLevelInternal=t}}get logLevel(){return this.logLevelInternal}},a="undefined"!=typeof BigInt64Array&&"function"==typeof BigInt64Array.from,u="undefined"!=typeof BigUint64Array&&"function"==typeof BigUint64Array.from,l=new Map([["float32",Float32Array],["uint8",Uint8Array],["int8",Int8Array],["uint16",Uint16Array],["int16",Int16Array],["int32",Int32Array],["bool",Uint8Array],["float64",Float64Array],["uint32",Uint32Array]]),c=new Map([[Float32Array,"float32"],[Uint8Array,"uint8"],[Int8Array,"int8"],[Uint16Array,"uint16"],[Int16Array,"int16"],[Int32Array,"int32"],[Float64Array,"float64"],[Uint32Array,"uint32"]]);a&&(l.set("int64",BigInt64Array),c.set(BigInt64Array,"int64")),u&&(l.set("uint64",BigUint64Array),c.set(BigUint64Array,"uint64"));class d{constructor(t,e,n){let r,i,o;if("string"==typeof t)if(r=t,o=n,"string"===t){if(!Array.isArray(e))throw new TypeError("A string tensor's data must be a string array.");i=e}else{const n=l.get(t);if(void 0===n)throw new TypeError(`Unsupported tensor type: ${t}.`);if(Array.isArray(e))i=n.from(e);else{if(!(e instanceof n))throw new TypeError(`A ${r} tensor's data must be type of ${n}`);i=e}}else if(o=e,Array.isArray(t)){if(0===t.length)throw new TypeError("Tensor type cannot be inferred from an empty array.");const e=typeof t[0];if("string"===e)r="string",i=t;else{if("boolean"!==e)throw new TypeError(`Invalid element type of data array: ${e}.`);r="bool",i=Uint8Array.from(t)}}else{const e=c.get(t.constructor);if(void 0===e)throw new TypeError(`Unsupported type for tensor data: ${t.constructor}.`);r=e,i=t}if(void 0===o)o=[i.length];else if(!Array.isArray(o))throw new TypeError("A tensor's dims must be a number array");const s=(t=>{let e=1;for(let n=0;n<t.length;n++){const r=t[n];if("number"!=typeof r||!Number.isSafeInteger(r))throw new TypeError(`dims[${n}] must be an integer, got: ${r}`);if(r<0)throw new RangeError(`dims[${n}] must be a non-negative integer, got: ${r}`);e*=r}return e})(o);if(s!==i.length)throw new Error(`Tensor's size(${s}) does not match data length(${i.length}).`);this.dims=o,this.type=r,this.data=i,this.size=s}static bufferToTensor(t,e){if(void 0===t)throw new Error("Image buffer must be defined");if(void 0===e.height||void 0===e.width)throw new Error("Image height and width must be defined");const{height:n,width:r}=e,i=e.norm;let o,s;o=void 0===i||void 0===i.mean?255:i.mean,s=void 0===i||void 0===i.bias?0:i.bias;const a=void 0!==e.bitmapFormat?e.bitmapFormat:"RGBA",u=void 0!==e.tensorFormat&&void 0!==e.tensorFormat?e.tensorFormat:"RGB",l=n*r,c="RGBA"===u?new Float32Array(4*l):new Float32Array(3*l);let p=4,h=0,f=1,g=2,b=3,m=0,y=l,x=2*l,_=-1;"RGB"===a&&(p=3,h=0,f=1,g=2,b=-1),"RGBA"===u?_=3*l:"RBG"===u?(m=0,x=l,y=2*l):"BGR"===u&&(x=0,y=l,m=2*l);for(let e=0;e<l;e++,h+=p,g+=p,f+=p,b+=p)c[m++]=(t[h]+s)/o,c[y++]=(t[f]+s)/o,c[x++]=(t[g]+s)/o,-1!==_&&-1!==b&&(c[_++]=(t[b]+s)/o);return new d("float32",c,"RGBA"===u?[1,4,n,r]:[1,3,n,r])}static async fromImage(t,e){const n="undefined"!=typeof HTMLImageElement&&t instanceof HTMLImageElement,r="undefined"!=typeof ImageData&&t instanceof ImageData,i="undefined"!=typeof ImageBitmap&&t instanceof ImageBitmap,o="undefined"!=typeof String&&(t instanceof String||"string"==typeof t);let s,a={};if(n){const n=document.createElement("canvas"),r=n.getContext("2d");if(null==r)throw new Error("Can not access image data");{let i=t.naturalHeight,o=t.naturalWidth;if(void 0!==e&&void 0!==e.resizedHeight&&void 0!==e.resizedWidth&&(i=e.resizedHeight,o=e.resizedWidth),void 0!==e){if(a=e,void 0!==e.tensorFormat)throw new Error("Image input config format must be RGBA for HTMLImageElement");if(a.tensorFormat="RGBA",void 0!==e.height&&e.height!==i)throw new Error("Image input config height doesn't match HTMLImageElement height");if(a.height=i,void 0!==e.width&&e.width!==o)throw new Error("Image input config width doesn't match HTMLImageElement width");a.width=o}else a.tensorFormat="RGBA",a.height=i,a.width=o;n.width=o,n.height=i,r.drawImage(t,0,0,o,i),s=r.getImageData(0,0,o,i).data}}else{if(!r){if(i){if(void 0===e)throw new Error("Please provide image config with format for Imagebitmap");if(void 0!==e.bitmapFormat)throw new Error("Image input config format must be defined for ImageBitmap");const n=document.createElement("canvas").getContext("2d");if(null!=n){const r=t.height,i=t.width;if(n.drawImage(t,0,0,i,r),s=n.getImageData(0,0,i,r).data,void 0!==e){if(void 0!==e.height&&e.height!==r)throw new Error("Image input config height doesn't match ImageBitmap height");if(a.height=r,void 0!==e.width&&e.width!==i)throw new Error("Image input config width doesn't match ImageBitmap width");a.width=i}else a.height=r,a.width=i;return d.bufferToTensor(s,a)}throw new Error("Can not access image data")}if(o)return new Promise(((n,r)=>{const i=document.createElement("canvas"),o=i.getContext("2d");if(!t||!o)return r();const s=new Image;s.crossOrigin="Anonymous",s.src=t,s.onload=()=>{i.width=s.width,i.height=s.height,o.drawImage(s,0,0,i.width,i.height);const t=o.getImageData(0,0,i.width,i.height);if(void 0!==e){if(void 0!==e.height&&e.height!==i.height)throw new Error("Image input config height doesn't match ImageBitmap height");if(a.height=i.height,void 0!==e.width&&e.width!==i.width)throw new Error("Image input config width doesn't match ImageBitmap width");a.width=i.width}else a.height=i.height,a.width=i.width;n(d.bufferToTensor(t.data,a))}}));throw new Error("Input data provided is not supported - aborted tensor creation")}{const n="RGBA";let r,i;if(void 0!==e&&void 0!==e.resizedWidth&&void 0!==e.resizedHeight?(r=e.resizedHeight,i=e.resizedWidth):(r=t.height,i=t.width),void 0!==e){if(a=e,void 0!==e.bitmapFormat&&e.bitmapFormat!==n)throw new Error("Image input config format must be RGBA for ImageData");a.bitmapFormat="RGBA"}else a.bitmapFormat="RGBA";if(a.height=r,a.width=i,void 0!==e){const e=document.createElement("canvas");e.width=i,e.height=r;const n=e.getContext("2d");if(null==n)throw new Error("Can not access image data");n.putImageData(t,0,0),s=n.getImageData(0,0,i,r).data}else s=t.data}}if(void 0!==s)return d.bufferToTensor(s,a);throw new Error("Input data provided is not supported - aborted tensor creation")}toImageData(t){var e,n;const r=document.createElement("canvas").getContext("2d");let i;if(null==r)throw new Error("Can not access image data");{const o=this.dims[3],s=this.dims[2],a=this.dims[1],u=void 0!==t&&void 0!==t.format?t.format:"RGB",l=void 0!==t&&void 0!==(null===(e=t.norm)||void 0===e?void 0:e.mean)?t.norm.mean:255,c=void 0!==t&&void 0!==(null===(n=t.norm)||void 0===n?void 0:n.bias)?t.norm.bias:0,d=s*o;if(void 0!==t){if(void 0!==t.height&&t.height!==s)throw new Error("Image output config height doesn't match tensor height");if(void 0!==t.width&&t.width!==o)throw new Error("Image output config width doesn't match tensor width");if(void 0!==t.format&&4===a&&"RGBA"!==t.format||3===a&&"RGB"!==t.format&&"BGR"!==t.format)throw new Error("Tensor format doesn't match input tensor dims")}const p=4;let h=0,f=1,g=2,b=3,m=0,y=d,x=2*d,_=-1;"RGBA"===u?(m=0,y=d,x=2*d,_=3*d):"RGB"===u?(m=0,y=d,x=2*d):"RBG"===u&&(m=0,x=d,y=2*d),i=r.createImageData(o,s);for(let t=0;t<s*o;h+=p,f+=p,g+=p,b+=p,t++)i.data[h]=(this.data[m++]-c)*l,i.data[f]=(this.data[y++]-c)*l,i.data[g]=(this.data[x++]-c)*l,i.data[b]=-1===_?255:(this.data[_++]-c)*l}return i}reshape(t){return new d(this.type,this.data,t)}}const p=d;class h{constructor(t){this.handler=t}async run(t,e,n){const r={};let i={};if("object"!=typeof t||null===t||t instanceof p||Array.isArray(t))throw new TypeError("'feeds' must be an object that use input names as keys and OnnxValue as corresponding values.");let o=!0;if("object"==typeof e){if(null===e)throw new TypeError("Unexpected argument[1]: cannot be null.");if(e instanceof p)throw new TypeError("'fetches' cannot be a Tensor");if(Array.isArray(e)){if(0===e.length)throw new TypeError("'fetches' cannot be an empty array.");o=!1;for(const t of e){if("string"!=typeof t)throw new TypeError("'fetches' must be a string array or an object.");if(-1===this.outputNames.indexOf(t))throw new RangeError(`'fetches' contains invalid output name: ${t}.`);r[t]=null}if("object"==typeof n&&null!==n)i=n;else if(void 0!==n)throw new TypeError("'options' must be an object.")}else{let t=!1;const s=Object.getOwnPropertyNames(e);for(const n of this.outputNames)if(-1!==s.indexOf(n)){const i=e[n];(null===i||i instanceof p)&&(t=!0,o=!1,r[n]=i)}if(t){if("object"==typeof n&&null!==n)i=n;else if(void 0!==n)throw new TypeError("'options' must be an object.")}else i=e}}else if(void 0!==e)throw new TypeError("Unexpected argument[1]: must be 'fetches' or 'options'.");for(const e of this.inputNames)if(void 0===t[e])throw new Error(`input '${e}' is missing in 'feeds'.`);if(o)for(const t of this.outputNames)r[t]=null;const s=await this.handler.run(t,r,i),a={};for(const t in s)Object.hasOwnProperty.call(s,t)&&(a[t]=new p(s[t].type,s[t].data,s[t].dims));return a}static async create(t,e,n,o){let s,a={};if("string"==typeof t){if(s=t,"object"==typeof e&&null!==e)a=e;else if(void 0!==e)throw new TypeError("'options' must be an object.")}else if(t instanceof Uint8Array){if(s=t,"object"==typeof e&&null!==e)a=e;else if(void 0!==e)throw new TypeError("'options' must be an object.")}else{if(!(t instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&t instanceof SharedArrayBuffer))throw new TypeError("Unexpected argument[0]: must be 'path' or 'buffer'.");{const r=t;let i=0,u=t.byteLength;if("object"==typeof e&&null!==e)a=e;else if("number"==typeof e){if(i=e,!Number.isSafeInteger(i))throw new RangeError("'byteOffset' must be an integer.");if(i<0||i>=r.byteLength)throw new RangeError(`'byteOffset' is out of range [0, ${r.byteLength}).`);if(u=t.byteLength-i,"number"==typeof n){if(u=n,!Number.isSafeInteger(u))throw new RangeError("'byteLength' must be an integer.");if(u<=0||i+u>r.byteLength)throw new RangeError(`'byteLength' is out of range (0, ${r.byteLength-i}].`);if("object"==typeof o&&null!==o)a=o;else if(void 0!==o)throw new TypeError("'options' must be an object.")}else if(void 0!==n)throw new TypeError("'byteLength' must be a number.")}else if(void 0!==e)throw new TypeError("'options' must be an object.");s=new Uint8Array(r,i,u)}}const u=(a.executionProviders||[]).map((t=>"string"==typeof t?t:t.name)),l=await(async t=>{const e=0===t.length?i:t,n=[];for(const t of e){const e=r[t];if(e){if(e.initialized)return e.backend;if(e.aborted)continue;const r=!!e.initPromise;try{return r||(e.initPromise=e.backend.init()),await e.initPromise,e.initialized=!0,e.backend}catch(i){r||n.push({name:t,err:i}),e.aborted=!0}finally{delete e.initPromise}}}throw new Error(`no available backend found. ERR: ${n.map((t=>`[${t.name}] ${t.err}`)).join(", ")}`)})(u),c=await l.createSessionHandler(s,a);return new h(c)}startProfiling(){this.handler.startProfiling()}endProfiling(){this.handler.endProfiling()}get inputNames(){return this.handler.inputNames}get outputNames(){return this.handler.outputNames}}const f=h},4537:t=>{"use strict";t.exports=function(t,e){for(var n=new Array(arguments.length-1),r=0,i=2,o=!0;i<arguments.length;)n[r++]=arguments[i++];return new Promise((function(i,s){n[r]=function(t){if(o)if(o=!1,t)s(t);else{for(var e=new Array(arguments.length-1),n=0;n<e.length;)e[n++]=arguments[n];i.apply(null,e)}};try{t.apply(e||null,n)}catch(t){o&&(o=!1,s(t))}}))}},7419:(t,e)=>{"use strict";var n=e;n.length=function(t){var e=t.length;if(!e)return 0;for(var n=0;--e%4>1&&"="===t.charAt(e);)++n;return Math.ceil(3*t.length)/4-n};for(var r=new Array(64),i=new Array(123),o=0;o<64;)i[r[o]=o<26?o+65:o<52?o+71:o<62?o-4:o-59|43]=o++;n.encode=function(t,e,n){for(var i,o=null,s=[],a=0,u=0;e<n;){var l=t[e++];switch(u){case 0:s[a++]=r[l>>2],i=(3&l)<<4,u=1;break;case 1:s[a++]=r[i|l>>4],i=(15&l)<<2,u=2;break;case 2:s[a++]=r[i|l>>6],s[a++]=r[63&l],u=0}a>8191&&((o||(o=[])).push(String.fromCharCode.apply(String,s)),a=0)}return u&&(s[a++]=r[i],s[a++]=61,1===u&&(s[a++]=61)),o?(a&&o.push(String.fromCharCode.apply(String,s.slice(0,a))),o.join("")):String.fromCharCode.apply(String,s.slice(0,a))};var s="invalid encoding";n.decode=function(t,e,n){for(var r,o=n,a=0,u=0;u<t.length;){var l=t.charCodeAt(u++);if(61===l&&a>1)break;if(void 0===(l=i[l]))throw Error(s);switch(a){case 0:r=l,a=1;break;case 1:e[n++]=r<<2|(48&l)>>4,r=l,a=2;break;case 2:e[n++]=(15&r)<<4|(60&l)>>2,r=l,a=3;break;case 3:e[n++]=(3&r)<<6|l,a=0}}if(1===a)throw Error(s);return n-o},n.test=function(t){return/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(t)}},9211:t=>{"use strict";function e(){this._listeners={}}t.exports=e,e.prototype.on=function(t,e,n){return(this._listeners[t]||(this._listeners[t]=[])).push({fn:e,ctx:n||this}),this},e.prototype.off=function(t,e){if(void 0===t)this._listeners={};else if(void 0===e)this._listeners[t]=[];else for(var n=this._listeners[t],r=0;r<n.length;)n[r].fn===e?n.splice(r,1):++r;return this},e.prototype.emit=function(t){var e=this._listeners[t];if(e){for(var n=[],r=1;r<arguments.length;)n.push(arguments[r++]);for(r=0;r<e.length;)e[r].fn.apply(e[r++].ctx,n)}return this}},945:t=>{"use strict";function e(t){return"undefined"!=typeof Float32Array?function(){var e=new Float32Array([-0]),n=new Uint8Array(e.buffer),r=128===n[3];function i(t,r,i){e[0]=t,r[i]=n[0],r[i+1]=n[1],r[i+2]=n[2],r[i+3]=n[3]}function o(t,r,i){e[0]=t,r[i]=n[3],r[i+1]=n[2],r[i+2]=n[1],r[i+3]=n[0]}function s(t,r){return n[0]=t[r],n[1]=t[r+1],n[2]=t[r+2],n[3]=t[r+3],e[0]}function a(t,r){return n[3]=t[r],n[2]=t[r+1],n[1]=t[r+2],n[0]=t[r+3],e[0]}t.writeFloatLE=r?i:o,t.writeFloatBE=r?o:i,t.readFloatLE=r?s:a,t.readFloatBE=r?a:s}():function(){function e(t,e,n,r){var i=e<0?1:0;if(i&&(e=-e),0===e)t(1/e>0?0:2147483648,n,r);else if(isNaN(e))t(2143289344,n,r);else if(e>34028234663852886e22)t((i<<31|2139095040)>>>0,n,r);else if(e<11754943508222875e-54)t((i<<31|Math.round(e/1401298464324817e-60))>>>0,n,r);else{var o=Math.floor(Math.log(e)/Math.LN2);t((i<<31|o+127<<23|8388607&Math.round(e*Math.pow(2,-o)*8388608))>>>0,n,r)}}function s(t,e,n){var r=t(e,n),i=2*(r>>31)+1,o=r>>>23&255,s=8388607&r;return 255===o?s?NaN:i*(1/0):0===o?1401298464324817e-60*i*s:i*Math.pow(2,o-150)*(s+8388608)}t.writeFloatLE=e.bind(null,n),t.writeFloatBE=e.bind(null,r),t.readFloatLE=s.bind(null,i),t.readFloatBE=s.bind(null,o)}(),"undefined"!=typeof Float64Array?function(){var e=new Float64Array([-0]),n=new Uint8Array(e.buffer),r=128===n[7];function i(t,r,i){e[0]=t,r[i]=n[0],r[i+1]=n[1],r[i+2]=n[2],r[i+3]=n[3],r[i+4]=n[4],r[i+5]=n[5],r[i+6]=n[6],r[i+7]=n[7]}function o(t,r,i){e[0]=t,r[i]=n[7],r[i+1]=n[6],r[i+2]=n[5],r[i+3]=n[4],r[i+4]=n[3],r[i+5]=n[2],r[i+6]=n[1],r[i+7]=n[0]}function s(t,r){return n[0]=t[r],n[1]=t[r+1],n[2]=t[r+2],n[3]=t[r+3],n[4]=t[r+4],n[5]=t[r+5],n[6]=t[r+6],n[7]=t[r+7],e[0]}function a(t,r){return n[7]=t[r],n[6]=t[r+1],n[5]=t[r+2],n[4]=t[r+3],n[3]=t[r+4],n[2]=t[r+5],n[1]=t[r+6],n[0]=t[r+7],e[0]}t.writeDoubleLE=r?i:o,t.writeDoubleBE=r?o:i,t.readDoubleLE=r?s:a,t.readDoubleBE=r?a:s}():function(){function e(t,e,n,r,i,o){var s=r<0?1:0;if(s&&(r=-r),0===r)t(0,i,o+e),t(1/r>0?0:2147483648,i,o+n);else if(isNaN(r))t(0,i,o+e),t(2146959360,i,o+n);else if(r>17976931348623157e292)t(0,i,o+e),t((s<<31|2146435072)>>>0,i,o+n);else{var a;if(r<22250738585072014e-324)t((a=r/5e-324)>>>0,i,o+e),t((s<<31|a/4294967296)>>>0,i,o+n);else{var u=Math.floor(Math.log(r)/Math.LN2);1024===u&&(u=1023),t(4503599627370496*(a=r*Math.pow(2,-u))>>>0,i,o+e),t((s<<31|u+1023<<20|1048576*a&1048575)>>>0,i,o+n)}}}function s(t,e,n,r,i){var o=t(r,i+e),s=t(r,i+n),a=2*(s>>31)+1,u=s>>>20&2047,l=4294967296*(1048575&s)+o;return 2047===u?l?NaN:a*(1/0):0===u?5e-324*a*l:a*Math.pow(2,u-1075)*(l+4503599627370496)}t.writeDoubleLE=e.bind(null,n,0,4),t.writeDoubleBE=e.bind(null,r,4,0),t.readDoubleLE=s.bind(null,i,0,4),t.readDoubleBE=s.bind(null,o,4,0)}(),t}function n(t,e,n){e[n]=255&t,e[n+1]=t>>>8&255,e[n+2]=t>>>16&255,e[n+3]=t>>>24}function r(t,e,n){e[n]=t>>>24,e[n+1]=t>>>16&255,e[n+2]=t>>>8&255,e[n+3]=255&t}function i(t,e){return(t[e]|t[e+1]<<8|t[e+2]<<16|t[e+3]<<24)>>>0}function o(t,e){return(t[e]<<24|t[e+1]<<16|t[e+2]<<8|t[e+3])>>>0}t.exports=e(e)},7199:module=>{"use strict";function inquire(moduleName){try{var mod=eval("quire".replace(/^/,"re"))(moduleName);if(mod&&(mod.length||Object.keys(mod).length))return mod}catch(t){}return null}module.exports=inquire},6662:t=>{"use strict";t.exports=function(t,e,n){var r=n||8192,i=r>>>1,o=null,s=r;return function(n){if(n<1||n>i)return t(n);s+n>r&&(o=t(r),s=0);var a=e.call(o,s,s+=n);return 7&s&&(s=1+(7|s)),a}}},4997:(t,e)=>{"use strict";var n=e;n.length=function(t){for(var e=0,n=0,r=0;r<t.length;++r)(n=t.charCodeAt(r))<128?e+=1:n<2048?e+=2:55296==(64512&n)&&56320==(64512&t.charCodeAt(r+1))?(++r,e+=4):e+=3;return e},n.read=function(t,e,n){if(n-e<1)return"";for(var r,i=null,o=[],s=0;e<n;)(r=t[e++])<128?o[s++]=r:r>191&&r<224?o[s++]=(31&r)<<6|63&t[e++]:r>239&&r<365?(r=((7&r)<<18|(63&t[e++])<<12|(63&t[e++])<<6|63&t[e++])-65536,o[s++]=55296+(r>>10),o[s++]=56320+(1023&r)):o[s++]=(15&r)<<12|(63&t[e++])<<6|63&t[e++],s>8191&&((i||(i=[])).push(String.fromCharCode.apply(String,o)),s=0);return i?(s&&i.push(String.fromCharCode.apply(String,o.slice(0,s))),i.join("")):String.fromCharCode.apply(String,o.slice(0,s))},n.write=function(t,e,n){for(var r,i,o=n,s=0;s<t.length;++s)(r=t.charCodeAt(s))<128?e[n++]=r:r<2048?(e[n++]=r>>6|192,e[n++]=63&r|128):55296==(64512&r)&&56320==(64512&(i=t.charCodeAt(s+1)))?(r=65536+((1023&r)<<10)+(1023&i),++s,e[n++]=r>>18|240,e[n++]=r>>12&63|128,e[n++]=r>>6&63|128,e[n++]=63&r|128):(e[n++]=r>>12|224,e[n++]=r>>6&63|128,e[n++]=63&r|128);return n-o}},3442:(t,e)=>{"use strict";e.__esModule=!0;var n=function(){function t(e){if(!e)throw new TypeError("Invalid argument; `value` has no value.");this.value=t.EMPTY,e&&t.isGuid(e)&&(this.value=e)}return t.isGuid=function(e){var n=e.toString();return e&&(e instanceof t||t.validator.test(n))},t.create=function(){return new t([t.gen(2),t.gen(1),t.gen(1),t.gen(1),t.gen(3)].join("-"))},t.createEmpty=function(){return new t("emptyguid")},t.parse=function(e){return new t(e)},t.raw=function(){return[t.gen(2),t.gen(1),t.gen(1),t.gen(1),t.gen(3)].join("-")},t.gen=function(t){for(var e="",n=0;n<t;n++)e+=(65536*(1+Math.random())|0).toString(16).substring(1);return e},t.prototype.equals=function(e){return t.isGuid(e)&&this.value===e.toString()},t.prototype.isEmpty=function(){return this.value===t.EMPTY},t.prototype.toString=function(){return this.value},t.prototype.toJSON=function(){return{value:this.value}},t.validator=new RegExp("^[a-z0-9]{8}-[a-z0-9]{4}-[a-z0-9]{4}-[a-z0-9]{4}-[a-z0-9]{12}$","i"),t.EMPTY="00000000-0000-0000-0000-000000000000",t}();e.Guid=n},3720:t=>{t.exports=n;var e=null;try{e=new WebAssembly.Instance(new WebAssembly.Module(new Uint8Array([0,97,115,109,1,0,0,0,1,13,2,96,0,1,127,96,4,127,127,127,127,1,127,3,7,6,0,1,1,1,1,1,6,6,1,127,1,65,0,11,7,50,6,3,109,117,108,0,1,5,100,105,118,95,115,0,2,5,100,105,118,95,117,0,3,5,114,101,109,95,115,0,4,5,114,101,109,95,117,0,5,8,103,101,116,95,104,105,103,104,0,0,10,191,1,6,4,0,35,0,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,126,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,127,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,128,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,129,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,130,34,4,66,32,135,167,36,0,32,4,167,11])),{}).exports}catch(t){}function n(t,e,n){this.low=0|t,this.high=0|e,this.unsigned=!!n}function r(t){return!0===(t&&t.__isLong__)}n.prototype.__isLong__,Object.defineProperty(n.prototype,"__isLong__",{value:!0}),n.isLong=r;var i={},o={};function s(t,e){var n,r,s;return e?(s=0<=(t>>>=0)&&t<256)&&(r=o[t])?r:(n=u(t,(0|t)<0?-1:0,!0),s&&(o[t]=n),n):(s=-128<=(t|=0)&&t<128)&&(r=i[t])?r:(n=u(t,t<0?-1:0,!1),s&&(i[t]=n),n)}function a(t,e){if(isNaN(t))return e?m:b;if(e){if(t<0)return m;if(t>=h)return v}else{if(t<=-f)return T;if(t+1>=f)return w}return t<0?a(-t,e).neg():u(t%p|0,t/p|0,e)}function u(t,e,r){return new n(t,e,r)}n.fromInt=s,n.fromNumber=a,n.fromBits=u;var l=Math.pow;function c(t,e,n){if(0===t.length)throw Error("empty string");if("NaN"===t||"Infinity"===t||"+Infinity"===t||"-Infinity"===t)return b;if("number"==typeof e?(n=e,e=!1):e=!!e,(n=n||10)<2||36<n)throw RangeError("radix");var r;if((r=t.indexOf("-"))>0)throw Error("interior hyphen");if(0===r)return c(t.substring(1),e,n).neg();for(var i=a(l(n,8)),o=b,s=0;s<t.length;s+=8){var u=Math.min(8,t.length-s),d=parseInt(t.substring(s,s+u),n);if(u<8){var p=a(l(n,u));o=o.mul(p).add(a(d))}else o=(o=o.mul(i)).add(a(d))}return o.unsigned=e,o}function d(t,e){return"number"==typeof t?a(t,e):"string"==typeof t?c(t,e):u(t.low,t.high,"boolean"==typeof e?e:t.unsigned)}n.fromString=c,n.fromValue=d;var p=4294967296,h=p*p,f=h/2,g=s(1<<24),b=s(0);n.ZERO=b;var m=s(0,!0);n.UZERO=m;var y=s(1);n.ONE=y;var x=s(1,!0);n.UONE=x;var _=s(-1);n.NEG_ONE=_;var w=u(-1,2147483647,!1);n.MAX_VALUE=w;var v=u(-1,-1,!0);n.MAX_UNSIGNED_VALUE=v;var T=u(0,-2147483648,!1);n.MIN_VALUE=T;var S=n.prototype;S.toInt=function(){return this.unsigned?this.low>>>0:this.low},S.toNumber=function(){return this.unsigned?(this.high>>>0)*p+(this.low>>>0):this.high*p+(this.low>>>0)},S.toString=function(t){if((t=t||10)<2||36<t)throw RangeError("radix");if(this.isZero())return"0";if(this.isNegative()){if(this.eq(T)){var e=a(t),n=this.div(e),r=n.mul(e).sub(this);return n.toString(t)+r.toInt().toString(t)}return"-"+this.neg().toString(t)}for(var i=a(l(t,6),this.unsigned),o=this,s="";;){var u=o.div(i),c=(o.sub(u.mul(i)).toInt()>>>0).toString(t);if((o=u).isZero())return c+s;for(;c.length<6;)c="0"+c;s=""+c+s}},S.getHighBits=function(){return this.high},S.getHighBitsUnsigned=function(){return this.high>>>0},S.getLowBits=function(){return this.low},S.getLowBitsUnsigned=function(){return this.low>>>0},S.getNumBitsAbs=function(){if(this.isNegative())return this.eq(T)?64:this.neg().getNumBitsAbs();for(var t=0!=this.high?this.high:this.low,e=31;e>0&&0==(t&1<<e);e--);return 0!=this.high?e+33:e+1},S.isZero=function(){return 0===this.high&&0===this.low},S.eqz=S.isZero,S.isNegative=function(){return!this.unsigned&&this.high<0},S.isPositive=function(){return this.unsigned||this.high>=0},S.isOdd=function(){return 1==(1&this.low)},S.isEven=function(){return 0==(1&this.low)},S.equals=function(t){return r(t)||(t=d(t)),(this.unsigned===t.unsigned||this.high>>>31!=1||t.high>>>31!=1)&&this.high===t.high&&this.low===t.low},S.eq=S.equals,S.notEquals=function(t){return!this.eq(t)},S.neq=S.notEquals,S.ne=S.notEquals,S.lessThan=function(t){return this.comp(t)<0},S.lt=S.lessThan,S.lessThanOrEqual=function(t){return this.comp(t)<=0},S.lte=S.lessThanOrEqual,S.le=S.lessThanOrEqual,S.greaterThan=function(t){return this.comp(t)>0},S.gt=S.greaterThan,S.greaterThanOrEqual=function(t){return this.comp(t)>=0},S.gte=S.greaterThanOrEqual,S.ge=S.greaterThanOrEqual,S.compare=function(t){if(r(t)||(t=d(t)),this.eq(t))return 0;var e=this.isNegative(),n=t.isNegative();return e&&!n?-1:!e&&n?1:this.unsigned?t.high>>>0>this.high>>>0||t.high===this.high&&t.low>>>0>this.low>>>0?-1:1:this.sub(t).isNegative()?-1:1},S.comp=S.compare,S.negate=function(){return!this.unsigned&&this.eq(T)?T:this.not().add(y)},S.neg=S.negate,S.add=function(t){r(t)||(t=d(t));var e=this.high>>>16,n=65535&this.high,i=this.low>>>16,o=65535&this.low,s=t.high>>>16,a=65535&t.high,l=t.low>>>16,c=0,p=0,h=0,f=0;return h+=(f+=o+(65535&t.low))>>>16,p+=(h+=i+l)>>>16,c+=(p+=n+a)>>>16,c+=e+s,u((h&=65535)<<16|(f&=65535),(c&=65535)<<16|(p&=65535),this.unsigned)},S.subtract=function(t){return r(t)||(t=d(t)),this.add(t.neg())},S.sub=S.subtract,S.multiply=function(t){if(this.isZero())return b;if(r(t)||(t=d(t)),e)return u(e.mul(this.low,this.high,t.low,t.high),e.get_high(),this.unsigned);if(t.isZero())return b;if(this.eq(T))return t.isOdd()?T:b;if(t.eq(T))return this.isOdd()?T:b;if(this.isNegative())return t.isNegative()?this.neg().mul(t.neg()):this.neg().mul(t).neg();if(t.isNegative())return this.mul(t.neg()).neg();if(this.lt(g)&&t.lt(g))return a(this.toNumber()*t.toNumber(),this.unsigned);var n=this.high>>>16,i=65535&this.high,o=this.low>>>16,s=65535&this.low,l=t.high>>>16,c=65535&t.high,p=t.low>>>16,h=65535&t.low,f=0,m=0,y=0,x=0;return y+=(x+=s*h)>>>16,m+=(y+=o*h)>>>16,y&=65535,m+=(y+=s*p)>>>16,f+=(m+=i*h)>>>16,m&=65535,f+=(m+=o*p)>>>16,m&=65535,f+=(m+=s*c)>>>16,f+=n*h+i*p+o*c+s*l,u((y&=65535)<<16|(x&=65535),(f&=65535)<<16|(m&=65535),this.unsigned)},S.mul=S.multiply,S.divide=function(t){if(r(t)||(t=d(t)),t.isZero())throw Error("division by zero");var n,i,o;if(e)return this.unsigned||-2147483648!==this.high||-1!==t.low||-1!==t.high?u((this.unsigned?e.div_u:e.div_s)(this.low,this.high,t.low,t.high),e.get_high(),this.unsigned):this;if(this.isZero())return this.unsigned?m:b;if(this.unsigned){if(t.unsigned||(t=t.toUnsigned()),t.gt(this))return m;if(t.gt(this.shru(1)))return x;o=m}else{if(this.eq(T))return t.eq(y)||t.eq(_)?T:t.eq(T)?y:(n=this.shr(1).div(t).shl(1)).eq(b)?t.isNegative()?y:_:(i=this.sub(t.mul(n)),o=n.add(i.div(t)));if(t.eq(T))return this.unsigned?m:b;if(this.isNegative())return t.isNegative()?this.neg().div(t.neg()):this.neg().div(t).neg();if(t.isNegative())return this.div(t.neg()).neg();o=b}for(i=this;i.gte(t);){n=Math.max(1,Math.floor(i.toNumber()/t.toNumber()));for(var s=Math.ceil(Math.log(n)/Math.LN2),c=s<=48?1:l(2,s-48),p=a(n),h=p.mul(t);h.isNegative()||h.gt(i);)h=(p=a(n-=c,this.unsigned)).mul(t);p.isZero()&&(p=y),o=o.add(p),i=i.sub(h)}return o},S.div=S.divide,S.modulo=function(t){return r(t)||(t=d(t)),e?u((this.unsigned?e.rem_u:e.rem_s)(this.low,this.high,t.low,t.high),e.get_high(),this.unsigned):this.sub(this.div(t).mul(t))},S.mod=S.modulo,S.rem=S.modulo,S.not=function(){return u(~this.low,~this.high,this.unsigned)},S.and=function(t){return r(t)||(t=d(t)),u(this.low&t.low,this.high&t.high,this.unsigned)},S.or=function(t){return r(t)||(t=d(t)),u(this.low|t.low,this.high|t.high,this.unsigned)},S.xor=function(t){return r(t)||(t=d(t)),u(this.low^t.low,this.high^t.high,this.unsigned)},S.shiftLeft=function(t){return r(t)&&(t=t.toInt()),0==(t&=63)?this:t<32?u(this.low<<t,this.high<<t|this.low>>>32-t,this.unsigned):u(0,this.low<<t-32,this.unsigned)},S.shl=S.shiftLeft,S.shiftRight=function(t){return r(t)&&(t=t.toInt()),0==(t&=63)?this:t<32?u(this.low>>>t|this.high<<32-t,this.high>>t,this.unsigned):u(this.high>>t-32,this.high>=0?0:-1,this.unsigned)},S.shr=S.shiftRight,S.shiftRightUnsigned=function(t){if(r(t)&&(t=t.toInt()),0==(t&=63))return this;var e=this.high;return t<32?u(this.low>>>t|e<<32-t,e>>>t,this.unsigned):u(32===t?e:e>>>t-32,0,this.unsigned)},S.shru=S.shiftRightUnsigned,S.shr_u=S.shiftRightUnsigned,S.toSigned=function(){return this.unsigned?u(this.low,this.high,!1):this},S.toUnsigned=function(){return this.unsigned?this:u(this.low,this.high,!0)},S.toBytes=function(t){return t?this.toBytesLE():this.toBytesBE()},S.toBytesLE=function(){var t=this.high,e=this.low;return[255&e,e>>>8&255,e>>>16&255,e>>>24,255&t,t>>>8&255,t>>>16&255,t>>>24]},S.toBytesBE=function(){var t=this.high,e=this.low;return[t>>>24,t>>>16&255,t>>>8&255,255&t,e>>>24,e>>>16&255,e>>>8&255,255&e]},n.fromBytes=function(t,e,r){return r?n.fromBytesLE(t,e):n.fromBytesBE(t,e)},n.fromBytesLE=function(t,e){return new n(t[0]|t[1]<<8|t[2]<<16|t[3]<<24,t[4]|t[5]<<8|t[6]<<16|t[7]<<24,e)},n.fromBytesBE=function(t,e){return new n(t[4]<<24|t[5]<<16|t[6]<<8|t[7],t[0]<<24|t[1]<<16|t[2]<<8|t[3],e)}},1446:(t,e,n)=>{"use strict";var r,i,o,s=n(2100),a=s.Reader,u=s.Writer,l=s.util,c=s.roots.default||(s.roots.default={});c.onnx=((o={}).Version=(r={},(i=Object.create(r))[r[0]="_START_VERSION"]=0,i[r[1]="IR_VERSION_2017_10_10"]=1,i[r[2]="IR_VERSION_2017_10_30"]=2,i[r[3]="IR_VERSION_2017_11_3"]=3,i[r[4]="IR_VERSION_2019_1_22"]=4,i[r[5]="IR_VERSION"]=5,i),o.AttributeProto=function(){function t(t){if(this.floats=[],this.ints=[],this.strings=[],this.tensors=[],this.graphs=[],t)for(var e=Object.keys(t),n=0;n<e.length;++n)null!=t[e[n]]&&(this[e[n]]=t[e[n]])}return t.prototype.name="",t.prototype.refAttrName="",t.prototype.docString="",t.prototype.type=0,t.prototype.f=0,t.prototype.i=l.Long?l.Long.fromBits(0,0,!1):0,t.prototype.s=l.newBuffer([]),t.prototype.t=null,t.prototype.g=null,t.prototype.floats=l.emptyArray,t.prototype.ints=l.emptyArray,t.prototype.strings=l.emptyArray,t.prototype.tensors=l.emptyArray,t.prototype.graphs=l.emptyArray,t.create=function(e){return new t(e)},t.encode=function(t,e){if(e||(e=u.create()),null!=t.name&&t.hasOwnProperty("name")&&e.uint32(10).string(t.name),null!=t.f&&t.hasOwnProperty("f")&&e.uint32(21).float(t.f),null!=t.i&&t.hasOwnProperty("i")&&e.uint32(24).int64(t.i),null!=t.s&&t.hasOwnProperty("s")&&e.uint32(34).bytes(t.s),null!=t.t&&t.hasOwnProperty("t")&&c.onnx.TensorProto.encode(t.t,e.uint32(42).fork()).ldelim(),null!=t.g&&t.hasOwnProperty("g")&&c.onnx.GraphProto.encode(t.g,e.uint32(50).fork()).ldelim(),null!=t.floats&&t.floats.length){e.uint32(58).fork();for(var n=0;n<t.floats.length;++n)e.float(t.floats[n]);e.ldelim()}if(null!=t.ints&&t.ints.length){for(e.uint32(66).fork(),n=0;n<t.ints.length;++n)e.int64(t.ints[n]);e.ldelim()}if(null!=t.strings&&t.strings.length)for(n=0;n<t.strings.length;++n)e.uint32(74).bytes(t.strings[n]);if(null!=t.tensors&&t.tensors.length)for(n=0;n<t.tensors.length;++n)c.onnx.TensorProto.encode(t.tensors[n],e.uint32(82).fork()).ldelim();if(null!=t.graphs&&t.graphs.length)for(n=0;n<t.graphs.length;++n)c.onnx.GraphProto.encode(t.graphs[n],e.uint32(90).fork()).ldelim();return null!=t.docString&&t.hasOwnProperty("docString")&&e.uint32(106).string(t.docString),null!=t.type&&t.hasOwnProperty("type")&&e.uint32(160).int32(t.type),null!=t.refAttrName&&t.hasOwnProperty("refAttrName")&&e.uint32(170).string(t.refAttrName),e},t.encodeDelimited=function(t,e){return this.encode(t,e).ldelim()},t.decode=function(t,e){t instanceof a||(t=a.create(t));for(var n=void 0===e?t.len:t.pos+e,r=new c.onnx.AttributeProto;t.pos<n;){var i=t.uint32();switch(i>>>3){case 1:r.name=t.string();break;case 21:r.refAttrName=t.string();break;case 13:r.docString=t.string();break;case 20:r.type=t.int32();break;case 2:r.f=t.float();break;case 3:r.i=t.int64();break;case 4:r.s=t.bytes();break;case 5:r.t=c.onnx.TensorProto.decode(t,t.uint32());break;case 6:r.g=c.onnx.GraphProto.decode(t,t.uint32());break;case 7:if(r.floats&&r.floats.length||(r.floats=[]),2==(7&i))for(var o=t.uint32()+t.pos;t.pos<o;)r.floats.push(t.float());else r.floats.push(t.float());break;case 8:if(r.ints&&r.ints.length||(r.ints=[]),2==(7&i))for(o=t.uint32()+t.pos;t.pos<o;)r.ints.push(t.int64());else r.ints.push(t.int64());break;case 9:r.strings&&r.strings.length||(r.strings=[]),r.strings.push(t.bytes());break;case 10:r.tensors&&r.tensors.length||(r.tensors=[]),r.tensors.push(c.onnx.TensorProto.decode(t,t.uint32()));break;case 11:r.graphs&&r.graphs.length||(r.graphs=[]),r.graphs.push(c.onnx.GraphProto.decode(t,t.uint32()));break;default:t.skipType(7&i)}}return r},t.decodeDelimited=function(t){return t instanceof a||(t=new a(t)),this.decode(t,t.uint32())},t.verify=function(t){if("object"!=typeof t||null===t)return"object expected";if(null!=t.name&&t.hasOwnProperty("name")&&!l.isString(t.name))return"name: string expected";if(null!=t.refAttrName&&t.hasOwnProperty("refAttrName")&&!l.isString(t.refAttrName))return"refAttrName: string expected";if(null!=t.docString&&t.hasOwnProperty("docString")&&!l.isString(t.docString))return"docString: string expected";if(null!=t.type&&t.hasOwnProperty("type"))switch(t.type){default:return"type: enum value expected";case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 9:case 10:}if(null!=t.f&&t.hasOwnProperty("f")&&"number"!=typeof t.f)return"f: number expected";if(null!=t.i&&t.hasOwnProperty("i")&&!(l.isInteger(t.i)||t.i&&l.isInteger(t.i.low)&&l.isInteger(t.i.high)))return"i: integer|Long expected";if(null!=t.s&&t.hasOwnProperty("s")&&!(t.s&&"number"==typeof t.s.length||l.isString(t.s)))return"s: buffer expected";if(null!=t.t&&t.hasOwnProperty("t")&&(n=c.onnx.TensorProto.verify(t.t)))return"t."+n;if(null!=t.g&&t.hasOwnProperty("g")&&(n=c.onnx.GraphProto.verify(t.g)))return"g."+n;if(null!=t.floats&&t.hasOwnProperty("floats")){if(!Array.isArray(t.floats))return"floats: array expected";for(var e=0;e<t.floats.length;++e)if("number"!=typeof t.floats[e])return"floats: number[] expected"}if(null!=t.ints&&t.hasOwnProperty("ints")){if(!Array.isArray(t.ints))return"ints: array expected";for(e=0;e<t.ints.length;++e)if(!(l.isInteger(t.ints[e])||t.ints[e]&&l.isInteger(t.ints[e].low)&&l.isInteger(t.ints[e].high)))return"ints: integer|Long[] expected"}if(null!=t.strings&&t.hasOwnProperty("strings")){if(!Array.isArray(t.strings))return"strings: array expected";for(e=0;e<t.strings.length;++e)if(!(t.strings[e]&&"number"==typeof t.strings[e].length||l.isString(t.strings[e])))return"strings: buffer[] expected"}if(null!=t.tensors&&t.hasOwnProperty("tensors")){if(!Array.isArray(t.tensors))return"tensors: array expected";for(e=0;e<t.tensors.length;++e)if(n=c.onnx.TensorProto.verify(t.tensors[e]))return"tensors."+n}if(null!=t.graphs&&t.hasOwnProperty("graphs")){if(!Array.isArray(t.graphs))return"graphs: array expected";for(e=0;e<t.graphs.length;++e){var n;if(n=c.onnx.GraphProto.verify(t.graphs[e]))return"graphs."+n}}return null},t.fromObject=function(t){if(t instanceof c.onnx.AttributeProto)return t;var e=new c.onnx.AttributeProto;switch(null!=t.name&&(e.name=String(t.name)),null!=t.refAttrName&&(e.refAttrName=String(t.refAttrName)),null!=t.docString&&(e.docString=String(t.docString)),t.type){case"UNDEFINED":case 0:e.type=0;break;case"FLOAT":case 1:e.type=1;break;case"INT":case 2:e.type=2;break;case"STRING":case 3:e.type=3;break;case"TENSOR":case 4:e.type=4;break;case"GRAPH":case 5:e.type=5;break;case"FLOATS":case 6:e.type=6;break;case"INTS":case 7:e.type=7;break;case"STRINGS":case 8:e.type=8;break;case"TENSORS":case 9:e.type=9;break;case"GRAPHS":case 10:e.type=10}if(null!=t.f&&(e.f=Number(t.f)),null!=t.i&&(l.Long?(e.i=l.Long.fromValue(t.i)).unsigned=!1:"string"==typeof t.i?e.i=parseInt(t.i,10):"number"==typeof t.i?e.i=t.i:"object"==typeof t.i&&(e.i=new l.LongBits(t.i.low>>>0,t.i.high>>>0).toNumber())),null!=t.s&&("string"==typeof t.s?l.base64.decode(t.s,e.s=l.newBuffer(l.base64.length(t.s)),0):t.s.length&&(e.s=t.s)),null!=t.t){if("object"!=typeof t.t)throw TypeError(".onnx.AttributeProto.t: object expected");e.t=c.onnx.TensorProto.fromObject(t.t)}if(null!=t.g){if("object"!=typeof t.g)throw TypeError(".onnx.AttributeProto.g: object expected");e.g=c.onnx.GraphProto.fromObject(t.g)}if(t.floats){if(!Array.isArray(t.floats))throw TypeError(".onnx.AttributeProto.floats: array expected");e.floats=[];for(var n=0;n<t.floats.length;++n)e.floats[n]=Number(t.floats[n])}if(t.ints){if(!Array.isArray(t.ints))throw TypeError(".onnx.AttributeProto.ints: array expected");for(e.ints=[],n=0;n<t.ints.length;++n)l.Long?(e.ints[n]=l.Long.fromValue(t.ints[n])).unsigned=!1:"string"==typeof t.ints[n]?e.ints[n]=parseInt(t.ints[n],10):"number"==typeof t.ints[n]?e.ints[n]=t.ints[n]:"object"==typeof t.ints[n]&&(e.ints[n]=new l.LongBits(t.ints[n].low>>>0,t.ints[n].high>>>0).toNumber())}if(t.strings){if(!Array.isArray(t.strings))throw TypeError(".onnx.AttributeProto.strings: array expected");for(e.strings=[],n=0;n<t.strings.length;++n)"string"==typeof t.strings[n]?l.base64.decode(t.strings[n],e.strings[n]=l.newBuffer(l.base64.length(t.strings[n])),0):t.strings[n].length&&(e.strings[n]=t.strings[n])}if(t.tensors){if(!Array.isArray(t.tensors))throw TypeError(".onnx.AttributeProto.tensors: array expected");for(e.tensors=[],n=0;n<t.tensors.length;++n){if("object"!=typeof t.tensors[n])throw TypeError(".onnx.AttributeProto.tensors: object expected");e.tensors[n]=c.onnx.TensorProto.fromObject(t.tensors[n])}}if(t.graphs){if(!Array.isArray(t.graphs))throw TypeError(".onnx.AttributeProto.graphs: array expected");for(e.graphs=[],n=0;n<t.graphs.length;++n){if("object"!=typeof t.graphs[n])throw TypeError(".onnx.AttributeProto.graphs: object expected");e.graphs[n]=c.onnx.GraphProto.fromObject(t.graphs[n])}}return e},t.toObject=function(t,e){e||(e={});var n={};if((e.arrays||e.defaults)&&(n.floats=[],n.ints=[],n.strings=[],n.tensors=[],n.graphs=[]),e.defaults){if(n.name="",n.f=0,l.Long){var r=new l.Long(0,0,!1);n.i=e.longs===String?r.toString():e.longs===Number?r.toNumber():r}else n.i=e.longs===String?"0":0;e.bytes===String?n.s="":(n.s=[],e.bytes!==Array&&(n.s=l.newBuffer(n.s))),n.t=null,n.g=null,n.docString="",n.type=e.enums===String?"UNDEFINED":0,n.refAttrName=""}if(null!=t.name&&t.hasOwnProperty("name")&&(n.name=t.name),null!=t.f&&t.hasOwnProperty("f")&&(n.f=e.json&&!isFinite(t.f)?String(t.f):t.f),null!=t.i&&t.hasOwnProperty("i")&&("number"==typeof t.i?n.i=e.longs===String?String(t.i):t.i:n.i=e.longs===String?l.Long.prototype.toString.call(t.i):e.longs===Number?new l.LongBits(t.i.low>>>0,t.i.high>>>0).toNumber():t.i),null!=t.s&&t.hasOwnProperty("s")&&(n.s=e.bytes===String?l.base64.encode(t.s,0,t.s.length):e.bytes===Array?Array.prototype.slice.call(t.s):t.s),null!=t.t&&t.hasOwnProperty("t")&&(n.t=c.onnx.TensorProto.toObject(t.t,e)),null!=t.g&&t.hasOwnProperty("g")&&(n.g=c.onnx.GraphProto.toObject(t.g,e)),t.floats&&t.floats.length){n.floats=[];for(var i=0;i<t.floats.length;++i)n.floats[i]=e.json&&!isFinite(t.floats[i])?String(t.floats[i]):t.floats[i]}if(t.ints&&t.ints.length)for(n.ints=[],i=0;i<t.ints.length;++i)"number"==typeof t.ints[i]?n.ints[i]=e.longs===String?String(t.ints[i]):t.ints[i]:n.ints[i]=e.longs===String?l.Long.prototype.toString.call(t.ints[i]):e.longs===Number?new l.LongBits(t.ints[i].low>>>0,t.ints[i].high>>>0).toNumber():t.ints[i];if(t.strings&&t.strings.length)for(n.strings=[],i=0;i<t.strings.length;++i)n.strings[i]=e.bytes===String?l.base64.encode(t.strings[i],0,t.strings[i].length):e.bytes===Array?Array.prototype.slice.call(t.strings[i]):t.strings[i];if(t.tensors&&t.tensors.length)for(n.tensors=[],i=0;i<t.tensors.length;++i)n.tensors[i]=c.onnx.TensorProto.toObject(t.tensors[i],e);if(t.graphs&&t.graphs.length)for(n.graphs=[],i=0;i<t.graphs.length;++i)n.graphs[i]=c.onnx.GraphProto.toObject(t.graphs[i],e);return null!=t.docString&&t.hasOwnProperty("docString")&&(n.docString=t.docString),null!=t.type&&t.hasOwnProperty("type")&&(n.type=e.enums===String?c.onnx.AttributeProto.AttributeType[t.type]:t.type),null!=t.refAttrName&&t.hasOwnProperty("refAttrName")&&(n.refAttrName=t.refAttrName),n},t.prototype.toJSON=function(){return this.constructor.toObject(this,s.util.toJSONOptions)},t.AttributeType=function(){var t={},e=Object.create(t);return e[t[0]="UNDEFINED"]=0,e[t[1]="FLOAT"]=1,e[t[2]="INT"]=2,e[t[3]="STRING"]=3,e[t[4]="TENSOR"]=4,e[t[5]="GRAPH"]=5,e[t[6]="FLOATS"]=6,e[t[7]="INTS"]=7,e[t[8]="STRINGS"]=8,e[t[9]="TENSORS"]=9,e[t[10]="GRAPHS"]=10,e}(),t}(),o.ValueInfoProto=function(){function t(t){if(t)for(var e=Object.keys(t),n=0;n<e.length;++n)null!=t[e[n]]&&(this[e[n]]=t[e[n]])}return t.prototype.name="",t.prototype.type=null,t.prototype.docString="",t.create=function(e){return new t(e)},t.encode=function(t,e){return e||(e=u.create()),null!=t.name&&t.hasOwnProperty("name")&&e.uint32(10).string(t.name),null!=t.type&&t.hasOwnProperty("type")&&c.onnx.TypeProto.encode(t.type,e.uint32(18).fork()).ldelim(),null!=t.docString&&t.hasOwnProperty("docString")&&e.uint32(26).string(t.docString),e},t.encodeDelimited=function(t,e){return this.encode(t,e).ldelim()},t.decode=function(t,e){t instanceof a||(t=a.create(t));for(var n=void 0===e?t.len:t.pos+e,r=new c.onnx.ValueInfoProto;t.pos<n;){var i=t.uint32();switch(i>>>3){case 1:r.name=t.string();break;case 2:r.type=c.onnx.TypeProto.decode(t,t.uint32());break;case 3:r.docString=t.string();break;default:t.skipType(7&i)}}return r},t.decodeDelimited=function(t){return t instanceof a||(t=new a(t)),this.decode(t,t.uint32())},t.verify=function(t){if("object"!=typeof t||null===t)return"object expected";if(null!=t.name&&t.hasOwnProperty("name")&&!l.isString(t.name))return"name: string expected";if(null!=t.type&&t.hasOwnProperty("type")){var e=c.onnx.TypeProto.verify(t.type);if(e)return"type."+e}return null!=t.docString&&t.hasOwnProperty("docString")&&!l.isString(t.docString)?"docString: string expected":null},t.fromObject=function(t){if(t instanceof c.onnx.ValueInfoProto)return t;var e=new c.onnx.ValueInfoProto;if(null!=t.name&&(e.name=String(t.name)),null!=t.type){if("object"!=typeof t.type)throw TypeError(".onnx.ValueInfoProto.type: object expected");e.type=c.onnx.TypeProto.fromObject(t.type)}return null!=t.docString&&(e.docString=String(t.docString)),e},t.toObject=function(t,e){e||(e={});var n={};return e.defaults&&(n.name="",n.type=null,n.docString=""),null!=t.name&&t.hasOwnProperty("name")&&(n.name=t.name),null!=t.type&&t.hasOwnProperty("type")&&(n.type=c.onnx.TypeProto.toObject(t.type,e)),null!=t.docString&&t.hasOwnProperty("docString")&&(n.docString=t.docString),n},t.prototype.toJSON=function(){return this.constructor.toObject(this,s.util.toJSONOptions)},t}(),o.NodeProto=function(){function t(t){if(this.input=[],this.output=[],this.attribute=[],t)for(var e=Object.keys(t),n=0;n<e.length;++n)null!=t[e[n]]&&(this[e[n]]=t[e[n]])}return t.prototype.input=l.emptyArray,t.prototype.output=l.emptyArray,t.prototype.name="",t.prototype.opType="",t.prototype.domain="",t.prototype.attribute=l.emptyArray,t.prototype.docString="",t.create=function(e){return new t(e)},t.encode=function(t,e){if(e||(e=u.create()),null!=t.input&&t.input.length)for(var n=0;n<t.input.length;++n)e.uint32(10).string(t.input[n]);if(null!=t.output&&t.output.length)for(n=0;n<t.output.length;++n)e.uint32(18).string(t.output[n]);if(null!=t.name&&t.hasOwnProperty("name")&&e.uint32(26).string(t.name),null!=t.opType&&t.hasOwnProperty("opType")&&e.uint32(34).string(t.opType),null!=t.attribute&&t.attribute.length)for(n=0;n<t.attribute.length;++n)c.onnx.AttributeProto.encode(t.attribute[n],e.uint32(42).fork()).ldelim();return null!=t.docString&&t.hasOwnProperty("docString")&&e.uint32(50).string(t.docString),null!=t.domain&&t.hasOwnProperty("domain")&&e.uint32(58).string(t.domain),e},t.encodeDelimited=function(t,e){return this.encode(t,e).ldelim()},t.decode=function(t,e){t instanceof a||(t=a.create(t));for(var n=void 0===e?t.len:t.pos+e,r=new c.onnx.NodeProto;t.pos<n;){var i=t.uint32();switch(i>>>3){case 1:r.input&&r.input.length||(r.input=[]),r.input.push(t.string());break;case 2:r.output&&r.output.length||(r.output=[]),r.output.push(t.string());break;case 3:r.name=t.string();break;case 4:r.opType=t.string();break;case 7:r.domain=t.string();break;case 5:r.attribute&&r.attribute.length||(r.attribute=[]),r.attribute.push(c.onnx.AttributeProto.decode(t,t.uint32()));break;case 6:r.docString=t.string();break;default:t.skipType(7&i)}}return r},t.decodeDelimited=function(t){return t instanceof a||(t=new a(t)),this.decode(t,t.uint32())},t.verify=function(t){if("object"!=typeof t||null===t)return"object expected";if(null!=t.input&&t.hasOwnProperty("input")){if(!Array.isArray(t.input))return"input: array expected";for(var e=0;e<t.input.length;++e)if(!l.isString(t.input[e]))return"input: string[] expected"}if(null!=t.output&&t.hasOwnProperty("output")){if(!Array.isArray(t.output))return"output: array expected";for(e=0;e<t.output.length;++e)if(!l.isString(t.output[e]))return"output: string[] expected"}if(null!=t.name&&t.hasOwnProperty("name")&&!l.isString(t.name))return"name: string expected";if(null!=t.opType&&t.hasOwnProperty("opType")&&!l.isString(t.opType))return"opType: string expected";if(null!=t.domain&&t.hasOwnProperty("domain")&&!l.isString(t.domain))return"domain: string expected";if(null!=t.attribute&&t.hasOwnProperty("attribute")){if(!Array.isArray(t.attribute))return"attribute: array expected";for(e=0;e<t.attribute.length;++e){var n=c.onnx.AttributeProto.verify(t.attribute[e]);if(n)return"attribute."+n}}return null!=t.docString&&t.hasOwnProperty("docString")&&!l.isString(t.docString)?"docString: string expected":null},t.fromObject=function(t){if(t instanceof c.onnx.NodeProto)return t;var e=new c.onnx.NodeProto;if(t.input){if(!Array.isArray(t.input))throw TypeError(".onnx.NodeProto.input: array expected");e.input=[];for(var n=0;n<t.input.length;++n)e.input[n]=String(t.input[n])}if(t.output){if(!Array.isArray(t.output))throw TypeError(".onnx.NodeProto.output: array expected");for(e.output=[],n=0;n<t.output.length;++n)e.output[n]=String(t.output[n])}if(null!=t.name&&(e.name=String(t.name)),null!=t.opType&&(e.opType=String(t.opType)),null!=t.domain&&(e.domain=String(t.domain)),t.attribute){if(!Array.isArray(t.attribute))throw TypeError(".onnx.NodeProto.attribute: array expected");for(e.attribute=[],n=0;n<t.attribute.length;++n){if("object"!=typeof t.attribute[n])throw TypeError(".onnx.NodeProto.attribute: object expected");e.attribute[n]=c.onnx.AttributeProto.fromObject(t.attribute[n])}}return null!=t.docString&&(e.docString=String(t.docString)),e},t.toObject=function(t,e){e||(e={});var n={};if((e.arrays||e.defaults)&&(n.input=[],n.output=[],n.attribute=[]),e.defaults&&(n.name="",n.opType="",n.docString="",n.domain=""),t.input&&t.input.length){n.input=[];for(var r=0;r<t.input.length;++r)n.input[r]=t.input[r]}if(t.output&&t.output.length)for(n.output=[],r=0;r<t.output.length;++r)n.output[r]=t.output[r];if(null!=t.name&&t.hasOwnProperty("name")&&(n.name=t.name),null!=t.opType&&t.hasOwnProperty("opType")&&(n.opType=t.opType),t.attribute&&t.attribute.length)for(n.attribute=[],r=0;r<t.attribute.length;++r)n.attribute[r]=c.onnx.AttributeProto.toObject(t.attribute[r],e);return null!=t.docString&&t.hasOwnProperty("docString")&&(n.docString=t.docString),null!=t.domain&&t.hasOwnProperty("domain")&&(n.domain=t.domain),n},t.prototype.toJSON=function(){return this.constructor.toObject(this,s.util.toJSONOptions)},t}(),o.ModelProto=function(){function t(t){if(this.opsetImport=[],this.metadataProps=[],t)for(var e=Object.keys(t),n=0;n<e.length;++n)null!=t[e[n]]&&(this[e[n]]=t[e[n]])}return t.prototype.irVersion=l.Long?l.Long.fromBits(0,0,!1):0,t.prototype.opsetImport=l.emptyArray,t.prototype.producerName="",t.prototype.producerVersion="",t.prototype.domain="",t.prototype.modelVersion=l.Long?l.Long.fromBits(0,0,!1):0,t.prototype.docString="",t.prototype.graph=null,t.prototype.metadataProps=l.emptyArray,t.create=function(e){return new t(e)},t.encode=function(t,e){if(e||(e=u.create()),null!=t.irVersion&&t.hasOwnProperty("irVersion")&&e.uint32(8).int64(t.irVersion),null!=t.producerName&&t.hasOwnProperty("producerName")&&e.uint32(18).string(t.producerName),null!=t.producerVersion&&t.hasOwnProperty("producerVersion")&&e.uint32(26).string(t.producerVersion),null!=t.domain&&t.hasOwnProperty("domain")&&e.uint32(34).string(t.domain),null!=t.modelVersion&&t.hasOwnProperty("modelVersion")&&e.uint32(40).int64(t.modelVersion),null!=t.docString&&t.hasOwnProperty("docString")&&e.uint32(50).string(t.docString),null!=t.graph&&t.hasOwnProperty("graph")&&c.onnx.GraphProto.encode(t.graph,e.uint32(58).fork()).ldelim(),null!=t.opsetImport&&t.opsetImport.length)for(var n=0;n<t.opsetImport.length;++n)c.onnx.OperatorSetIdProto.encode(t.opsetImport[n],e.uint32(66).fork()).ldelim();if(null!=t.metadataProps&&t.metadataProps.length)for(n=0;n<t.metadataProps.length;++n)c.onnx.StringStringEntryProto.encode(t.metadataProps[n],e.uint32(114).fork()).ldelim();return e},t.encodeDelimited=function(t,e){return this.encode(t,e).ldelim()},t.decode=function(t,e){t instanceof a||(t=a.create(t));for(var n=void 0===e?t.len:t.pos+e,r=new c.onnx.ModelProto;t.pos<n;){var i=t.uint32();switch(i>>>3){case 1:r.irVersion=t.int64();break;case 8:r.opsetImport&&r.opsetImport.length||(r.opsetImport=[]),r.opsetImport.push(c.onnx.OperatorSetIdProto.decode(t,t.uint32()));break;case 2:r.producerName=t.string();break;case 3:r.producerVersion=t.string();break;case 4:r.domain=t.string();break;case 5:r.modelVersion=t.int64();break;case 6:r.docString=t.string();break;case 7:r.graph=c.onnx.GraphProto.decode(t,t.uint32());break;case 14:r.metadataProps&&r.metadataProps.length||(r.metadataProps=[]),r.metadataProps.push(c.onnx.StringStringEntryProto.decode(t,t.uint32()));break;default:t.skipType(7&i)}}return r},t.decodeDelimited=function(t){return t instanceof a||(t=new a(t)),this.decode(t,t.uint32())},t.verify=function(t){if("object"!=typeof t||null===t)return"object expected";if(null!=t.irVersion&&t.hasOwnProperty("irVersion")&&!(l.isInteger(t.irVersion)||t.irVersion&&l.isInteger(t.irVersion.low)&&l.isInteger(t.irVersion.high)))return"irVersion: integer|Long expected";if(null!=t.opsetImport&&t.hasOwnProperty("opsetImport")){if(!Array.isArray(t.opsetImport))return"opsetImport: array expected";for(var e=0;e<t.opsetImport.length;++e)if(n=c.onnx.OperatorSetIdProto.verify(t.opsetImport[e]))return"opsetImport."+n}if(null!=t.producerName&&t.hasOwnProperty("producerName")&&!l.isString(t.producerName))return"producerName: string expected";if(null!=t.producerVersion&&t.hasOwnProperty("producerVersion")&&!l.isString(t.producerVersion))return"producerVersion: string expected";if(null!=t.domain&&t.hasOwnProperty("domain")&&!l.isString(t.domain))return"domain: string expected";if(null!=t.modelVersion&&t.hasOwnProperty("modelVersion")&&!(l.isInteger(t.modelVersion)||t.modelVersion&&l.isInteger(t.modelVersion.low)&&l.isInteger(t.modelVersion.high)))return"modelVersion: integer|Long expected";if(null!=t.docString&&t.hasOwnProperty("docString")&&!l.isString(t.docString))return"docString: string expected";if(null!=t.graph&&t.hasOwnProperty("graph")&&(n=c.onnx.GraphProto.verify(t.graph)))return"graph."+n;if(null!=t.metadataProps&&t.hasOwnProperty("metadataProps")){if(!Array.isArray(t.metadataProps))return"metadataProps: array expected";for(e=0;e<t.metadataProps.length;++e){var n;if(n=c.onnx.StringStringEntryProto.verify(t.metadataProps[e]))return"metadataProps."+n}}return null},t.fromObject=function(t){if(t instanceof c.onnx.ModelProto)return t;var e=new c.onnx.ModelProto;if(null!=t.irVersion&&(l.Long?(e.irVersion=l.Long.fromValue(t.irVersion)).unsigned=!1:"string"==typeof t.irVersion?e.irVersion=parseInt(t.irVersion,10):"number"==typeof t.irVersion?e.irVersion=t.irVersion:"object"==typeof t.irVersion&&(e.irVersion=new l.LongBits(t.irVersion.low>>>0,t.irVersion.high>>>0).toNumber())),t.opsetImport){if(!Array.isArray(t.opsetImport))throw TypeError(".onnx.ModelProto.opsetImport: array expected");e.opsetImport=[];for(var n=0;n<t.opsetImport.length;++n){if("object"!=typeof t.opsetImport[n])throw TypeError(".onnx.ModelProto.opsetImport: object expected");e.opsetImport[n]=c.onnx.OperatorSetIdProto.fromObject(t.opsetImport[n])}}if(null!=t.producerName&&(e.producerName=String(t.producerName)),null!=t.producerVersion&&(e.producerVersion=String(t.producerVersion)),null!=t.domain&&(e.domain=String(t.domain)),null!=t.modelVersion&&(l.Long?(e.modelVersion=l.Long.fromValue(t.modelVersion)).unsigned=!1:"string"==typeof t.modelVersion?e.modelVersion=parseInt(t.modelVersion,10):"number"==typeof t.modelVersion?e.modelVersion=t.modelVersion:"object"==typeof t.modelVersion&&(e.modelVersion=new l.LongBits(t.modelVersion.low>>>0,t.modelVersion.high>>>0).toNumber())),null!=t.docString&&(e.docString=String(t.docString)),null!=t.graph){if("object"!=typeof t.graph)throw TypeError(".onnx.ModelProto.graph: object expected");e.graph=c.onnx.GraphProto.fromObject(t.graph)}if(t.metadataProps){if(!Array.isArray(t.metadataProps))throw TypeError(".onnx.ModelProto.metadataProps: array expected");for(e.metadataProps=[],n=0;n<t.metadataProps.length;++n){if("object"!=typeof t.metadataProps[n])throw TypeError(".onnx.ModelProto.metadataProps: object expected");e.metadataProps[n]=c.onnx.StringStringEntryProto.fromObject(t.metadataProps[n])}}return e},t.toObject=function(t,e){e||(e={});var n={};if((e.arrays||e.defaults)&&(n.opsetImport=[],n.metadataProps=[]),e.defaults){if(l.Long){var r=new l.Long(0,0,!1);n.irVersion=e.longs===String?r.toString():e.longs===Number?r.toNumber():r}else n.irVersion=e.longs===String?"0":0;n.producerName="",n.producerVersion="",n.domain="",l.Long?(r=new l.Long(0,0,!1),n.modelVersion=e.longs===String?r.toString():e.longs===Number?r.toNumber():r):n.modelVersion=e.longs===String?"0":0,n.docString="",n.graph=null}if(null!=t.irVersion&&t.hasOwnProperty("irVersion")&&("number"==typeof t.irVersion?n.irVersion=e.longs===String?String(t.irVersion):t.irVersion:n.irVersion=e.longs===String?l.Long.prototype.toString.call(t.irVersion):e.longs===Number?new l.LongBits(t.irVersion.low>>>0,t.irVersion.high>>>0).toNumber():t.irVersion),null!=t.producerName&&t.hasOwnProperty("producerName")&&(n.producerName=t.producerName),null!=t.producerVersion&&t.hasOwnProperty("producerVersion")&&(n.producerVersion=t.producerVersion),null!=t.domain&&t.hasOwnProperty("domain")&&(n.domain=t.domain),null!=t.modelVersion&&t.hasOwnProperty("modelVersion")&&("number"==typeof t.modelVersion?n.modelVersion=e.longs===String?String(t.modelVersion):t.modelVersion:n.modelVersion=e.longs===String?l.Long.prototype.toString.call(t.modelVersion):e.longs===Number?new l.LongBits(t.modelVersion.low>>>0,t.modelVersion.high>>>0).toNumber():t.modelVersion),null!=t.docString&&t.hasOwnProperty("docString")&&(n.docString=t.docString),null!=t.graph&&t.hasOwnProperty("graph")&&(n.graph=c.onnx.GraphProto.toObject(t.graph,e)),t.opsetImport&&t.opsetImport.length){n.opsetImport=[];for(var i=0;i<t.opsetImport.length;++i)n.opsetImport[i]=c.onnx.OperatorSetIdProto.toObject(t.opsetImport[i],e)}if(t.metadataProps&&t.metadataProps.length)for(n.metadataProps=[],i=0;i<t.metadataProps.length;++i)n.metadataProps[i]=c.onnx.StringStringEntryProto.toObject(t.metadataProps[i],e);return n},t.prototype.toJSON=function(){return this.constructor.toObject(this,s.util.toJSONOptions)},t}(),o.StringStringEntryProto=function(){function t(t){if(t)for(var e=Object.keys(t),n=0;n<e.length;++n)null!=t[e[n]]&&(this[e[n]]=t[e[n]])}return t.prototype.key="",t.prototype.value="",t.create=function(e){return new t(e)},t.encode=function(t,e){return e||(e=u.create()),null!=t.key&&t.hasOwnProperty("key")&&e.uint32(10).string(t.key),null!=t.value&&t.hasOwnProperty("value")&&e.uint32(18).string(t.value),e},t.encodeDelimited=function(t,e){return this.encode(t,e).ldelim()},t.decode=function(t,e){t instanceof a||(t=a.create(t));for(var n=void 0===e?t.len:t.pos+e,r=new c.onnx.StringStringEntryProto;t.pos<n;){var i=t.uint32();switch(i>>>3){case 1:r.key=t.string();break;case 2:r.value=t.string();break;default:t.skipType(7&i)}}return r},t.decodeDelimited=function(t){return t instanceof a||(t=new a(t)),this.decode(t,t.uint32())},t.verify=function(t){return"object"!=typeof t||null===t?"object expected":null!=t.key&&t.hasOwnProperty("key")&&!l.isString(t.key)?"key: string expected":null!=t.value&&t.hasOwnProperty("value")&&!l.isString(t.value)?"value: string expected":null},t.fromObject=function(t){if(t instanceof c.onnx.StringStringEntryProto)return t;var e=new c.onnx.StringStringEntryProto;return null!=t.key&&(e.key=String(t.key)),null!=t.value&&(e.value=String(t.value)),e},t.toObject=function(t,e){e||(e={});var n={};return e.defaults&&(n.key="",n.value=""),null!=t.key&&t.hasOwnProperty("key")&&(n.key=t.key),null!=t.value&&t.hasOwnProperty("value")&&(n.value=t.value),n},t.prototype.toJSON=function(){return this.constructor.toObject(this,s.util.toJSONOptions)},t}(),o.TensorAnnotation=function(){function t(t){if(this.quantParameterTensorNames=[],t)for(var e=Object.keys(t),n=0;n<e.length;++n)null!=t[e[n]]&&(this[e[n]]=t[e[n]])}return t.prototype.tensorName="",t.prototype.quantParameterTensorNames=l.emptyArray,t.create=function(e){return new t(e)},t.encode=function(t,e){if(e||(e=u.create()),null!=t.tensorName&&t.hasOwnProperty("tensorName")&&e.uint32(10).string(t.tensorName),null!=t.quantParameterTensorNames&&t.quantParameterTensorNames.length)for(var n=0;n<t.quantParameterTensorNames.length;++n)c.onnx.StringStringEntryProto.encode(t.quantParameterTensorNames[n],e.uint32(18).fork()).ldelim();return e},t.encodeDelimited=function(t,e){return this.encode(t,e).ldelim()},t.decode=function(t,e){t instanceof a||(t=a.create(t));for(var n=void 0===e?t.len:t.pos+e,r=new c.onnx.TensorAnnotation;t.pos<n;){var i=t.uint32();switch(i>>>3){case 1:r.tensorName=t.string();break;case 2:r.quantParameterTensorNames&&r.quantParameterTensorNames.length||(r.quantParameterTensorNames=[]),r.quantParameterTensorNames.push(c.onnx.StringStringEntryProto.decode(t,t.uint32()));break;default:t.skipType(7&i)}}return r},t.decodeDelimited=function(t){return t instanceof a||(t=new a(t)),this.decode(t,t.uint32())},t.verify=function(t){if("object"!=typeof t||null===t)return"object expected";if(null!=t.tensorName&&t.hasOwnProperty("tensorName")&&!l.isString(t.tensorName))return"tensorName: string expected";if(null!=t.quantParameterTensorNames&&t.hasOwnProperty("quantParameterTensorNames")){if(!Array.isArray(t.quantParameterTensorNames))return"quantParameterTensorNames: array expected";for(var e=0;e<t.quantParameterTensorNames.length;++e){var n=c.onnx.StringStringEntryProto.verify(t.quantParameterTensorNames[e]);if(n)return"quantParameterTensorNames."+n}}return null},t.fromObject=function(t){if(t instanceof c.onnx.TensorAnnotation)return t;var e=new c.onnx.TensorAnnotation;if(null!=t.tensorName&&(e.tensorName=String(t.tensorName)),t.quantParameterTensorNames){if(!Array.isArray(t.quantParameterTensorNames))throw TypeError(".onnx.TensorAnnotation.quantParameterTensorNames: array expected");e.quantParameterTensorNames=[];for(var n=0;n<t.quantParameterTensorNames.length;++n){if("object"!=typeof t.quantParameterTensorNames[n])throw TypeError(".onnx.TensorAnnotation.quantParameterTensorNames: object expected");e.quantParameterTensorNames[n]=c.onnx.StringStringEntryProto.fromObject(t.quantParameterTensorNames[n])}}return e},t.toObject=function(t,e){e||(e={});var n={};if((e.arrays||e.defaults)&&(n.quantParameterTensorNames=[]),e.defaults&&(n.tensorName=""),null!=t.tensorName&&t.hasOwnProperty("tensorName")&&(n.tensorName=t.tensorName),t.quantParameterTensorNames&&t.quantParameterTensorNames.length){n.quantParameterTensorNames=[];for(var r=0;r<t.quantParameterTensorNames.length;++r)n.quantParameterTensorNames[r]=c.onnx.StringStringEntryProto.toObject(t.quantParameterTensorNames[r],e)}return n},t.prototype.toJSON=function(){return this.constructor.toObject(this,s.util.toJSONOptions)},t}(),o.GraphProto=function(){function t(t){if(this.node=[],this.initializer=[],this.input=[],this.output=[],this.valueInfo=[],this.quantizationAnnotation=[],t)for(var e=Object.keys(t),n=0;n<e.length;++n)null!=t[e[n]]&&(this[e[n]]=t[e[n]])}return t.prototype.node=l.emptyArray,t.prototype.name="",t.prototype.initializer=l.emptyArray,t.prototype.docString="",t.prototype.input=l.emptyArray,t.prototype.output=l.emptyArray,t.prototype.valueInfo=l.emptyArray,t.prototype.quantizationAnnotation=l.emptyArray,t.create=function(e){return new t(e)},t.encode=function(t,e){if(e||(e=u.create()),null!=t.node&&t.node.length)for(var n=0;n<t.node.length;++n)c.onnx.NodeProto.encode(t.node[n],e.uint32(10).fork()).ldelim();if(null!=t.name&&t.hasOwnProperty("name")&&e.uint32(18).string(t.name),null!=t.initializer&&t.initializer.length)for(n=0;n<t.initializer.length;++n)c.onnx.TensorProto.encode(t.initializer[n],e.uint32(42).fork()).ldelim();if(null!=t.docString&&t.hasOwnProperty("docString")&&e.uint32(82).string(t.docString),null!=t.input&&t.input.length)for(n=0;n<t.input.length;++n)c.onnx.ValueInfoProto.encode(t.input[n],e.uint32(90).fork()).ldelim();if(null!=t.output&&t.output.length)for(n=0;n<t.output.length;++n)c.onnx.ValueInfoProto.encode(t.output[n],e.uint32(98).fork()).ldelim();if(null!=t.valueInfo&&t.valueInfo.length)for(n=0;n<t.valueInfo.length;++n)c.onnx.ValueInfoProto.encode(t.valueInfo[n],e.uint32(106).fork()).ldelim();if(null!=t.quantizationAnnotation&&t.quantizationAnnotation.length)for(n=0;n<t.quantizationAnnotation.length;++n)c.onnx.TensorAnnotation.encode(t.quantizationAnnotation[n],e.uint32(114).fork()).ldelim();return e},t.encodeDelimited=function(t,e){return this.encode(t,e).ldelim()},t.decode=function(t,e){t instanceof a||(t=a.create(t));for(var n=void 0===e?t.len:t.pos+e,r=new c.onnx.GraphProto;t.pos<n;){var i=t.uint32();switch(i>>>3){case 1:r.node&&r.node.length||(r.node=[]),r.node.push(c.onnx.NodeProto.decode(t,t.uint32()));break;case 2:r.name=t.string();break;case 5:r.initializer&&r.initializer.length||(r.initializer=[]),r.initializer.push(c.onnx.TensorProto.decode(t,t.uint32()));break;case 10:r.docString=t.string();break;case 11:r.input&&r.input.length||(r.input=[]),r.input.push(c.onnx.ValueInfoProto.decode(t,t.uint32()));break;case 12:r.output&&r.output.length||(r.output=[]),r.output.push(c.onnx.ValueInfoProto.decode(t,t.uint32()));break;case 13:r.valueInfo&&r.valueInfo.length||(r.valueInfo=[]),r.valueInfo.push(c.onnx.ValueInfoProto.decode(t,t.uint32()));break;case 14:r.quantizationAnnotation&&r.quantizationAnnotation.length||(r.quantizationAnnotation=[]),r.quantizationAnnotation.push(c.onnx.TensorAnnotation.decode(t,t.uint32()));break;default:t.skipType(7&i)}}return r},t.decodeDelimited=function(t){return t instanceof a||(t=new a(t)),this.decode(t,t.uint32())},t.verify=function(t){if("object"!=typeof t||null===t)return"object expected";if(null!=t.node&&t.hasOwnProperty("node")){if(!Array.isArray(t.node))return"node: array expected";for(var e=0;e<t.node.length;++e)if(n=c.onnx.NodeProto.verify(t.node[e]))return"node."+n}if(null!=t.name&&t.hasOwnProperty("name")&&!l.isString(t.name))return"name: string expected";if(null!=t.initializer&&t.hasOwnProperty("initializer")){if(!Array.isArray(t.initializer))return"initializer: array expected";for(e=0;e<t.initializer.length;++e)if(n=c.onnx.TensorProto.verify(t.initializer[e]))return"initializer."+n}if(null!=t.docString&&t.hasOwnProperty("docString")&&!l.isString(t.docString))return"docString: string expected";if(null!=t.input&&t.hasOwnProperty("input")){if(!Array.isArray(t.input))return"input: array expected";for(e=0;e<t.input.length;++e)if(n=c.onnx.ValueInfoProto.verify(t.input[e]))return"input."+n}if(null!=t.output&&t.hasOwnProperty("output")){if(!Array.isArray(t.output))return"output: array expected";for(e=0;e<t.output.length;++e)if(n=c.onnx.ValueInfoProto.verify(t.output[e]))return"output."+n}if(null!=t.valueInfo&&t.hasOwnProperty("valueInfo")){if(!Array.isArray(t.valueInfo))return"valueInfo: array expected";for(e=0;e<t.valueInfo.length;++e)if(n=c.onnx.ValueInfoProto.verify(t.valueInfo[e]))return"valueInfo."+n}if(null!=t.quantizationAnnotation&&t.hasOwnProperty("quantizationAnnotation")){if(!Array.isArray(t.quantizationAnnotation))return"quantizationAnnotation: array expected";for(e=0;e<t.quantizationAnnotation.length;++e){var n;if(n=c.onnx.TensorAnnotation.verify(t.quantizationAnnotation[e]))return"quantizationAnnotation."+n}}return null},t.fromObject=function(t){if(t instanceof c.onnx.GraphProto)return t;var e=new c.onnx.GraphProto;if(t.node){if(!Array.isArray(t.node))throw TypeError(".onnx.GraphProto.node: array expected");e.node=[];for(var n=0;n<t.node.length;++n){if("object"!=typeof t.node[n])throw TypeError(".onnx.GraphProto.node: object expected");e.node[n]=c.onnx.NodeProto.fromObject(t.node[n])}}if(null!=t.name&&(e.name=String(t.name)),t.initializer){if(!Array.isArray(t.initializer))throw TypeError(".onnx.GraphProto.initializer: array expected");for(e.initializer=[],n=0;n<t.initializer.length;++n){if("object"!=typeof t.initializer[n])throw TypeError(".onnx.GraphProto.initializer: object expected");e.initializer[n]=c.onnx.TensorProto.fromObject(t.initializer[n])}}if(null!=t.docString&&(e.docString=String(t.docString)),t.input){if(!Array.isArray(t.input))throw TypeError(".onnx.GraphProto.input: array expected");for(e.input=[],n=0;n<t.input.length;++n){if("object"!=typeof t.input[n])throw TypeError(".onnx.GraphProto.input: object expected");e.input[n]=c.onnx.ValueInfoProto.fromObject(t.input[n])}}if(t.output){if(!Array.isArray(t.output))throw TypeError(".onnx.GraphProto.output: array expected");for(e.output=[],n=0;n<t.output.length;++n){if("object"!=typeof t.output[n])throw TypeError(".onnx.GraphProto.output: object expected");e.output[n]=c.onnx.ValueInfoProto.fromObject(t.output[n])}}if(t.valueInfo){if(!Array.isArray(t.valueInfo))throw TypeError(".onnx.GraphProto.valueInfo: array expected");for(e.valueInfo=[],n=0;n<t.valueInfo.length;++n){if("object"!=typeof t.valueInfo[n])throw TypeError(".onnx.GraphProto.valueInfo: object expected");e.valueInfo[n]=c.onnx.ValueInfoProto.fromObject(t.valueInfo[n])}}if(t.quantizationAnnotation){if(!Array.isArray(t.quantizationAnnotation))throw TypeError(".onnx.GraphProto.quantizationAnnotation: array expected");for(e.quantizationAnnotation=[],n=0;n<t.quantizationAnnotation.length;++n){if("object"!=typeof t.quantizationAnnotation[n])throw TypeError(".onnx.GraphProto.quantizationAnnotation: object expected");e.quantizationAnnotation[n]=c.onnx.TensorAnnotation.fromObject(t.quantizationAnnotation[n])}}return e},t.toObject=function(t,e){e||(e={});var n={};if((e.arrays||e.defaults)&&(n.node=[],n.initializer=[],n.input=[],n.output=[],n.valueInfo=[],n.quantizationAnnotation=[]),e.defaults&&(n.name="",n.docString=""),t.node&&t.node.length){n.node=[];for(var r=0;r<t.node.length;++r)n.node[r]=c.onnx.NodeProto.toObject(t.node[r],e)}if(null!=t.name&&t.hasOwnProperty("name")&&(n.name=t.name),t.initializer&&t.initializer.length)for(n.initializer=[],r=0;r<t.initializer.length;++r)n.initializer[r]=c.onnx.TensorProto.toObject(t.initializer[r],e);if(null!=t.docString&&t.hasOwnProperty("docString")&&(n.docString=t.docString),t.input&&t.input.length)for(n.input=[],r=0;r<t.input.length;++r)n.input[r]=c.onnx.ValueInfoProto.toObject(t.input[r],e);if(t.output&&t.output.length)for(n.output=[],r=0;r<t.output.length;++r)n.output[r]=c.onnx.ValueInfoProto.toObject(t.output[r],e);if(t.valueInfo&&t.valueInfo.length)for(n.valueInfo=[],r=0;r<t.valueInfo.length;++r)n.valueInfo[r]=c.onnx.ValueInfoProto.toObject(t.valueInfo[r],e);if(t.quantizationAnnotation&&t.quantizationAnnotation.length)for(n.quantizationAnnotation=[],r=0;r<t.quantizationAnnotation.length;++r)n.quantizationAnnotation[r]=c.onnx.TensorAnnotation.toObject(t.quantizationAnnotation[r],e);return n},t.prototype.toJSON=function(){return this.constructor.toObject(this,s.util.toJSONOptions)},t}(),o.TensorProto=function(){function t(t){if(this.dims=[],this.floatData=[],this.int32Data=[],this.stringData=[],this.int64Data=[],this.externalData=[],this.doubleData=[],this.uint64Data=[],t)for(var e=Object.keys(t),n=0;n<e.length;++n)null!=t[e[n]]&&(this[e[n]]=t[e[n]])}return t.prototype.dims=l.emptyArray,t.prototype.dataType=0,t.prototype.segment=null,t.prototype.floatData=l.emptyArray,t.prototype.int32Data=l.emptyArray,t.prototype.stringData=l.emptyArray,t.prototype.int64Data=l.emptyArray,t.prototype.name="",t.prototype.docString="",t.prototype.rawData=l.newBuffer([]),t.prototype.externalData=l.emptyArray,t.prototype.dataLocation=0,t.prototype.doubleData=l.emptyArray,t.prototype.uint64Data=l.emptyArray,t.create=function(e){return new t(e)},t.encode=function(t,e){if(e||(e=u.create()),null!=t.dims&&t.dims.length){e.uint32(10).fork();for(var n=0;n<t.dims.length;++n)e.int64(t.dims[n]);e.ldelim()}if(null!=t.dataType&&t.hasOwnProperty("dataType")&&e.uint32(16).int32(t.dataType),null!=t.segment&&t.hasOwnProperty("segment")&&c.onnx.TensorProto.Segment.encode(t.segment,e.uint32(26).fork()).ldelim(),null!=t.floatData&&t.floatData.length){for(e.uint32(34).fork(),n=0;n<t.floatData.length;++n)e.float(t.floatData[n]);e.ldelim()}if(null!=t.int32Data&&t.int32Data.length){for(e.uint32(42).fork(),n=0;n<t.int32Data.length;++n)e.int32(t.int32Data[n]);e.ldelim()}if(null!=t.stringData&&t.stringData.length)for(n=0;n<t.stringData.length;++n)e.uint32(50).bytes(t.stringData[n]);if(null!=t.int64Data&&t.int64Data.length){for(e.uint32(58).fork(),n=0;n<t.int64Data.length;++n)e.int64(t.int64Data[n]);e.ldelim()}if(null!=t.name&&t.hasOwnProperty("name")&&e.uint32(66).string(t.name),null!=t.rawData&&t.hasOwnProperty("rawData")&&e.uint32(74).bytes(t.rawData),null!=t.doubleData&&t.doubleData.length){for(e.uint32(82).fork(),n=0;n<t.doubleData.length;++n)e.double(t.doubleData[n]);e.ldelim()}if(null!=t.uint64Data&&t.uint64Data.length){for(e.uint32(90).fork(),n=0;n<t.uint64Data.length;++n)e.uint64(t.uint64Data[n]);e.ldelim()}if(null!=t.docString&&t.hasOwnProperty("docString")&&e.uint32(98).string(t.docString),null!=t.externalData&&t.externalData.length)for(n=0;n<t.externalData.length;++n)c.onnx.StringStringEntryProto.encode(t.externalData[n],e.uint32(106).fork()).ldelim();return null!=t.dataLocation&&t.hasOwnProperty("dataLocation")&&e.uint32(112).int32(t.dataLocation),e},t.encodeDelimited=function(t,e){return this.encode(t,e).ldelim()},t.decode=function(t,e){t instanceof a||(t=a.create(t));for(var n=void 0===e?t.len:t.pos+e,r=new c.onnx.TensorProto;t.pos<n;){var i=t.uint32();switch(i>>>3){case 1:if(r.dims&&r.dims.length||(r.dims=[]),2==(7&i))for(var o=t.uint32()+t.pos;t.pos<o;)r.dims.push(t.int64());else r.dims.push(t.int64());break;case 2:r.dataType=t.int32();break;case 3:r.segment=c.onnx.TensorProto.Segment.decode(t,t.uint32());break;case 4:if(r.floatData&&r.floatData.length||(r.floatData=[]),2==(7&i))for(o=t.uint32()+t.pos;t.pos<o;)r.floatData.push(t.float());else r.floatData.push(t.float());break;case 5:if(r.int32Data&&r.int32Data.length||(r.int32Data=[]),2==(7&i))for(o=t.uint32()+t.pos;t.pos<o;)r.int32Data.push(t.int32());else r.int32Data.push(t.int32());break;case 6:r.stringData&&r.stringData.length||(r.stringData=[]),r.stringData.push(t.bytes());break;case 7:if(r.int64Data&&r.int64Data.length||(r.int64Data=[]),2==(7&i))for(o=t.uint32()+t.pos;t.pos<o;)r.int64Data.push(t.int64());else r.int64Data.push(t.int64());break;case 8:r.name=t.string();break;case 12:r.docString=t.string();break;case 9:r.rawData=t.bytes();break;case 13:r.externalData&&r.externalData.length||(r.externalData=[]),r.externalData.push(c.onnx.StringStringEntryProto.decode(t,t.uint32()));break;case 14:r.dataLocation=t.int32();break;case 10:if(r.doubleData&&r.doubleData.length||(r.doubleData=[]),2==(7&i))for(o=t.uint32()+t.pos;t.pos<o;)r.doubleData.push(t.double());else r.doubleData.push(t.double());break;case 11:if(r.uint64Data&&r.uint64Data.length||(r.uint64Data=[]),2==(7&i))for(o=t.uint32()+t.pos;t.pos<o;)r.uint64Data.push(t.uint64());else r.uint64Data.push(t.uint64());break;default:t.skipType(7&i)}}return r},t.decodeDelimited=function(t){return t instanceof a||(t=new a(t)),this.decode(t,t.uint32())},t.verify=function(t){if("object"!=typeof t||null===t)return"object expected";if(null!=t.dims&&t.hasOwnProperty("dims")){if(!Array.isArray(t.dims))return"dims: array expected";for(var e=0;e<t.dims.length;++e)if(!(l.isInteger(t.dims[e])||t.dims[e]&&l.isInteger(t.dims[e].low)&&l.isInteger(t.dims[e].high)))return"dims: integer|Long[] expected"}if(null!=t.dataType&&t.hasOwnProperty("dataType")&&!l.isInteger(t.dataType))return"dataType: integer expected";if(null!=t.segment&&t.hasOwnProperty("segment")&&(n=c.onnx.TensorProto.Segment.verify(t.segment)))return"segment."+n;if(null!=t.floatData&&t.hasOwnProperty("floatData")){if(!Array.isArray(t.floatData))return"floatData: array expected";for(e=0;e<t.floatData.length;++e)if("number"!=typeof t.floatData[e])return"floatData: number[] expected"}if(null!=t.int32Data&&t.hasOwnProperty("int32Data")){if(!Array.isArray(t.int32Data))return"int32Data: array expected";for(e=0;e<t.int32Data.length;++e)if(!l.isInteger(t.int32Data[e]))return"int32Data: integer[] expected"}if(null!=t.stringData&&t.hasOwnProperty("stringData")){if(!Array.isArray(t.stringData))return"stringData: array expected";for(e=0;e<t.stringData.length;++e)if(!(t.stringData[e]&&"number"==typeof t.stringData[e].length||l.isString(t.stringData[e])))return"stringData: buffer[] expected"}if(null!=t.int64Data&&t.hasOwnProperty("int64Data")){if(!Array.isArray(t.int64Data))return"int64Data: array expected";for(e=0;e<t.int64Data.length;++e)if(!(l.isInteger(t.int64Data[e])||t.int64Data[e]&&l.isInteger(t.int64Data[e].low)&&l.isInteger(t.int64Data[e].high)))return"int64Data: integer|Long[] expected"}if(null!=t.name&&t.hasOwnProperty("name")&&!l.isString(t.name))return"name: string expected";if(null!=t.docString&&t.hasOwnProperty("docString")&&!l.isString(t.docString))return"docString: string expected";if(null!=t.rawData&&t.hasOwnProperty("rawData")&&!(t.rawData&&"number"==typeof t.rawData.length||l.isString(t.rawData)))return"rawData: buffer expected";if(null!=t.externalData&&t.hasOwnProperty("externalData")){if(!Array.isArray(t.externalData))return"externalData: array expected";for(e=0;e<t.externalData.length;++e){var n;if(n=c.onnx.StringStringEntryProto.verify(t.externalData[e]))return"externalData."+n}}if(null!=t.dataLocation&&t.hasOwnProperty("dataLocation"))switch(t.dataLocation){default:return"dataLocation: enum value expected";case 0:case 1:}if(null!=t.doubleData&&t.hasOwnProperty("doubleData")){if(!Array.isArray(t.doubleData))return"doubleData: array expected";for(e=0;e<t.doubleData.length;++e)if("number"!=typeof t.doubleData[e])return"doubleData: number[] expected"}if(null!=t.uint64Data&&t.hasOwnProperty("uint64Data")){if(!Array.isArray(t.uint64Data))return"uint64Data: array expected";for(e=0;e<t.uint64Data.length;++e)if(!(l.isInteger(t.uint64Data[e])||t.uint64Data[e]&&l.isInteger(t.uint64Data[e].low)&&l.isInteger(t.uint64Data[e].high)))return"uint64Data: integer|Long[] expected"}return null},t.fromObject=function(t){if(t instanceof c.onnx.TensorProto)return t;var e=new c.onnx.TensorProto;if(t.dims){if(!Array.isArray(t.dims))throw TypeError(".onnx.TensorProto.dims: array expected");e.dims=[];for(var n=0;n<t.dims.length;++n)l.Long?(e.dims[n]=l.Long.fromValue(t.dims[n])).unsigned=!1:"string"==typeof t.dims[n]?e.dims[n]=parseInt(t.dims[n],10):"number"==typeof t.dims[n]?e.dims[n]=t.dims[n]:"object"==typeof t.dims[n]&&(e.dims[n]=new l.LongBits(t.dims[n].low>>>0,t.dims[n].high>>>0).toNumber())}if(null!=t.dataType&&(e.dataType=0|t.dataType),null!=t.segment){if("object"!=typeof t.segment)throw TypeError(".onnx.TensorProto.segment: object expected");e.segment=c.onnx.TensorProto.Segment.fromObject(t.segment)}if(t.floatData){if(!Array.isArray(t.floatData))throw TypeError(".onnx.TensorProto.floatData: array expected");for(e.floatData=[],n=0;n<t.floatData.length;++n)e.floatData[n]=Number(t.floatData[n])}if(t.int32Data){if(!Array.isArray(t.int32Data))throw TypeError(".onnx.TensorProto.int32Data: array expected");for(e.int32Data=[],n=0;n<t.int32Data.length;++n)e.int32Data[n]=0|t.int32Data[n]}if(t.stringData){if(!Array.isArray(t.stringData))throw TypeError(".onnx.TensorProto.stringData: array expected");for(e.stringData=[],n=0;n<t.stringData.length;++n)"string"==typeof t.stringData[n]?l.base64.decode(t.stringData[n],e.stringData[n]=l.newBuffer(l.base64.length(t.stringData[n])),0):t.stringData[n].length&&(e.stringData[n]=t.stringData[n])}if(t.int64Data){if(!Array.isArray(t.int64Data))throw TypeError(".onnx.TensorProto.int64Data: array expected");for(e.int64Data=[],n=0;n<t.int64Data.length;++n)l.Long?(e.int64Data[n]=l.Long.fromValue(t.int64Data[n])).unsigned=!1:"string"==typeof t.int64Data[n]?e.int64Data[n]=parseInt(t.int64Data[n],10):"number"==typeof t.int64Data[n]?e.int64Data[n]=t.int64Data[n]:"object"==typeof t.int64Data[n]&&(e.int64Data[n]=new l.LongBits(t.int64Data[n].low>>>0,t.int64Data[n].high>>>0).toNumber())}if(null!=t.name&&(e.name=String(t.name)),null!=t.docString&&(e.docString=String(t.docString)),null!=t.rawData&&("string"==typeof t.rawData?l.base64.decode(t.rawData,e.rawData=l.newBuffer(l.base64.length(t.rawData)),0):t.rawData.length&&(e.rawData=t.rawData)),t.externalData){if(!Array.isArray(t.externalData))throw TypeError(".onnx.TensorProto.externalData: array expected");for(e.externalData=[],n=0;n<t.externalData.length;++n){if("object"!=typeof t.externalData[n])throw TypeError(".onnx.TensorProto.externalData: object expected");e.externalData[n]=c.onnx.StringStringEntryProto.fromObject(t.externalData[n])}}switch(t.dataLocation){case"DEFAULT":case 0:e.dataLocation=0;break;case"EXTERNAL":case 1:e.dataLocation=1}if(t.doubleData){if(!Array.isArray(t.doubleData))throw TypeError(".onnx.TensorProto.doubleData: array expected");for(e.doubleData=[],n=0;n<t.doubleData.length;++n)e.doubleData[n]=Number(t.doubleData[n])}if(t.uint64Data){if(!Array.isArray(t.uint64Data))throw TypeError(".onnx.TensorProto.uint64Data: array expected");for(e.uint64Data=[],n=0;n<t.uint64Data.length;++n)l.Long?(e.uint64Data[n]=l.Long.fromValue(t.uint64Data[n])).unsigned=!0:"string"==typeof t.uint64Data[n]?e.uint64Data[n]=parseInt(t.uint64Data[n],10):"number"==typeof t.uint64Data[n]?e.uint64Data[n]=t.uint64Data[n]:"object"==typeof t.uint64Data[n]&&(e.uint64Data[n]=new l.LongBits(t.uint64Data[n].low>>>0,t.uint64Data[n].high>>>0).toNumber(!0))}return e},t.toObject=function(t,e){e||(e={});var n={};if((e.arrays||e.defaults)&&(n.dims=[],n.floatData=[],n.int32Data=[],n.stringData=[],n.int64Data=[],n.doubleData=[],n.uint64Data=[],n.externalData=[]),e.defaults&&(n.dataType=0,n.segment=null,n.name="",e.bytes===String?n.rawData="":(n.rawData=[],e.bytes!==Array&&(n.rawData=l.newBuffer(n.rawData))),n.docString="",n.dataLocation=e.enums===String?"DEFAULT":0),t.dims&&t.dims.length){n.dims=[];for(var r=0;r<t.dims.length;++r)"number"==typeof t.dims[r]?n.dims[r]=e.longs===String?String(t.dims[r]):t.dims[r]:n.dims[r]=e.longs===String?l.Long.prototype.toString.call(t.dims[r]):e.longs===Number?new l.LongBits(t.dims[r].low>>>0,t.dims[r].high>>>0).toNumber():t.dims[r]}if(null!=t.dataType&&t.hasOwnProperty("dataType")&&(n.dataType=t.dataType),null!=t.segment&&t.hasOwnProperty("segment")&&(n.segment=c.onnx.TensorProto.Segment.toObject(t.segment,e)),t.floatData&&t.floatData.length)for(n.floatData=[],r=0;r<t.floatData.length;++r)n.floatData[r]=e.json&&!isFinite(t.floatData[r])?String(t.floatData[r]):t.floatData[r];if(t.int32Data&&t.int32Data.length)for(n.int32Data=[],r=0;r<t.int32Data.length;++r)n.int32Data[r]=t.int32Data[r];if(t.stringData&&t.stringData.length)for(n.stringData=[],r=0;r<t.stringData.length;++r)n.stringData[r]=e.bytes===String?l.base64.encode(t.stringData[r],0,t.stringData[r].length):e.bytes===Array?Array.prototype.slice.call(t.stringData[r]):t.stringData[r];if(t.int64Data&&t.int64Data.length)for(n.int64Data=[],r=0;r<t.int64Data.length;++r)"number"==typeof t.int64Data[r]?n.int64Data[r]=e.longs===String?String(t.int64Data[r]):t.int64Data[r]:n.int64Data[r]=e.longs===String?l.Long.prototype.toString.call(t.int64Data[r]):e.longs===Number?new l.LongBits(t.int64Data[r].low>>>0,t.int64Data[r].high>>>0).toNumber():t.int64Data[r];if(null!=t.name&&t.hasOwnProperty("name")&&(n.name=t.name),null!=t.rawData&&t.hasOwnProperty("rawData")&&(n.rawData=e.bytes===String?l.base64.encode(t.rawData,0,t.rawData.length):e.bytes===Array?Array.prototype.slice.call(t.rawData):t.rawData),t.doubleData&&t.doubleData.length)for(n.doubleData=[],r=0;r<t.doubleData.length;++r)n.doubleData[r]=e.json&&!isFinite(t.doubleData[r])?String(t.doubleData[r]):t.doubleData[r];if(t.uint64Data&&t.uint64Data.length)for(n.uint64Data=[],r=0;r<t.uint64Data.length;++r)"number"==typeof t.uint64Data[r]?n.uint64Data[r]=e.longs===String?String(t.uint64Data[r]):t.uint64Data[r]:n.uint64Data[r]=e.longs===String?l.Long.prototype.toString.call(t.uint64Data[r]):e.longs===Number?new l.LongBits(t.uint64Data[r].low>>>0,t.uint64Data[r].high>>>0).toNumber(!0):t.uint64Data[r];if(null!=t.docString&&t.hasOwnProperty("docString")&&(n.docString=t.docString),t.externalData&&t.externalData.length)for(n.externalData=[],r=0;r<t.externalData.length;++r)n.externalData[r]=c.onnx.StringStringEntryProto.toObject(t.externalData[r],e);return null!=t.dataLocation&&t.hasOwnProperty("dataLocation")&&(n.dataLocation=e.enums===String?c.onnx.TensorProto.DataLocation[t.dataLocation]:t.dataLocation),n},t.prototype.toJSON=function(){return this.constructor.toObject(this,s.util.toJSONOptions)},t.DataType=function(){var t={},e=Object.create(t);return e[t[0]="UNDEFINED"]=0,e[t[1]="FLOAT"]=1,e[t[2]="UINT8"]=2,e[t[3]="INT8"]=3,e[t[4]="UINT16"]=4,e[t[5]="INT16"]=5,e[t[6]="INT32"]=6,e[t[7]="INT64"]=7,e[t[8]="STRING"]=8,e[t[9]="BOOL"]=9,e[t[10]="FLOAT16"]=10,e[t[11]="DOUBLE"]=11,e[t[12]="UINT32"]=12,e[t[13]="UINT64"]=13,e[t[14]="COMPLEX64"]=14,e[t[15]="COMPLEX128"]=15,e[t[16]="BFLOAT16"]=16,e}(),t.Segment=function(){function t(t){if(t)for(var e=Object.keys(t),n=0;n<e.length;++n)null!=t[e[n]]&&(this[e[n]]=t[e[n]])}return t.prototype.begin=l.Long?l.Long.fromBits(0,0,!1):0,t.prototype.end=l.Long?l.Long.fromBits(0,0,!1):0,t.create=function(e){return new t(e)},t.encode=function(t,e){return e||(e=u.create()),null!=t.begin&&t.hasOwnProperty("begin")&&e.uint32(8).int64(t.begin),null!=t.end&&t.hasOwnProperty("end")&&e.uint32(16).int64(t.end),e},t.encodeDelimited=function(t,e){return this.encode(t,e).ldelim()},t.decode=function(t,e){t instanceof a||(t=a.create(t));for(var n=void 0===e?t.len:t.pos+e,r=new c.onnx.TensorProto.Segment;t.pos<n;){var i=t.uint32();switch(i>>>3){case 1:r.begin=t.int64();break;case 2:r.end=t.int64();break;default:t.skipType(7&i)}}return r},t.decodeDelimited=function(t){return t instanceof a||(t=new a(t)),this.decode(t,t.uint32())},t.verify=function(t){return"object"!=typeof t||null===t?"object expected":null!=t.begin&&t.hasOwnProperty("begin")&&!(l.isInteger(t.begin)||t.begin&&l.isInteger(t.begin.low)&&l.isInteger(t.begin.high))?"begin: integer|Long expected":null!=t.end&&t.hasOwnProperty("end")&&!(l.isInteger(t.end)||t.end&&l.isInteger(t.end.low)&&l.isInteger(t.end.high))?"end: integer|Long expected":null},t.fromObject=function(t){if(t instanceof c.onnx.TensorProto.Segment)return t;var e=new c.onnx.TensorProto.Segment;return null!=t.begin&&(l.Long?(e.begin=l.Long.fromValue(t.begin)).unsigned=!1:"string"==typeof t.begin?e.begin=parseInt(t.begin,10):"number"==typeof t.begin?e.begin=t.begin:"object"==typeof t.begin&&(e.begin=new l.LongBits(t.begin.low>>>0,t.begin.high>>>0).toNumber())),null!=t.end&&(l.Long?(e.end=l.Long.fromValue(t.end)).unsigned=!1:"string"==typeof t.end?e.end=parseInt(t.end,10):"number"==typeof t.end?e.end=t.end:"object"==typeof t.end&&(e.end=new l.LongBits(t.end.low>>>0,t.end.high>>>0).toNumber())),e},t.toObject=function(t,e){e||(e={});var n={};if(e.defaults){if(l.Long){var r=new l.Long(0,0,!1);n.begin=e.longs===String?r.toString():e.longs===Number?r.toNumber():r}else n.begin=e.longs===String?"0":0;l.Long?(r=new l.Long(0,0,!1),n.end=e.longs===String?r.toString():e.longs===Number?r.toNumber():r):n.end=e.longs===String?"0":0}return null!=t.begin&&t.hasOwnProperty("begin")&&("number"==typeof t.begin?n.begin=e.longs===String?String(t.begin):t.begin:n.begin=e.longs===String?l.Long.prototype.toString.call(t.begin):e.longs===Number?new l.LongBits(t.begin.low>>>0,t.begin.high>>>0).toNumber():t.begin),null!=t.end&&t.hasOwnProperty("end")&&("number"==typeof t.end?n.end=e.longs===String?String(t.end):t.end:n.end=e.longs===String?l.Long.prototype.toString.call(t.end):e.longs===Number?new l.LongBits(t.end.low>>>0,t.end.high>>>0).toNumber():t.end),n},t.prototype.toJSON=function(){return this.constructor.toObject(this,s.util.toJSONOptions)},t}(),t.DataLocation=function(){var t={},e=Object.create(t);return e[t[0]="DEFAULT"]=0,e[t[1]="EXTERNAL"]=1,e}(),t}(),o.TensorShapeProto=function(){function t(t){if(this.dim=[],t)for(var e=Object.keys(t),n=0;n<e.length;++n)null!=t[e[n]]&&(this[e[n]]=t[e[n]])}return t.prototype.dim=l.emptyArray,t.create=function(e){return new t(e)},t.encode=function(t,e){if(e||(e=u.create()),null!=t.dim&&t.dim.length)for(var n=0;n<t.dim.length;++n)c.onnx.TensorShapeProto.Dimension.encode(t.dim[n],e.uint32(10).fork()).ldelim();return e},t.encodeDelimited=function(t,e){return this.encode(t,e).ldelim()},t.decode=function(t,e){t instanceof a||(t=a.create(t));for(var n=void 0===e?t.len:t.pos+e,r=new c.onnx.TensorShapeProto;t.pos<n;){var i=t.uint32();i>>>3==1?(r.dim&&r.dim.length||(r.dim=[]),r.dim.push(c.onnx.TensorShapeProto.Dimension.decode(t,t.uint32()))):t.skipType(7&i)}return r},t.decodeDelimited=function(t){return t instanceof a||(t=new a(t)),this.decode(t,t.uint32())},t.verify=function(t){if("object"!=typeof t||null===t)return"object expected";if(null!=t.dim&&t.hasOwnProperty("dim")){if(!Array.isArray(t.dim))return"dim: array expected";for(var e=0;e<t.dim.length;++e){var n=c.onnx.TensorShapeProto.Dimension.verify(t.dim[e]);if(n)return"dim."+n}}return null},t.fromObject=function(t){if(t instanceof c.onnx.TensorShapeProto)return t;var e=new c.onnx.TensorShapeProto;if(t.dim){if(!Array.isArray(t.dim))throw TypeError(".onnx.TensorShapeProto.dim: array expected");e.dim=[];for(var n=0;n<t.dim.length;++n){if("object"!=typeof t.dim[n])throw TypeError(".onnx.TensorShapeProto.dim: object expected");e.dim[n]=c.onnx.TensorShapeProto.Dimension.fromObject(t.dim[n])}}return e},t.toObject=function(t,e){e||(e={});var n={};if((e.arrays||e.defaults)&&(n.dim=[]),t.dim&&t.dim.length){n.dim=[];for(var r=0;r<t.dim.length;++r)n.dim[r]=c.onnx.TensorShapeProto.Dimension.toObject(t.dim[r],e)}return n},t.prototype.toJSON=function(){return this.constructor.toObject(this,s.util.toJSONOptions)},t.Dimension=function(){function t(t){if(t)for(var e=Object.keys(t),n=0;n<e.length;++n)null!=t[e[n]]&&(this[e[n]]=t[e[n]])}var e;return t.prototype.dimValue=l.Long?l.Long.fromBits(0,0,!1):0,t.prototype.dimParam="",t.prototype.denotation="",Object.defineProperty(t.prototype,"value",{get:l.oneOfGetter(e=["dimValue","dimParam"]),set:l.oneOfSetter(e)}),t.create=function(e){return new t(e)},t.encode=function(t,e){return e||(e=u.create()),null!=t.dimValue&&t.hasOwnProperty("dimValue")&&e.uint32(8).int64(t.dimValue),null!=t.dimParam&&t.hasOwnProperty("dimParam")&&e.uint32(18).string(t.dimParam),null!=t.denotation&&t.hasOwnProperty("denotation")&&e.uint32(26).string(t.denotation),e},t.encodeDelimited=function(t,e){return this.encode(t,e).ldelim()},t.decode=function(t,e){t instanceof a||(t=a.create(t));for(var n=void 0===e?t.len:t.pos+e,r=new c.onnx.TensorShapeProto.Dimension;t.pos<n;){var i=t.uint32();switch(i>>>3){case 1:r.dimValue=t.int64();break;case 2:r.dimParam=t.string();break;case 3:r.denotation=t.string();break;default:t.skipType(7&i)}}return r},t.decodeDelimited=function(t){return t instanceof a||(t=new a(t)),this.decode(t,t.uint32())},t.verify=function(t){if("object"!=typeof t||null===t)return"object expected";var e={};if(null!=t.dimValue&&t.hasOwnProperty("dimValue")&&(e.value=1,!(l.isInteger(t.dimValue)||t.dimValue&&l.isInteger(t.dimValue.low)&&l.isInteger(t.dimValue.high))))return"dimValue: integer|Long expected";if(null!=t.dimParam&&t.hasOwnProperty("dimParam")){if(1===e.value)return"value: multiple values";if(e.value=1,!l.isString(t.dimParam))return"dimParam: string expected"}return null!=t.denotation&&t.hasOwnProperty("denotation")&&!l.isString(t.denotation)?"denotation: string expected":null},t.fromObject=function(t){if(t instanceof c.onnx.TensorShapeProto.Dimension)return t;var e=new c.onnx.TensorShapeProto.Dimension;return null!=t.dimValue&&(l.Long?(e.dimValue=l.Long.fromValue(t.dimValue)).unsigned=!1:"string"==typeof t.dimValue?e.dimValue=parseInt(t.dimValue,10):"number"==typeof t.dimValue?e.dimValue=t.dimValue:"object"==typeof t.dimValue&&(e.dimValue=new l.LongBits(t.dimValue.low>>>0,t.dimValue.high>>>0).toNumber())),null!=t.dimParam&&(e.dimParam=String(t.dimParam)),null!=t.denotation&&(e.denotation=String(t.denotation)),e},t.toObject=function(t,e){e||(e={});var n={};return e.defaults&&(n.denotation=""),null!=t.dimValue&&t.hasOwnProperty("dimValue")&&("number"==typeof t.dimValue?n.dimValue=e.longs===String?String(t.dimValue):t.dimValue:n.dimValue=e.longs===String?l.Long.prototype.toString.call(t.dimValue):e.longs===Number?new l.LongBits(t.dimValue.low>>>0,t.dimValue.high>>>0).toNumber():t.dimValue,e.oneofs&&(n.value="dimValue")),null!=t.dimParam&&t.hasOwnProperty("dimParam")&&(n.dimParam=t.dimParam,e.oneofs&&(n.value="dimParam")),null!=t.denotation&&t.hasOwnProperty("denotation")&&(n.denotation=t.denotation),n},t.prototype.toJSON=function(){return this.constructor.toObject(this,s.util.toJSONOptions)},t}(),t}(),o.TypeProto=function(){function t(t){if(t)for(var e=Object.keys(t),n=0;n<e.length;++n)null!=t[e[n]]&&(this[e[n]]=t[e[n]])}var e;return t.prototype.tensorType=null,t.prototype.denotation="",Object.defineProperty(t.prototype,"value",{get:l.oneOfGetter(e=["tensorType"]),set:l.oneOfSetter(e)}),t.create=function(e){return new t(e)},t.encode=function(t,e){return e||(e=u.create()),null!=t.tensorType&&t.hasOwnProperty("tensorType")&&c.onnx.TypeProto.Tensor.encode(t.tensorType,e.uint32(10).fork()).ldelim(),null!=t.denotation&&t.hasOwnProperty("denotation")&&e.uint32(50).string(t.denotation),e},t.encodeDelimited=function(t,e){return this.encode(t,e).ldelim()},t.decode=function(t,e){t instanceof a||(t=a.create(t));for(var n=void 0===e?t.len:t.pos+e,r=new c.onnx.TypeProto;t.pos<n;){var i=t.uint32();switch(i>>>3){case 1:r.tensorType=c.onnx.TypeProto.Tensor.decode(t,t.uint32());break;case 6:r.denotation=t.string();break;default:t.skipType(7&i)}}return r},t.decodeDelimited=function(t){return t instanceof a||(t=new a(t)),this.decode(t,t.uint32())},t.verify=function(t){if("object"!=typeof t||null===t)return"object expected";if(null!=t.tensorType&&t.hasOwnProperty("tensorType")){var e=c.onnx.TypeProto.Tensor.verify(t.tensorType);if(e)return"tensorType."+e}return null!=t.denotation&&t.hasOwnProperty("denotation")&&!l.isString(t.denotation)?"denotation: string expected":null},t.fromObject=function(t){if(t instanceof c.onnx.TypeProto)return t;var e=new c.onnx.TypeProto;if(null!=t.tensorType){if("object"!=typeof t.tensorType)throw TypeError(".onnx.TypeProto.tensorType: object expected");e.tensorType=c.onnx.TypeProto.Tensor.fromObject(t.tensorType)}return null!=t.denotation&&(e.denotation=String(t.denotation)),e},t.toObject=function(t,e){e||(e={});var n={};return e.defaults&&(n.denotation=""),null!=t.tensorType&&t.hasOwnProperty("tensorType")&&(n.tensorType=c.onnx.TypeProto.Tensor.toObject(t.tensorType,e),e.oneofs&&(n.value="tensorType")),null!=t.denotation&&t.hasOwnProperty("denotation")&&(n.denotation=t.denotation),n},t.prototype.toJSON=function(){return this.constructor.toObject(this,s.util.toJSONOptions)},t.Tensor=function(){function t(t){if(t)for(var e=Object.keys(t),n=0;n<e.length;++n)null!=t[e[n]]&&(this[e[n]]=t[e[n]])}return t.prototype.elemType=0,t.prototype.shape=null,t.create=function(e){return new t(e)},t.encode=function(t,e){return e||(e=u.create()),null!=t.elemType&&t.hasOwnProperty("elemType")&&e.uint32(8).int32(t.elemType),null!=t.shape&&t.hasOwnProperty("shape")&&c.onnx.TensorShapeProto.encode(t.shape,e.uint32(18).fork()).ldelim(),e},t.encodeDelimited=function(t,e){return this.encode(t,e).ldelim()},t.decode=function(t,e){t instanceof a||(t=a.create(t));for(var n=void 0===e?t.len:t.pos+e,r=new c.onnx.TypeProto.Tensor;t.pos<n;){var i=t.uint32();switch(i>>>3){case 1:r.elemType=t.int32();break;case 2:r.shape=c.onnx.TensorShapeProto.decode(t,t.uint32());break;default:t.skipType(7&i)}}return r},t.decodeDelimited=function(t){return t instanceof a||(t=new a(t)),this.decode(t,t.uint32())},t.verify=function(t){if("object"!=typeof t||null===t)return"object expected";if(null!=t.elemType&&t.hasOwnProperty("elemType")&&!l.isInteger(t.elemType))return"elemType: integer expected";if(null!=t.shape&&t.hasOwnProperty("shape")){var e=c.onnx.TensorShapeProto.verify(t.shape);if(e)return"shape."+e}return null},t.fromObject=function(t){if(t instanceof c.onnx.TypeProto.Tensor)return t;var e=new c.onnx.TypeProto.Tensor;if(null!=t.elemType&&(e.elemType=0|t.elemType),null!=t.shape){if("object"!=typeof t.shape)throw TypeError(".onnx.TypeProto.Tensor.shape: object expected");e.shape=c.onnx.TensorShapeProto.fromObject(t.shape)}return e},t.toObject=function(t,e){e||(e={});var n={};return e.defaults&&(n.elemType=0,n.shape=null),null!=t.elemType&&t.hasOwnProperty("elemType")&&(n.elemType=t.elemType),null!=t.shape&&t.hasOwnProperty("shape")&&(n.shape=c.onnx.TensorShapeProto.toObject(t.shape,e)),n},t.prototype.toJSON=function(){return this.constructor.toObject(this,s.util.toJSONOptions)},t}(),t}(),o.OperatorSetIdProto=function(){function t(t){if(t)for(var e=Object.keys(t),n=0;n<e.length;++n)null!=t[e[n]]&&(this[e[n]]=t[e[n]])}return t.prototype.domain="",t.prototype.version=l.Long?l.Long.fromBits(0,0,!1):0,t.create=function(e){return new t(e)},t.encode=function(t,e){return e||(e=u.create()),null!=t.domain&&t.hasOwnProperty("domain")&&e.uint32(10).string(t.domain),null!=t.version&&t.hasOwnProperty("version")&&e.uint32(16).int64(t.version),e},t.encodeDelimited=function(t,e){return this.encode(t,e).ldelim()},t.decode=function(t,e){t instanceof a||(t=a.create(t));for(var n=void 0===e?t.len:t.pos+e,r=new c.onnx.OperatorSetIdProto;t.pos<n;){var i=t.uint32();switch(i>>>3){case 1:r.domain=t.string();break;case 2:r.version=t.int64();break;default:t.skipType(7&i)}}return r},t.decodeDelimited=function(t){return t instanceof a||(t=new a(t)),this.decode(t,t.uint32())},t.verify=function(t){return"object"!=typeof t||null===t?"object expected":null!=t.domain&&t.hasOwnProperty("domain")&&!l.isString(t.domain)?"domain: string expected":null!=t.version&&t.hasOwnProperty("version")&&!(l.isInteger(t.version)||t.version&&l.isInteger(t.version.low)&&l.isInteger(t.version.high))?"version: integer|Long expected":null},t.fromObject=function(t){if(t instanceof c.onnx.OperatorSetIdProto)return t;var e=new c.onnx.OperatorSetIdProto;return null!=t.domain&&(e.domain=String(t.domain)),null!=t.version&&(l.Long?(e.version=l.Long.fromValue(t.version)).unsigned=!1:"string"==typeof t.version?e.version=parseInt(t.version,10):"number"==typeof t.version?e.version=t.version:"object"==typeof t.version&&(e.version=new l.LongBits(t.version.low>>>0,t.version.high>>>0).toNumber())),e},t.toObject=function(t,e){e||(e={});var n={};if(e.defaults)if(n.domain="",l.Long){var r=new l.Long(0,0,!1);n.version=e.longs===String?r.toString():e.longs===Number?r.toNumber():r}else n.version=e.longs===String?"0":0;return null!=t.domain&&t.hasOwnProperty("domain")&&(n.domain=t.domain),null!=t.version&&t.hasOwnProperty("version")&&("number"==typeof t.version?n.version=e.longs===String?String(t.version):t.version:n.version=e.longs===String?l.Long.prototype.toString.call(t.version):e.longs===Number?new l.LongBits(t.version.low>>>0,t.version.high>>>0).toNumber():t.version),n},t.prototype.toJSON=function(){return this.constructor.toObject(this,s.util.toJSONOptions)},t}(),o),t.exports=c},2100:(t,e,n)=>{"use strict";t.exports=n(9482)},9482:(t,e,n)=>{"use strict";var r=e;function i(){r.util._configure(),r.Writer._configure(r.BufferWriter),r.Reader._configure(r.BufferReader)}r.build="minimal",r.Writer=n(1173),r.BufferWriter=n(3155),r.Reader=n(1408),r.BufferReader=n(593),r.util=n(9693),r.rpc=n(5994),r.roots=n(5054),r.configure=i,i()},1408:(t,e,n)=>{"use strict";t.exports=u;var r,i=n(9693),o=i.LongBits,s=i.utf8;function a(t,e){return RangeError("index out of range: "+t.pos+" + "+(e||1)+" > "+t.len)}function u(t){this.buf=t,this.pos=0,this.len=t.length}var l,c="undefined"!=typeof Uint8Array?function(t){if(t instanceof Uint8Array||Array.isArray(t))return new u(t);throw Error("illegal buffer")}:function(t){if(Array.isArray(t))return new u(t);throw Error("illegal buffer")},d=function(){return i.Buffer?function(t){return(u.create=function(t){return i.Buffer.isBuffer(t)?new r(t):c(t)})(t)}:c};function p(){var t=new o(0,0),e=0;if(!(this.len-this.pos>4)){for(;e<3;++e){if(this.pos>=this.len)throw a(this);if(t.lo=(t.lo|(127&this.buf[this.pos])<<7*e)>>>0,this.buf[this.pos++]<128)return t}return t.lo=(t.lo|(127&this.buf[this.pos++])<<7*e)>>>0,t}for(;e<4;++e)if(t.lo=(t.lo|(127&this.buf[this.pos])<<7*e)>>>0,this.buf[this.pos++]<128)return t;if(t.lo=(t.lo|(127&this.buf[this.pos])<<28)>>>0,t.hi=(t.hi|(127&this.buf[this.pos])>>4)>>>0,this.buf[this.pos++]<128)return t;if(e=0,this.len-this.pos>4){for(;e<5;++e)if(t.hi=(t.hi|(127&this.buf[this.pos])<<7*e+3)>>>0,this.buf[this.pos++]<128)return t}else for(;e<5;++e){if(this.pos>=this.len)throw a(this);if(t.hi=(t.hi|(127&this.buf[this.pos])<<7*e+3)>>>0,this.buf[this.pos++]<128)return t}throw Error("invalid varint encoding")}function h(t,e){return(t[e-4]|t[e-3]<<8|t[e-2]<<16|t[e-1]<<24)>>>0}function f(){if(this.pos+8>this.len)throw a(this,8);return new o(h(this.buf,this.pos+=4),h(this.buf,this.pos+=4))}u.create=d(),u.prototype._slice=i.Array.prototype.subarray||i.Array.prototype.slice,u.prototype.uint32=(l=4294967295,function(){if(l=(127&this.buf[this.pos])>>>0,this.buf[this.pos++]<128)return l;if(l=(l|(127&this.buf[this.pos])<<7)>>>0,this.buf[this.pos++]<128)return l;if(l=(l|(127&this.buf[this.pos])<<14)>>>0,this.buf[this.pos++]<128)return l;if(l=(l|(127&this.buf[this.pos])<<21)>>>0,this.buf[this.pos++]<128)return l;if(l=(l|(15&this.buf[this.pos])<<28)>>>0,this.buf[this.pos++]<128)return l;if((this.pos+=5)>this.len)throw this.pos=this.len,a(this,10);return l}),u.prototype.int32=function(){return 0|this.uint32()},u.prototype.sint32=function(){var t=this.uint32();return t>>>1^-(1&t)|0},u.prototype.bool=function(){return 0!==this.uint32()},u.prototype.fixed32=function(){if(this.pos+4>this.len)throw a(this,4);return h(this.buf,this.pos+=4)},u.prototype.sfixed32=function(){if(this.pos+4>this.len)throw a(this,4);return 0|h(this.buf,this.pos+=4)},u.prototype.float=function(){if(this.pos+4>this.len)throw a(this,4);var t=i.float.readFloatLE(this.buf,this.pos);return this.pos+=4,t},u.prototype.double=function(){if(this.pos+8>this.len)throw a(this,4);var t=i.float.readDoubleLE(this.buf,this.pos);return this.pos+=8,t},u.prototype.bytes=function(){var t=this.uint32(),e=this.pos,n=this.pos+t;if(n>this.len)throw a(this,t);return this.pos+=t,Array.isArray(this.buf)?this.buf.slice(e,n):e===n?new this.buf.constructor(0):this._slice.call(this.buf,e,n)},u.prototype.string=function(){var t=this.bytes();return s.read(t,0,t.length)},u.prototype.skip=function(t){if("number"==typeof t){if(this.pos+t>this.len)throw a(this,t);this.pos+=t}else do{if(this.pos>=this.len)throw a(this)}while(128&this.buf[this.pos++]);return this},u.prototype.skipType=function(t){switch(t){case 0:this.skip();break;case 1:this.skip(8);break;case 2:this.skip(this.uint32());break;case 3:for(;4!=(t=7&this.uint32());)this.skipType(t);break;case 5:this.skip(4);break;default:throw Error("invalid wire type "+t+" at offset "+this.pos)}return this},u._configure=function(t){r=t,u.create=d(),r._configure();var e=i.Long?"toLong":"toNumber";i.merge(u.prototype,{int64:function(){return p.call(this)[e](!1)},uint64:function(){return p.call(this)[e](!0)},sint64:function(){return p.call(this).zzDecode()[e](!1)},fixed64:function(){return f.call(this)[e](!0)},sfixed64:function(){return f.call(this)[e](!1)}})}},593:(t,e,n)=>{"use strict";t.exports=o;var r=n(1408);(o.prototype=Object.create(r.prototype)).constructor=o;var i=n(9693);function o(t){r.call(this,t)}o._configure=function(){i.Buffer&&(o.prototype._slice=i.Buffer.prototype.slice)},o.prototype.string=function(){var t=this.uint32();return this.buf.utf8Slice?this.buf.utf8Slice(this.pos,this.pos=Math.min(this.pos+t,this.len)):this.buf.toString("utf-8",this.pos,this.pos=Math.min(this.pos+t,this.len))},o._configure()},5054:t=>{"use strict";t.exports={}},5994:(t,e,n)=>{"use strict";e.Service=n(7948)},7948:(t,e,n)=>{"use strict";t.exports=i;var r=n(9693);function i(t,e,n){if("function"!=typeof t)throw TypeError("rpcImpl must be a function");r.EventEmitter.call(this),this.rpcImpl=t,this.requestDelimited=Boolean(e),this.responseDelimited=Boolean(n)}(i.prototype=Object.create(r.EventEmitter.prototype)).constructor=i,i.prototype.rpcCall=function t(e,n,i,o,s){if(!o)throw TypeError("request must be specified");var a=this;if(!s)return r.asPromise(t,a,e,n,i,o);if(a.rpcImpl)try{return a.rpcImpl(e,n[a.requestDelimited?"encodeDelimited":"encode"](o).finish(),(function(t,n){if(t)return a.emit("error",t,e),s(t);if(null!==n){if(!(n instanceof i))try{n=i[a.responseDelimited?"decodeDelimited":"decode"](n)}catch(t){return a.emit("error",t,e),s(t)}return a.emit("data",n,e),s(null,n)}a.end(!0)}))}catch(t){return a.emit("error",t,e),void setTimeout((function(){s(t)}),0)}else setTimeout((function(){s(Error("already ended"))}),0)},i.prototype.end=function(t){return this.rpcImpl&&(t||this.rpcImpl(null,null,null),this.rpcImpl=null,this.emit("end").off()),this}},1945:(t,e,n)=>{"use strict";t.exports=i;var r=n(9693);function i(t,e){this.lo=t>>>0,this.hi=e>>>0}var o=i.zero=new i(0,0);o.toNumber=function(){return 0},o.zzEncode=o.zzDecode=function(){return this},o.length=function(){return 1};var s=i.zeroHash="\0\0\0\0\0\0\0\0";i.fromNumber=function(t){if(0===t)return o;var e=t<0;e&&(t=-t);var n=t>>>0,r=(t-n)/4294967296>>>0;return e&&(r=~r>>>0,n=~n>>>0,++n>4294967295&&(n=0,++r>4294967295&&(r=0))),new i(n,r)},i.from=function(t){if("number"==typeof t)return i.fromNumber(t);if(r.isString(t)){if(!r.Long)return i.fromNumber(parseInt(t,10));t=r.Long.fromString(t)}return t.low||t.high?new i(t.low>>>0,t.high>>>0):o},i.prototype.toNumber=function(t){if(!t&&this.hi>>>31){var e=1+~this.lo>>>0,n=~this.hi>>>0;return e||(n=n+1>>>0),-(e+4294967296*n)}return this.lo+4294967296*this.hi},i.prototype.toLong=function(t){return r.Long?new r.Long(0|this.lo,0|this.hi,Boolean(t)):{low:0|this.lo,high:0|this.hi,unsigned:Boolean(t)}};var a=String.prototype.charCodeAt;i.fromHash=function(t){return t===s?o:new i((a.call(t,0)|a.call(t,1)<<8|a.call(t,2)<<16|a.call(t,3)<<24)>>>0,(a.call(t,4)|a.call(t,5)<<8|a.call(t,6)<<16|a.call(t,7)<<24)>>>0)},i.prototype.toHash=function(){return String.fromCharCode(255&this.lo,this.lo>>>8&255,this.lo>>>16&255,this.lo>>>24,255&this.hi,this.hi>>>8&255,this.hi>>>16&255,this.hi>>>24)},i.prototype.zzEncode=function(){var t=this.hi>>31;return this.hi=((this.hi<<1|this.lo>>>31)^t)>>>0,this.lo=(this.lo<<1^t)>>>0,this},i.prototype.zzDecode=function(){var t=-(1&this.lo);return this.lo=((this.lo>>>1|this.hi<<31)^t)>>>0,this.hi=(this.hi>>>1^t)>>>0,this},i.prototype.length=function(){var t=this.lo,e=(this.lo>>>28|this.hi<<4)>>>0,n=this.hi>>>24;return 0===n?0===e?t<16384?t<128?1:2:t<2097152?3:4:e<16384?e<128?5:6:e<2097152?7:8:n<128?9:10}},9693:function(t,e,n){"use strict";var r=e;function i(t,e,n){for(var r=Object.keys(e),i=0;i<r.length;++i)void 0!==t[r[i]]&&n||(t[r[i]]=e[r[i]]);return t}function o(t){function e(t,n){if(!(this instanceof e))return new e(t,n);Object.defineProperty(this,"message",{get:function(){return t}}),Error.captureStackTrace?Error.captureStackTrace(this,e):Object.defineProperty(this,"stack",{value:(new Error).stack||""}),n&&i(this,n)}return(e.prototype=Object.create(Error.prototype)).constructor=e,Object.defineProperty(e.prototype,"name",{get:function(){return t}}),e.prototype.toString=function(){return this.name+": "+this.message},e}r.asPromise=n(4537),r.base64=n(7419),r.EventEmitter=n(9211),r.float=n(945),r.inquire=n(7199),r.utf8=n(4997),r.pool=n(6662),r.LongBits=n(1945),r.isNode=Boolean(void 0!==n.g&&n.g&&n.g.process&&n.g.process.versions&&n.g.process.versions.node),r.global=r.isNode&&n.g||"undefined"!=typeof window&&window||"undefined"!=typeof self&&self||this,r.emptyArray=Object.freeze?Object.freeze([]):[],r.emptyObject=Object.freeze?Object.freeze({}):{},r.isInteger=Number.isInteger||function(t){return"number"==typeof t&&isFinite(t)&&Math.floor(t)===t},r.isString=function(t){return"string"==typeof t||t instanceof String},r.isObject=function(t){return t&&"object"==typeof t},r.isset=r.isSet=function(t,e){var n=t[e];return!(null==n||!t.hasOwnProperty(e))&&("object"!=typeof n||(Array.isArray(n)?n.length:Object.keys(n).length)>0)},r.Buffer=function(){try{var t=r.inquire("buffer").Buffer;return t.prototype.utf8Write?t:null}catch(t){return null}}(),r._Buffer_from=null,r._Buffer_allocUnsafe=null,r.newBuffer=function(t){return"number"==typeof t?r.Buffer?r._Buffer_allocUnsafe(t):new r.Array(t):r.Buffer?r._Buffer_from(t):"undefined"==typeof Uint8Array?t:new Uint8Array(t)},r.Array="undefined"!=typeof Uint8Array?Uint8Array:Array,r.Long=r.global.dcodeIO&&r.global.dcodeIO.Long||r.global.Long||r.inquire("long"),r.key2Re=/^true|false|0|1$/,r.key32Re=/^-?(?:0|[1-9][0-9]*)$/,r.key64Re=/^(?:[\\x00-\\xff]{8}|-?(?:0|[1-9][0-9]*))$/,r.longToHash=function(t){return t?r.LongBits.from(t).toHash():r.LongBits.zeroHash},r.longFromHash=function(t,e){var n=r.LongBits.fromHash(t);return r.Long?r.Long.fromBits(n.lo,n.hi,e):n.toNumber(Boolean(e))},r.merge=i,r.lcFirst=function(t){return t.charAt(0).toLowerCase()+t.substring(1)},r.newError=o,r.ProtocolError=o("ProtocolError"),r.oneOfGetter=function(t){for(var e={},n=0;n<t.length;++n)e[t[n]]=1;return function(){for(var t=Object.keys(this),n=t.length-1;n>-1;--n)if(1===e[t[n]]&&void 0!==this[t[n]]&&null!==this[t[n]])return t[n]}},r.oneOfSetter=function(t){return function(e){for(var n=0;n<t.length;++n)t[n]!==e&&delete this[t[n]]}},r.toJSONOptions={longs:String,enums:String,bytes:String,json:!0},r._configure=function(){var t=r.Buffer;t?(r._Buffer_from=t.from!==Uint8Array.from&&t.from||function(e,n){return new t(e,n)},r._Buffer_allocUnsafe=t.allocUnsafe||function(e){return new t(e)}):r._Buffer_from=r._Buffer_allocUnsafe=null}},1173:(t,e,n)=>{"use strict";t.exports=d;var r,i=n(9693),o=i.LongBits,s=i.base64,a=i.utf8;function u(t,e,n){this.fn=t,this.len=e,this.next=void 0,this.val=n}function l(){}function c(t){this.head=t.head,this.tail=t.tail,this.len=t.len,this.next=t.states}function d(){this.len=0,this.head=new u(l,0,0),this.tail=this.head,this.states=null}var p=function(){return i.Buffer?function(){return(d.create=function(){return new r})()}:function(){return new d}};function h(t,e,n){e[n]=255&t}function f(t,e){this.len=t,this.next=void 0,this.val=e}function g(t,e,n){for(;t.hi;)e[n++]=127&t.lo|128,t.lo=(t.lo>>>7|t.hi<<25)>>>0,t.hi>>>=7;for(;t.lo>127;)e[n++]=127&t.lo|128,t.lo=t.lo>>>7;e[n++]=t.lo}function b(t,e,n){e[n]=255&t,e[n+1]=t>>>8&255,e[n+2]=t>>>16&255,e[n+3]=t>>>24}d.create=p(),d.alloc=function(t){return new i.Array(t)},i.Array!==Array&&(d.alloc=i.pool(d.alloc,i.Array.prototype.subarray)),d.prototype._push=function(t,e,n){return this.tail=this.tail.next=new u(t,e,n),this.len+=e,this},f.prototype=Object.create(u.prototype),f.prototype.fn=function(t,e,n){for(;t>127;)e[n++]=127&t|128,t>>>=7;e[n]=t},d.prototype.uint32=function(t){return this.len+=(this.tail=this.tail.next=new f((t>>>=0)<128?1:t<16384?2:t<2097152?3:t<268435456?4:5,t)).len,this},d.prototype.int32=function(t){return t<0?this._push(g,10,o.fromNumber(t)):this.uint32(t)},d.prototype.sint32=function(t){return this.uint32((t<<1^t>>31)>>>0)},d.prototype.uint64=function(t){var e=o.from(t);return this._push(g,e.length(),e)},d.prototype.int64=d.prototype.uint64,d.prototype.sint64=function(t){var e=o.from(t).zzEncode();return this._push(g,e.length(),e)},d.prototype.bool=function(t){return this._push(h,1,t?1:0)},d.prototype.fixed32=function(t){return this._push(b,4,t>>>0)},d.prototype.sfixed32=d.prototype.fixed32,d.prototype.fixed64=function(t){var e=o.from(t);return this._push(b,4,e.lo)._push(b,4,e.hi)},d.prototype.sfixed64=d.prototype.fixed64,d.prototype.float=function(t){return this._push(i.float.writeFloatLE,4,t)},d.prototype.double=function(t){return this._push(i.float.writeDoubleLE,8,t)};var m=i.Array.prototype.set?function(t,e,n){e.set(t,n)}:function(t,e,n){for(var r=0;r<t.length;++r)e[n+r]=t[r]};d.prototype.bytes=function(t){var e=t.length>>>0;if(!e)return this._push(h,1,0);if(i.isString(t)){var n=d.alloc(e=s.length(t));s.decode(t,n,0),t=n}return this.uint32(e)._push(m,e,t)},d.prototype.string=function(t){var e=a.length(t);return e?this.uint32(e)._push(a.write,e,t):this._push(h,1,0)},d.prototype.fork=function(){return this.states=new c(this),this.head=this.tail=new u(l,0,0),this.len=0,this},d.prototype.reset=function(){return this.states?(this.head=this.states.head,this.tail=this.states.tail,this.len=this.states.len,this.states=this.states.next):(this.head=this.tail=new u(l,0,0),this.len=0),this},d.prototype.ldelim=function(){var t=this.head,e=this.tail,n=this.len;return this.reset().uint32(n),n&&(this.tail.next=t.next,this.tail=e,this.len+=n),this},d.prototype.finish=function(){for(var t=this.head.next,e=this.constructor.alloc(this.len),n=0;t;)t.fn(t.val,e,n),n+=t.len,t=t.next;return e},d._configure=function(t){r=t,d.create=p(),r._configure()}},3155:(t,e,n)=>{"use strict";t.exports=o;var r=n(1173);(o.prototype=Object.create(r.prototype)).constructor=o;var i=n(9693);function o(){r.call(this)}function s(t,e,n){t.length<40?i.utf8.write(t,e,n):e.utf8Write?e.utf8Write(t,n):e.write(t,n)}o._configure=function(){o.alloc=i._Buffer_allocUnsafe,o.writeBytesBuffer=i.Buffer&&i.Buffer.prototype instanceof Uint8Array&&"set"===i.Buffer.prototype.set.name?function(t,e,n){e.set(t,n)}:function(t,e,n){if(t.copy)t.copy(e,n,0,t.length);else for(var r=0;r<t.length;)e[n++]=t[r++]}},o.prototype.bytes=function(t){i.isString(t)&&(t=i._Buffer_from(t,"base64"));var e=t.length>>>0;return this.uint32(e),e&&this._push(o.writeBytesBuffer,e,t),this},o.prototype.string=function(t){var e=i.Buffer.byteLength(t);return this.uint32(e),e&&this._push(s,e,t),this},o._configure()},7714:(t,e,n)=>{"use strict";e.R=void 0;const r=n(6919),i=n(7448);e.R=new class{async init(){}async createSessionHandler(t,e){const n=new r.Session(e);return await n.loadModel(t),new i.OnnxjsSessionHandler(n)}}},6018:function(t,e,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(t,e,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(e,n);i&&!("get"in i?!e.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return e[n]}}),Object.defineProperty(t,r,i)}:function(t,e,n,r){void 0===r&&(r=n),t[r]=e[n]}),i=this&&this.__exportStar||function(t,e){for(var n in t)"default"===n||Object.prototype.hasOwnProperty.call(e,n)||r(e,t,n)};Object.defineProperty(e,"__esModule",{value:!0}),i(n(8453),e);const o=n(8453);{const t=n(7714).R;(0,o.registerBackend)("webgl",t,-10)}},246:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.createAttributeWithCacheKey=void 0;class n{constructor(t){Object.assign(this,t)}get cacheKey(){return this._cacheKey||(this._cacheKey=Object.getOwnPropertyNames(this).sort().map((t=>`${this[t]}`)).join(";")),this._cacheKey}}e.createAttributeWithCacheKey=t=>new n(t)},7778:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Attribute=void 0;const r=n(1446),i=n(9395),o=n(9162),s=n(2517);var a=i.onnxruntime.experimental.fbs;class u{constructor(t){if(this._attributes=new Map,null!=t){for(const e of t)e instanceof r.onnx.AttributeProto?this._attributes.set(e.name,[u.getValue(e),u.getType(e)]):e instanceof a.Attribute&&this._attributes.set(e.name(),[u.getValue(e),u.getType(e)]);if(this._attributes.size<t.length)throw new Error("duplicated attribute names")}}set(t,e,n){this._attributes.set(t,[n,e])}delete(t){this._attributes.delete(t)}getFloat(t,e){return this.get(t,"float",e)}getInt(t,e){return this.get(t,"int",e)}getString(t,e){return this.get(t,"string",e)}getTensor(t,e){return this.get(t,"tensor",e)}getFloats(t,e){return this.get(t,"floats",e)}getInts(t,e){return this.get(t,"ints",e)}getStrings(t,e){return this.get(t,"strings",e)}getTensors(t,e){return this.get(t,"tensors",e)}get(t,e,n){const r=this._attributes.get(t);if(void 0===r){if(void 0!==n)return n;throw new Error(`required attribute not found: ${t}`)}if(r[1]!==e)throw new Error(`type mismatch: expected ${e} but got ${r[1]}`);return r[0]}static getType(t){const e=t instanceof r.onnx.AttributeProto?t.type:t.type();switch(e){case r.onnx.AttributeProto.AttributeType.FLOAT:return"float";case r.onnx.AttributeProto.AttributeType.INT:return"int";case r.onnx.AttributeProto.AttributeType.STRING:return"string";case r.onnx.AttributeProto.AttributeType.TENSOR:return"tensor";case r.onnx.AttributeProto.AttributeType.FLOATS:return"floats";case r.onnx.AttributeProto.AttributeType.INTS:return"ints";case r.onnx.AttributeProto.AttributeType.STRINGS:return"strings";case r.onnx.AttributeProto.AttributeType.TENSORS:return"tensors";default:throw new Error(`attribute type is not supported yet: ${r.onnx.AttributeProto.AttributeType[e]}`)}}static getValue(t){const e=t instanceof r.onnx.AttributeProto?t.type:t.type();if(e===r.onnx.AttributeProto.AttributeType.GRAPH||e===r.onnx.AttributeProto.AttributeType.GRAPHS)throw new Error("graph attribute is not supported yet");const n=this.getValueNoCheck(t);if(e===r.onnx.AttributeProto.AttributeType.INT&&s.LongUtil.isLong(n))return s.LongUtil.longToNumber(n);if(e===r.onnx.AttributeProto.AttributeType.INTS){const t=n,e=new Array(t.length);for(let n=0;n<t.length;n++){const r=t[n];e[n]=s.LongUtil.longToNumber(r)}return e}if(e===r.onnx.AttributeProto.AttributeType.TENSOR)return t instanceof r.onnx.AttributeProto?o.Tensor.fromProto(n):o.Tensor.fromOrtTensor(n);if(e===r.onnx.AttributeProto.AttributeType.TENSORS){if(t instanceof r.onnx.AttributeProto)return n.map((t=>o.Tensor.fromProto(t)));if(t instanceof a.Attribute)return n.map((t=>o.Tensor.fromOrtTensor(t)))}if(e===r.onnx.AttributeProto.AttributeType.STRING&&t instanceof r.onnx.AttributeProto){const t=n;return(0,s.decodeUtf8String)(t)}return e===r.onnx.AttributeProto.AttributeType.STRINGS&&t instanceof r.onnx.AttributeProto?n.map(s.decodeUtf8String):n}static getValueNoCheck(t){return t instanceof r.onnx.AttributeProto?this.getValueNoCheckFromOnnxFormat(t):this.getValueNoCheckFromOrtFormat(t)}static getValueNoCheckFromOnnxFormat(t){switch(t.type){case r.onnx.AttributeProto.AttributeType.FLOAT:return t.f;case r.onnx.AttributeProto.AttributeType.INT:return t.i;case r.onnx.AttributeProto.AttributeType.STRING:return t.s;case r.onnx.AttributeProto.AttributeType.TENSOR:return t.t;case r.onnx.AttributeProto.AttributeType.GRAPH:return t.g;case r.onnx.AttributeProto.AttributeType.FLOATS:return t.floats;case r.onnx.AttributeProto.AttributeType.INTS:return t.ints;case r.onnx.AttributeProto.AttributeType.STRINGS:return t.strings;case r.onnx.AttributeProto.AttributeType.TENSORS:return t.tensors;case r.onnx.AttributeProto.AttributeType.GRAPHS:return t.graphs;default:throw new Error(`unsupported attribute type: ${r.onnx.AttributeProto.AttributeType[t.type]}`)}}static getValueNoCheckFromOrtFormat(t){switch(t.type()){case a.AttributeType.FLOAT:return t.f();case a.AttributeType.INT:return t.i();case a.AttributeType.STRING:return t.s();case a.AttributeType.TENSOR:return t.t();case a.AttributeType.GRAPH:return t.g();case a.AttributeType.FLOATS:return t.floatsArray();case a.AttributeType.INTS:{const e=[];for(let n=0;n<t.intsLength();n++)e.push(t.ints(n));return e}case a.AttributeType.STRINGS:{const e=[];for(let n=0;n<t.stringsLength();n++)e.push(t.strings(n));return e}case a.AttributeType.TENSORS:{const e=[];for(let n=0;n<t.tensorsLength();n++)e.push(t.tensors(n));return e}default:throw new Error(`unsupported attribute type: ${a.AttributeType[t.type()]}`)}}}e.Attribute=u},7091:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.resolveBackend=e.backend=void 0;const r=n(5038),i=new Map;async function o(t){const n=e.backend;if(void 0!==n[t]&&function(t){const e=t;return"initialize"in e&&"function"==typeof e.initialize&&"createSessionHandler"in e&&"function"==typeof e.createSessionHandler&&"dispose"in e&&"function"==typeof e.dispose}(n[t])){const e=n[t];let r=e.initialize();if("object"==typeof r&&"then"in r&&(r=await r),r)return i.set(t,e),e}}e.backend={webgl:new r.WebGLBackend},e.resolveBackend=async function t(e){if(!e)return t(["webgl"]);{const t="string"==typeof e?[e]:e;for(const e of t){const t=i.get(e);if(t)return t;const n=await o(e);if(n)return n}}throw new Error("no available backend to use")}},5038:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.WebGLBackend=void 0;const r=n(8453),i=n(6231),o=n(6416),s=n(7305);e.WebGLBackend=class{get contextId(){return r.env.webgl.contextId}set contextId(t){r.env.webgl.contextId=t}get matmulMaxBatchSize(){return r.env.webgl.matmulMaxBatchSize}set matmulMaxBatchSize(t){r.env.webgl.matmulMaxBatchSize=t}get textureCacheMode(){return r.env.webgl.textureCacheMode}set textureCacheMode(t){r.env.webgl.textureCacheMode=t}get pack(){return r.env.webgl.pack}set pack(t){r.env.webgl.pack=t}get async(){return r.env.webgl.async}set async(t){r.env.webgl.async=t}initialize(){try{return this.glContext=(0,s.createWebGLContext)(this.contextId),"number"!=typeof this.matmulMaxBatchSize&&(this.matmulMaxBatchSize=16),"string"!=typeof this.textureCacheMode&&(this.textureCacheMode="full"),"boolean"!=typeof this.pack&&(this.pack=!1),"boolean"!=typeof this.async&&(this.async=!1),i.Logger.setWithEnv(r.env),i.Logger.verbose("WebGLBackend",`Created WebGLContext: ${typeof this.glContext} with matmulMaxBatchSize: ${this.matmulMaxBatchSize}; textureCacheMode: ${this.textureCacheMode}; pack: ${this.pack}; async: ${this.async}.`),!0}catch(t){return i.Logger.warning("WebGLBackend",`Unable to initialize WebGLBackend. ${t}`),!1}}createSessionHandler(t){return new o.WebGLSessionHandler(this,t)}dispose(){this.glContext.dispose()}}},5107:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.CoordsGlslLib=void 0;const r=n(2517),i=n(8520),o=n(5060),s=n(7859),a=n(9390);class u extends i.GlslLib{constructor(t){super(t)}getFunctions(){return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},this.offsetToCoords()),this.coordsToOffset()),this.toVec()),this.valueFrom()),this.getCommonUtilFuncs()),this.getInputsSamplingSnippets()),this.getOutputSamplingSnippet())}getCustomTypes(){return{}}offsetToCoords(){return{offsetToCoords:new i.GlslLibRoutine("\n      vec2 offsetToCoords(int offset, int width, int height) {\n        int t = offset / width;\n        int s = offset - t*width;\n        vec2 coords = (vec2(s,t) + vec2(0.5,0.5)) / vec2(width, height);\n        return coords;\n      }\n      ")}}coordsToOffset(){return{coordsToOffset:new i.GlslLibRoutine("\n      int coordsToOffset(vec2 coords, int width, int height) {\n        float s = coords.s * float(width);\n        float t = coords.t * float(height);\n        int offset = int(t) * width + int(s);\n        return offset;\n      }\n      ")}}getOutputSamplingSnippet(){const t=this.context.outputTextureLayout;return t.isPacked?this.getPackedOutputSamplingSnippet(t):this.getUnpackedOutputSamplingSnippet(t)}getPackedOutputSamplingSnippet(t){const e=t.unpackedShape,n=[t.width,t.height],r={},s="getOutputCoords";switch(e.length){case 0:r[s]=this.getOutputScalarCoords();break;case 1:r[s]=this.getOutputPacked1DCoords(e,n);break;case 2:r[s]=this.getOutputPacked2DCoords(e,n);break;case 3:r[s]=this.getOutputPacked3DCoords(e,n);break;default:r[s]=this.getOutputPackedNDCoords(e,n)}const a=`\n      void setOutput(vec4 val) {\n        ${(0,o.getGlsl)(this.context.glContext.version).output} = val;\n      }\n    `;return r.floatTextureSetRGBA=new i.GlslLibRoutine(a),r}getUnpackedOutputSamplingSnippet(t){const e=t.unpackedShape,n=[t.width,t.height],r={},s="getOutputCoords";switch(e.length){case 0:r[s]=this.getOutputScalarCoords();break;case 1:r[s]=this.getOutputUnpacked1DCoords(e,n);break;case 2:r[s]=this.getOutputUnpacked2DCoords(e,n);break;case 3:r[s]=this.getOutputUnpacked3DCoords(e,n);break;case 4:r[s]=this.getOutputUnpacked4DCoords(e,n);break;case 5:r[s]=this.getOutputUnpacked5DCoords(e,n);break;case 6:r[s]=this.getOutputUnpacked6DCoords(e,n);break;default:throw new Error(`Unsupported output dimensionality: ${e.length}`)}const a=`\n        void setOutput(float val) {\n          ${(0,o.getGlsl)(this.context.glContext.version).output} = vec4(val, 0, 0, 0);\n        }\n    `;return r.floatTextureSetR=new i.GlslLibRoutine(a),r}getOutputScalarCoords(){return new i.GlslLibRoutine("\n      int getOutputCoords() {\n        return 0;\n      }\n    ")}getOutputPacked1DCoords(t,e){const n=e;let r="";return 1===n[0]?(r=`\n          int getOutputCoords() {\n            return 2 * int(TexCoords.y * ${n[1]}.0);\n          }\n        `,new i.GlslLibRoutine(r)):1===n[1]?(r=`\n          int getOutputCoords() {\n            return 2 * int(TexCoords.x * ${n[0]}.0);\n          }\n        `,new i.GlslLibRoutine(r)):(r=`\n        int getOutputCoords() {\n          ivec2 resTexRC = ivec2(TexCoords.xy *\n                                 vec2(${n[0]}, ${n[1]}));\n          return 2 * (resTexRC.y * ${n[0]} + resTexRC.x);\n        }\n      `,new i.GlslLibRoutine(r))}getOutputPacked2DCoords(t,e){let n="";if(r.ArrayUtil.arraysEqual(t,e))return n=`\n        ivec2 getOutputCoords() {\n          return 2 * ivec2(TexCoords.xy * vec2(${e[0]}, ${e[1]}));\n        }\n      `,new i.GlslLibRoutine(n);const o=e,s=Math.ceil(t[1]/2);return n=`\n        ivec2 getOutputCoords() {\n          ivec2 resTexRC = ivec2(TexCoords.xy *\n                                vec2(${o[0]}, ${o[1]}));\n\n          int index = resTexRC.y * ${o[0]} + resTexRC.x;\n\n          // reverse r and c order for packed texture\n          int r = imod(index, ${s}) * 2;\n          int c = 2 * (index / ${s});\n\n          return ivec2(r, c);\n        }\n      `,new i.GlslLibRoutine(n)}getOutputPacked3DCoords(t,e){const n=[e[0],e[1]],r=Math.ceil(t[2]/2),o=r*Math.ceil(t[1]/2),s=`\n        ivec3 getOutputCoords() {\n          ivec2 resTexRC = ivec2(TexCoords.xy *\n                                vec2(${n[0]}, ${n[1]}));\n          int index = resTexRC.y * ${n[0]} + resTexRC.x;\n\n          int b = index / ${o};\n          index -= b * ${o};\n\n          // reverse r and c order for packed texture\n          int r = imod(index, ${r}) * 2;\n          int c = 2 * (index / ${r});\n\n          return ivec3(b, r, c);\n        }\n      `;return new i.GlslLibRoutine(s)}getOutputPackedNDCoords(t,e){const n=[e[0],e[1]],r=Math.ceil(t[t.length-1]/2),o=r*Math.ceil(t[t.length-2]/2);let s=o,a="",u="b, r, c";for(let e=2;e<t.length-1;e++)s*=t[t.length-e-1],a=`\n      int b${e} = index / ${s};\n      index -= b${e} * ${s};\n    `+a,u=`b${e}, `+u;const l=`\n      ivec${t.length} getOutputCoords() {\n        ivec2 resTexRC = ivec2(TexCoords.xy *\n                              vec2(${n[0]}, ${n[1]}));\n        int index = resTexRC.y * ${n[0]} + resTexRC.x;\n\n        ${a}\n\n        int b = index / ${o};\n        index -= b * ${o};\n\n        // reverse r and c order for packed texture\n        int r = imod(index, ${r}) * 2;\n        int c = 2 * (index / ${r});\n\n        return ivec${t.length}(${u});\n      }\n    `;return new i.GlslLibRoutine(l)}getOutputUnpacked1DCoords(t,e){const n=`\n        int getOutputCoords() {\n          ivec2 resTexRC = ivec2(TexCoords.xy *\n                                vec2(${e[0]}, ${e[1]}));\n          return resTexRC.y * ${e[0]} + resTexRC.x;\n        }\n      `;return new i.GlslLibRoutine(n)}getOutputUnpacked2DCoords(t,e){const n=`\n        ivec2 getOutputCoords() {\n          ivec2 resTexRC = ivec2(TexCoords.xy *\n                                vec2(${e[0]}, ${e[1]}));\n          int index = resTexRC.y * ${e[0]} + resTexRC.x;\n          int r = index / ${t[1]};\n          int c = index - r * ${t[1]};\n          return ivec2(r, c);\n        }\n      `;return new i.GlslLibRoutine(n)}getOutputUnpacked3DCoords(t,e){let n="";const r=t.length;let o=null;r<2&&(o=[]),o=new Array(r-1),o[r-2]=t[r-1];for(let e=r-3;e>=0;--e)o[e]=o[e+1]*t[e+1];const s=["r","c","d"],a=o.map(((t,e)=>`int ${s[e]} = index / ${t}; ${e===o.length-1?`int ${s[e+1]} = index - ${s[e]} * ${t}`:`index -= ${s[e]} * ${t}`};`)).join("");return n=`\n        ivec3 getOutputCoords() {\n          ivec2 resTexRC = ivec2(TexCoords.xy *\n                                vec2(${e[0]}, ${e[1]}));\n          int index = resTexRC.y * ${e[0]} + resTexRC.x;\n          ${a}\n          return ivec3(r, c, d);\n        }\n      `,new i.GlslLibRoutine(n)}getOutputUnpacked4DCoords(t,e){let n="";const r=t.length;let o=null;r<2&&(o=[]),o=new Array(r-1),o[r-2]=t[r-1];for(let e=r-3;e>=0;--e)o[e]=o[e+1]*t[e+1];const s=["r","c","d","d2"],a=o.map(((t,e)=>`int ${s[e]} = index / ${t}; ${e===o.length-1?`int ${s[e+1]} = index - ${s[e]} * ${t}`:`index -= ${s[e]} * ${t}`};`)).join("");return n=`\n      ivec4 getOutputCoords() {\n          ivec2 resTexRC = ivec2(TexCoords.xy *\n                                vec2(${e[0]}, ${e[1]}));\n          int index = resTexRC.y * ${e[0]} + resTexRC.x;\n          ${a}\n          return ivec4(r, c, d, d2);\n        }\n      `,new i.GlslLibRoutine(n)}getOutputUnpacked5DCoords(t,e){let n="";const r=t.length;let o=null;r<2&&(o=[]),o=new Array(r-1),o[r-2]=t[r-1];for(let e=r-3;e>=0;--e)o[e]=o[e+1]*t[e+1];const s=["r","c","d","d2","d3"],a=o.map(((t,e)=>`int ${s[e]} = index / ${t}; ${e===o.length-1?`int ${s[e+1]} = index - ${s[e]} * ${t}`:`index -= ${s[e]} * ${t}`};`)).join("");return n=`\n      ivec5 getOutputCoords() {\n          ivec2 resTexRC = ivec2(TexCoords.xy *\n                                vec2(${e[0]}, ${e[1]}));\n          int index = resTexRC.y * ${e[0]} + resTexRC.x;\n          ${a}\n          return ivec5(r, c, d, d2, d3);\n        }\n      `,new i.GlslLibRoutine(n)}getOutputUnpacked6DCoords(t,e){let n="";const r=t.length;let o=null;r<2&&(o=[]),o=new Array(r-1),o[r-2]=t[r-1];for(let e=r-3;e>=0;--e)o[e]=o[e+1]*t[e+1];const s=["r","c","d","d2","d3","d4"],a=o.map(((t,e)=>`int ${s[e]} = index / ${t}; ${e===o.length-1?`int ${s[e+1]} = index - ${s[e]} * ${t}`:`index -= ${s[e]} * ${t}`};`)).join("");return n=`\n     ivec6 getOutputCoords() {\n         ivec2 resTexRC = ivec2(TexCoords.xy *\n                               vec2(${e[0]}, ${e[1]}));\n         int index = resTexRC.y * ${e[0]} + resTexRC.x;\n         ${a}\n         return ivec6(r, c, d, d2, d3, d4);\n       }\n     `,new i.GlslLibRoutine(n)}getCommonUtilFuncs(){const t={};let e="uvFromFlat";t[e]=new i.GlslLibRoutine("\n    vec2 uvFromFlat(int texNumR, int texNumC, int index) {\n      int texC = index / texNumR;\n      int texR = index - texC * texNumR;\n      // TODO: swap texR, texC order in following function so row is corresponding to u and column is corresponding to\n      //       v.\n      return (vec2(texR, texC) + halfCR) / vec2(texNumR, texNumC);\n    }\n    "),e="packedUVfrom1D",t[e]=new i.GlslLibRoutine("\n      vec2 packedUVfrom1D(int texNumR, int texNumC, int index) {\n        int texelIndex = index / 2;\n        int texR = texelIndex / texNumC;\n        int texC = texelIndex - texR * texNumC;\n        return (vec2(texC, texR) + halfCR) / vec2(texNumC, texNumR);\n      }\n      "),e="packedUVfrom2D",t[e]=new i.GlslLibRoutine("\n      vec2 packedUVfrom2D(int texNumR, int texNumC, int texelsInLogicalRow, int row, int col) {\n        int texelIndex = (row / 2) * texelsInLogicalRow + (col / 2);\n        int texR = texelIndex / texNumC;\n        int texC = texelIndex - texR * texNumC;\n        return (vec2(texC, texR) + halfCR) / vec2(texNumC, texNumR);\n      }\n      "),e="packedUVfrom3D",t[e]=new i.GlslLibRoutine("\n      vec2 packedUVfrom3D(int texNumR, int texNumC,\n          int texelsInBatch, int texelsInLogicalRow, int b,\n          int row, int col) {\n        int index = b * texelsInBatch + (row / 2) * texelsInLogicalRow + (col / 2);\n        int texR = index / texNumC;\n        int texC = index - texR * texNumC;\n        return (vec2(texC, texR) + halfCR) / vec2(texNumC, texNumR);\n      }\n      "),e="sampleTexture";const n=(0,o.getGlsl)(this.context.glContext.version);return t[e]=new i.GlslLibRoutine(`\n        float sampleTexture(sampler2D textureSampler, vec2 uv) {\n            return ${n.texture2D}(textureSampler, uv).r;\n        }`),t}getInputsSamplingSnippets(){const t={},e=this.context.outputTextureLayout;return this.context.programInfo.inputNames.forEach(((n,r)=>{const i=this.context.inputTextureLayouts[r],o=(0,a.generateShaderFuncNameFromInputSamplerName)(n);i.isPacked?t[o]=this.getPackedSamplerFromInput(o,n,i):t[o]=this.getUnpackedSamplerFromInput(o,n,i);const s=(0,a.generateShaderFuncNameFromInputSamplerNameAtOutCoords)(n);i.unpackedShape.length<=e.unpackedShape.length&&(i.isPacked?t[s]=this.getPackedSamplerAtOutputCoords(s,i,e,n):t[s]=this.getUnpackedSamplerAtOutputCoords(s,i,e,n))})),t}getPackedSamplerAtOutputCoords(t,e,n,o){const s=e.unpackedShape,u=n.unpackedShape,l=o,c=(0,a.generateShaderFuncNameFromInputSamplerName)(l),d=s.length,p=u.length,h=r.BroadcastUtil.getBroadcastDims(s,u),f=(0,a.getCoordsDataType)(p),g=p-d;let b;const m=(0,a.getGlChannels)();b=0===d?"":p<2&&h.length>=1?"coords = 0;":h.map((t=>`coords.${m[t+g]} = 0;`)).join("\n");let y="";y=p<2&&d>0?"coords":s.map(((t,e)=>`coords.${m[e+g]}`)).join(", ");let x="return outputValue;";const _=1===r.ShapeUtil.size(s),w=1===r.ShapeUtil.size(u);if(1!==d||_||w){if(_&&!w)x=1===p?"\n          return vec4(outputValue.x, outputValue.x, 0., 0.);\n        ":"\n          return vec4(outputValue.x);\n        ";else if(h.length){const t=d-2,e=d-1;h.indexOf(t)>-1&&h.indexOf(e)>-1?x="return vec4(outputValue.x);":h.indexOf(t)>-1?x="return vec4(outputValue.x, outputValue.y, outputValue.x, outputValue.y);":h.indexOf(e)>-1&&(x="return vec4(outputValue.xx, outputValue.zz);")}}else x="\n        return vec4(outputValue.xy, outputValue.xy);\n      ";const v=`\n      vec4 ${t}() {\n        ${f} coords = getOutputCoords();\n        \n        int lastDim = coords.${m[p-1]};\n        coords.${m[p-1]} = coords.${m[p-2]};\n        coords.${m[p-2]} = lastDim;\n      \n        ${b}\n        vec4 outputValue = ${c}(${y});\n        ${x}\n      }\n    `;return new i.GlslLibRoutine(v,["coordinates.getOutputCoords"])}getUnpackedSamplerAtOutputCoords(t,e,n,o){const s=[n.width,n.height],u=[e.width,e.height],l=e.unpackedShape.length,c=n.unpackedShape.length,d=e.unpackedShape,p=n.unpackedShape,h=(0,a.generateShaderFuncNameFromInputSamplerName)(o);if(l===c&&r.ArrayUtil.arraysEqual(u,s)){const e=`\n          float ${t}() {\n            return sampleTexture(${o}, TexCoords);\n          }\n        `;return new i.GlslLibRoutine(e,["coordinates.sampleTexture"])}const f=(0,a.getCoordsDataType)(c),g=r.BroadcastUtil.getBroadcastDims(d,p),b=c-l;let m;const y=(0,a.getGlChannels)();m=0===l?"":c<2&&g.length>=1?"coords = 0;":g.map((t=>`coords.${y[t+b]} = 0;`)).join("\n");let x="";x=c<2&&l>0?"coords":e.unpackedShape.map(((t,e)=>`coords.${y[e+b]}`)).join(", ");const _=`\n        float ${t}() {\n          ${f} coords = getOutputCoords();\n          ${m}\n          return ${h}(${x});\n        }\n      `;return new i.GlslLibRoutine(_,["coordinates.getOutputCoords"])}getPackedSamplerFromInput(t,e,n){switch(n.unpackedShape.length){case 0:return this.getPackedSamplerScalar(t,e);case 1:return this.getPackedSampler1D(t,e,n);case 2:return this.getPackedSampler2D(t,e,n);case 3:return this.getPackedSampler3D(t,e,n);default:return this.getPackedSamplerND(t,e,n)}}getUnpackedSamplerFromInput(t,e,n){const r=n.unpackedShape;switch(r.length){case 0:return this.getUnpackedSamplerScalar(t,e,n);case 1:return this.getUnpackedSampler1D(t,e,n);case 2:return this.getUnpackedSampler2D(t,e,n);case 3:return this.getUnpackedSampler3D(t,e,n);case 4:return this.getUnpackedSampler4D(t,e,n);case 5:return this.getUnpackedSampler5D(t,e,n);case 6:return this.getUnpackedSampler6D(t,e,n);default:throw new Error(`Unsupported dimension ${r.length}-D`)}}getPackedSamplerScalar(t,e){const n=`\n          vec4 ${t}() {\n            return ${(0,o.getGlsl)(this.context.glContext.version).texture2D}(${e}, halfCR);\n          }\n        `;return new i.GlslLibRoutine(n)}getPackedSampler1D(t,e,n){const r=[n.width,n.height],s=[r[1],r[0]],a=(0,o.getGlsl)(this.context.glContext.version),u=`vec4 ${t}(int index) {\n      vec2 uv = packedUVfrom1D(\n      ${s[0]}, ${s[1]}, index);\n      return ${a.texture2D}(${e}, uv);\n    }`;return new i.GlslLibRoutine(u,["coordinates.packedUVfrom1D"])}getPackedSampler2D(t,e,n){const s=n.unpackedShape,a=[n.width,n.height],u=(0,o.getGlsl)(this.context.glContext.version),l=a[0],c=a[1];if(null!=a&&r.ArrayUtil.arraysEqual(s,a)){const n=`vec4 ${t}(int row, int col) {\n        vec2 uv = (vec2(col, row) + halfCR) / vec2(${c}.0, ${l}.0);\n        return ${u.texture2D}(${e}, uv);\n      }`;return new i.GlslLibRoutine(n)}const d=a,p=Math.ceil(s[1]/2),h=`vec4 ${t}(int row, int col) {\n      vec2 uv = packedUVfrom2D(${d[1]}, ${d[0]}, ${p}, row, col);\n      return ${u.texture2D}(${e}, uv);\n    }`;return new i.GlslLibRoutine(h,["coordinates.packedUVfrom2D"])}getPackedSampler3D(t,e,n){const r=n.unpackedShape,s=[n.width,n.height],u=[s[0],s[1]],l=(0,o.getGlsl)(this.context.glContext.version);if(1===r[0]){const o=r.slice(1),s=[1,2],u=(0,a.squeezeInputShape)(r,o),l=["b","row","col"],c=JSON.parse(JSON.stringify(n));c.unpackedShape=u;const d=this.getPackedSamplerFromInput(t,e,c),p=`${d.routineBody}\n      vec4 ${t}(int b, int row, int col) {\n        return ${t}(${(0,a.getSqueezedParams)(l,s)});\n      } `;return new i.GlslLibRoutine(p,d.dependencies)}const c=u[0],d=u[1],p=Math.ceil(r[2]/2),h=`vec4 ${t}(int b, int row, int col) {\n      vec2 uv = packedUVfrom3D(\n        ${d}, ${c}, ${p*Math.ceil(r[1]/2)}, ${p}, b, row, col);\n      return ${l.texture2D}(${e}, uv);}`;return new i.GlslLibRoutine(h,["coordinates.packedUVfrom3D"])}getPackedSamplerND(t,e,n){const r=n.unpackedShape,s=r.length,a=[n.width,n.height],u=(0,o.getGlsl)(this.context.glContext.version),l=[a[0],a[1]],c=l[1],d=l[0],p=Math.ceil(r[s-1]/2);let h=p*Math.ceil(r[s-2]/2),f="int b, int row, int col",g=`b * ${h} + (row / 2) * ${p} + (col / 2)`;for(let t=2;t<s-1;t++)f=`int b${t}, `+f,h*=r[s-t-1],g=`b${t} * ${h} + `+g;const b=`vec4 ${t}(${f}) {\n      int index = ${g};\n      int texR = index / ${d};\n      int texC = index - texR * ${d};\n      vec2 uv = (vec2(texC, texR) + halfCR) / vec2(${d}, ${c});\n      return ${u.texture2D}(${e}, uv);\n    }`;return new i.GlslLibRoutine(b)}getUnpackedSamplerScalar(t,e,n){const[r,o]=[n.width,n.height];if(1===r&&1===o){const n=`\n          float ${t}() {\n            return sampleTexture(${e}, halfCR);\n          }\n        `;return new i.GlslLibRoutine(n,["coordinates.sampleTexture"])}const s=`\n        float ${t}() {\n          int offset_${e} = coordsToOffset(TexCoords, ${r}, ${o});\n          vec2 uv = uvFromFlat(${r}, ${o}, offset_${e});\n          return sampleTexture(${e}, uv);\n        }\n      `;return new i.GlslLibRoutine(s,["coordinates.uvFromFlat","coordinates.sampleTexture","coordinates.coordsToOffset"])}getUnpackedSampler1D(t,e,n){const r=n.width,o=n.height;if(1===o&&1===r){const n=`\n        float ${t}(int index) {\n          return sampleTexture(${e}, halfCR);\n        }\n      `;return new i.GlslLibRoutine(n,["coordinates.sampleTexture"])}if(1===o){const n=`\n          float ${t}(int index) {\n            vec2 uv = vec2((float(index) + 0.5) / ${r}.0, 0.5);\n            return sampleTexture(${e}, uv);\n          }\n        `;return new i.GlslLibRoutine(n,["coordinates.sampleTexture"])}if(1===r){const n=`\n          float ${t}(int index) {\n            vec2 uv = vec2(0.5, (float(index) + 0.5) / ${o}.0);\n            return sampleTexture(${e}, uv);\n          }\n        `;return new i.GlslLibRoutine(n,["coordinates.sampleTexture"])}const s=`\n        float ${t}(int index) {\n          vec2 uv = uvFromFlat(${r}, ${o}, index);\n          return sampleTexture(${e}, uv);\n        }\n      `;return new i.GlslLibRoutine(s,["coordinates.uvFromFlat","coordinates.sampleTexture"])}getUnpackedSampler2D(t,e,n){const o=n.unpackedShape,u=[n.height,n.width];if(null!=u&&r.ArrayUtil.arraysEqual(o,u)){const n=`\n          float ${t}(int row, int col) {\n            vec2 uv = (vec2(row, col) + halfCR) / vec2(${u[1]}.0, ${u[0]}.0);\n            return sampleTexture(${e}, uv);\n          }\n        `;return new i.GlslLibRoutine(n,["coordinates.sampleTexture"])}const{newShape:l,keptDims:c}=(0,s.squeezeShape)(o),d=l;if(d.length<o.length){const r=(0,a.squeezeInputShape)(o,d),s=JSON.parse(JSON.stringify(n));s.unpackedShape=r;const u=["col","row"],l=`\n          ${this.getUnpackedSamplerFromInput(t,e,s).routineBody}\n          float ${t}(int row, int col) {\n            return ${t}(${(0,a.getSqueezedParams)(u,c)});\n          }\n        `;return new i.GlslLibRoutine(l,["coordinates.sampleTexture"])}const p=u[1],h=u[0];if(1===h){const n=`\n          float ${t}(int row, int col) {\n            int offset_${e} = coordsToOffset(TexCoords, ${p}, ${h});\n            float index = dot(vec3(row, col, offset_${e}), vec3(${o[1]}, 1, 1));\n            vec2 uv = vec2(0.5, (index + 0.5) / ${p}.0);\n            return sampleTexture(${e}, uv);\n          }\n        `;return new i.GlslLibRoutine(n,["coordinates.sampleTexture","coordinates.coordsToOffset"])}if(1===p){const n=`\n          float ${t}(int row, int col) {\n            int offset_${e} = coordsToOffset(TexCoords, ${p}, ${h});\n            float index = dot(vec3(row, col, offset_${e}), vec3(${o[1]}, 1, 1));\n            vec2 uv = vec2((index + 0.5) / ${h}.0, 0.5);\n            return sampleTexture(${e}, uv);\n          }\n        `;return new i.GlslLibRoutine(n,["coordinates.sampleTexture","coordinates.coordsToOffset"])}const f=`\n        float ${t}(int row, int col) {\n          int index = col * ${o[1]} + row;\n          vec2 uv = uvFromFlat(${p}, ${h}, index);\n          return sampleTexture(${e}, uv);\n        }\n      `;return new i.GlslLibRoutine(f,["coordinates.uvFromFlat","coordinates.sampleTexture","coordinates.coordsToOffset"])}getUnpackedSampler3D(t,e,n){const r=n.unpackedShape,o=r[1]*r[2],u=r[2],{newShape:l,keptDims:c}=(0,s.squeezeShape)(r),d=l;if(d.length<r.length){const o=(0,a.squeezeInputShape)(r,d),s=["batch","col","row"],u=JSON.parse(JSON.stringify(n));u.unpackedShape=o;const l=this.getUnpackedSamplerFromInput(t,e,u),p=c.reverse(),h=`\n          ${l.routineBody}\n          float ${t}(int batch, int row, int col) {\n            return ${t}(${(0,a.getSqueezedParams)(s,p)});\n          }\n        `;return new i.GlslLibRoutine(h,l.dependencies)}const p=`\n          float ${t}(int depth, int row, int col) {\n            // Explicitly use integer operations as dot() only works on floats.\n            int index = depth * ${o} + col * ${u} + row;\n            vec2 uv = uvFromFlat(${n.width}, ${n.height}, index);\n            return sampleTexture(${e}, uv);\n          }\n      `;return new i.GlslLibRoutine(p,["coordinates.uvFromFlat","coordinates.sampleTexture","coordinates.coordsToOffset"])}getUnpackedSampler4D(t,e,n){const r=n.unpackedShape,o=r[3],s=r[2]*o,a=`\n        float ${t}(int row, int col, int depth, int depth2) {\n          int index = row * ${r[1]*s} + col * ${s} +\n              depth2 * ${o} + depth;\n          vec2 uv = uvFromFlat(${n.width}, ${n.height}, index);\n          return sampleTexture(${e}, uv);\n        }\n      `;return new i.GlslLibRoutine(a,["coordinates.uvFromFlat","coordinates.sampleTexture"])}getUnpackedSampler5D(t,e,n){const r=n.unpackedShape,o=r[4],u=r[3]*o,l=r[2]*u,c=r[1]*l,{newShape:d,keptDims:p}=(0,s.squeezeShape)(r);if(d.length<r.length){const o=(0,a.squeezeInputShape)(r,d),s=["row","col","depth","depth2","depth3"],u=JSON.parse(JSON.stringify(n));u.unpackedShape=o;const l=`\n          ${this.getUnpackedSamplerFromInput(t,e,u).routineBody}\n          float ${t}(int row, int col, int depth, int depth2, int depth3) {\n            return ${t}(${(0,a.getSqueezedParams)(s,p)});\n          }\n        `;return new i.GlslLibRoutine(l,["coordinates.sampleTexture","coordinates.uvFromFlat"])}const h=`\n        float ${t}(int row, int col, int depth, int depth2, int depth3) {\n          int index = row * ${c} + col * ${l} + depth * ${u} +\n          depth3 * ${o} + depth2;\n          vec2 uv = uvFromFlat(${n.width}, ${n.height}, index);\n          return sampleTexture(${e}, uv);\n        }\n      `;return new i.GlslLibRoutine(h,["coordinates.sampleTexture","coordinates.uvFromFlat"])}getUnpackedSampler6D(t,e,n){const r=n.unpackedShape,o=r[5],u=r[4]*o,l=r[3]*u,c=r[2]*l,d=r[1]*c,{newShape:p,keptDims:h}=(0,s.squeezeShape)(r);if(p.length<r.length){const o=(0,a.squeezeInputShape)(r,p),s=["row","col","depth","depth2","depth3","depth4"],u=JSON.parse(JSON.stringify(n));u.unpackedShape=o;const l=`\n            ${this.getUnpackedSamplerFromInput(t,e,u).routineBody}\n            float ${t}(int row, int col, int depth,\n              int depth2, int depth3, int depth4) {\n              return ${t}(${(0,a.getSqueezedParams)(s,h)});\n            }\n          `;return new i.GlslLibRoutine(l,["coordinates.sampleTexture","coordinates.uvFromFlat"])}const f=`\n          float ${t}(int row, int col, int depth,\n            int depth2, int depth3, int depth4) {\n            int index = row * ${d} + col * ${c} + depth * ${l} +\n            depth2 * ${u} + depth3 * ${o} + depth4;\n            vec2 uv = uvFromFlat(${n.width}, ${n.height}, index);\n            return sampleTexture(${e}, uv);\n          }\n        `;return new i.GlslLibRoutine(f,["coordinates.uvFromFlat","coordinates.sampleTexture","coordinates.coordsToOffset"])}toVec(){const t=this.context.outputTextureLayout,e=t.shape.length,n=t.strides,r=t.width,o=t.height,s=[];for(let t=0;t<e-1;++t)s.push(`\n        c[${t}] = offset / ${n[t]};`),s.push(`\n        offset -= c[${t}] * ${n[t]};`);s.push(`\n        c[${e-1}] = offset;`);const a=`\n      void toVec(vec2 texCoords, out int c[${e}]) {\n        int offset = coordsToOffset(texCoords, ${r}, ${o});\n        ${s.join("")}\n      }\n      void toVec(int offset, out int c[${e}]) {\n        ${s.join("")}\n      }\n    `;return{toVec:new i.GlslLibRoutine(a,["coordinates.coordsToOffset"])}}valueFrom(){const t={};return this.context.programInfo.inputNames.forEach(((e,n)=>{const r=this.context.inputTextureLayouts[n],o=(r.unpackedShape.length>0?r.unpackedShape:r.shape).length;let s=`_${e}`;t[s]=new i.GlslLibRoutine(this.getValueFromSingle(e,o,r.width,r.height,!1),[`shapeUtils.indicesToOffset${s}`,"coordinates.offsetToCoords","fragcolor.getColorAsFloat"]),s+="_T",t[s]=new i.GlslLibRoutine(this.getValueFromSingle(e,o,r.width,r.height,!0),[`shapeUtils.indicesToOffset${s}`,"coordinates.offsetToCoords","fragcolor.getColorAsFloat"])})),t}getValueFromSingle(t,e,n,r,i){let s=`_${t}`;return i&&(s+="_T"),`\n        float ${s}(int m[${e}]) {\n          int offset = indicesToOffset${s}(m);\n          vec2 coords = offsetToCoords(offset, ${n}, ${r});\n          float value = getColorAsFloat(${(0,o.getGlsl)(this.context.glContext.version).texture2D}(${t}, coords));\n          return value;\n        }\n        `}getPackedValueFrom(t,e,n,r,i){let s=`_${t}_Pack`;return i&&(s+="_T"),`\n        vec4 ${s}(int m[${e}]) {\n          int offset = indicesToOffset_${t}(m);\n          vec2 coords = offsetToCoords(offset, ${n}, ${r});\n          return ${(0,o.getGlsl)(this.context.glContext.version).texture2D}(${t}, coords);\n        }\n        `}}e.CoordsGlslLib=u},8520:(t,e)=>{"use strict";var n;Object.defineProperty(e,"__esModule",{value:!0}),e.TopologicalSortGlslRoutines=e.GlslLibRoutineNode=e.GlslLibRoutine=e.GlslLib=e.GlslContext=e.FunctionType=void 0,(n=e.FunctionType||(e.FunctionType={}))[n.ValueBased=0]="ValueBased",n[n.Positional=1]="Positional",e.GlslContext=class{constructor(t,e,n,r){this.glContext=t,this.programInfo=e,this.inputTextureLayouts=n,this.outputTextureLayout=r}},e.GlslLib=class{constructor(t){this.context=t}},e.GlslLibRoutine=class{constructor(t,e){this.routineBody=t,this.dependencies=e}},e.GlslLibRoutineNode=class{constructor(t,e,n){this.name=t,this.dependencies=n||[],e&&(this.routineBody=e)}addDependency(t){t&&this.dependencies.push(t)}},e.TopologicalSortGlslRoutines=class{static returnOrderedNodes(t){if(!t||0===t.length)return[];if(1===t.length)return t;const e=new Set,n=new Set,r=new Array;return this.createOrderedNodes(t,e,n,r),r}static createOrderedNodes(t,e,n,r){for(let i=0;i<t.length;++i)this.dfsTraverse(t[i],e,n,r)}static dfsTraverse(t,e,n,r){if(!t||n.has(t.name))return;if(e.has(t.name))throw new Error("Cyclic dependency detected. Can't topologically sort routines needed for shader.");e.add(t.name);const i=t.dependencies;if(i&&i.length>0)for(let t=0;t<i.length;++t)this.dfsTraverse(i[t],e,n,r);r.push(t),n.add(t.name),e.delete(t.name)}}},7341:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.EncodingGlslLib=void 0;const r=n(8520);class i extends r.GlslLib{constructor(t){super(t)}getFunctions(){return Object.assign(Object.assign({},this.encodeFloat32()),this.decodeFloat32())}getCustomTypes(){return{}}encodeFloat32(){return{encode:new r.GlslLibRoutine("highp vec4 encode(highp float f) {\n        return vec4(f, 0.0, 0.0, 0.0);\n      }\n        ")}}decodeFloat32(){return{decode:new r.GlslLibRoutine("highp float decode(highp vec4 rgba) {\n        return rgba.r;\n      }\n        ")}}encodeUint8(){const t=i.isLittleEndian()?"rgba.rgba=rgba.abgr;":"";return{encode:new r.GlslLibRoutine(`\n      highp vec4 encode(highp float f) {\n        highp float F = abs(f);\n        highp float Sign = step(0.0,-f);\n        highp float Exponent = floor(log2(F));\n        highp float Mantissa = (exp2(- Exponent) * F);\n        Exponent = floor(log2(F) + 127.0) + floor(log2(Mantissa));\n        highp vec4 rgba;\n        rgba[0] = 128.0 * Sign  + floor(Exponent*exp2(-1.0));\n        rgba[1] = 128.0 * mod(Exponent,2.0) + mod(floor(Mantissa*128.0),128.0);\n        rgba[2] = floor(mod(floor(Mantissa*exp2(23.0 -8.0)),exp2(8.0)));\n        rgba[3] = floor(exp2(23.0)*mod(Mantissa,exp2(-15.0)));\n        ${t}\n        rgba = rgba / 255.0; // values need to be normalized to [0,1]\n        return rgba;\n    }\n        `)}}decodeUint8(){const t=i.isLittleEndian()?"rgba.rgba=rgba.abgr;":"";return{decode:new r.GlslLibRoutine(`\n        highp float decode(highp vec4 rgba) {\n          rgba = rgba * 255.0; // values need to be de-normalized from [0,1] to [0,255]\n          ${t}\n          highp float Sign = 1.0 - step(128.0,rgba[0])*2.0;\n          highp float Exponent = 2.0 * mod(rgba[0],128.0) + step(128.0,rgba[1]) - 127.0;\n          highp float Mantissa = mod(rgba[1],128.0)*65536.0 + rgba[2]*256.0 +rgba[3] + float(0x800000);\n          highp float Result =  Sign * exp2(Exponent) * (Mantissa * exp2(-23.0 ));\n          return Result;\n      }\n        `)}}static isLittleEndian(){const t=new ArrayBuffer(4),e=new Uint32Array(t),n=new Uint8Array(t);if(e[0]=3735928559,239===n[0])return!0;if(222===n[0])return!1;throw new Error("unknown endianness")}}e.EncodingGlslLib=i},9894:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.FragColorGlslLib=void 0;const r=n(8520),i=n(5060);class o extends r.GlslLib{constructor(t){super(t)}getFunctions(){return Object.assign(Object.assign({},this.setFragColor()),this.getColorAsFloat())}getCustomTypes(){return{}}setFragColor(){const t=(0,i.getGlsl)(this.context.glContext.version);return{setFragColor:new r.GlslLibRoutine(`\n        void setFragColor(float value) {\n            ${t.output} = encode(value);\n        }\n        `,["encoding.encode"])}}getColorAsFloat(){return{getColorAsFloat:new r.GlslLibRoutine("\n        float getColorAsFloat(vec4 color) {\n            return decode(color);\n        }\n        ",["encoding.decode"])}}}e.FragColorGlslLib=o},2848:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.replaceInlines=void 0;const n=/@inline[\s\n\r]+(\w+)[\s\n\r]+([0-9a-zA-Z_]+)\s*\(([^)]*)\)\s*{(([^}]|[\n\r])*)}/gm;e.replaceInlines=function(t){const e={};let r;for(;null!==(r=n.exec(t));){const t=r[3].split(",").map((t=>{const e=t.trim().split(" ");return e&&2===e.length?{type:e[0],name:e[1]}:null})).filter((t=>null!==t));e[r[2]]={params:t,body:r[4]}}for(const n in e){const i="(\\w+)?\\s+([_0-9a-zA-Z]+)\\s+=\\s+__FUNC__\\((.*)\\)\\s*;".replace("__FUNC__",n),o=new RegExp(i,"gm");for(;null!==(r=o.exec(t));){const i=r[1],o=r[2],s=r[3].split(","),a=i?`${i} ${o};`:"";let u=e[n].body,l="";e[n].params.forEach(((t,e)=>{t&&(l+=`${t.type} ${t.name} = ${s[e]};\n`)})),u=`${l}\n ${u}`,u=u.replace("return",`${o} = `);const c=`\n      ${a}\n      {\n        ${u}\n      }\n      `;t=t.replace(r[0],c)}}return t.replace(n,"")}},8879:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.GlslPreprocessor=void 0;const r=n(8520),i=n(2848),o=n(5483),s=n(5060);e.GlslPreprocessor=class{constructor(t,e,n,i){this.libs={},this.glslLibRoutineDependencyGraph={},this.context=new r.GlslContext(t,e,n,i),Object.keys(o.glslRegistry).forEach((t=>{const e=new o.glslRegistry[t](this.context);this.libs[t]=e}));const s=this.glslLibRoutineDependencyGraph;for(const t in this.libs){const e=this.libs[t].getFunctions();for(const n in e){const i=t+"."+n;let o;s[i]?(o=s[i],o.routineBody=e[n].routineBody):(o=new r.GlslLibRoutineNode(i,e[n].routineBody),s[i]=o);const a=e[n].dependencies;if(a)for(let t=0;t<a.length;++t)if(s[a[t]])o.addDependency(s[a[t]]);else{const e=new r.GlslLibRoutineNode(a[t]);s[a[t]]=e,o.addDependency(e)}}}}preprocess(){const t=this.context.programInfo;let e=t.shaderSource;return this.context.programInfo.hasMain||(e=`${e}\n      ${(0,s.getDefaultFragShaderMain)(this.context.glContext.version,this.context.outputTextureLayout.shape.length)}`),e=(0,i.replaceInlines)(e),`${(0,s.getFragShaderPreamble)(this.context.glContext.version)}\n    ${this.getUniforms(t.inputNames,t.variables)}\n    ${this.getImports(e)}\n    ${e}`}getImports(t){const e=this.selectGlslLibRoutinesToBeIncluded(t);if(0===e.length)return"";let n="";for(let t=0;t<e.length;++t){if(!e[t].routineBody)throw new Error(`Missing body for the Glsl Library routine: ${e[t].name}`);n+=e[t].routineBody+"\n"}return n}selectGlslLibRoutinesToBeIncluded(t){const e=[];return Object.keys(this.glslLibRoutineDependencyGraph).forEach((n=>{const r=n.split(".")[1];-1!==t.indexOf(r)&&e.push(this.glslLibRoutineDependencyGraph[n])})),r.TopologicalSortGlslRoutines.returnOrderedNodes(e)}getUniforms(t,e){const n=[];if(t)for(const e of t)n.push(`uniform sampler2D ${e};`);if(e)for(const t of e)n.push(`uniform ${t.type} ${t.name}${t.arrayLength?`[${t.arrayLength}]`:""};`);return n.join("\n")}}},5483:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.glslRegistry=void 0;const r=n(5107),i=n(7341),o=n(9894),s=n(2655),a=n(3891);e.glslRegistry={encoding:i.EncodingGlslLib,fragcolor:o.FragColorGlslLib,vec:a.VecGlslLib,shapeUtils:s.ShapeUtilsGlslLib,coordinates:r.CoordsGlslLib}},2655:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ShapeUtilsGlslLib=void 0;const r=n(8520);class i extends r.GlslLib{constructor(t){super(t)}getFunctions(){return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},this.bcastIndex()),this.bcastMatmulIndex()),this.offsetToIndices()),this.indicesToOffset()),this.incrementIndices())}getCustomTypes(){return{}}bcastIndex(){const t=this.context.outputTextureLayout.shape.length,e={};return this.context.programInfo.inputNames.forEach(((n,i)=>{const o=this.context.inputTextureLayouts[i].unpackedShape;if(o.length<=t){const i=o.length,s=t-i,a=`bcastIndices_${n}`;let u="";for(let t=0;t<i;++t)u+=`\n          realIndices[${t}] = int( mod(float(bcastedIndices[${s+t}]), ${o[t]}.0) );\n          `;const l=`\n        void ${a} (int bcastedIndices[${t}], out int realIndices[${i}]) {\n          ${u}\n        }\n        `;e[a]=new r.GlslLibRoutine(l)}})),e}bcastMatmulIndex(){const t=this.context.outputTextureLayout.shape.length,e={};return this.context.programInfo.inputNames.forEach(((n,i)=>{const o=this.context.inputTextureLayouts[i].shape;if(!(o.length<2||o.length>t)){const i=o.length,s=t-i,a=`bcastMatmulIndices_${n}`;let u="";for(let t=0;t<i-2;++t)u+=`\n          realIndices[${t}] = int( mod(float(bcastedIndices[${s+t}]), ${o[t]}.0) );\n          `;const l=`\n        void ${a}(int bcastedIndices[${t}], out int realIndices[${i}]) {\n          ${u}\n          realIndices[${i-1}] = bcastedIndices[${t-1}];\n          realIndices[${i-2}] = bcastedIndices[${t-2}];\n        }\n        `;e[a]=new r.GlslLibRoutine(l)}})),e}indicesToOffset(){const t={};return this.context.programInfo.inputNames.forEach(((e,n)=>{const o=this.context.inputTextureLayouts[n].shape,s=this.context.inputTextureLayouts[n].strides,a=o.length;let u=`indicesToOffset_${e}`;t[u]=new r.GlslLibRoutine(i.indexToOffsetSingle(u,a,s)),u=`indicesToOffset_${e}_T`,t[u]=new r.GlslLibRoutine(i.indexToOffsetSingle(u,a,s.slice().reverse()))})),t}static indexToOffsetSingle(t,e,n){let r="";for(let t=e-1;t>=0;--t)r+=`\n        offset += indices[${t}] * ${n[t]};\n        `;return`\n      int ${t}(int indices[${e}]) {\n        int offset = 0;\n        ${r}\n        return offset;\n      }\n      `}offsetToIndices(){const t={};return this.context.programInfo.inputNames.forEach(((e,n)=>{const o=this.context.inputTextureLayouts[n].shape,s=this.context.inputTextureLayouts[n].strides,a=o.length;let u=`offsetToIndices_${e}`;t[u]=new r.GlslLibRoutine(i.offsetToIndicesSingle(u,a,s)),u=`offsetToIndices_${e}_T`,t[u]=new r.GlslLibRoutine(i.offsetToIndicesSingle(u,a,s.slice().reverse()))})),t}static offsetToIndicesSingle(t,e,n){const r=[];for(let t=0;t<e-1;++t)r.push(`\n      indices[${t}] = offset / ${n[t]};`),r.push(`\n        offset -= indices[${t}] * ${n[t]};`);return r.push(`\n      indices[${e-1}] = offset;`),`\n      void ${t}(int offset, out int indices[${e}]) {\n        ${r.join("")}\n      }\n      `}incrementIndices(){const t={};return this.context.programInfo.inputNames.forEach(((e,n)=>{const i=this.context.inputTextureLayouts[n].shape,o=i.length,s=`incrementIndices_${e}`;let a="";for(let t=0;t<o;++t)a+=`\n        shape[${t}] = ${i[t]};`;const u=`\n        void ${s}(int axis, out int indices[${o}]) {\n          int shape[${o}];\n          ${a};\n          for(int i = ${o} -1 ; i >= 0; --i) {\n            if(i > axis) continue;\n            indices[i] += 1;\n            if(indices[i] < shape[i]) {\n              break;\n            }\n            indices[i] = 0;\n          }\n        }\n        `;t[s]=new r.GlslLibRoutine(u)})),t}}e.ShapeUtilsGlslLib=i},5060:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getDefaultFragShaderMain=e.getFragShaderPreamble=e.getVertexShaderSource=e.getGlsl=void 0;const n={version:"",attribute:"attribute",varyingVertex:"varying",varyingFrag:"varying",texture2D:"texture2D",output:"gl_FragColor",outputDeclaration:""},r={version:"#version 300 es",attribute:"in",varyingVertex:"out",varyingFrag:"in",texture2D:"texture",output:"outputColor",outputDeclaration:"out vec4 outputColor;"};function i(t){return 1===t?n:r}e.getGlsl=i,e.getVertexShaderSource=function(t){const e=i(t);return`${e.version}\n      precision highp float;\n      ${e.attribute} vec3 position;\n      ${e.attribute} vec2 textureCoord;\n\n      ${e.varyingVertex} vec2 TexCoords;\n\n      void main()\n      {\n          gl_Position = vec4(position, 1.0);\n          TexCoords = textureCoord;\n      }`},e.getFragShaderPreamble=function(t){const e=i(t);return`${e.version}\n    precision highp float;\n    precision highp int;\n    precision highp sampler2D;\n    ${e.varyingFrag} vec2 TexCoords;\n    ${e.outputDeclaration}\n    const vec2 halfCR = vec2(0.5, 0.5);\n\n    // Custom vector types to handle higher dimenalities.\n    struct ivec5\n    {\n      int x;\n      int y;\n      int z;\n      int w;\n      int u;\n    };\n\n    struct ivec6\n    {\n      int x;\n      int y;\n      int z;\n      int w;\n      int u;\n      int v;\n    };\n\n    int imod(int x, int y) {\n      return x - y * (x / y);\n    }\n\n    `},e.getDefaultFragShaderMain=function(t,e){return`\n  void main() {\n    int indices[${e}];\n    toVec(TexCoords, indices);\n    vec4 result = vec4(process(indices));\n    ${i(t).output} = result;\n  }\n  `}},3891:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.VecGlslLib=void 0;const r=n(8520);class i extends r.GlslLib{constructor(t){super(t)}getCustomTypes(){return{}}getFunctions(){return Object.assign(Object.assign(Object.assign(Object.assign({},this.binaryVecFunctions()),this.copyVec()),this.setVecItem()),this.getVecItem())}binaryVecFunctions(){const t=this.context.outputTextureLayout.shape.length,e={add:"+=",sub:"-=",mul:"*=",div:"/="},n={};for(const i in e){const o=`${i}Vec`;let s="";for(let n=0;n<t;++n)s+=`\n          dest[${n}] ${e[i]} src[${n}];\n          `;const a=`\n        void ${o}(int src[${t}], out int dest[${t}]) {\n          ${s}\n        }\n        `;n[o]=new r.GlslLibRoutine(a)}return n}copyVec(){const t=this.context.outputTextureLayout.shape.length;let e="";for(let n=0;n<t;++n)e+=`\n        dest[${n}] = src[${n}];\n        `;const n=`\n      void copyVec(int src[${t}], out int dest[${t}]) {\n        ${e}\n      }\n      `;return{copyVec:new r.GlslLibRoutine(n)}}setVecItem(){const t=this.context.outputTextureLayout.shape.length;let e=`\n        if(index < 0)\n            index =${t} + index;\n        if (index == 0)\n            m[0] = value;\n        `;for(let n=1;n<t-1;++n)e+=`\n        else if (index == ${n})\n            m[${n}] = value;\n            `;e+=`\n        else\n            m[${t-1}] = value;\n        `;const n=`\n      void setVecItem(out int m[${t}], int index, int value) {\n        ${e}\n      }\n        `;return{setVecItem:new r.GlslLibRoutine(n)}}getVecItem(){const t=this.context.outputTextureLayout.shape.length;let e=`\n        if(index < 0)\n            index = ${t} + index;\n        if (index == 0)\n            return m[0];\n      `;for(let n=1;n<t-1;++n)e+=`\n        else if (index == ${n})\n            return m[${n}];\n      `;e+=`\n        else\n            return m[${t-1}];\n        `;const n=`\n      int getVecItem(int m[${t}], int index) {\n        ${e}\n      }\n    `;return{getVecItem:new r.GlslLibRoutine(n)}}}e.VecGlslLib=i},8316:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.WebGLInferenceHandler=void 0;const r=n(6231),i=n(9162),o=n(2517),s=n(2403),a=n(7019),u=n(8710),l=n(5611),c=n(4057),d=n(2039);e.WebGLInferenceHandler=class{constructor(t){this.session=t,this.packedTextureDataCache=new Map,this.unpackedTextureDataCache=new Map}calculateTextureWidthAndHeight(t,e){return(0,c.calculateTextureWidthAndHeight)(this.session.layoutStrategy,t,e)}executeProgram(t,e){if(e.length<t.inputNames.length)throw new Error(`Input size mustn't be less than ${t.inputNames.length}.`);if(t.inputNames.length!==t.inputTypes.length)throw new Error("input names size does not match input types");const n=[];for(let r=0;r<t.inputNames.length;++r)n[r]=this.getOrCreateTextureData(e[r],t.inputTypes[r]);const r=((t,e)=>{const n=e.map((t=>`${t.unpackedShape.join(",")};${t.width}x${t.height}`)).join("_");let r=t.name;return t.cacheHint&&(r+="["+t.cacheHint+"]"),r+=":"+n,r})(t,n);let i=this.session.programManager.getArtifact(r);const o=i?i.programInfo:"function"==typeof t.get?t.get():t,s=(0,c.createTextureLayoutFromTextureType)(this.session.layoutStrategy,o.output.dims,o.output.textureType),a=this.createTextureData(s,o.output.type);return i||(i=this.session.programManager.build(o,n,a),this.session.programManager.setArtifact(r,i)),this.runProgram(i,n,a),a}run(t,e){return this.executeProgram(t,e).tensor}runProgram(t,e,n){for(let n=0;n<e.length;++n)if(!!e[n].isPacked!=(t.programInfo.inputTypes[n]===d.TextureType.packed))throw new Error(`input[${n}] property packed inconsistent`);if(!!n.isPacked!=(t.programInfo.output.textureType===d.TextureType.packed))throw new Error("output property packed inconsistent");this.session.programManager.run(t,e,n)}getOrCreateTextureData(t,e){let n=this.getTextureData(t.dataId,e===d.TextureType.packed);if(!n&&(n=this.getTextureData(t.dataId,e!==d.TextureType.packed),n))return e===d.TextureType.packed?this.pack(n):this.unpack(n);if(!n){const r=(0,c.createTextureLayoutFromTextureType)(this.session.layoutStrategy,t.dims,e);if(e===d.TextureType.packedLastDimension){const n=1,r=4,i=t.dims;if(4===i.length){const o=[i[0],Math.ceil(i[1]*i[2]*i[3]/r)],s=(0,c.createTextureLayoutFromTextureType)(this.session.layoutStrategy,o,e);let a=t.numberData;if(i[1]*i[2]*i[3]%r!=0){const e=i[0],o=i[1]*i[2]*i[3],s=Math.ceil(o*n/r)*r;a=new Float32Array(e*s);for(let r=0;r<e;++r){const e=r*o,i=r*s+r%n*o;a.set(t.numberData.subarray(e,e+o),i)}}return this.createTextureData(s,t.type,a,t,1)}}if(e===d.TextureType.packed){const e=(0,c.createTextureLayoutFromShape)(this.session.layoutStrategy,t.dims,1,[],{reverseWH:!0}),r=this.createTextureData(e,t.type,t.numberData,t,1);n=this.pack(r)}else n=this.createTextureData(r,t.type,t.numberData,t,1)}return n}createTextureDataFromLayoutBindTensor(t,e,n,r){return this.createTextureData(t,e,n,r,1)}createTextureData(t,e,n,i,o){r.Logger.verbose("InferenceHandler",`Creating TextureData: layout:[${JSON.stringify(t)}]`);const s=this.session.textureManager.createTextureFromLayout(e,t,n,o);return this.createTextureDataFromTexture(t,e,s,i)}reshapeUnpacked(t,e){const n=this.getOrCreateTextureData(t,d.TextureType.unpacked),r={channels:n.channels,height:n.height,width:n.width,shape:0!==e.length?e:[1],strides:o.ShapeUtil.computeStrides(e),unpackedShape:e};return this.createTextureDataFromTexture(r,t.type,n.texture).tensor}reshapePacked(t,e){const n=this.getOrCreateTextureData(t,d.TextureType.packed);if((0,a.isReshapeCheap)(t.dims,e)){const r={channels:n.channels,height:n.height,width:n.width,shape:0!==e.length?e:[1],strides:o.ShapeUtil.computeStrides(e),unpackedShape:e,isPacked:!0};return this.createTextureDataFromTexture(r,t.type,n.texture).tensor}const r=(0,a.processDims3D)(t.dims),i=(0,a.processDims3D)(e),s=this.reshapePacked(t,r),u=this.run((0,a.createPackedReshape3DProgramInfoLoader)(this,s,i),[s]);return this.reshapePacked(u,e)}cast(t,e){const n=this.getOrCreateTextureData(t,d.TextureType.unpacked);return this.createTextureDataFromTexture(n,e,n.texture).tensor}createTextureDataFromTexture(t,e,n,r,o){const s=Object.assign(Object.assign({},t),{tensor:r||new i.Tensor(t.unpackedShape,e,(t=>this.readTexture(s)),(async t=>this.readTextureAsync(s)),void 0,o),texture:n});return this.setTextureData(s.tensor.dataId,s,t.isPacked),s}getTextureData(t,e=!1){return this.session.isInitializer(t)?this.session.getTextureData(t,e):e?this.packedTextureDataCache.get(t):this.unpackedTextureDataCache.get(t)}setTextureData(t,e,n=!1){this.session.isInitializer(t)?this.session.setTextureData(t,e,n):(n?this.packedTextureDataCache:this.unpackedTextureDataCache).set(t,e)}isTextureLayoutCached(t,e=!1){return!!this.getTextureData(t.dataId,e)}dispose(){this.session.textureManager.clearActiveTextures(),this.packedTextureDataCache.forEach((t=>this.session.textureManager.releaseTexture(t))),this.packedTextureDataCache=new Map,this.unpackedTextureDataCache.forEach((t=>this.session.textureManager.releaseTexture(t))),this.unpackedTextureDataCache=new Map}readTexture(t){return t.isPacked?this.readTexture(this.unpack(t)):this.session.backend.glContext.isFloat32DownloadSupported?this.session.textureManager.readTexture(t,t.tensor.type,t.channels):this.session.textureManager.readUint8TextureAsFloat((0,u.encodeAsUint8)(this,t))}async readTextureAsync(t){return t.isPacked?this.readTextureAsync(this.unpack(t)):this.session.backend.glContext.isFloat32DownloadSupported?this.session.textureManager.readTextureAsync(t,t.tensor.type,t.channels):this.session.textureManager.readUint8TextureAsFloat((0,u.encodeAsUint8)(this,t))}pack(t){return this.executeProgram((0,s.createPackProgramInfoLoader)(this,t.tensor),[t.tensor])}unpack(t){return this.executeProgram((0,l.createUnpackProgramInfoLoader)(this,t.tensor),[t.tensor])}}},1640:function(t,e,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(t,e,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(e,n);i&&!("get"in i?!e.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return e[n]}}),Object.defineProperty(t,r,i)}:function(t,e,n,r){void 0===r&&(r=n),t[r]=e[n]}),i=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),o=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var n in t)"default"!==n&&Object.prototype.hasOwnProperty.call(t,n)&&r(e,t,n);return i(e,t),e};Object.defineProperty(e,"__esModule",{value:!0}),e.WEBGL_OP_RESOLVE_RULES=void 0;const s=n(2898),a=o(n(7839)),u=n(4196),l=n(2069),c=n(8138),d=n(9663),p=n(5193),h=n(7992),f=n(1253),g=n(4776),b=n(6572),m=n(3346),y=n(5623),x=n(2870),_=n(2143),w=n(4939),v=n(718),T=n(2268),S=n(8117),I=n(2278),O=n(5524),A=n(5975),E=n(3933),$=n(6558),P=n(5723),D=n(3738),N=o(n(4909)),k=n(8428),L=n(9793);e.WEBGL_OP_RESOLVE_RULES=[["Abs","","6+",N.abs],["Acos","","7+",N.acos],["Add","","7+",a.add],["And","","7+",a.and],["Asin","","7+",N.asin],["Atan","","7+",N.atan],["AveragePool","","7+",_.averagePool,_.parseAveragePoolAttributes],["BatchNormalization","","7+",s.batchNormalization,s.parseBatchNormalizationAttributes],["Cast","","6+",u.cast,u.parseCastAttributes],["Ceil","","6+",N.ceil],["Clip","","6-10",N.clip,N.parseClipAttributes],["Clip","","11+",N.clipV11],["Concat","","4+",l.concat,l.parseConcatAttributes],["Conv","","1+",c.conv,c.parseConvAttributes],["ConvTranspose","","1+",d.convTranspose,d.parseConvTransposeAttributes],["Cos","","7+",N.cos],["Div","","7+",a.div],["Dropout","","7+",N.identity],["DepthToSpace","","1+",p.depthToSpace,p.parseDepthToSpaceAttributes],["Equal","","7+",a.equal],["Elu","","6+",N.elu,N.parseEluAttributes],["Exp","","6+",N.exp],["Flatten","","1+",h.flatten,h.parseFlattenAttributes],["Floor","","6+",N.floor],["FusedConv","com.microsoft","1+",c.conv,c.parseConvAttributes],["Gather","","1+",f.gather,f.parseGatherAttributes],["Gemm","","7-10",g.gemm,g.parseGemmAttributesV7],["Gemm","","11+",g.gemm,g.parseGemmAttributesV11],["GlobalAveragePool","","1+",_.globalAveragePool,_.parseGlobalAveragePoolAttributes],["GlobalMaxPool","","1+",_.globalMaxPool],["Greater","","7+",a.greater],["Identity","","1+",N.identity],["ImageScaler","","1+",b.imageScaler,b.parseImageScalerAttributes],["InstanceNormalization","","6+",m.instanceNormalization,m.parseInstanceNormalizationAttributes],["LeakyRelu","","6+",N.leakyRelu,N.parseLeakyReluAttributes],["Less","","7+",a.less],["Log","","6+",N.log],["MatMul","","1+",y.matMul,y.parseMatMulAttributes],["MaxPool","","1+",_.maxPool,_.parseMaxPoolAttributes],["Mul","","7+",a.mul],["Neg","","6+",N.neg],["Not","","1+",N.not],["Or","","7+",a.or],["Pad","","2-10",x.padV2,x.parsePadAttributesV2],["Pad","","11+",x.padV11,x.parsePadAttributesV11],["Pow","","7+",a.pow],["PRelu","","7+",a.pRelu],["ReduceLogSum","","1+",w.reduceLogSum,w.parseReduceAttributes],["ReduceMax","","1+",w.reduceMax,w.parseReduceAttributes],["ReduceMean","","1+",w.reduceMean,w.parseReduceAttributes],["ReduceMin","","1+",w.reduceMin,w.parseReduceAttributes],["ReduceProd","","1+",w.reduceProd,w.parseReduceAttributes],["ReduceSum","","1-12",w.reduceSum,w.parseReduceAttributes],["ReduceSumSquare","","1+",w.reduceLogSumSquare,w.parseReduceAttributes],["Relu","","6+",N.relu],["Reshape","","5+",v.reshape],["Resize","","10",T.resize,T.parseResizeAttributesV10],["Resize","","11+",T.resize,T.parseResizeAttributesV11],["Shape","","1+",S.shape],["Sigmoid","","6+",N.sigmoid],["Sin","","7+",N.sin],["Slice","","10+",I.sliceV10],["Slice","","1-9",I.slice,I.parseSliceAttributes],["Softmax","","1-12",O.softmax,O.parseSoftmaxAttributes],["Softmax","","13+",O.softmaxV13,O.parseSoftmaxAttributesV13],["Split","","2-12",A.split,A.parseSplitAttributes],["Sqrt","","6+",N.sqrt],["Squeeze","","1-12",E.squeeze,E.parseSqueezeAttributes],["Squeeze","","13+",E.squeezeV13],["Sub","","7+",a.sub],["Sum","","6+",$.sum],["Tan","","7+",N.tan],["Tanh","","6+",N.tanh],["Tile","","6+",P.tile],["Transpose","","1+",D.transpose,D.parseTransposeAttributes],["Upsample","","7-8",L.upsample,L.parseUpsampleAttributesV7],["Upsample","","9",L.upsample,L.parseUpsampleAttributesV9],["Unsqueeze","","1-12",k.unsqueeze,k.parseUnsqueezeAttributes],["Unsqueeze","","13+",k.unsqueezeV13],["Xor","","7+",a.xor]]},2898:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.parseBatchNormalizationAttributes=e.batchNormalization=void 0;const r=n(246),i=n(5060),o=n(2039),s={name:"BatchNormalization",inputNames:["A","Scale","B","Mean","Variance"],inputTypes:[o.TextureType.unpacked,o.TextureType.unpacked,o.TextureType.unpacked,o.TextureType.unpacked,o.TextureType.unpacked]};e.batchNormalization=(t,e,n)=>(u(e),[t.run(Object.assign(Object.assign({},s),{cacheHint:n.cacheKey,get:()=>a(t,e,n)}),e)]),e.parseBatchNormalizationAttributes=t=>{const e=t.attributes.getFloat("epsilon",1e-5),n=t.attributes.getFloat("momentum",.9),i=t.attributes.getInt("spatial",1);return(0,r.createAttributeWithCacheKey)({epsilon:e,momentum:n,spatial:i})};const a=(t,e,n)=>{const r=(0,i.getGlsl)(t.session.backend.glContext.version),a=e[0].dims.length,[u,l]=t.calculateTextureWidthAndHeight(e[1].dims,o.TextureType.unpacked),c=`\n  float process(int[${a}] indices) {\n    vec2 position = offsetToCoords(indices[1], ${u}, ${l});\n    float scale = getColorAsFloat(${r.texture2D}(Scale, position));\n    float mean = getColorAsFloat(${r.texture2D}(Mean, position));\n    float variance = getColorAsFloat(${r.texture2D}(Variance, position));\n    float b = getColorAsFloat(${r.texture2D}(B, position));\n\n    return scale * ( (_A(indices) - mean) / sqrt(variance + float(${n.epsilon})) ) + b;\n  }`;return Object.assign(Object.assign({},s),{output:{dims:e[0].dims,type:e[0].type,textureType:o.TextureType.unpacked},shaderSource:c})},u=t=>{if(!t||5!==t.length)throw new Error("BatchNormalization requires 5 inputs.");const e=t[0],n=t[1],r=t[2],i=t[3],o=t[4];if(e.dims.length<3||1!==n.dims.length||1!==r.dims.length||1!==i.dims.length||1!==o.dims.length)throw new Error("invalid input shape.");if(n.dims[0]!==e.dims[1]||r.dims[0]!==e.dims[1]||i.dims[0]!==e.dims[1]||o.dims[0]!==e.dims[1])throw new Error("invalid input shape.");if("float32"!==e.type&&"float64"!==e.type||"float32"!==n.type&&"float64"!==n.type||"float32"!==r.type&&"float64"!==r.type||"float32"!==i.type&&"float64"!==i.type||"float32"!==o.type&&"float64"!==o.type)throw new Error("invalid input tensor types.")}},7839:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.xor=e.sub=e.pRelu=e.pow=e.or=e.mul=e.less=e.greater=e.equal=e.div=e.and=e.add=e.glslPRelu=e.glslPow=e.glslXor=e.glslOr=e.glslAnd=e.glslLess=e.glslGreater=e.glslEqual=e.glslSub=e.glslMul=e.glslDiv=e.glslAdd=void 0;const r=n(2517),i=n(8520),o=n(5060),s=n(2039);function a(){const t="add_";return{body:`\n  float ${t}(float a, float b) {\n    return a + b;\n  }\n  vec4 ${t}(vec4 v1, vec4 v2) {\n    return v1 + v2;\n  }\n  `,name:t,type:i.FunctionType.ValueBased}}function u(){const t="div_";return{body:`\n  float ${t}(float a, float b) {\n    return a / b;\n  }\n  vec4 ${t}(vec4 v1, vec4 v2) {\n    return v1 / v2;\n  }\n  `,name:t,type:i.FunctionType.ValueBased}}function l(){const t="mul_";return{body:`\n  float ${t}(float a, float b) {\n    return a * b;\n  }\n  vec4 ${t}(vec4 v1, vec4 v2) {\n    return v1 * v2;\n  }\n  `,name:t,type:i.FunctionType.ValueBased}}function c(){const t="sub_";return{body:`\n  float ${t}(float a, float b) {\n    return a - b;\n  }\n  vec4 ${t}(vec4 v1, vec4 v2) {\n    return v1 - v2;\n  }\n  `,name:t,type:i.FunctionType.ValueBased}}function d(){const t="equal_";return{body:`\n  float ${t}(float a, float b) {\n    return float(a == b);\n  }\n  vec4 ${t}(vec4 v1, vec4 v2) {\n    return vec4(equal(v1, v2));\n  }\n  `,name:t,type:i.FunctionType.ValueBased}}function p(){const t="greater_";return{body:`\n  float ${t}(float a, float b) {\n    return float(a > b);\n  }\n  vec4 ${t}(vec4 v1, vec4 v2) {\n    return vec4( v1.r > v2.r ,\n      v1.g > v2.g,\n      v1.b > v2.b,\n      v1.a > v2.a );\n  }\n  `,name:t,type:i.FunctionType.ValueBased}}function h(){const t="less_";return{body:`\n  float ${t}(float a, float b) {\n    return float(a < b);\n  }\n  vec4 ${t}(vec4 v1, vec4 v2) {\n    return vec4( v1.r < v2.r ,\n                v1.g < v2.g,\n                v1.b < v2.b,\n                v1.a < v2.a );\n  }\n  `,name:t,type:i.FunctionType.ValueBased}}function f(){const t="and_";return{body:`\n  float ${t}(float a, float b) {\n    return float( bool(a) && bool(b) );\n  }\n  vec4 ${t}(vec4 v1, vec4 v2) {\n    bvec4 b1 = bvec4(v1);\n    bvec4 b2 = bvec4(v2);\n    return vec4( b1.r && b2.r ,\n                b1.g && b2.g,\n                b1.b && b2.b,\n                b1.a && b2.a );\n  }\n  `,name:t,type:i.FunctionType.ValueBased}}function g(){const t="or_";return{body:`\n  float ${t}(float a, float b) {\n    return float( bool(a) || bool(b) );\n  }\n  vec4 ${t}(vec4 v1, vec4 v2) {\n    bvec4 b1 = bvec4(v1);\n    bvec4 b2 = bvec4(v2);\n    return vec4( b1.r || b2.r ,\n                b1.g || b2.g,\n                b1.b || b2.b,\n                b1.a || b2.a );\n  }\n  `,name:t,type:i.FunctionType.ValueBased}}function b(){const t="xor_";return{body:`\n  float ${t}(float a, float b) {\n    return float( bool(a) ^^ bool(b) );\n  }\n  vec4 ${t}(vec4 v1, vec4 v2) {\n    bvec4 b1 = bvec4(v1);\n    bvec4 b2 = bvec4(v2);\n    return vec4( b1.r ^^ b2.r ,\n                b1.g ^^ b2.g,\n                b1.b ^^ b2.b,\n                b1.a ^^ b2.a );\n  }\n  `,name:t,type:i.FunctionType.ValueBased}}function m(){return function(t){const e=`${t}_`;return{body:`\n  float ${e}(float a, float b) {\n    return ${t}(a, b);\n  }\n  vec4 ${e}(vec4 v1, vec4 v2) {\n    return ${t}(v1, v2);\n  }\n  `,name:e,type:i.FunctionType.ValueBased}}("pow")}function y(){const t="prelu_";return{body:`\n  float ${t}(float a, float b) {\n    return a < 0.0 ? a * b: a;\n  }\n  vec4 ${t}(vec4 v1, vec4 v2) {\n    return vec4(\n      v1.r < 0.0 ? v1.r * v2.r: v1.r,\n      v1.g < 0.0 ? v1.g * v2.g: v1.g,\n      v1.b < 0.0 ? v1.b * v2.b: v1.b,\n      v1.a < 0.0 ? v1.a * v2.a: v1.a\n      );\n  }\n  `,name:t,type:i.FunctionType.ValueBased}}e.glslAdd=a,e.glslDiv=u,e.glslMul=l,e.glslSub=c,e.glslEqual=d,e.glslGreater=p,e.glslLess=h,e.glslAnd=f,e.glslOr=g,e.glslXor=b,e.glslPow=m,e.glslPRelu=y;const x=(t,e,n,r=e[0].type,i)=>{const o=t.session.pack?s.TextureType.packed:s.TextureType.unpacked;return{name:n.name,inputNames:["A","B"],inputTypes:[o,o],cacheHint:i,get:()=>_(t,e,n,r)}},_=(t,e,n,i=e[0].type)=>{const a=t.session.pack?s.TextureType.packed:s.TextureType.unpacked,u=!r.ShapeUtil.areEqual(e[0].dims,e[1].dims);let l=e[0].dims;const c=t.session.pack;if(u){const s=r.BroadcastUtil.calcShape(e[0].dims,e[1].dims,!1);if(!s)throw new Error("Can't perform binary op on the given tensors");l=s;const u=l.length,d=0!==e[0].dims.length?e[0].dims.length:1,p=0!==e[1].dims.length?e[1].dims.length:1,h=0!==e[0].dims.length?"bcastIndices_A(indices, aindices);":"aindices[0] = 0;",f=0!==e[1].dims.length?"bcastIndices_B(indices, bindices);":"bindices[0] = 0;",g=(0,o.getGlsl)(t.session.backend.glContext.version),b=c?`\n      ${n.body}\n      void main() {\n        vec4 a = getAAtOutCoords();\n        vec4 b = getBAtOutCoords();\n        vec4 result = ${n.name}(a, b);\n        ${g.output} = result;\n      }`:`\n      ${n.body}\n      float process(int indices[${u}]) {\n        int aindices[${d}];\n        int bindices[${p}];\n        ${h}\n        ${f}\n        return ${n.name}(_A(aindices), _B(bindices));\n      }`;return{name:n.name,inputNames:["A","B"],inputTypes:[a,a],output:{dims:l,type:i,textureType:a},shaderSource:b,hasMain:c}}const d=(0,o.getGlsl)(t.session.backend.glContext.version),p=`\n    ${n.body}\n    void main() {\n      vec4 v1 = ${d.texture2D}(A, TexCoords);\n      vec4 v2 = ${d.texture2D}(B, TexCoords);\n      vec4 result = ${n.name}(v1, v2);\n      ${d.output} = result;\n    }\n    `;return{name:n.name,inputNames:["A","B"],inputTypes:[a,a],output:{dims:e[0].dims,type:i,textureType:a},shaderSource:p,hasMain:!0}};e.add=(t,e)=>[t.run(x(t,e,a()),e)],e.and=(t,e)=>[t.run(x(t,e,f(),"bool"),e)],e.div=(t,e)=>[t.run(x(t,e,u()),e)],e.equal=(t,e)=>[t.run(x(t,e,d(),"bool"),e)],e.greater=(t,e)=>[t.run(x(t,e,p(),"bool"),e)],e.less=(t,e)=>[t.run(x(t,e,h(),"bool"),e)],e.mul=(t,e)=>[t.run(x(t,e,l()),e)],e.or=(t,e)=>[t.run(x(t,e,g(),"bool"),e)],e.pow=(t,e)=>[t.run(x(t,e,m()),e)],e.pRelu=(t,e)=>[t.run(x(t,e,y()),e)],e.sub=(t,e)=>[t.run(x(t,e,c()),e)],e.xor=(t,e)=>[t.run(x(t,e,b(),"bool"),e)]},4196:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.parseCastAttributes=e.cast=void 0;const r=n(2517);e.cast=(t,e,n)=>(i(e),[t.cast(e[0],n)]),e.parseCastAttributes=t=>r.ProtoUtil.tensorDataTypeFromProto(t.attributes.getInt("to"));const i=t=>{if(!t||1!==t.length)throw new Error("Cast requires 1 input.");if("string"===t[0].type)throw new Error("Invalid input type.")}},1163:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.createPackedConcatProgramInfoLoader=void 0;const r=n(5060),i=n(2039),o=n(9390),s=n(2827);e.createPackedConcatProgramInfoLoader=(t,e,n)=>{const u=(l=e.length,c=n.cacheKey,{name:"Concat (packed)",inputNames:Array.from({length:l},((t,e)=>`X${e}`)),inputTypes:Array(l).fill(i.TextureType.packed),cacheHint:c});var l,c;return Object.assign(Object.assign({},u),{get:()=>((t,e,n,u)=>{const l=n[0].dims.slice();if(u>=l.length||u<-1*l.length)throw new Error("axis specified for concat doesn't match input dimensionality");u<0&&(u=l.length+u);const c=l.slice(0);for(let t=1;t<n.length;t++){const e=n[t].dims.slice();for(let t=0;t<l.length;t++)if(t===u)c[u]+=e[t];else if(l[t]!==e[t])throw new Error("non concat dimensions must match")}const d=c.length,p=(0,s.getChannels)("coords",d),h=(0,o.getCoordsDataType)(d),f=(0,s.unpackFromChannel)(),g=n.map((t=>t.dims)),b=(0,o.getGlChannels)(d),m=new Array(g.length-1);m[0]=g[0][u];for(let t=1;t<m.length;t++)m[t]=m[t-1]+g[t][u];const y=b[u],x=b.slice(-2),_=b.join();let w=`if (${y} < ${m[0]}) {\n        return getChannel(\n            getX0(${_}), vec2(${x.join()}));\n        }`;for(let t=1;t<m.length;t++){const e=m[t-1];w+=`\n            if (${y} < ${m[t]}  && ${y} >= ${m[t-1]}) {\n              return getChannel(\n                getX${t}(${a(b,y,e)}),\n                vec2(${a(x,y,e)}));\n            }`}const v=m.length,T=m[m.length-1];w+=`\n            return getChannel(\n              getX${v}(${a(b,y,T)}),\n              vec2(${a(x,y,T)}));`;const S=(0,r.getGlsl)(t.session.backend.glContext.version),I=`\n          ${f}\n          float getValue(${b.map((t=>"int "+t))}) {\n            ${w}\n          }\n\n          void main() {\n            ${h} coords = getOutputCoords();\n            int lastDim = coords.${b[d-1]};\n            coords.${b[d-1]} = coords.${b[d-2]};\n            coords.${b[d-2]} = lastDim;\n\n            vec4 result = vec4(getValue(${p}), 0., 0., 0.);\n\n            ${p[d-1]} = ${p[d-1]} + 1;\n            if (${p[d-1]} < ${c[d-1]}) {\n              result.g = getValue(${p});\n            }\n\n            ${p[d-2]} = ${p[d-2]} + 1;\n            if (${p[d-2]} < ${c[d-2]}) {\n              result.a = getValue(${p});\n            }\n\n            ${p[d-1]} = ${p[d-1]} - 1;\n            if (${p[d-2]} < ${c[d-2]} &&\n                ${p[d-1]} < ${c[d-1]}) {\n              result.b = getValue(${p});\n            }\n            ${S.output} = result;\n          }\n        `;return Object.assign(Object.assign({},e),{output:{dims:c,type:n[0].type,textureType:i.TextureType.packed},shaderSource:I,hasMain:!0})})(t,u,e,n.axis)})};const a=(t,e,n)=>{const r=t.indexOf(e);return t.map(((t,e)=>e===r?`${t} - ${n}`:t)).join()}},2069:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.parseConcatAttributes=e.concat=void 0;const r=n(246),i=n(2039),o=n(1163);e.concat=(t,e,n)=>(d(e),t.session.pack&&e[0].dims.length>1?[t.run((0,o.createPackedConcatProgramInfoLoader)(t,e,n),e)]:[t.run(s(t,e,n),e)]);const s=(t,e,n)=>{const r=(o=e.length,s=n.cacheKey,{name:"Concat",inputNames:Array.from({length:o},((t,e)=>`X${e}`)),inputTypes:Array(o).fill(i.TextureType.unpacked),cacheHint:s});var o,s;return Object.assign(Object.assign({},r),{get:()=>((t,e,n,r)=>{const o=n[0].dims.slice();if(r>=o.length||r<-1*o.length)throw new Error("axis specified for concat doesn't match input dimensionality");r<0&&(r=o.length+r);const s=o.slice(0);for(let t=1;t<n.length;t++){const e=n[t].dims.slice();for(let t=0;t<o.length;t++)if(t===r)s[r]+=e[t];else if(o[t]!==e[t])throw new Error("non concat dimensions must match")}const d=s.length,p=new Array(n.length);let h=0;for(let t=0;t<p.length;++t)h+=n[t].dims[r],p[t]=h;let f="";f=n.length<5?a(p):u(p);const g=`\n        ${l(n.length,d)}\n        ${c(p)}\n        ${f}\n        float process(int indices[${d}]) {\n          int textureIndex = getTextureWhereDataResides (indices[${r}]);\n\n          if(textureIndex != 0) {\n            indices[${r}] = indices[${r}] - int(getSizeInConcatAxisValueFromIndex(textureIndex-int(1)));\n          }\n\n          return fetchDataFromCorrectTexture(textureIndex, indices);\n        }`;return Object.assign(Object.assign({},e),{output:{dims:s,type:n[0].type,textureType:i.TextureType.unpacked},shaderSource:g})})(0,r,e,n.axis)})},a=t=>`int getTextureWhereDataResides(int index) {\n      ${t.map(((t,e)=>`if(index<${t}) {return ${e};}\n`)).join("")}\n    }`,u=t=>a(t),l=(t,e)=>{const n=[`float fetchDataFromCorrectTexture(int textureIndex, int indices[${e}]) {`];for(let e=0;e<t;++e)0===e?n.push(`\tif (textureIndex == ${e}) { return _X${e}(indices); }`):e===t-1?n.push(`\telse { return _X${e}(indices); }`):n.push(`\telse if (textureIndex == ${e}) { return _X${e}(indices); }`);return n.push("\t}"),n.join("\n")},c=t=>{const e=["int getSizeInConcatAxisValueFromIndex(int index) {"];for(let n=0;n<t.length;++n)0===n?e.push(`\tif (index == ${n}) { return ${t[n]}; }`):n===t.length-1?e.push(`\telse { return ${t[n]}; }`):e.push(`\telse if (index == ${n}) { return ${t[n]}; }`);return e.push("\t}"),e.join("\n")};e.parseConcatAttributes=t=>(0,r.createAttributeWithCacheKey)({axis:t.attributes.getInt("axis")});const d=t=>{if(!t||t.length<1)throw new Error("too few inputs");const e=t[0].type,n=t[0].dims.length;if("string"===e)throw new Error("string tensor is not supported yet");for(const r of t){if(r.type!==e)throw new Error("input tensors should be one type");if(r.dims.length!==n)throw new Error("input tensors should have the same shape")}}},4770:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.createUnpackedGroupedConvProgramInfoLoader=void 0;const r=n(6231),i=n(5060),o=n(2039),s=n(8138),a=n(2823);e.createUnpackedGroupedConvProgramInfoLoader=(t,e,n)=>{const u=(l=e.length>2,c=n.cacheKey,{name:"GroupedConv",inputNames:l?["X","W","Bias"]:["X","W"],inputTypes:l?[o.TextureType.unpacked,o.TextureType.unpacked,o.TextureType.unpacked]:[o.TextureType.unpacked,o.TextureType.unpacked],cacheHint:c});var l,c;return Object.assign(Object.assign({},u),{get:()=>((t,e,n,u)=>{const l=e.length>2?"value += getBias(output_channel);":"",c=e[0].dims.slice(),d=e[1].dims.slice(),p=d[0]/u.group;r.Logger.verbose("GroupedConv",`autpPad:${u.autoPad}, dilations:${u.dilations}, group:${u.group}, kernelShape:${u.kernelShape}, pads:${u.pads}, strides:${u.strides}`);const h=(0,s.calculateOutputShape)(c,d,u.dilations,u.pads,u.strides),f=(0,i.getGlsl)(t.session.backend.glContext.version),{activationFunction:g,applyActivation:b}=(0,a.getActivationSnippet)(u),m=`\n  const ivec2 strides = ivec2(${u.strides[0]}, ${u.strides[1]});\n  const ivec2 pads = ivec2(${u.pads[0]}, ${u.pads[1]});\n  ${g}\n  void main() {\n    ivec4 coords = getOutputCoords();\n    int batch = coords.x;\n    int output_channel = coords.y;\n    ivec2 xRCCorner = coords.zw * strides - pads;\n    int group_id = output_channel / ${p};\n\n    float value = 0.0;\n    for (int wInChannel = 0; wInChannel < ${d[1]}; wInChannel++) {\n      int input_channel = group_id * ${d[1]} + wInChannel;\n      for (int wHeight = 0; wHeight < ${d[2]}; wHeight++) {\n        int xHeight = xRCCorner.x + wHeight * ${u.dilations[0]};\n\n        if (xHeight < 0 || xHeight >= ${c[2]}) {\n          continue;\n        }\n\n        for (int wWidth = 0; wWidth < ${d[3]}; wWidth++) {\n          int xWidth = xRCCorner.y + wWidth * ${u.dilations[1]};\n          if (xWidth < 0 || xWidth >= ${c[3]}) {\n            continue;\n          }\n\n          float xVal = getX(batch, input_channel, xWidth, xHeight);\n          float wVal = getW(output_channel, wInChannel, wWidth, wHeight);\n          value += xVal*wVal;\n        }\n      }\n    }\n    ${l}\n    ${b}\n    ${f.output} = vec4(value, .0, .0, .0);\n  }\n`;return Object.assign(Object.assign({},n),{output:{dims:h,type:e[0].type,textureType:o.TextureType.unpacked},shaderSource:m,hasMain:!0})})(t,e,u,n)})}},1386:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.conv2DPacked=e.conv2DPackedPointwise=void 0;const r=n(8138),i=n(8555),o=n(708);e.conv2DPackedPointwise=(t,e,n)=>{const i=e[0].dims,s=e[1].dims,a=(0,r.calculateOutputShape)(i,s,n.dilations,n.pads,n.strides),u=t.reshapePacked(e[0],[i[1],i[2]*i[3]]),l=t.reshapePacked(e[1],[s[0],s[1]]),c=e.length>2?[l,u,e[2]]:[l,u],d=t.run((0,o.createPackedMatmulProgramInfoLoader)(t,c,n),c);return t.reshapePacked(d,a)},e.conv2DPacked=(t,e,n)=>{const s=e[0].dims,a=e[1].dims,u=(0,r.calculateOutputShape)(s,a,n.dilations,n.pads,n.strides),l=t.run((0,i.createPackedIm2ColProgramInfoLoader)(t,e[0],e[1],u,n),[e[0]]),c=t.reshapePacked(e[1],[a[0],a[1]*a[2]*a[3]]),d=3===e.length?[c,l,e[2]]:[c,l],p=t.run((0,o.createPackedMatmulProgramInfoLoader)(t,d,n),d);return t.reshapePacked(p,u)}},9663:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.parseConvTransposeAttributes=e.convTranspose=void 0;const r=n(246),i=n(5060),o=n(2039),s=n(2823),a=(t,e,n,r,i,o)=>(t-1)*e+n+(r-1)*i+1-o,u=(t,e,n,r,i)=>{const o=Math.floor(t/2);"SAME_UPPER"===e?(n[r]=o,n[i]=t-o):"SAME_LOWER"===e&&(n[r]=t-o,n[i]=o)};e.convTranspose=(t,e,n)=>(p(e,n),l(t,e,n));const l=(t,e,n)=>{const r=d(n,e);return[c(t,e,r)]},c=(t,e,n)=>t.run(((t,e,n)=>{const r=(a=e.length>2,u=n.cacheKey,{name:"ConvTranspose",inputNames:a?["X","W","B"]:["X","W"],inputTypes:a?[o.TextureType.unpacked,o.TextureType.unpacked,o.TextureType.unpacked]:[o.TextureType.unpacked,o.TextureType.unpacked],cacheHint:u});var a,u;return Object.assign(Object.assign({},r),{get:()=>((t,e,n,r)=>{const a=e.length>2?"getB(output_channel)":"0.0",u=e[0].dims,l=e[1].dims,c=l[1],d=l[0]/r.group,p=[e[0].dims[0],e[1].dims[1]*r.group,...r.outputShape],h=(0,i.getGlsl)(t.session.backend.glContext.version),{activationFunction:f,applyActivation:g}=(0,s.getActivationSnippet)(r),b=`\n  const ivec2 strides = ivec2(${r.strides[0]}, ${r.strides[1]});\n  const ivec2 pads = ivec2(${r.pads[0]}, ${r.pads[1]});\n  ${f}\n  void main() {\n    ivec4 coords = getOutputCoords();\n    int batch = coords.x;\n    int output_channel = coords.y;\n\n    ivec2 loc = coords.zw + pads;\n\n    int group_id = output_channel / ${c};\n    int wOutChannel = output_channel - group_id * ${c};\n\n    float value = ${a};\n    for (int inChannelOffset = 0; inChannelOffset < ${d}; inChannelOffset++) {\n      int input_channel = group_id * ${d} + inChannelOffset;\n      for (int wWOff = 0; wWOff < ${l[2]}; wWOff++) {\n        for (int wHOff = 0; wHOff < ${l[3]}; wHOff++) {\n          ivec2 wOff = ivec2(wWOff * ${r.dilations[0]}, wHOff * ${r.dilations[1]});\n          ivec2 wLoc = loc - wOff;\n          ivec2 wLocIn = wLoc / strides;\n          if (\n            wLocIn * strides == wLoc &&\n            wLocIn.x >= 0 && wLocIn.x < ${u[2]} &&\n            wLocIn.y >= 0 && wLocIn.y < ${u[3]}\n          ) {\n            float xVal = getX(batch, input_channel, wLocIn.y, wLocIn.x);\n            float wVal = getW(input_channel, wOutChannel, wHOff, wWOff);\n            value += xVal * wVal;\n          }\n        }\n      }\n    }\n    ${g}\n    ${h.output} = vec4(value, .0, .0, .0);\n  }\n`;return Object.assign(Object.assign({},n),{output:{dims:p,type:e[0].type,textureType:o.TextureType.unpacked},shaderSource:b,hasMain:!0})})(t,e,r,n)})})(t,e,n),e),d=(t,e)=>{const n=t.kernelShape.slice();if(0===t.kernelShape.length)for(let t=2;t<e[1].dims.length;++t)n.push(e[1].dims[t]);const r=t.pads.slice(),i=t.outputShape.slice();((t,e,n,r,i,o,s,l)=>{const c=t.length-2,d=0===l.length;for(let p=0;p<c;++p){const h=d?t[p+2]*o[p]:l[p],f=a(t[p+2],o[p],i[p],e[p],n[p],h);u(f,r,i,p,p+c),d&&l.push(o[p]*(t[p+2]-1)+s[p]+(e[p]-1)*n[p]+1-i[p]-i[p+c])}})(e[0].dims,n,t.dilations,t.autoPad,r,t.strides,t.outputPadding,i);const o=Object.assign({},t);return Object.assign(o,{kernelShape:n,pads:r,outputShape:i,cacheKey:t.cacheKey}),o};e.parseConvTransposeAttributes=t=>{const e=t.attributes,n=(0,s.parseInternalActivationAttributes)(e),i=e.getString("auto_pad","NOTSET"),o=e.getInts("dilations",[1,1]),a=e.getInt("group",1),u=e.getInts("kernel_shape",[]),l=e.getInts("output_padding",[0,0]),c=e.getInts("output_shape",[]),d=e.getInts("pads",[0,0,0,0]),p=e.getInts("strides",[1,1]);return(0,r.createAttributeWithCacheKey)(Object.assign({autoPad:i,dilations:o,group:a,kernelShape:u,outputPadding:l,outputShape:c,pads:d,strides:p},n))};const p=(t,e)=>{if(!t||2!==t.length&&3!==t.length)throw new Error("Conv requires 2 or 3 inputs");if(4!==t[0].dims.length||4!==t[1].dims.length)throw new Error("currently only support 2-dimensional conv");if(t[0].dims[1]!==t[1].dims[0])throw new Error("FILTER_IN_CHANNEL should be equal to DATA_CHANNEL");const n=t[1].dims[1]*e.group;if(3===t.length&&(1!==t[2].dims.length||t[2].dims[0]!==n))throw new Error("invalid bias");const r=t[0].dims.length-2;if(e.dilations.length!==r)throw new Error(`dilations should be ${r}D`);if(e.strides.length!==r)throw new Error(`strides should be ${r}D`);if(e.pads.length!==2*r)throw new Error(`pads should be ${2*r}D`);if(e.outputPadding.length!==r)throw new Error(`output_padding should be ${r}D`);if(0!==e.kernelShape.length&&e.kernelShape.length!==t[1].dims.length-2)throw new Error("invalid kernel shape");if(0!==e.outputShape.length&&e.outputShape.length!==t[0].dims.length-2)throw new Error("invalid output shape");if("float32"!==t[0].type||"float32"!==t[1].type)throw new Error("ConvTranspose input(X,W) should be float tensor");if(3===t.length&&"float32"!==t[2].type)throw new Error("ConvTranspose input(bias) should be float tensor")}},8138:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.parseConvAttributes=e.conv=e.calculateOutputShape=void 0;const r=n(246),i=n(2517),o=n(4770),s=n(1386),a=n(9828),u=n(2823),l=n(3248),c=n(5623);e.calculateOutputShape=(t,e,n,r,i)=>{const o=t[0],s=t.slice(2),a=s.length,u=e[0],l=e.slice(2).map(((t,e)=>t+(t-1)*(n[e]-1))),c=s.map(((t,e)=>t+r[e]+r[e+a])).map(((t,e)=>Math.floor((t-l[e]+i[e])/i[e])));return[o,u].concat(...c)},e.conv=(t,e,n)=>(g(e,n),d(t,e,n));const d=(t,e,n)=>{const r=f(n,e),i=t.session.pack,a=1===r.kernelShape[0]&&1===r.kernelShape[1];return r.group>1?[t.run((0,o.createUnpackedGroupedConvProgramInfoLoader)(t,e,r),e)]:a&&i?[p(t,e,r)]:i&&4===e[0].dims.length&&1===e[0].dims[0]&&!a?[(0,s.conv2DPacked)(t,e,r)]:[h(t,e,r)]},p=(t,n,r)=>{const i=n[0].dims,o=n[1].dims,s=(0,e.calculateOutputShape)(i,o,r.dilations,r.pads,r.strides),a=t.reshapeUnpacked(n[0],[i[1],i[2]*i[3]]),u=t.reshapeUnpacked(n[1],[o[0],o[1]]),l=n.length>2?[u,a,n[2]]:[u,a],d=t.run((0,c.createMatmulProgramInfoLoader)(l,r),l);return t.reshapeUnpacked(d,s)},h=(t,n,r)=>{const i=n[0].dims,o=n[1].dims,s=(0,e.calculateOutputShape)(i,o,r.dilations,r.pads,r.strides),u=t.run((0,l.createIm2ColProgramInfoLoader)(t,n[0],n[1],s,r),[n[0]]),c=3===n.length?[u,n[1],n[2]]:[u,n[1]];return t.run((0,a.createDotProductProgramInfoLoader)(t,n,s,r),c)},f=(t,e)=>{const n=t.kernelShape.slice();if(0===t.kernelShape.length)for(let t=2;t<e[1].dims.length;++t)n.push(e[1].dims[t]);const r=t.pads.slice();i.PoolConvUtil.adjustPadsBasedOnAutoPad(e[0].dims,t.strides,t.dilations,n,r,t.autoPad);const o=Object.assign({},t);return Object.assign(o,{kernelShape:n,pads:r,cacheKey:t.cacheKey}),o};e.parseConvAttributes=t=>{const e=t.attributes,n=(0,u.parseInternalActivationAttributes)(e),i=e.getString("auto_pad","NOTSET"),o=e.getInts("dilations",[1,1]),s=e.getInt("group",1),a=e.getInts("kernel_shape",[]),l=e.getInts("pads",[0,0,0,0]),c=e.getInts("strides",[1,1]);return(0,r.createAttributeWithCacheKey)(Object.assign({autoPad:i,dilations:o,group:s,kernelShape:a,pads:l,strides:c},n))};const g=(t,e)=>{if(!t||2!==t.length&&3!==t.length)throw new Error("Conv requires 2 or 3 inputs");if(4!==t[0].dims.length||4!==t[1].dims.length)throw new Error("currently only support 2-dimensional conv");if(t[0].dims[1]!==t[1].dims[1]*e.group)throw new Error("FILTER_IN_CHANNEL should be equal to DATA_CHANNEL");if(3===t.length&&(1!==t[2].dims.length||t[1].dims[0]!==t[2].dims[0]))throw new Error("invalid bias");const n=t[0].dims.length-2;if(e.dilations.length!==n)throw new Error(`dilations should be ${n}D`);if(e.strides.length!==n)throw new Error(`strides should be ${n}D`);if(e.pads.length!==2*n)throw new Error(`pads should be ${2*n}D`);if(0!==e.kernelShape.length&&e.kernelShape.length!==t[1].dims.length-2)throw new Error("invalid kernel shape");if("float32"!==t[0].type||"float32"!==t[1].type)throw new Error("Conv input(X,W) should be float tensor");if(3===t.length&&"float32"!==t[2].type)throw new Error("Conv input(bias) should be float tensor")}},5193:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.parseDepthToSpaceAttributes=e.depthToSpace=void 0;const r=n(3738);e.depthToSpace=(t,e,n)=>{i(e);const o=n.blocksize,s=o*o,a="DCR"===n.mode?[0,3,4,1,5,2]:[0,1,4,2,5,3],u="DCR"===n.mode?[e[0].dims[0],o,o,e[0].dims[1]/s,e[0].dims[2],e[0].dims[3]]:[e[0].dims[0],e[0].dims[1]/s,o,o,e[0].dims[2],e[0].dims[3]],l=t.reshapeUnpacked(e[0],u),c={perm:a,cacheKey:`${a}`},[d]=(0,r.transpose)(t,[l],c),p=[e[0].dims[0],e[0].dims[1]/s,e[0].dims[2]*o,e[0].dims[3]*o];return[t.reshapeUnpacked(d,p)]},e.parseDepthToSpaceAttributes=t=>{const e=t.attributes.getInt("blocksize");if(e<1)throw new Error(`blocksize must be >= 1, but got : ${e} for DepthToSpace`);const n=t.attributes.getString("mode","DCR");if("DCR"!==n&&"CRD"!==n)throw new Error(`unrecognized mode: ${n} for DepthToSpace`);return{mode:n,blocksize:e}};const i=t=>{if(1!==t.length)throw new Error(`DepthToSpace expect 1 inputs, but got ${t.length}`);if("string"===t[0].type||4!==t[0].dims.length)throw new TypeError("DepthToSpace input should be a 4-D numeric tensor")}},9828:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.createDotProductProgramInfoLoader=void 0;const r=n(2517),i=n(5060),o=n(2039),s=n(2823),a=n(3248);e.createDotProductProgramInfoLoader=(t,e,n,u)=>{const l=((t,e)=>({name:"ConvDotProduct",inputNames:t?["Im2Col","K","B"]:["Im2Col","K"],inputTypes:t?[o.TextureType.unpacked,o.TextureType.packedLastDimension,o.TextureType.unpacked]:[o.TextureType.unpacked,o.TextureType.packedLastDimension],cacheKey:e.activationCacheKey}))(e.length>2,u);return Object.assign(Object.assign({},l),{get:()=>((t,e,n,u,l)=>{const c=n[0].dims,d=n[1].dims,p=[d[0],Math.ceil(c[1]*d[2]*d[3]/4)],h=(0,a.calculateIm2ColDims)(c,d,u),[f,g]=t.calculateTextureWidthAndHeight(p,o.TextureType.packedLastDimension),b=r.ShapeUtil.computeStrides(h),[m,y]=t.calculateTextureWidthAndHeight(h,o.TextureType.packedLastDimension),x=u.length,_=n.length<3?"0.0":"_B(b)",w=Math.ceil(c[1]*d[2]*d[3]/4),{activationFunction:v,applyActivation:T}=(0,s.getActivationSnippet)(l),S=(0,i.getGlsl)(t.session.backend.glContext.version),I=`\n${v}\nfloat process(int indices[${x}]) {\n  int b[1];\n  b[0] = indices[1];\n  int im2col[4];\n  im2col[0] = indices[0];\n  im2col[1] = indices[2];\n  im2col[2] = indices[3];\n  int im2colOffset = im2col[0] * ${b[0]} + im2col[1] * ${b[1]} + im2col[2] * ${b[2]};\n  int kernelOffset = indices[1] * ${p[1]};\n  float value = ${_};\n  for (int i = 0; i < ${w}; ++i) {\n    vec2 im2colCoords = offsetToCoords(im2colOffset, ${m}, ${y});\n    vec2 kernelCoords = offsetToCoords(kernelOffset, ${f}, ${g});\n    value += dot(${S.texture2D}(Im2Col, im2colCoords), ${S.texture2D}(K, kernelCoords));\n    ++im2colOffset;\n    ++kernelOffset;\n  }\n  ${T}\n  return value;\n}`;return Object.assign(Object.assign({},e),{output:{dims:u,type:n[0].type,textureType:o.TextureType.unpacked},shaderSource:I})})(t,l,e,n,u)})}},7992:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.parseFlattenAttributes=e.flatten=void 0;const r=n(2517);e.flatten=(t,e,n)=>{i(e,n);const o=r.ShapeUtil.flattenShape(e[0].dims,n);return[t.reshapeUnpacked(e[0],o)]},e.parseFlattenAttributes=t=>t.attributes.getInt("axis",1);const i=(t,e)=>{if(!t||1!==t.length)throw new Error("Flatten requires 1 input.");const n=t[0].dims.length;if(0===n)throw new Error("scalar tensor is not supported.");if(e<-n||e>n)throw new Error("Invalid axis");if("string"===t[0].type)throw new Error("string tensor is not supported.")}},2823:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.parseInternalActivationAttributes=e.getActivationSnippet=void 0;const r=n(2517),i=n(4909);e.getActivationSnippet=function(t){let e;switch(t.activation){case"Relu":e=(0,i.glslRelu)();break;case"Sigmoid":e=(0,i.glslSigmoid)();break;case"Clip":e=(0,i.glslClip)(t.clipMin,t.clipMax);break;default:return{activationFunction:"",applyActivation:""}}const n=e.name;return{activationFunction:e.body,applyActivation:`value = ${n}_(value);`}},e.parseInternalActivationAttributes=t=>{const e=t.getString("activation","");if("Clip"===e){const[n,i]=t.getFloats("activation_params",[r.MIN_CLIP,r.MAX_CLIP]);return{activation:e,clipMax:i,clipMin:n,activationCacheKey:`${e}:${n},${i}`}}return{activation:e,activationCacheKey:e}}},1253:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.parseGatherAttributes=e.gather=void 0;const r=n(246),i=n(782),o=n(2517),s=n(2039);e.gather=(t,e,n)=>(l(e,n.axis),[t.run(u(t,e,n),e)]),e.parseGatherAttributes=t=>(0,r.createAttributeWithCacheKey)({axis:t.attributes.getInt("axis",0)});const a={name:"Gather",inputNames:["A","B"],inputTypes:[s.TextureType.unpacked,s.TextureType.unpacked]},u=(t,e,n)=>{const r=Object.assign(Object.assign({},a),{cacheHint:n.cacheKey});return Object.assign(Object.assign({},r),{get:()=>((t,e,n,r)=>{const i=n[0].dims.slice(),a=n[1].dims.slice(),u=new Array(i.length+a.length-1);r=o.ShapeUtil.normalizeAxis(r,i.length);const l=[];for(let t=0;t<u.length;t++)t<r?(u[t]=i[t],l.push(`inputIdx[${t}] = outputIdx[${t}];`)):t<r+a.length?(u[t]=a[t-r],l.push(`indexDataIdx[${t-r}] = outputIdx[${t}];`)):(u[t]=i[t-a.length+1],l.push(`inputIdx[${t-a.length+1}] = outputIdx[${t}];`));const c=`\n      float process(int outputIdx[${u.length||1}]) {\n        int inputIdx[${i.length}];\n        int indexDataIdx[${a.length||1}];\n        indexDataIdx[0] = 0;\n        ${l.join("\n        ")}\n        int idx = int(_B(indexDataIdx));\n        inputIdx[${r}] = idx < 0 ? idx + ${i[r]} : idx;\n        return _A(inputIdx);\n      }`;return Object.assign(Object.assign({},e),{output:{dims:u,type:n[0].type,textureType:s.TextureType.unpacked},shaderSource:c})})(0,r,e,n.axis)})},l=(t,e)=>{if(!t||2!==t.length)throw new Error("Gather requires 2 inputs.");const n=t[0].dims.length;if(n<1)throw new Error("Invalid input shape.");if(e<-n||e>n-1)throw new Error("Invalid axis.");if(-1===i.NUMBER_TYPES.indexOf(t[0].type))throw new Error("Invaid input type.");if("int32"!==t[1].type&&"int16"!==t[1].type)throw new Error("Invaid input type.")}},4776:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.parseGemmAttributesV11=e.parseGemmAttributesV7=e.gemm=void 0;const r=n(246),i=n(2517),o=n(2039);e.gemm=(t,e,n)=>(l(e,n),[t.run(a(e,n),e)]);const s=(t,e)=>{const n=0!==t.attributes.getInt("transA",0),i=0!==t.attributes.getInt("transB",0),o=t.attributes.getFloat("alpha",1),s=t.attributes.getFloat("beta",1);return(0,r.createAttributeWithCacheKey)({transA:n,transB:i,alpha:o,beta:s,isOptionalC:e})};e.parseGemmAttributesV7=t=>s(t,!1),e.parseGemmAttributesV11=t=>s(t,!0);const a=(t,e)=>{const n={name:"Gemm",inputNames:3===t.length?["A","B","C"]:["A","B"],inputTypes:3===t.length?[o.TextureType.unpacked,o.TextureType.unpacked,o.TextureType.unpacked]:[o.TextureType.unpacked,o.TextureType.unpacked],key:e.cacheKey};return Object.assign(Object.assign({},n),{get:()=>u(n,t,e)})},u=(t,e,n)=>{const r=e[0].dims.slice(),s=e[1].dims.slice(),[a,u]=i.GemmUtil.getShapeOfGemmResult(r,n.transA,s,n.transB,3===e.length?e[2].dims:void 0),l=[a,u];if(!l)throw new Error("Can't use gemm on the given tensors");let c=r[r.length-1],d="";n.transA&&(c=r[0]),n.transA&&n.transB?d="value += _A_T(a) * _B_T(b);":n.transA&&!n.transB?d="value += _A_T(a) * _B(b);":!n.transA&&n.transB?d="value += _A(a) * _B_T(b);":n.transA||n.transB||(d="value += _A(a) * _B(b);");const p=l.length,h=`\n      float process(int indices[${p}]) {\n          int a[${p}];\n          int b[${p}];\n          ${3===e.length?`int c[${e[2].dims.length}];`:""}\n\n          copyVec(indices, a);\n          copyVec(indices, b);\n          ${3===e.length?"bcastIndices_C(indices, c);":""}\n\n          float value = 0.0;\n          for (int k=0; k<${c}; ++k) {\n              a[${p-1}] = k;\n              b[${p-2}] = k;\n              ${d}\n          }\n\n          value = value * alpha;\n          ${3===e.length?"value += beta * _C(c);":""}\n          return value;\n      }`;return Object.assign(Object.assign({},t),{output:{dims:l,type:e[0].type,textureType:o.TextureType.unpacked},variables:[{name:"alpha",type:"float",data:n.alpha},{name:"beta",type:"float",data:n.beta}],shaderSource:h})},l=(t,e)=>{if(!t)throw new Error("Input is missing");if(e.isOptionalC&&(t.length<2||t.length>3))throw new Error("Invaid input shape.");if(!e.isOptionalC&&3!==t.length)throw new Error("Gemm requires 3 inputs");if(3===t.length&&1!==t[2].dims.length&&2!==t[2].dims.length)throw new Error("Invalid input shape of C");if("float32"!==t[0].type&&"float64"!==t[0].type||"float32"!==t[1].type&&"float64"!==t[1].type||3===t.length&&"float32"!==t[2].type&&"float64"!==t[2].type)throw new Error("Invalid input type.");if(t[0].type!==t[1].type||3===t.length&&t[0].type!==t[2].type)throw new Error("Input types are mismatched")}},8555:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.createPackedIm2ColProgramInfoLoader=void 0;const r=n(5060),i=n(2039),o=n(2827);e.createPackedIm2ColProgramInfoLoader=(t,e,n,s,a)=>{const u=(l=a.cacheKey,{name:"Im2Col (packed)",inputNames:["A"],inputTypes:[i.TextureType.packed],cacheHint:l});var l;return Object.assign(Object.assign({},u),{get:()=>((t,e,n,s,a,u)=>{const l=n.dims,c=s.dims,d=a.length,p=[c[1]*c[2]*c[3],a[2]*a[3]],h=c[2]*c[3],f=(0,o.unpackFromChannel)(),g=(0,r.getGlsl)(t.session.backend.glContext.version);let b="";for(let t=0;t<=1;t++)for(let e=0;e<=1;e++)b+=`\n            blockIndex = rc.x + ${e};\n            pos = rc.y + ${t};\n\n            if(blockIndex < ${p[1]} && pos < ${p[0]}) {\n              offsetY = int(blockIndex / (${a[d-1]})) * ${u.strides[0]} -\n                ${u.pads[0]};\n              d0 = offsetY + ${u.dilations[0]} * (imod(pos, ${h}) / ${c[2]});\n\n              if(d0 < ${l[2]} && d0 >= 0) {\n                offsetX = imod(blockIndex, ${a[d-1]}) * ${u.strides[1]} -\n                  ${u.pads[1]};\n                d1 = offsetX + ${u.dilations[1]} * imod(imod(pos, ${h}), ${c[2]});\n\n                if(d1 < ${l[3]} && d1 >= 0) {\n\n                  ch = int(float(pos)/ ${h}.);\n                    innerDims = vec2(d0, d1);\n                    result[${2*t+e}] = getChannel(\n                      getA(0, ch, int(innerDims.x),\n                      int(innerDims.y)), innerDims);\n                }\n              }\n            }\n\n          `;const m=`\n      ${f}\n\n      void main() {\n        ivec2 rc = getOutputCoords();\n          vec4 result = vec4(0.0);\n          int blockIndex, pos, offsetY, d0, offsetX, d1, ch;\n          vec2 innerDims;\n          ${b}\n          ${g.output} = result;\n      }\n            `;return Object.assign(Object.assign({},e),{output:{dims:p,type:n.type,textureType:i.TextureType.packed},shaderSource:m,hasMain:!0})})(t,u,e,n,s,a)})}},3248:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.calculateIm2ColDims=e.createIm2ColProgramInfoLoader=void 0;const r=n(2039);e.createIm2ColProgramInfoLoader=(t,n,i,o,s)=>{const a=(u=s.cacheKey,{name:"Im2Col",inputNames:["X"],inputTypes:[r.TextureType.unpacked],cacheHint:u});var u;return Object.assign(Object.assign({},a),{get:()=>((t,n,i,o,s,a)=>{const u=i.dims,l=o.dims,c=s.length,d=(0,e.calculateIm2ColDims)(u,l,s,4),p=`\n        const int XC = ${u[1]};\n        const int XH = ${u[2]};\n        const int XW = ${u[3]};\n        const int KH = ${a.kernelShape[0]};\n        const int KW = ${a.kernelShape[1]};\n        const int dilationH = ${a.dilations[0]};\n        const int dilationW = ${a.dilations[1]};\n        const int strideH = ${a.strides[0]};\n        const int strideW = ${a.strides[1]};\n        const int padH = ${a.pads[0]};\n        const int padW = ${a.pads[1]};\n        const int KHKW = KH*KW;\n        const int XCKHKW = XC * KHKW;\n        const int outputChannels = 4;\n        vec4 process(int indices[${c}]) {\n          int b  = indices[0]; // batch size\n          int oh = indices[1] * strideH - padH; //output height\n          int ow = indices[2] * strideW - padW; //output width\n          int p = indices[3] * outputChannels; //patch\n          vec4 value = vec4(0.0);\n          for(int i=0; i < outputChannels; ++i) {\n            if(p < XCKHKW) {\n              int patchC = p / KHKW;\n              int patchH = (p - patchC*KHKW) / KW;\n              int patchW = (p - patchC*KHKW) - patchH * KW;\n              int xh2 = oh + patchH * dilationH;\n              int xw2 = ow + patchW * dilationW;\n              int x[${u.length}];\n              x[0] = b;\n              x[1] = patchC;\n              x[2] = xh2;\n              x[3] = xw2;\n              if(xh2 >= 0 &&\n                  xh2 < XH &&\n                  xw2 >= 0 &&\n                  xw2 < XW) {\n                value[i] = _X(x);\n              }\n            }\n            ++p;\n          }\n          return value;\n        }\n        `;return Object.assign(Object.assign({},n),{output:{dims:d,type:i.type,textureType:r.TextureType.packedLastDimension},shaderSource:p})})(0,a,n,i,o,s)})},e.calculateIm2ColDims=(t,e,n,r=4)=>[n[0],n[2],n[3],Math.ceil(t[1]*e[2]*e[3]/r)]},6572:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.parseImageScalerAttributes=e.imageScaler=void 0;const r=n(246),i=n(2039);e.imageScaler=(t,e,n)=>(u(e),[t.run(s(t,e,n),e)]),e.parseImageScalerAttributes=t=>{const e=t.attributes.getFloat("scale"),n=t.attributes.getFloats("bias");return(0,r.createAttributeWithCacheKey)({scale:e,bias:n})};const o={name:"ImageScaler",inputNames:["X"],inputTypes:[i.TextureType.unpacked]},s=(t,e,n)=>{const r=Object.assign(Object.assign({},o),{cacheHint:n.cacheKey});return Object.assign(Object.assign({},r),{get:()=>((t,e,n,r)=>{const o=n[0].dims.slice(),s=o.length,u=`\n      ${a(r.bias.length)}\n      float process(int indices[${s}]) {\n        return _X(indices) * scale + getBias(bias, indices[1]);\n      }`;return Object.assign(Object.assign({},e),{output:{dims:o,type:n[0].type,textureType:i.TextureType.unpacked},variables:[{name:"bias",type:"float",arrayLength:r.bias.length,data:r.bias},{name:"scale",type:"float",data:r.scale}],shaderSource:u})})(0,r,e,n)})},a=t=>{const e=[`float getBias(float bias[${t}], int channel) {`];for(let n=0;n<t;++n)0===n?e.push(`\tif (channel == ${n}) { return bias[${n}]; }`):n===t-1?e.push(`\telse { return bias[${n}]; }`):e.push(`\telse if (channel == ${n}) { return bias[${n}]; }`);return e.push("\t}"),e.join("\n")},u=t=>{if(!t||1!==t.length)throw new Error("ImageScaler requires 1 input.");if(4!==t[0].dims.length)throw new Error("Invalid input shape.");if("float32"!==t[0].type&&"float64"!==t[0].type)throw new Error("Invalid input type.")}},3346:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.parseInstanceNormalizationAttributes=e.instanceNormalization=void 0;const r=n(5060),i=n(2039);e.instanceNormalization=(t,e,n)=>{l(e);const r=t.run(s(e[0]),e);return[t.run(u(t,e[0],n,r.dims),[e[0],r,e[1],e[2]])]},e.parseInstanceNormalizationAttributes=t=>t.attributes.getFloat("epsilon",1e-5);const o={name:"InstanceNormalization_MeanAndVariance",inputNames:["X"],inputTypes:[i.TextureType.unpacked]},s=t=>Object.assign(Object.assign({},o),{get:()=>((t,e)=>{const n=e.dims.slice(),r=n[1],o=n[2]*n[3],s=[n[0],r],a=`\n      vec4 process(int[2] indices) {\n        vec4 v = vec4(0.0);\n        int a[4];\n        a[0] = indices[0];\n        a[1] = indices[1];\n        float temp = 0.0;\n        for(int a2=0; a2<${n[2]}; a2++) {\n          a[2] = a2;\n          for(int a3=0; a3<${n[3]}; a3++) {\n            a[3] = a3;\n            float x = _X(a);\n            temp += x;\n          }\n        }\n        float mean = temp / float(${o});\n        temp = 0.0;\n        for(int a2=0; a2<${n[2]}; a2++) {\n          a[2] = a2;\n          for(int a3=0; a3<${n[3]}; a3++) {\n            a[3] = a3;\n            float x = _X(a);\n            temp += (x - mean) * (x - mean);\n          }\n        }\n        v.r = mean;\n        v.g = temp / float(${o});\n\n        return v;\n      }`;return Object.assign(Object.assign({},t),{output:{dims:s,type:e.type,textureType:i.TextureType.packedLastDimension},shaderSource:a})})(o,t)}),a={name:"InstanceNormalization_ComputeOutput",inputNames:["X","MeanAndVariance","Scale","B"],inputTypes:[i.TextureType.unpacked,i.TextureType.packedLastDimension,i.TextureType.unpacked,i.TextureType.unpacked]},u=(t,e,n,o)=>{const s=Object.assign(Object.assign({},a),{cacheHint:`${n}`});return Object.assign(Object.assign({},s),{get:()=>((t,e,n,o,s)=>{const a=(0,r.getGlsl)(t.session.backend.glContext.version),[u,l]=t.calculateTextureWidthAndHeight(s,i.TextureType.packedLastDimension),[c,d]=[u/4,l],p=`\n      vec4 get_MeanAndVariance(int[2] mv) {\n        int offset = indicesToOffset_MeanAndVariance(mv);\n        vec2 coords = offsetToCoords(offset, ${c}, ${d});\n        return ${a.texture2D}(MeanAndVariance, coords);\n      }\n\n      float process(int[4] indices) {\n        int mv[2];\n        mv[0] = indices[0];\n        mv[1] = indices[1];\n        vec4 mean_and_variance = get_MeanAndVariance(mv);\n        float mean = mean_and_variance.r;\n        float variance = mean_and_variance.g;\n\n        int sb[1];\n        sb[0] = indices[1];\n        float scale = _Scale(sb);\n        float b = _B(sb);\n\n        return scale * (_X(indices) - mean) / sqrt(variance + epsilon) + b;\n      }`;return Object.assign(Object.assign({},e),{output:{dims:n.dims,type:n.type,textureType:i.TextureType.unpacked},variables:[{name:"epsilon",type:"float",data:o}],shaderSource:p})})(t,s,e,n,o)})},l=t=>{if(!t||3!==t.length)throw new Error("InstanceNormalization requires 3 inputs.");const e=t[0],n=t[1],r=t[2];if(e.dims.length<3||1!==n.dims.length||1!==r.dims.length)throw new Error("Invalid input shape.");if(n.dims[0]!==e.dims[1]||r.dims[0]!==e.dims[1])throw new Error("Input shapes are mismatched.");if("float32"!==e.type&&"float64"!==e.type||"float32"!==n.type&&"float64"!==n.type||"float32"!==r.type&&"float64"!==r.type)throw new Error("Invalid input type.");if(4!==t[0].dims.length)throw new Error("Only support 4-D input shape.")}},708:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.createPackedMatmulProgramInfoLoader=void 0;const r=n(2517),i=n(5060),o=n(2039),s=n(9390),a=n(2823),u=n(5623);e.createPackedMatmulProgramInfoLoader=(t,e,n)=>{const l=(c=e.length>2,d=n.activationCacheKey,{name:"MatMul (packed)",inputNames:c?["A","B","Bias"]:["A","B"],inputTypes:c?[o.TextureType.packed,o.TextureType.packed,o.TextureType.packed]:[o.TextureType.packed,o.TextureType.packed],cacheHint:d});var c,d;return Object.assign(Object.assign({},l),{get:()=>((t,e,n,l)=>{const c=n.length>2,d=c?"value += getBiasForMatmul();":"",p=n[0].dims,h=n[1].dims,f=r.BroadcastUtil.calcShape(p,h,!0),g=!r.ShapeUtil.areEqual(n[0].dims,n[1].dims);if(!f)throw new Error("Can't use matmul on the given tensors");const b=p[p.length-1],m=Math.ceil(b/2),y=p.length,x=h.length,_=(0,i.getGlsl)(t.session.backend.glContext.version),w=(0,s.getCoordsDataType)(f.length),v=f.length,T=(0,s.getGlChannels)(),{activationFunction:S,applyActivation:I}=(0,a.getActivationSnippet)(l),O=c?`${(0,u.getBiasForMatmul)(w,T,n[2].dims,f,!0)}`:"",A=g?`${function(t,e,n,i){let o=[],s=[];const a=n[0].dims,u=n[1].dims,l=a.length,c=u.length,d=i.length,p=d-l,h=d-c;o=a.map(((t,n)=>`coords.${e[n+p]}`)),o[l-1]="i*2",o.join(", "),s=u.map(((t,n)=>`coords.${e[n+h]}`)),s[c-2]="i*2",s.join(", ");const f=r.BroadcastUtil.getBroadcastDims(a,i),g=r.BroadcastUtil.getBroadcastDims(u,i),b=f.map((t=>`coords.${e[t+p]} = 0;`)).join("\n"),m=g.map((t=>`coords.${e[t+h]} = 0;`)).join("\n"),y=`int lastDim = coords.${e[d-1]};\n  coords.${e[d-1]} = coords.${e[d-2]};\n  coords.${e[d-2]} = lastDim;`;return`\nvec4 getAAtOutCoordsMatmul(int i) {\n  ${t} coords = getOutputCoords();\n  ${y}\n  ${b}\n  vec4 outputValue = getA(${o});\n  return outputValue;\n}\n\nvec4 getBAtOutCoordsMatmul(int i) {\n  ${t} coords = getOutputCoords();\n  ${y}\n  ${m}\n  vec4 outputValue = getB(${s});\n  return outputValue;\n}`}(w,T,n,f)}`:"",E=g?"getAAtOutCoordsMatmul(i)":`getA(${function(t,e){let n="";for(let r=0;r<e-2;r++)n+=`rc.${t[r]}, `;return n+=`rc.${t[e-2]}, i*2`,n}(T,y)})`,$=g?"getBAtOutCoordsMatmul(i)":`getB(${function(t,e){let n="";for(let r=0;r<e-2;r++)n+=`rc.${t[r]}, `;return n+=`i*2, rc.${t[e-1]}`,n}(T,x)})`,P=`\n            ${A}\n            ${O}\n            ${S}\n            void main() {\n              ${g?"":`${w} rc =\n          getOutputCoords(); int lastDim = rc.${T[v-1]}; rc.${T[v-1]} =\n          rc.${T[v-2]}; rc.${T[v-2]} = lastDim;\n      `}\n\n              vec4 value = vec4(0);\n              for (int i = 0; i < ${m}; i++) {\n                vec4 a = ${E};\n                vec4 b = ${$};\n\n                value += (a.rrbb * b.rgrg);\n                value += (a.ggaa * b.baba);\n              }\n              ${d}\n              ${I}\n              ${_.output} = value;\n            }`;return Object.assign(Object.assign({},e),{output:{dims:f,type:n[0].type,textureType:o.TextureType.packed},shaderSource:P,hasMain:!0})})(t,l,e,n)})}},5623:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getBiasForMatmul=e.createMatmulProgramInfoLoader=e.parseMatMulAttributes=e.matMul=void 0;const r=n(2517),i=n(2039),o=n(9390),s=n(2823),a=n(708);function u(t,e){const n=(a=t.length>2,u=e.activationCacheKey,{name:"MatMul",inputNames:a?["A","B","Bias"]:["A","B"],inputTypes:a?[i.TextureType.unpacked,i.TextureType.unpacked,i.TextureType.unpacked]:[i.TextureType.unpacked,i.TextureType.unpacked],cacheHint:u});var a,u;return Object.assign(Object.assign({},n),{get:()=>function(t,e,n){const a=e[0].dims,u=e[1].dims,l=r.BroadcastUtil.calcShape(a,u,!0);if(!l)throw new Error("Can't use matmul on the given tensors");const d=(0,o.getCoordsDataType)(l.length),p=(0,o.getGlChannels)(),{activationFunction:h,applyActivation:f}=(0,s.getActivationSnippet)(n),g=e.length>2,b=g?"value += getBiasForMatmul();":"",m=g?`${c(d,p,e[2].dims,l,!1)}`:"",y=l.length,x=a.length,_=u.length,w=`\n    ${h}\n    ${m}\n    float process(int indices[${y}]) {\n        int a[${x}];\n        int b[${_}];\n        bcastMatmulIndices_A(indices, a);\n        bcastMatmulIndices_B(indices, b);\n\n        float value;\n        for (int k=0; k<${a[a.length-1]}; ++k) {\n            a[${x-1}] = k;\n            b[${_-2}] = k;\n            value += _A(a) * _B(b);\n        }\n        ${b}\n        ${f}\n        return value;\n    }`;return Object.assign(Object.assign({},t),{output:{dims:l,type:e[0].type,textureType:i.TextureType.unpacked},shaderSource:w})}(n,t,e)})}e.matMul=(t,e,n)=>(l(e),t.session.pack?[t.run((0,a.createPackedMatmulProgramInfoLoader)(t,e,n),e)]:[t.run(u(e,n),e)]),e.parseMatMulAttributes=t=>(0,s.parseInternalActivationAttributes)(t.attributes),e.createMatmulProgramInfoLoader=u;const l=t=>{if(!t||2!==t.length)throw new Error("MatMul requires 2 inputs.");if(t[0].dims[t[0].dims.length-1]!==t[1].dims[t[1].dims.length-2])throw new Error("shared dimension does not match.");if("float32"!==t[0].type&&"float64"!==t[0].type||"float32"!==t[1].type&&"float64"!==t[1].type)throw new Error("inputs should be float type");if(t[0].type!==t[1].type)throw new Error("inputs types should match")};function c(t,e,n,i,o){let s="";const a=n.length,u=i.length,l=u-a;s=u<2&&a>0?"coords":n.map(((t,n)=>`coords.${e[n+l]}`)).join(", ");const c=r.BroadcastUtil.getBroadcastDims(n,i).map((t=>`coords.${e[t+l]} = 0;`)).join("\n");let d="vec4(outputValue.xx, outputValue.yy)";return 1===r.ShapeUtil.size(n)&&(d="vec4(outputValue.x)"),o?`\nvec4 getBiasForMatmul() {\n  ${t} coords = getOutputCoords();\n  ${c}\n  vec4 outputValue = getBias(${s});\n  return ${d};\n}`:`\nfloat getBiasForMatmul() {\n  ${t} coords = getOutputCoords();\n  ${c}\n  return getBias(coords.x);\n}`}e.getBiasForMatmul=c},2403:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.createPackProgramInfoLoader=void 0;const r=n(5060),i=n(2039),o=n(9390),s=n(2827),a={name:"pack",inputNames:["A"],inputTypes:[i.TextureType.unpackedReversed]};e.createPackProgramInfoLoader=(t,e)=>Object.assign(Object.assign({},a),{get:()=>((t,e)=>{const n=(0,r.getGlsl)(t.session.backend.glContext.version),u=e.dims,l=u.length,c=e.dims.length,d=(0,o.getCoordsDataType)(c),p=(0,s.getChannels)("rc",c),h=(f=c,g=p,b=u[u.length-2],m=u[u.length-1],0===f||1===f?"":`\n    int r = ${g[f-2]};\n    int c = ${g[f-1]};\n    int rp1 = ${g[f-2]} + 1;\n    int cp1 = ${g[f-1]} + 1;\n    bool rEdge = rp1 >= ${m};\n    bool cEdge = cp1 >= ${b};\n    `);var f,g,b,m;let y;y=0===l?[1,1]:1===l?[u[0],1]:[u[c-1],u[c-2]];const x=function(t,e,n){if(0===t)return"false";if(1===t)return`rc > ${e[0]}`;let r="";for(let i=t-2;i<t;i++)r+=`${n[i]} >= ${e[i-t+2]}`,i<t-1&&(r+="||");return r}(c,y,p),_=function(t,e){const n=t.length;if(0===n)return"getA(), 0, 0, 0";if(1===n)return`getA(rc),\n            rc + 1 >= ${t[0]} ? 0. : getA(rc + 1),\n            0, 0`;let r="";if(n>2)for(let t=0;t<n-2;++t)r+=`${e[t]},`;return`getA(${r}r, c),\n          rEdge ? 0. : getA(${r}rp1, c),\n          cEdge ? 0. : getA(${r}r, cp1),\n          rEdge || cEdge ? 0. : getA(${r}rp1, cp1)`}(u,p),w=`\n        void main() {\n          ${d} rc = getOutputCoords();\n\n          if(${x}) {\n            ${n.output} = vec4(0);\n          } else {\n            ${h}\n\n            ${n.output} = vec4(${_});\n          }\n        }\n      `;return Object.assign(Object.assign({},a),{hasMain:!0,output:{dims:e.dims,type:e.type,textureType:i.TextureType.packed},shaderSource:w})})(t,e)})},2827:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.unpackFromChannel=e.getChannels=e.getVecChannels=void 0;const r=n(9390);function i(t,e){return(0,r.getGlChannels)(e).map((e=>`${t}.${e}`))}e.getVecChannels=i,e.getChannels=function(t,e){return 1===e?[t]:i(t,e)},e.unpackFromChannel=function(){return"\n    float getChannel(vec4 frag, int dim) {\n      int modCoord = imod(dim, 2);\n      return modCoord == 0 ? frag.r : frag.g;\n    }\n\n    float getChannel(vec4 frag, vec2 innerDims) {\n      vec2 modCoord = mod(innerDims, 2.);\n      return modCoord.x == 0. ?\n        (modCoord.y == 0. ? frag.r : frag.g) :\n        (modCoord.y == 0. ? frag.b : frag.a);\n    }\n  "}},2870:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.parsePadAttributesV11=e.padV11=e.parsePadAttributesV2=e.padV2=void 0;const r=n(246),i=n(2517),o=n(5060),s=n(2039),a={name:"Pad",inputNames:["A"],inputTypes:[s.TextureType.unpacked]};e.padV2=(t,e,n)=>(c(e),[t.run(Object.assign(Object.assign({},a),{cacheHint:n.cacheKey,get:()=>l(t,e[0],n)}),e)]),e.parsePadAttributesV2=t=>{const e=t.attributes.getString("mode","constant"),n=t.attributes.getFloat("value",0),i=t.attributes.getInts("pads");return(0,r.createAttributeWithCacheKey)({mode:e,value:n,pads:i})},e.padV11=(t,n,r)=>{d(n);const i=u(t,n,r);return(0,e.padV2)(t,[n[0]],i)},e.parsePadAttributesV11=t=>t.attributes.getString("mode","constant");const u=(t,e,n)=>{if(!t.session.isInitializer(e[1].dataId)||e.length>=3&&!t.session.isInitializer(e[2].dataId))throw new Error("dynamic pad attributes are not allowed");const i=Array.from(e[1].integerData),o=e.length>=3?e[2].floatData[0]:0;return(0,r.createAttributeWithCacheKey)({mode:n,pads:i,value:o})},l=(t,e,n)=>{const r=i.ShapeUtil.padShape(e.dims.slice(),n.pads),o=r.length,a=`\n      ${p(t,e,n)}\n      float process(int[${o}] indices) {\n          return padA(indices);\n      }`;return{name:"Pad",inputNames:["A"],inputTypes:[s.TextureType.unpacked],output:{dims:r,type:e.type,textureType:s.TextureType.unpacked},shaderSource:a}},c=t=>{if(!t||1!==t.length)throw new Error("Pad requires 1 input");if("float32"!==t[0].type&&"float64"!==t[0].type)throw new Error("Invalid input type.")},d=t=>{if(!t||2!==t.length&&3!==t.length)throw new Error("Pad requires 2 or 3 inputs");if("int32"!==t[1].type)throw new Error("Invalid input type.");if(t.length>=3&&"string"===t[2].type)throw new Error("Invalid input type.")},p=(t,e,n)=>{const r=(0,o.getGlsl)(t.session.backend.glContext.version),[a,u]=t.calculateTextureWidthAndHeight(e.dims,s.TextureType.unpacked),l=i.ShapeUtil.computeStrides(e.dims);switch(n.mode){case"constant":return h(r,e.dims,l,a,u,n.pads,n.value);case"reflect":return f(r,e.dims,l,a,u,n.pads);case"edge":return g(r,e.dims,l,a,u,n.pads);default:throw new Error("Invalid mode")}},h=(t,e,n,r,i,o,s)=>{const a=e.length;let u="";for(let t=a-1;t>=0;--t)u+=`\n        k = m[${t}] - ${o[t]};\n        if (k < 0)  return constant;\n        if (k >= ${e[t]}) return constant;\n        offset += k * ${n[t]};\n        `;return`\n      float padA(int m[${a}]) {\n        const float constant = float(${s});\n        int offset = 0;\n        int k = 0;\n        ${u}\n        vec2 coords = offsetToCoords(offset, ${r}, ${i});\n        float value = getColorAsFloat(${t.texture2D}(A, coords));\n        return value;\n      }\n      `},f=(t,e,n,r,i,o)=>{const s=e.length;let a="";for(let t=s-1;t>=0;--t)a+=`\n        k = m[${t}] - ${o[t]};\n        if (k < 0) { k = -k; }\n        {\n          const int _2n_1 = ${2*(e[t]-1)};\n          k = int( mod( float(k), float(_2n_1) ) ) ;\n          if(k >= ${e[t]}) { k = _2n_1 - k; }\n        }\n        offset += k * ${n[t]};\n        `;return`\n      float padA(int m[${s}]) {\n        int offset = 0;\n        int k = 0;\n        ${a}\n        vec2 coords = offsetToCoords(offset, ${r}, ${i});\n        float value = getColorAsFloat(${t.texture2D}(A, coords));\n        return value;\n      }\n      `},g=(t,e,n,r,i,o)=>{const s=e.length;let a="";for(let t=s-1;t>=0;--t)a+=`\n        k = m[${t}] - ${o[t]};\n        if (k < 0)  k = 0;\n        if (k >= ${e[t]}) k = ${e[t]-1};\n        offset += k * ${n[t]};\n      `;return`\n      float padA(int m[${s}]) {\n        int offset = 0;\n        int k = 0;\n        ${a}\n        vec2 coords = offsetToCoords(offset, ${r}, ${i});\n        float value = getColorAsFloat(${t.texture2D}(A, coords));\n        return value;\n      }\n      `}},2143:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.globalMaxPool=e.parseMaxPoolAttributes=e.maxPool=e.parseGlobalAveragePoolAttributes=e.globalAveragePool=e.parseAveragePoolAttributes=e.averagePool=void 0;const r=n(246),i=n(2517),o=n(2039);e.averagePool=(t,e,n)=>{d(e);const r={name:"AveragePool",inputNames:["X"],inputTypes:[o.TextureType.unpacked],cacheHint:n.cacheKey};return[t.run(Object.assign(Object.assign({},r),{get:()=>s(e,r,!1,n)}),e)]},e.parseAveragePoolAttributes=t=>{const e=t.attributes.getString("auto_pad","NOTSET"),n=t.attributes.getInt("ceil_mode",0),i=0!==t.attributes.getInt("count_include_pad",0),o=t.attributes.getInts("kernel_shape"),s=t.attributes.getInts("strides",[]),a=t.attributes.getInts("pads",[]);if(0!==n)throw new Error("using ceil() in shape computation is not yet supported for AveragePool");return(0,r.createAttributeWithCacheKey)({autoPad:e,ceilMode:n,countIncludePad:i,kernelShape:o,strides:s,pads:a})};const s=(t,e,n,r)=>{const[s,a]=u(t,r,n),l=i.ShapeUtil.size(s.kernelShape);let c="";s.countIncludePad?c+=`value /= float(${l});`:c+=`value /= float(${l} - pad);`;const d=`\n        ${p(t[0].dims,s,"value += _X(x);",c,"0.0")}\n      `;return Object.assign(Object.assign({},e),{output:{dims:a,type:t[0].type,textureType:o.TextureType.unpacked},shaderSource:d})};e.globalAveragePool=(t,e,n)=>{d(e);const r={name:"GlobalAveragePool",inputNames:["X"],inputTypes:[o.TextureType.unpacked],cacheHint:`${n.countIncludePad}`};return[t.run(Object.assign(Object.assign({},r),{get:()=>s(e,r,!0,n)}),e)]},e.parseGlobalAveragePoolAttributes=t=>{const e=0!==t.attributes.getInt("count_include_pad",0);return(0,r.createAttributeWithCacheKey)({autoPad:"",ceilMode:0,countIncludePad:e,kernelShape:[],strides:[],pads:[]})},e.maxPool=(t,e,n)=>{d(e);const r={name:"MaxPool",inputNames:["X"],inputTypes:[o.TextureType.unpacked],cacheHint:n.cacheKey};return[t.run(Object.assign(Object.assign({},r),{get:()=>a(e,r,!1,n)}),e)]},e.parseMaxPoolAttributes=t=>{const e=t.attributes.getString("auto_pad","NOTSET"),n=t.attributes.getInt("ceil_mode",0),i=t.attributes.getInts("kernel_shape"),o=t.attributes.getInts("strides",[]),s=t.attributes.getInts("pads",[]),a=t.attributes.getInt("storage_order",0),u=t.attributes.getInts("dilations",[]);if(0!==a)throw new Error("column major storage order is not yet supported for MaxPool");if(0!==n)throw new Error("using ceil() in shape computation is not yet supported for MaxPool");return(0,r.createAttributeWithCacheKey)({autoPad:e,ceilMode:n,countIncludePad:!1,kernelShape:i,strides:o,pads:s,storageOrder:a,dilations:u})};const a=(t,e,n,r)=>{const[i,s]=u(t,r,n),a=`\n      ${p(t[0].dims,i,"\n      value = max(_X(x), value);\n    ","","-1e5")}\n    `;return Object.assign(Object.assign({},e),{output:{dims:s,type:t[0].type,textureType:o.TextureType.unpacked},shaderSource:a})},u=(t,e,n)=>{const r=t[0].dims.slice(),o=Object.hasOwnProperty.call(e,"dilations"),s=e.kernelShape.slice(),a=e.strides.slice(),u=o?e.dilations.slice():[],l=e.pads.slice();i.PoolConvUtil.adjustPoolAttributes(n,r,s,a,u,l);const c=i.PoolConvUtil.computePoolOutputShape(n,r,a,u,s,l,e.autoPad),d=Object.assign({},e);return o?Object.assign(d,{kernelShape:s,strides:a,pads:l,dilations:u,cacheKey:e.cacheKey}):Object.assign(d,{kernelShape:s,strides:a,pads:l,cacheKey:e.cacheKey}),[d,c]},l={autoPad:"",ceilMode:0,countIncludePad:!1,kernelShape:[],strides:[],pads:[],storageOrder:0,dilations:[],cacheKey:""},c={name:"GlobalMaxPool",inputNames:["X"],inputTypes:[o.TextureType.unpacked]};e.globalMaxPool=(t,e)=>(d(e),[t.run(Object.assign(Object.assign({},c),{get:()=>a(e,c,!0,l)}),e)]);const d=t=>{if(!t||1!==t.length)throw new Error("Pool ops requires 1 input.");if("float32"!==t[0].type&&"float64"!==t[0].type)throw new Error("Invalid input type.")},p=(t,e,n,r,o)=>{const s=t.length;if(e.kernelShape.length<=2){const i=e.kernelShape[e.kernelShape.length-1],a=e.strides[e.strides.length-1],u=e.pads[e.pads.length/2-1],l=e.pads[e.pads.length-1],c=t[s-1];let d="",p="",h="";if(d=u+l!==0?`\n          for (int i = 0; i < ${i}; i++) {\n            x[${s} - 1] = indices[${s} - 1] * ${a} - ${u} + i;\n            if (x[${s} - 1] < 0 || x[${s} - 1] >= ${c}) {\n              pad++;\n              continue;\n            }\n            ${n}\n          }`:`\n          for (int i = 0; i < ${i}; i++) {\n            x[${s} - 1] = indices[${s} - 1] * ${a} - ${u} + i;\n            ${n}\n          }`,2===e.kernelShape.length){const n=e.kernelShape[e.kernelShape.length-2],r=e.strides[e.strides.length-2],o=e.pads[e.pads.length/2-2],a=e.pads[e.pads.length-2],u=t[s-2];p=o+a!==0?`\n            for (int j = 0; j < ${n}; j++) {\n              x[${s} - 2] = indices[${s} - 2] * ${r} - ${o} + j;\n              if (x[${s} - 2] < 0 || x[${s} - 2] >= ${u}) {\n                pad+= ${i};\n                continue;\n              }\n          `:`\n            for (int j = 0; j < ${n}; j++) {\n              x[${s} - 2] = indices[${s} - 2] * ${r} - ${o} + j;\n            `,h="\n          }\n        "}return`\n        float process(int indices[${s}]) {\n          int x[${s}];\n          copyVec(indices, x);\n\n          float value = ${o};\n          int pad = 0;\n          ${p}\n          ${d}\n          ${h}\n          ${r}\n          return value;\n        }\n      `}{const a=i.ShapeUtil.size(e.kernelShape),u=i.ShapeUtil.computeStrides(e.kernelShape),l=u.length,c=e.pads.length,d=f(l),p=h(t,"inputDims"),g=h(e.pads,"pads"),b=h(u,"kernelStrides"),m=h(e.strides,"strides");let y="";return y=e.pads.reduce(((t,e)=>t+e))?`\n            if (x[j] >= inputDims[j] || x[j] < 0) {\n              pad++;\n              isPad = true;\n              break;\n            }\n          }\n          if (!isPad) {\n            ${n}\n          }`:`\n          }\n          ${n}\n        `,`\n        ${d}\n        float process(int indices[${s}]) {\n          int x[${s}];\n          copyVec(indices, x);\n          int offset[${l}];\n          int pads[${c}];\n          int inputDims[${s}];\n          int kernelStrides[${l}];\n          int strides[${l}];\n          ${g}\n          ${p}\n          ${m}\n          ${b}\n\n          float value = ${o};\n          int pad = 0;\n          bool isPad = false;\n          for (int i = 0; i < ${a}; i++) {\n            offsetToIndices(i, kernelStrides, offset);\n            isPad = false;\n            for (int j = ${s} - ${l}; j < ${s}; j++) {\n              x[j] = indices[j] * strides[j - ${s} + ${l}]\n                + offset[j - ${s} + ${l}] - pads[j - 2];\n              ${y}\n          }\n          ${r}\n\n          return value;\n        }\n      `}},h=(t,e)=>{let n="";for(let r=0;r<t.length;r++)n+=`\n      ${e}[${r}] = ${t[r]};\n    `;return n},f=t=>`\n  void offsetToIndices(int offset, int[${t}] strides, out int[${t}] indices) {\n    if (${t} == 0) {\n      return;\n    }\n    for (int i = 0; i < ${t} - 1; ++i) {\n      indices[i] = offset / strides[i];\n      offset -= indices[i] * strides[i];\n    }\n    indices[${t} - 1] = offset;\n  }`},4939:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.reduceLogSumSquare=e.reduceLogSum=e.reduceProd=e.reduceMin=e.reduceMax=e.reduceMean=e.reduceSum=e.parseReduceAttributes=void 0;const r=n(246),i=n(782),o=n(2517),s=n(2039),a=(t,e,n,r,i)=>{l(e);const o={name:r,inputNames:["A"],inputTypes:[s.TextureType.unpacked]};return[t.run(Object.assign(Object.assign({},o),{cacheHint:n.cacheKey,get:()=>u(t,e,n,r,i,o)}),e)]};e.parseReduceAttributes=t=>{const e=t.attributes.getInts("axes",[]),n=1===t.attributes.getInt("keepdims",1);return(0,r.createAttributeWithCacheKey)({axes:e,keepDims:n})};const u=(t,e,n,r,i,a)=>{const u=[],l=e[0].dims.length||1,c=[],d=o.ShapeUtil.normalizeAxes(n.axes,e[0].dims.length),p=i(e,d);let h=p[1];for(let t=0;t<e[0].dims.length;t++)d.indexOf(t)>=0||0===d.length?(n.keepDims&&u.push(1),h=`\n          for(int j${t} = 0; j${t} < ${e[0].dims[t]}; j${t}++) {\n            inputIdx[${t}] = j${t};\n            ${h}\n          }`):(c.push(`inputIdx[${t}] = outputIdx[${u.length}];`),u.push(e[0].dims[t]));const f=`\n      float process(int outputIdx[${u.length||1}]) {\n        float value;                 // final result\n        int inputIdx[${l}];      // addressing input data\n        ${c.join("\n")}\n        ${p[0]}       // init ops for reduce max/min\n        ${h}\n        ${p[2]}       // final computation for reduce mean\n        return value;\n      }`;return Object.assign(Object.assign({},a),{output:{dims:u,type:e[0].type,textureType:s.TextureType.unpacked},shaderSource:f})},l=t=>{if(!t||1!==t.length)throw new Error("Reduce op requires 1 input.");if(-1===i.NUMBER_TYPES.indexOf(t[0].type))throw new Error("Invalid input type.")};e.reduceSum=(t,e,n)=>a(t,e,n,"ReduceSum",(()=>["value = 0.0;","value += _A(inputIdx);",""])),e.reduceMean=(t,e,n)=>a(t,e,n,"ReduceMean",((t,e)=>{let n=1;for(let r=0;r<t[0].dims.length;r++)(e.indexOf(r)>=0||0===e.length)&&(n*=t[0].dims[r]);return["value = 0.0;","value += _A(inputIdx);",`value /= ${n}.;`]})),e.reduceMax=(t,e,n)=>a(t,e,n,"ReduceMax",((t,e)=>{const n=[];for(let r=0;r<t[0].dims.length;r++)(e.indexOf(r)>=0||0===e.length)&&n.push(`inputIdx[${r}] = 0;`);return[`${n.join("\n")}\nvalue = _A(inputIdx);`,"value = max(value, _A(inputIdx));",""]})),e.reduceMin=(t,e,n)=>a(t,e,n,"ReduceMin",((t,e)=>{const n=[];for(let r=0;r<t[0].dims.length;r++)(e.indexOf(r)>=0||0===e.length)&&n.push(`inputIdx[${r}] = 0;`);return[`${n.join("\n")}\nvalue = _A(inputIdx);`,"value = min(value, _A(inputIdx));",""]})),e.reduceProd=(t,e,n)=>a(t,e,n,"ReduceProd",(()=>["value = 1.0;","value *= _A(inputIdx);",""])),e.reduceLogSum=(t,e,n)=>a(t,e,n,"ReduceLogSum",(()=>["value = 0.0;","value += _A(inputIdx);","value = log(value);"])),e.reduceLogSumSquare=(t,e,n)=>a(t,e,n,"ReduceLogSumSquare",(()=>["float t; value = 0.0;","t = _A(inputIdx); value += t * t;",""]))},7019:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.isReshapeCheap=e.processDims3D=e.createPackedReshape3DProgramInfoLoader=void 0;const r=n(2517),i=n(5060),o=n(2039),s=n(2827);e.createPackedReshape3DProgramInfoLoader=(t,e,n)=>{const a=(t=>({name:"Reshape (packed)",inputTypes:[o.TextureType.packed],inputNames:["A"],cacheHint:`${t}`}))(n);return Object.assign(Object.assign({},a),{get:()=>((t,e,n,a)=>{const u=e.dims,l=a;let c="";for(let t=0;t<4;t++){let e="";switch(t){case 0:e="outputCoords = rc;";break;case 1:e="outputCoords = ivec3(rc.x, rc.y+1, rc.z);";break;case 2:e="outputCoords = ivec3(rc.x, rc.y, rc.z+1);";break;case 3:e="outputCoords = ivec3(rc.x, rc.y+1, rc.z+1);";break;default:throw new Error}c+=`\n        ${e}\n        ${t>0?"if(outputCoords.y < rows && outputCoords.z < cols){":""}\n          int flattenedIndex = getFlattenedIndex(outputCoords);\n\n          ivec3 inputRC = inputCoordsFromReshapedOutCoords(flattenedIndex);\n          vec2 innerDims = vec2(float(inputRC.y),float(inputRC.z));\n\n          result[${t}] = getChannel(getA(inputRC.x, inputRC.y, inputRC.z), innerDims);\n\n        ${t>0?"}":""}\n      `}const d=(0,i.getGlsl)(t.session.backend.glContext.version),p=`\n      ${function(t){const e=r.ShapeUtil.computeStrides(t),n=["b","r","c"],i="index";return`\n    ivec3 inputCoordsFromReshapedOutCoords(int index) {\n      ${e.map(((t,r)=>`int ${n[r]} = ${i} / ${t}; ${r===e.length-1?`int ${n[r+1]} = ${i} - ${n[r]} * ${t}`:`index -= ${n[r]} * ${t}`};`)).join("")}\n      return ivec3(b, r, c);\n    }\n  `}(u)}\n      ${function(t){const e=r.ShapeUtil.computeStrides(t);return`\n  int getFlattenedIndex(ivec3 coords) {\n    // reverse y, z order\n    return coords.x * ${e[0]} + coords.z * ${e[1]} + coords.y;\n  }\n`}(l)}\n      ${(0,s.unpackFromChannel)()}\n\n      void main() {\n        ivec3 rc = getOutputCoords();\n\n        vec4 result = vec4(0.0);\n\n        ivec3 outputCoords;\n        int rows = ${l[2]};\n        int cols = ${l[1]};\n\n        ${c}\n        ${d.output} = result;\n      }\n    `;return Object.assign(Object.assign({},n),{output:{dims:l,type:e.type,textureType:o.TextureType.packed},shaderSource:p,hasMain:!0})})(t,e,a,n)})},e.processDims3D=function(t){if(0===t.length)return[1,1,1];let e=1;for(let n=0;n<t.length-2;++n)e*=t[n];return[e,t.length>1?t[t.length-2]:1,t[t.length-1]]},e.isReshapeCheap=function(t,e){let n=!1;return n=0===t.length||0===e.length||(t.length<2||e.length<2?t[t.length-1]===e[e.length-1]:t[t.length-1]===e[e.length-1]&&t[t.length-2]===e[e.length-2]),n}},718:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.reshape=void 0;const r=n(2517);e.reshape=(t,e)=>{const n=r.ShapeUtil.calculateReshapedDims(e[0].dims,e[1].integerData);return t.session.pack?[t.reshapePacked(e[0],n)]:[t.reshapeUnpacked(e[0],n)]}},2268:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.parseResizeAttributesV11=e.parseResizeAttributesV10=e.resize=void 0;const r=n(5060),i=n(2039),o=n(9390),s=n(2827),a=n(9793),u={name:"Resize",inputNames:["A"],inputTypes:[i.TextureType.packed]};e.resize=(t,e,n)=>((0,a.validateInputs)(e,n),[t.run(Object.assign(Object.assign({},u),{cacheHint:n.cacheKey,get:()=>l(t,e,n)}),e)]),e.parseResizeAttributesV10=t=>(0,a.parseUpsampleAttributes)(t,10),e.parseResizeAttributesV11=t=>(0,a.parseUpsampleAttributes)(t,11);const l=(t,e,n)=>{const a=(0,r.getGlsl)(t.session.backend.glContext.version),[l,d]=c(e,n);if(l.every((t=>1===t))&&"tf_crop_and_resize"!==n.coordinateTransformMode)return Object.assign(Object.assign({},u),{output:{dims:d,type:e[0].type,textureType:i.TextureType.packed},hasMain:!0,shaderSource:`void main() {\n                    vec4 v = ${a.texture2D}(X, TexCoords);\n                    ${a.output} = v;\n                }`});const p=d.length;if(p<2)throw new Error(`output dimension should be at least 2, but got ${p}`);const h=d[p-2],f=d[p-1],g=e[0].dims;if(p!==g.length)throw new Error(`output dimension should match input ${g.length}, but got ${p}`);const b=g[p-2],m=g[p-1],y=l[p-2],x=l[p-1];let _="";if("linear"!==n.mode)throw new Error(`resize (packed) does not support mode: '${n.mode}'`);switch(n.coordinateTransformMode){case"asymmetric":_="\n                    vec4 getSourceFracIndex(ivec4 coords) {\n                        return vec4(coords) / scaleWHWH;\n                    }\n                ";break;case"half_pixel":_="\n                    vec4 getSourceFracIndex(ivec4 coords) {\n                        return (vec4(coords) + 0.5) / scaleWHWH - 0.5;\n                    }\n                ";break;case"pytorch_half_pixel":_=`\n                    vec4 getSourceFracIndex(ivec4 coords) {\n                        vec4 fcoords = vec4(coords);\n                        return vec4(\n                            ${f}.0 > 1.0 ? (fcoords.x + 0.5) / scaleWHWH.x - 0.5 : 0.0,\n                            ${h}.0 > 1.0 ? (fcoords.y + 0.5) / scaleWHWH.y - 0.5 : 0.0,\n                            ${f}.0 > 1.0 ? (fcoords.z + 0.5) / scaleWHWH.z - 0.5 : 0.0,\n                            ${h}.0 > 1.0 ? (fcoords.w + 0.5) / scaleWHWH.w - 0.5 : 0.0\n                          );\n                    }\n                `;break;case"align_corners":_=`\n                    vec4 getSourceFracIndex(ivec4 coords) {\n                        vec4 resized = vec4(${f}.0 - 1.0, ${h}.0 - 1.0, ${f}.0 - 1.0,\n                            ${h}.0 - 1.0);\n                        vec4 original = vec4(${m}.0 - 1.0, ${b}.0 - 1.0, ${m}.0 - 1.0,\n                            ${b}.0 - 1.0);\n                        vec4 new_scale = original / resized;\n                        return vec4(coords) * new_scale;\n                    }\n                `;break;default:throw new Error(`resize (packed) does not support coordinateTransformMode:                                 '${n.coordinateTransformMode}'`)}const w=(0,o.getCoordsDataType)(p),v=`\n            const vec2 inputWH = vec2(${b}.0, ${m}.0);\n            const vec4 scaleWHWH = vec4(float(${y}), float(${x}), float(${y}), float(${x}));\n            ${(0,s.unpackFromChannel)()}\n            ${_}\n            float getAValue(int x10, int r, int c, int d) {\n                return getChannel(getA(x10, r, c, d), vec2(c, d));\n            }\n            void main() {\n                ${w} rc = getOutputCoords();\n\n                int batch = rc[0];\n                int depth = rc[1];\n\n                // retrieve the 4 coordinates that is used in the 4 packed output values.\n                ivec4 coords = ivec4(rc.wz, rc.w + 1, rc.z + 1);\n\n                // calculate the source index in fraction\n                vec4 sourceFrac = getSourceFracIndex(coords);\n\n                // get the lower and upper bound of the 4 values that will be packed into one texel.\n                ivec4 x00 = ivec4(max(sourceFrac.xy, vec2(0.0)), min(inputWH - 1.0, ceil(sourceFrac.xy)));\n                ivec4 x01 = ivec4(max(sourceFrac.xw, vec2(0.0)), min(inputWH - 1.0, ceil(sourceFrac.xw)));\n                ivec4 x10 = ivec4(max(sourceFrac.zy, vec2(0.0)), min(inputWH - 1.0, ceil(sourceFrac.zy)));\n                ivec4 x11 = ivec4(max(sourceFrac.zw, vec2(0.0)), min(inputWH - 1.0, ceil(sourceFrac.zw)));\n\n                bool hasNextRow = rc.w < ${h-1};\n                bool hasNextCol = rc.z < ${f-1};\n\n                // pack x00, x01, x10, x11's top-left corner into one vec4 structure\n                vec4 topLeft = vec4(\n                    getAValue(batch, depth, x00.x, x00.y),\n                    hasNextCol ? getAValue(batch, depth, x01.x, x01.y) : 0.0,\n                    hasNextRow ? getAValue(batch, depth, x10.x, x10.y) : 0.0,\n                    (hasNextRow && hasNextCol) ? getAValue(batch, depth, x11.x, x11.y) : 0.0);\n\n                // pack x00, x01, x10, x11's top-right corner into one vec4 structure\n                vec4 topRight = vec4(\n                    getAValue(batch, depth, x00.x, x00.w),\n                    hasNextCol ? getAValue(batch, depth, x01.x, x01.w) : 0.0,\n                    hasNextRow ? getAValue(batch, depth, x10.x, x10.w) : 0.0,\n                    (hasNextRow && hasNextCol) ? getAValue(batch, depth, x11.x, x11.w) : 0.0);\n\n                // pack x00, x01, x10, x11's bottom-left corner into one vec4 structure\n                vec4 bottomLeft = vec4(\n                    getAValue(batch, depth, x00.z, x00.y),\n                    hasNextCol ? getAValue(batch, depth, x01.z, x01.y) : 0.0,\n                    hasNextRow ? getAValue(batch, depth, x10.z, x10.y) : 0.0,\n                    (hasNextRow && hasNextCol) ? getAValue(batch, depth, x11.z, x11.y) : 0.0);\n\n                // pack x00, x01, x10, x11's bottom-right corner into one vec4 structure\n                vec4 bottomRight = vec4(\n                    getAValue(batch, depth, x00.z, x00.w),\n                    hasNextCol ? getAValue(batch, depth, x01.z, x01.w) : 0.0,\n                    hasNextRow ? getAValue(batch, depth, x10.z, x10.w) : 0.0,\n                    (hasNextRow && hasNextCol) ? getAValue(batch, depth, x11.z, x11.w) : 0.0);\n\n                // calculate the interpolation fraction on u and v direction\n                vec4 frac = vec4(sourceFrac) - floor(sourceFrac);\n                vec4 clampFrac = clamp(frac, vec4(0.0), vec4(1.0));\n\n                vec4 top = mix(topLeft, topRight, clampFrac.ywyw);\n                vec4 bottom = mix(bottomLeft, bottomRight, clampFrac.ywyw);\n                vec4 newValue = mix(top, bottom, clampFrac.xxzz);\n\n                ${a.output} = vec4(newValue);\n            }\n        `;return Object.assign(Object.assign({},u),{output:{dims:d,type:e[0].type,textureType:i.TextureType.packed},hasMain:!0,shaderSource:v})},c=(t,e)=>{const n=t[0].dims;let r,i=e.scales;if(0===i.length){const o=t[e.scalesInputIdx];if(o&&0!==o.size){if(t[e.sizesInputIdx])throw new Error("Only one of scales or sizes must be provided as input.");i=d(o,e.mode,e.isResize)}else{const o=t[e.sizesInputIdx];if(!o||0===o.size)throw new Error("Either scales or sizes MUST be provided as input.");r=Array.from(o.integerData),i=p(r,n,e.mode,e.isResize)}}else if(t[e.sizesInputIdx])throw new Error("Only one of scales or sizes must be provided as input.");const o=r||n.map(((t,e)=>Math.floor(t*i[e])));return[i,o]},d=(t,e,n)=>{const r=Array.from(t.floatData);return(0,a.scalesValidation)(r,e,n),r},p=(t,e,n,r)=>{const i=e.length,o=new Array(i);for(let n=0,r=i;n<r;n++)if(0===e[n]){if(0!==t[n])throw new Error("Input dim is zero but required output dim is non-zero.");o[n]=1}else o[n]=t[n]/e[n];return(0,a.scalesValidation)(o,n,r),o}},8117:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.shape=void 0;const r=n(9162);e.shape=(t,e)=>(i(e),[new r.Tensor([e[0].dims.length],"int32",void 0,void 0,new Int32Array(e[0].dims))]);const i=t=>{if(!t||1!==t.length)throw new Error("Shape requires 1 input.")}},2278:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.sliceV10=e.parseSliceAttributes=e.slice=void 0;const r=n(246),i=n(782),o=n(2517),s=n(2039),a={name:"Slice",inputNames:["A"],inputTypes:[s.TextureType.unpacked]};e.slice=(t,e,n)=>(l(e),[t.run(Object.assign(Object.assign({},a),{cacheHint:n.cacheKey,get:()=>u(t,e[0],n)}),e)]),e.parseSliceAttributes=t=>{const e=t.attributes.getInts("starts"),n=t.attributes.getInts("ends"),i=t.attributes.getInts("axes",[]);return(0,r.createAttributeWithCacheKey)({starts:e,ends:n,axes:i})};const u=(t,e,n)=>{const r=0===n.axes.length?e.dims.slice(0).map(((t,e)=>e)):n.axes,i=o.ShapeUtil.normalizeAxes(r,e.dims.length),u=n.starts.map(((t,n)=>t>e.dims[i[n]]-1?e.dims[i[n]]:o.ShapeUtil.normalizeAxis(t,e.dims[i[n]]))),l=n.ends.map(((t,n)=>t>e.dims[i[n]]-1?e.dims[i[n]]:o.ShapeUtil.normalizeAxis(t,e.dims[i[n]]))),c=e.dims.slice(),d=[];for(let t=0;t<i.length;t++)c[i[t]]=l[t]-u[t],u[t]>0&&d.push(`outputIdx[${i[t]}] += ${u[t]};`);const p=`\n      float process(int outputIdx[${c.length}]) {\n        ${d.join("\n      ")}\n        return _A(outputIdx);\n      }`;return Object.assign(Object.assign({},a),{output:{dims:c,type:e.type,textureType:s.TextureType.unpacked},shaderSource:p})},l=t=>{if(!t||1!==t.length)throw new Error("Slice requires 1 input.");if(-1===i.NUMBER_TYPES.indexOf(t[0].type))throw new Error("Invalid input type.")};e.sliceV10=(t,e)=>{d(e);const n=c(t,e);return[t.run(Object.assign(Object.assign({},a),{cacheHint:n.cacheKey,get:()=>u(t,e[0],n)}),[e[0]])]};const c=(t,e)=>{if(!t.session.isInitializer(e[1].dataId)||!t.session.isInitializer(e[2].dataId)||e.length>=4&&!t.session.isInitializer(e[3].dataId)||e.length>=5&&!t.session.isInitializer(e[4].dataId))throw new Error("dynamic slice attributes are not allowed");if(e.length>=5&&e[4].integerData.some((t=>1!==t)))throw new Error("currently non-1 steps is not supported for Slice");const n=Array.from(e[1].integerData),r=Array.from(e[2].integerData),i=e.length>=4?Array.from(e[3].integerData):[];return{starts:n,ends:r,axes:i,cacheKey:`${i};${n};${r}`}},d=t=>{if(!t||t.length<3||t.length>5)throw new Error("Invalid input number.");if("int32"!==t[1].type||1!==t[1].dims.length)throw new Error("Invalid input type.");if("int32"!==t[2].type||1!==t[2].dims.length)throw new Error("Invalid input type.");if(t.length>=4&&("int32"!==t[3].type||1!==t[3].dims.length))throw new Error("Invalid input type.");if(t.length>=5&&("int32"!==t[4].type||1!==t[4].dims.length))throw new Error("Invalid input type.")}},5524:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.softmaxV13=e.parseSoftmaxAttributesV13=e.parseSoftmaxAttributes=e.softmax=void 0;const r=n(246),i=n(2517),o=n(5060),s=n(2039),a=n(3738),u={name:"SoftmaxComputeMax",inputNames:["A"],inputTypes:[s.TextureType.unpacked]},l={name:"SoftmaxComputeScale",inputNames:["A","Max"],inputTypes:[s.TextureType.unpacked,s.TextureType.unpacked]},c={name:"SoftMax",inputNames:["A","Max","Norm"],inputTypes:[s.TextureType.unpacked,s.TextureType.unpacked,s.TextureType.unpacked]};e.softmax=(t,e,n)=>{g(e);const r=e[0].dims.slice(),o=i.ShapeUtil.normalizeAxis(n.axis,r.length),s=i.ShapeUtil.sizeToDimension(r,o),a=i.ShapeUtil.sizeFromDimension(r,o);return d(t,e,n,s,a)},e.parseSoftmaxAttributes=t=>(0,r.createAttributeWithCacheKey)({axis:t.attributes.getInt("axis",1)}),e.parseSoftmaxAttributesV13=t=>(0,r.createAttributeWithCacheKey)({axis:t.attributes.getInt("axis",-1)}),e.softmaxV13=(t,e,n)=>{g(e);const o=e[0].dims.slice(),s=i.ShapeUtil.normalizeAxis(n.axis,o.length),u=o.length,l=s!==u-1,c=[];let p,h=[],f=[];l&&(h=Array.from({length:u}).map(((t,e)=>e)),h[s]=u-1,h[u-1]=s,h.map((t=>c.push(o[t]))),p=(0,r.createAttributeWithCacheKey)({perm:h}),f=(0,a.transpose)(t,e,p));const b=l?i.ShapeUtil.sizeToDimension(c,u-1):i.ShapeUtil.sizeToDimension(o,u-1),m=l?i.ShapeUtil.sizeFromDimension(c,u-1):i.ShapeUtil.sizeFromDimension(o,u-1),y=d(t,l?f:e,n,b,m);return l?(0,a.transpose)(t,y,p):y};const d=(t,e,n,r,i)=>{const o=p(t,e[0],r,i,[r]),s=t.run(Object.assign(Object.assign({},u),{cacheHint:n.cacheKey,get:()=>o}),e),a=h(t,e[0],r,i,o.output.dims,[r]),d=t.run(Object.assign(Object.assign({},l),{cacheHint:n.cacheKey,get:()=>a}),[e[0],s]),g=f(t,e[0],r,i,o.output.dims,a.output.dims);return[t.run(Object.assign(Object.assign({},c),{cacheHint:n.cacheKey,get:()=>g}),[e[0],s,d])]},p=(t,e,n,r,i)=>{const[a,l]=t.calculateTextureWidthAndHeight(e.dims,s.TextureType.unpacked),c=i.length;if(n<1||r<1)throw new Error("Logical row count N and feature count D must be greater than or equal to 1");if(1!==i.length)throw new Error("Dimensionality of the output should be 1");if(i[0]!==n)throw new Error("Shape of the output should be equal to logical row count");const d=(0,o.getGlsl)(t.session.backend.glContext.version),p=`\n      float process(int[${c}] indices) {\n        int logical_row_start_offset = indices[0] * ${r};\n\n        float max = getColorAsFloat(${d.texture2D}(A, offsetToCoords(logical_row_start_offset, ${a},\n        ${l} )));\n        for(int i=1; i<${r}; ++i)\n        {\n          float current = getColorAsFloat(${d.texture2D}(A, offsetToCoords(logical_row_start_offset + i,\n            ${a}, ${l})));\n          if(current > max)\n          max = current;\n        }\n\n        return max;\n      }`;return Object.assign(Object.assign({},u),{output:{dims:i,type:e.type,textureType:s.TextureType.unpacked},shaderSource:p})},h=(t,e,n,r,i,a)=>{const[u,c]=t.calculateTextureWidthAndHeight(e.dims,s.TextureType.unpacked),d=a.length;if(n<1||r<1)throw new Error("Logical row count N and feature count D must be greater than or equal to 1");if(1!==a.length)throw new Error("Dimensionality of the output should be 1");if(a[0]!==n)throw new Error("Shape of the output should be equal to logical row count");if(1!==i.length)throw new Error("Dimensionality of the intermediate results should be 1");if(i[0]!==n)throw new Error("Shape of the intermediate results should be equal to logical row count");const p=`\n      float process(int[${d}] indices) {\n        int logical_row_start_offset = indices[0] * ${r};\n\n        float norm_factor = 0.0;\n        float max = _Max(indices);\n        for(int i=0; i<${r}; ++i)\n        {\n          norm_factor += exp(getColorAsFloat(${(0,o.getGlsl)(t.session.backend.glContext.version).texture2D}(A, offsetToCoords(logical_row_start_offset + i,\n            ${u}, ${c}))) - max);\n        }\n\n        return norm_factor;\n      }`;return Object.assign(Object.assign({},l),{output:{dims:a,type:e.type,textureType:s.TextureType.unpacked},shaderSource:p})},f=(t,e,n,r,i,o)=>{const[a,u]=t.calculateTextureWidthAndHeight(e.dims,s.TextureType.unpacked),l=e.dims.length;if(n<1||r<1)throw new Error("Logical row count N and feature count D must be greater than or equal to 1");if(1!==i.length||1!==o.length)throw new Error("Dimensionality of the intermediate results should be 1");if(i[0]!==n||o[0]!==n)throw new Error("Shape of the intermediate results should be equal to logical row count");const d=`\n      float process(int[${l}] indices) {\n\n      // get offset of current logical tensor index from the 2-D texture coordinates (TexCoords)\n      int offset = coordsToOffset(TexCoords, ${a}, ${u});\n\n      //determine the logical row for this index\n      int logical_row_index[1];\n      logical_row_index[0] = offset / ${r};\n\n      float norm_factor = _Norm(logical_row_index);\n\n      // avoid possible division by 0\n      // if norm_facor is 0, all elements are zero\n      // if so, return 0\n      if(norm_factor == 0.0)\n        return 0.0;\n\n      return exp(_A(indices) - _Max(logical_row_index)) / norm_factor;\n    }`;return Object.assign(Object.assign({},c),{output:{dims:e.dims,type:e.type,textureType:s.TextureType.unpacked},shaderSource:d})},g=t=>{if(!t||1!==t.length)throw new Error("Softmax requires 1 input.");if("float32"!==t[0].type&&"float64"!==t[0].type)throw new Error("Invalid input type")}},5975:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.parseSplitAttributes=e.split=void 0;const r=n(246),i=n(2517),o=n(2039),s={name:"Split",inputNames:["A"],inputTypes:[o.TextureType.unpacked]};e.split=(t,e,n)=>{l(e);const r=i.ShapeUtil.normalizeAxis(n.axis,e[0].dims.length),o=a(t,e,r,n),c=[];for(let i=0;i<o;++i)c.push(t.run(Object.assign(Object.assign({},s),{cacheHint:`${n.cacheKey};${i}`,get:()=>u(t,e[0],n,r,i)}),e));return c},e.parseSplitAttributes=t=>{const e=t.attributes.getInt("axis",0),n=t.attributes.getInts("split",[]),i=t.outputs.length;return(0,r.createAttributeWithCacheKey)({axis:e,split:n,numOutputs:i})};const a=(t,e,n,r)=>{const[,o]=i.SplitUtil.splitShape(e[0].dims,n,r.split,r.numOutputs);return o.length},u=(t,e,n,r,a)=>{const[u,l]=i.SplitUtil.splitShape(e.dims,r,n.split,n.numOutputs),c=l[a],d=u[a],p=`\n      float process(int indices[${d.length}]) {\n        indices[${r}] += ${c};\n        return _A(indices);\n      }\n    `;return Object.assign(Object.assign({},s),{cacheHint:`${n.cacheKey}:${a}`,output:{dims:d,type:e.type,textureType:o.TextureType.unpacked},shaderSource:p})},l=t=>{if(!t||1!==t.length)throw new Error("Split requires one input.");if("int8"!==t[0].type&&"uint8"!==t[0].type&&"int16"!==t[0].type&&"uint16"!==t[0].type&&"int32"!==t[0].type&&"uint32"!==t[0].type&&"float32"!==t[0].type&&"float64"!==t[0].type&&"bool"!==t[0].type)throw new Error("Invalid input type.")}},3933:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.parseSqueezeAttributes=e.squeezeV13=e.squeeze=void 0;const r=n(2517);e.squeeze=(t,e,n)=>{i(e);const o=r.ShapeUtil.squeezeShape(e[0].dims,n);return[t.reshapeUnpacked(e[0],o)]},e.squeezeV13=(t,n)=>(o(n),(0,e.squeeze)(t,[n[0]],Array.from(n[1].integerData))),e.parseSqueezeAttributes=t=>t.attributes.getInts("axes");const i=t=>{if(!t||1!==t.length)throw new Error("Squeeze requires 1 input.");if("string"===t[0].type)throw new Error("invalid input tensor types.")},o=t=>{if(!t||2!==t.length)throw new Error("Squeeze requires 2 inputs.");if("int32"!==t[1].type)throw new Error("Invalid input type.")}},6558:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.sum=void 0;const r=n(5060),i=n(2039);e.sum=(t,e)=>{s(e);const n={name:"Sum",inputNames:e.map(((t,e)=>`X${e}`)),inputTypes:new Array(e.length).fill(i.TextureType.unpacked)};return[t.run(Object.assign(Object.assign({},n),{get:()=>o(t,e,n)}),e)]};const o=(t,e,n)=>{const o=(0,r.getGlsl)(t.session.backend.glContext.version),s=e[0].dims.slice(),a=`\n      void main() {\n        vec4 result = ${e.map(((t,e)=>`${o.texture2D}(X${e},TexCoords)`)).join(" + ")};\n        ${o.output} = result;\n      }\n    `;return Object.assign(Object.assign({},n),{output:{dims:s,type:e[0].type,textureType:i.TextureType.unpacked},hasMain:!0,shaderSource:a})},s=t=>{if(!t||0===t.length)throw new Error("Sum requires inputs.");const e=t[0].dims.length;for(let n=1;n<t.length;n++){if(e!==t[n].dims.length)throw new Error("Input shapes are mismatched.");for(let r=0;r<e;r++)if(t[0].dims[r]!==t[n].dims[r])throw new Error("Input shapes are not matched.")}if("float32"!==t[0].type&&"float64"!==t[0].type)throw new Error("Invalid input type.");for(let e=1;e<t.length;e++)if(t[0].type!==t[e].type)throw new Error("Input types are not matched.")}},5723:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.tile=void 0;const r=n(782),i=n(2039);e.tile=(t,e)=>{s(e);const n={name:"Tile",inputNames:["A"],inputTypes:[i.TextureType.unpacked]};return[t.run(Object.assign(Object.assign({},n),{get:()=>o(t,e,n)}),e)]};const o=(t,e,n)=>{const r=e[0].dims.slice(),o=new Array(r.length),s=[];for(let t=0;t<r.length;t++)o[t]=r[t]*e[1].numberData[t],s.push(`inputIdx[${t}] = int(mod(float(outputIdx[${t}]), ${r[t]}.));`);const a=o.length,u=`\n      float process(int outputIdx[${a}]) {\n        int inputIdx[${a}];\n        ${s.join("\n")}\n        return _A(inputIdx);\n      }\n    `;return Object.assign(Object.assign({},n),{output:{dims:o,type:e[0].type,textureType:i.TextureType.unpacked},shaderSource:u})},s=t=>{if(!t||2!==t.length)throw new Error("Tile requires 2 input.");if(1!==t[1].dims.length)throw new Error("The second input shape must 1 dimension.");if(t[1].dims[0]!==t[0].dims.length)throw new Error("Invalid input shape.");if(-1===r.NUMBER_TYPES.indexOf(t[0].type))throw new Error("Invalid input type.");if("int32"!==t[1].type&&"int16"!==t[1].type)throw new Error("Invalid repeat type.")}},3738:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.parseTransposeAttributes=e.transpose=void 0;const r=n(246),i=n(2517),o=n(2039),s={name:"Transpose",inputNames:["A"],inputTypes:[o.TextureType.unpacked]};e.transpose=(t,e,n)=>(d(e),[t.run(Object.assign(Object.assign({},s),{cacheHint:n.cacheKey,get:()=>a(t,e[0],n.perm)}),e)]),e.parseTransposeAttributes=t=>(0,r.createAttributeWithCacheKey)({perm:t.attributes.getInts("perm",[])});const a=(t,e,n)=>{const r=e.dims;n=u(r,n);const i=l(r,n),a=r.length,d=`\n      ${c("perm",n,a)}\n      float process(int indices[${a}]) {\n        int a[${a}];\n        perm(a, indices);\n        return _A(a);\n      }`;return Object.assign(Object.assign({},s),{output:{dims:i,type:e.type,textureType:o.TextureType.unpacked},shaderSource:d})},u=(t,e)=>(e&&e.length!==t.length&&(e=[...t.keys()].reverse()),e),l=(t,e)=>(e=u(t,e),i.ShapeUtil.sortBasedOnPerm(t,e)),c=(t,e,n)=>{const r=[];r.push(`void ${t}(out int a[${n}], int src[${n}]) {`);for(let t=0;t<n;++t)r.push(`\ta[${e[t]}]=src[${t}];`);return r.push("\t}"),r.join("\n")},d=t=>{if(!t||1!==t.length)throw new Error("Transpose requires 1 input.");if("float32"!==t[0].type&&"float64"!==t[0].type)throw new Error("input should be float tensor")}},8710:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.encodeAsUint8=void 0;const r=n(5060),i=n(2039);e.encodeAsUint8=(t,e)=>{const n=e.shape,o=(0,r.getGlsl)(t.session.backend.glContext.version),s=`\n    const float FLOAT_MAX = 1.70141184e38;\n    const float FLOAT_MIN = 1.17549435e-38;\n\n    bool isNaN(float val) {\n      return (val < 1.0 || 0.0 < val || val == 0.0) ? false : true;\n    }\n\n    highp vec4 encodeAsUint8(highp float v) {\n      if (isNaN(v)) {\n        return vec4(255, 255, 255, 255);\n      }\n\n      highp float av = abs(v);\n\n      if(av < FLOAT_MIN) {\n        return vec4(0.0, 0.0, 0.0, 0.0);\n      } else if(v > FLOAT_MAX) {\n        return vec4(0.0, 0.0, 128.0, 127.0) / 255.0;\n      } else if(v < -FLOAT_MAX) {\n        return vec4(0.0, 0.0,  128.0, 255.0) / 255.0;\n      }\n\n      highp vec4 c = vec4(0,0,0,0);\n\n      highp float e = floor(log2(av));\n      highp float m = exp2(fract(log2(av))) - 1.0;\n\n      c[2] = floor(128.0 * m);\n      m -= c[2] / 128.0;\n      c[1] = floor(32768.0 * m);\n      m -= c[1] / 32768.0;\n      c[0] = floor(8388608.0 * m);\n\n      highp float ebias = e + 127.0;\n      c[3] = floor(ebias / 2.0);\n      ebias -= c[3] * 2.0;\n      c[2] += floor(ebias) * 128.0;\n\n      c[3] += 128.0 * step(0.0, -v);\n\n      return c / 255.0;\n    }\n\n    void main() {\n      float value = ${o.texture2D}(X,TexCoords).r;\n      ${o.output} = encodeAsUint8(value);\n    }`,a={name:"Uint8Encode",inputTypes:[i.TextureType.unpacked],inputNames:["X"],output:{dims:n,type:e.tensor.type,textureType:i.TextureType.downloadUint8AsFloat},shaderSource:s,hasMain:!0};return t.executeProgram(a,[e.tensor])}},4909:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.tanh=e.tan=e.sqrt=e.sin=e.sigmoid=e.relu=e.not=e.neg=e.log=e.parseLeakyReluAttributes=e.leakyRelu=e.identity=e.floor=e.exp=e.parseEluAttributes=e.elu=e.cos=e.ceil=e.clipV11=e.parseClipAttributes=e.clip=e.atan=e.asin=e.acos=e.abs=e.glslTanh=e.glslTan=e.glslSqrt=e.glslSigmoid=e.glslRelu=e.glslSin=e.glslNot=e.glslNeg=e.glslLog=e.glslLeakyRelu=e.glslIdentity=e.glslClip=e.glslFloor=e.glslExp=e.glslElu=e.glslCos=e.glslCeil=e.glslAtan=e.glslAsin=e.glslAcos=e.glslAbs=void 0;const r=n(246),i=n(2517),o=n(8520),s=n(5060),a=n(2039);function u(){return $("abs")}function l(){return $("acos")}function c(){return $("asin")}function d(){return $("atan")}function p(){return $("ceil")}function h(){return $("cos")}function f(t){const e="elu";return{body:`\n  const float alpha = float(${t});\n\n  float ${e}_(float a) {\n    return a >= 0.0 ? a: (exp(a) - 1.0) * alpha;\n  }\n  vec4 ${e}_(vec4 v) {\n    return vec4(${e}_(v.x), ${e}_(v.y), ${e}_(v.z), ${e}_(v.w));\n  }\n  `,name:e,type:o.FunctionType.ValueBased}}function g(){return $("exp")}function b(){return $("floor")}function m(t,e){const n="clip";return{body:`\n  const float min = float(${t});\n  const float max = float(${e});\n\n  float ${n}_(float a) {\n    return clamp(a, min, max);\n  }\n  vec4 ${n}_(vec4 v) {\n    return clamp(v, min, max);\n  }\n  `,name:n,type:o.FunctionType.ValueBased}}function y(){const t="indentity";return{body:`\n  float ${t}_(float a) {\n    return a;\n  }\n  vec4 ${t}_(vec4 v) {\n    return v;\n  }\n  `,name:t,type:o.FunctionType.ValueBased}}function x(t){const e="leakyRelu";return{body:`\n  const float alpha = float(${t});\n\n  float ${e}_(float a) {\n    return a < 0.0 ? a * alpha : a;\n  }\n  vec4 ${e}_(vec4 v) {\n    return vec4(${e}_(v.x), ${e}_(v.y), ${e}_(v.z), ${e}_(v.w));\n  }\n  `,name:e,type:o.FunctionType.ValueBased}}function _(){return $("log")}function w(){const t="neg";return{body:`\n  float ${t}_(float a) {\n    return -a;\n  }\n  vec4 ${t}_(vec4 v) {\n    return -v;\n  }\n  `,name:t,type:o.FunctionType.ValueBased}}function v(){const t="not";return{body:`\n  float ${t}_(float a) {\n    return float( ! bool(a) );\n  }\n  bool ${t}_(bool a) {\n    return !a;\n  }\n  vec4 ${t}_(vec4 v) {\n    return vec4(!bool(v.x), !bool(v.y), !bool(v.z), !bool(v.w));\n  }\n  bvec4 ${t}_(bvec4 v) {\n    return bvec4(!v.x, !v.y, !v.z, !v.w);\n  }\n  `,name:t,type:o.FunctionType.ValueBased}}function T(){return $("sin")}function S(){const t="relu";return{body:`\n  float ${t}_(float a) {\n    return max( a, 0.0 );\n  }\n  vec4 ${t}_(vec4 v) {\n    return max( v, 0.0 );\n  }\n  `,name:t,type:o.FunctionType.ValueBased}}function I(){const t="sigmoid";return{body:`\n  float ${t}_(float a) {\n    return 1.0 / (1.0 + exp(-a));\n  }\n  vec4 ${t}_(vec4 v) {\n    return 1.0 / (1.0 + exp(-v));\n  }\n  `,name:t,type:o.FunctionType.ValueBased}}function O(){return $("sqrt")}function A(){return $("tan")}function E(){const t="tanh";return{body:`\n  float ${t}_(float a) {\n    a = clamp(a, -10., 10.);\n    a = exp(2.*a);\n    return (a - 1.) / (a + 1.);\n  }\n  vec4 ${t}_(vec4 v) {\n    v = clamp(v, -10., 10.);\n    v = exp(2.*v);\n    return (v - 1.) / (v + 1.);\n  }\n  `,name:t,type:o.FunctionType.ValueBased}}function $(t){return{body:`\n  float ${t}_(float a) {\n    return ${t}(a);\n  }\n  vec4 ${t}_(vec4 v) {\n    return ${t}(v);\n  }\n  `,name:t,type:o.FunctionType.ValueBased}}e.glslAbs=u,e.glslAcos=l,e.glslAsin=c,e.glslAtan=d,e.glslCeil=p,e.glslCos=h,e.glslElu=f,e.glslExp=g,e.glslFloor=b,e.glslClip=m,e.glslIdentity=y,e.glslLeakyRelu=x,e.glslLog=_,e.glslNeg=w,e.glslNot=v,e.glslSin=T,e.glslRelu=S,e.glslSigmoid=I,e.glslSqrt=O,e.glslTan=A,e.glslTanh=E;const P=(t,e,n,r)=>{const i=t.session.pack?a.TextureType.packed:a.TextureType.unpacked,o={name:n.name,inputTypes:[i],inputNames:["A"],cacheHint:r};return Object.assign(Object.assign({},o),{get:()=>((t,e,n,r)=>{const i=t.session.pack?a.TextureType.packed:a.TextureType.unpacked,o=(0,s.getGlsl)(t.session.backend.glContext.version);return Object.assign(Object.assign({},e),{output:{dims:n.dims,type:n.type,textureType:i},shaderSource:`\n     ${r.body}\n     void main() {\n       vec4 v = ${o.texture2D}(A, TexCoords);\n       v = ${r.name}_(v);\n       ${o.output} = v;\n     }\n     `,hasMain:!0})})(t,o,e,n)})};e.abs=(t,e)=>[t.run(P(t,e[0],u()),e)],e.acos=(t,e)=>[t.run(P(t,e[0],l()),e)],e.asin=(t,e)=>[t.run(P(t,e[0],c()),e)],e.atan=(t,e)=>[t.run(P(t,e[0],d()),e)],e.clip=(t,e,n)=>[t.run(P(t,e[0],m(n.min,n.max),n.cacheKey),e)],e.parseClipAttributes=t=>(0,r.createAttributeWithCacheKey)({min:t.attributes.getFloat("min",i.MIN_CLIP),max:t.attributes.getFloat("max",i.MAX_CLIP)}),e.clipV11=(t,n)=>{const r=D(t,n);return(0,e.clip)(t,[n[0]],r)};const D=(t,e)=>{if(e.length>=3&&(!t.session.isInitializer(e[1].dataId)||!t.session.isInitializer(e[2].dataId)))throw new Error("dynamic clip attributes are not allowed");const n=e.length>=3?e[1].numberData[0]:i.MIN_CLIP,o=e.length>=3?e[2].numberData[0]:i.MAX_CLIP;return(0,r.createAttributeWithCacheKey)({min:n,max:o})};e.ceil=(t,e)=>[t.run(P(t,e[0],p()),e)],e.cos=(t,e)=>[t.run(P(t,e[0],h()),e)],e.elu=(t,e,n)=>[t.run(P(t,e[0],f(n.alpha),n.cacheKey),e)],e.parseEluAttributes=t=>(0,r.createAttributeWithCacheKey)({alpha:t.attributes.getFloat("alpha",1)}),e.exp=(t,e)=>[t.run(P(t,e[0],g()),e)],e.floor=(t,e)=>[t.run(P(t,e[0],b()),e)],e.identity=(t,e)=>[t.run(P(t,e[0],y()),e)],e.leakyRelu=(t,e,n)=>[t.run(P(t,e[0],x(n.alpha),n.cacheKey),e)],e.parseLeakyReluAttributes=t=>(0,r.createAttributeWithCacheKey)({alpha:t.attributes.getFloat("alpha",.01)}),e.log=(t,e)=>[t.run(P(t,e[0],_()),e)],e.neg=(t,e)=>[t.run(P(t,e[0],w()),e)],e.not=(t,e)=>[t.run(P(t,e[0],v()),e)],e.relu=(t,e)=>[t.run(P(t,e[0],S()),e)],e.sigmoid=(t,e)=>[t.run(P(t,e[0],I()),e)],e.sin=(t,e)=>[t.run(P(t,e[0],T()),e)],e.sqrt=(t,e)=>[t.run(P(t,e[0],O()),e)],e.tan=(t,e)=>[t.run(P(t,e[0],A()),e)],e.tanh=(t,e)=>[t.run(P(t,e[0],E()),e)]},5611:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.createUnpackProgramInfoLoader=e.createUnpackProgramInfo=void 0;const r=n(5060),i=n(2039),o=n(9390),s=n(2827),a={name:"unpack",inputNames:["A"],inputTypes:[i.TextureType.packed]};e.createUnpackProgramInfo=(t,e)=>{const n=e.dims.length,u=(0,s.getChannels)("rc",n),l=u.slice(-2),c=(0,o.getCoordsDataType)(n),d=(0,s.unpackFromChannel)(),p=0===e.dims.length?"":function(t,e){if(1===t)return"rc";let n="";for(let r=0;r<t;r++)n+=e[r],r<t-1&&(n+=",");return n}(n,u),h=n<=1?"rc":`vec2(${l.join(",")})`,f=`\n    ${d}\n    void main() {\n      ${c} rc = getOutputCoords();\n\n       // Sample the texture with the coords to get the rgba channel value.\n       vec4 packedInput = getA(${p});\n\n       ${(0,r.getGlsl)(t.session.backend.glContext.version).output} = vec4(getChannel(packedInput, ${h}), 0, 0, 0);\n     }\n   `;return Object.assign(Object.assign({},a),{hasMain:!0,output:{dims:e.dims,type:e.type,textureType:i.TextureType.unpacked},shaderSource:f})},e.createUnpackProgramInfoLoader=(t,n)=>Object.assign(Object.assign({},a),{get:()=>(0,e.createUnpackProgramInfo)(t,n)})},8428:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.parseUnsqueezeAttributes=e.unsqueezeV13=e.unsqueeze=void 0;const r=n(2517);e.unsqueeze=(t,e,n)=>{i(e);const o=r.ShapeUtil.unsqueezeShape(e[0].dims,n);return[t.reshapeUnpacked(e[0],o)]},e.unsqueezeV13=(t,n)=>(o(n),(0,e.unsqueeze)(t,[n[0]],Array.from(n[1].integerData))),e.parseUnsqueezeAttributes=t=>t.attributes.getInts("axes");const i=t=>{if(!t||1!==t.length)throw new Error("Unsqueeze requires 1 input.");if("string"===t[0].type)throw new Error("invalid input tensor types.")},o=t=>{if(!t||2!==t.length)throw new Error("Unsqueeze requires 2 inputs.");if("int32"!==t[1].type)throw new Error("Invalid input type.")}},9793:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.scalesValidation=e.validateInputs=e.parseUpsampleAttributes=e.parseUpsampleAttributesV9=e.parseUpsampleAttributesV7=e.upsample=void 0;const r=n(246),i=n(5060),o=n(2039),s={name:"Upsample",inputNames:["X"],inputTypes:[o.TextureType.unpacked]};e.upsample=(t,n,r)=>((0,e.validateInputs)(n,r),[t.run(Object.assign(Object.assign({},s),{cacheHint:r.cacheKey,get:()=>a(t,n,r)}),n)]),e.parseUpsampleAttributesV7=t=>(0,e.parseUpsampleAttributes)(t,7),e.parseUpsampleAttributesV9=t=>(0,e.parseUpsampleAttributes)(t,9),e.parseUpsampleAttributes=(t,n)=>{const i=n>=10,o=t.attributes.getString("mode","nearest");if("nearest"!==o&&"linear"!==o&&(n<11||"cubic"!==o))throw new Error(`unrecognized mode: ${o}`);let s=[];n<9&&(s=t.attributes.getFloats("scales"),(0,e.scalesValidation)(s,o,i));const a=t.attributes.getFloat("extrapolation_value",0),u=n>10?t.attributes.getString("coordinate_transformation_mode","half_pixel"):"asymmetric";if(-1===["asymmetric","pytorch_half_pixel","tf_half_pixel_for_nn","align_corners","tf_crop_and_resize","half_pixel"].indexOf(u))throw new Error(`coordinate_transform_mode '${u}' is not supported`);const l="tf_crop_and_resize"===u,c=l,d="nearest"===o&&n>=11?t.attributes.getString("nearest_mode","round_prefer_floor"):"";if(-1===["round_prefer_floor","round_prefer_ceil","floor","ceil",""].indexOf(d))throw new Error(`nearest_mode '${d}' is not supported`);const p=t.attributes.getFloat("cubic_coeff_a",-.75),h=0!==t.attributes.getInt("exclude_outside",0);if(h&&"cubic"!==o)throw new Error("exclude_outside can be set to 1 only when mode is CUBIC.");const f=n<11||"nearest"===o&&"asymmetric"===u&&"floor"===d;let g=0,b=0,m=0;return n>10?t.inputs.length>2?(g=1,b=2,m=3):(b=1,m=2):9===n&&(b=1),(0,r.createAttributeWithCacheKey)({opset:n,isResize:i,mode:o,scales:s,extrapolationValue:a,coordinateTransformMode:u,useExtrapolation:c,needRoiInput:l,nearestMode:d,cubicCoefficientA:p,excludeOutside:h,useNearest2xOptimization:f,roiInputIdx:g,scalesInputIdx:b,sizesInputIdx:m})};const a=(t,e,n)=>{const r=(0,i.getGlsl)(t.session.backend.glContext.version),[a,u]=t.calculateTextureWidthAndHeight(e[0].dims,o.TextureType.unpacked),l=e[0].dims.map(((t,e)=>Math.floor(t*n.scales[e]))),[c,d]=t.calculateTextureWidthAndHeight(l,o.TextureType.unpacked),p=l.length,h=new Array(p),f=new Array(p);let g=`\n      int output_pitches[${p}];\n      int input_pitches[${p}];\n      `;for(let t=p-1;t>=0;t--)h[t]=t===p-1?1:h[t+1]*l[t+1],f[t]=t===p-1?1:f[t+1]*e[0].dims[t+1],g+=`\n        output_pitches[${t}] = ${h[t]};\n        input_pitches[${t}] = ${f[t]};\n        `;const b=`\n      float getInputFloat(int index) {\n        vec2 coords = offsetToCoords(index, ${a}, ${u});\n        float value = getColorAsFloat(${r.texture2D}(X, coords));\n        return value;\n      }\n      `,m="nearest"===n.mode?`\n    ${b}\n    float process(int indices[${p}]) {\n      int input_index = 0;\n      int output_index = coordsToOffset(TexCoords, ${c}, ${d});\n\n      ${g}\n\n      int d, m;\n      for (int dim = 0; dim < ${p}; ++dim) {\n        d = output_index / output_pitches[dim];\n        m = output_index - d * output_pitches[dim];\n        output_index = m;\n\n        if (scales[dim] != 1 && d > 0) {\n          int d2 = d / scales[dim];\n          m = d - d2 * scales[dim];\n          d = d2;\n        }\n        input_index += input_pitches[dim] * d;\n      }\n\n      return getInputFloat(input_index);\n    }`:4===p?`\n    ${b}\n    float process(int indices[4]) {\n      int input_index = 0;\n      int output_index = coordsToOffset(TexCoords, ${c}, ${d});\n\n      ${g}\n\n      int m;\n      int index_of_dim0, index_of_dim1, index_of_dim2, index_of_dim3;\n      index_of_dim0 = output_index / output_pitches[0];\n      m = output_index - index_of_dim0 * output_pitches[0];\n      index_of_dim1 = m / output_pitches[1];\n      m = m - index_of_dim1 * output_pitches[1];\n      index_of_dim2 = m / output_pitches[2];\n      m = m - index_of_dim2 * output_pitches[2];\n      index_of_dim3 = m;\n\n      int index_of_input_dim2, index_of_input_dim3, x_offset, y_offset;\n      index_of_input_dim2 = index_of_dim2 / scales[2];\n      y_offset = index_of_dim2 - index_of_input_dim2 * scales[2];\n      index_of_input_dim3 = index_of_dim3 / scales[3];\n      x_offset = index_of_dim3 - index_of_input_dim3 * scales[3];\n\n      input_index = index_of_dim0 * input_pitches[0] +\n            index_of_dim1 * input_pitches[1] +\n            index_of_input_dim2 * input_pitches[2] +\n            index_of_input_dim3;\n\n      float x00 = getInputFloat(input_index);\n      float x10, x01, x11;\n\n      bool end_of_dim2 = false;\n      if (index_of_input_dim2 == (${e[0].dims[2]} - 1)) {\n        // It's the end in dimension 2\n        x01 = x00;\n        end_of_dim2 = true;\n      } else {\n        x01 = getInputFloat(input_index + input_pitches[2]);\n      }\n\n      if (index_of_input_dim3 == (input_pitches[2] - 1)) {\n        // It's the end in dimension 3\n        x10 = x00;\n        x11 = x01;\n      }\n      else {\n        x10 = getInputFloat(input_index + 1);\n        x11 = end_of_dim2 ? x10 : getInputFloat(input_index + input_pitches[2] + 1);\n      }\n\n      float y0 = x00 + float(y_offset) * (x01 - x00) / float(scales[2]);\n      float y1 = x10 + float(y_offset) * (x11 - x10) / float(scales[2]);\n      return y0 + float(x_offset) * (y1 - y0) / float(scales[3]);\n    }`:`\n    ${b}\n    float process(int indices[2]) {\n      int input_index = 0;\n      int output_index = coordsToOffset(TexCoords, ${c}, ${d});\n\n      ${g}\n\n      int m;\n      int index_of_dim0, index_of_dim1;\n      index_of_dim0 = output_index / output_pitches[0];\n      m = output_index - index_of_dim0 * output_pitches[0];\n      index_of_dim1 = m;\n\n      int index_of_input_dim0, index_of_input_dim1, x_offset, y_offset;\n      index_of_input_dim0 = index_of_dim0 / scales[0];\n      y_offset = index_of_dim0 - index_of_input_dim0 * scales[0];\n      index_of_input_dim1 = index_of_dim1 / scales[1];\n      x_offset = index_of_dim1 - index_of_input_dim1 * scales[1];\n\n      input_index = index_of_input_dim0 * input_pitches[0] + index_of_input_dim1;\n\n      float x00 = getInputFloat(input_index);\n      float x10, x01, x11;\n\n      bool end_of_dim0 = false;\n      if (index_of_input_dim0 == (${e[0].dims[0]} - 1)) {\n        // It's the end in dimension 0\n        x01 = x00;\n        end_of_dim0 = true;\n      } else {\n        x01 = getInputFloat(input_index + input_pitches[0]);\n      }\n\n      if (index_of_input_dim1 == (input_pitches[0] - 1)) {\n        // It's the end in dimension 1\n        x10 = x00;\n        x11 = x01;\n      }\n      else {\n        x10 = getInputFloat(input_index + 1);\n        x11 = end_of_dim0 ? x10 : getInputFloat(input_index + input_pitches[0] + 1);\n      }\n\n      float y0 = x00 + float(y_offset) * (x01 - x00) / float(scales[0]);\n      float y1 = x10 + float(y_offset) * (x11 - x10) / float(scales[0]);\n      return y0 + float(x_offset) * (y1 - y0) / float(scales[1]);\n    }`;return Object.assign(Object.assign({},s),{output:{dims:l,type:e[0].type,textureType:o.TextureType.unpacked},shaderSource:m,variables:[{name:"scales",type:"int",arrayLength:n.scales.length,data:n.scales.map((t=>Math.ceil(t)))}]})};e.validateInputs=(t,e)=>{if(!t||e.opset<9&&1!==t.length||e.opset>=9&&e.opset<11&&2!==t.length||e.opset>=11&&t.length<2)throw new Error("invalid inputs.");if(e.scales.length>0&&t[0].dims.length!==e.scales.length)throw new Error("Invalid input shape.");if("string"===t[0].type)throw new Error("Invalid input tensor types.")},e.scalesValidation=(t,e,n)=>{if(n){for(const e of t)if(e<=0)throw new Error("Scale value should be greater than 0.")}else for(const e of t)if(e<1)throw new Error("Scale value should be greater than or equal to 1.");if(!("linear"!==e&&"cubic"!==e||2===t.length||4===t.length&&1===t[0]&&1===t[1]))throw new Error(`'Linear' mode and 'Cubic' mode only support 2-D inputs ('Bilinear', 'Bicubic')         or 4-D inputs with the corresponding outermost 2 scale values being 1         in the ${n?"Resize":"Upsample"} opeartor.`)}},1958:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ProgramManager=void 0;const r=n(8453),i=n(6231),o=n(8879),s=n(5060);e.ProgramManager=class{constructor(t,e,n){this.profiler=t,this.glContext=e,this.textureLayoutStrategy=n,this.repo=new Map,this.attributesBound=!1}getArtifact(t){return this.repo.get(t)}setArtifact(t,e){this.repo.set(t,e)}run(t,e,n){var r;this.profiler.event("op",`ProgramManager.run ${null!==(r=t.programInfo.name)&&void 0!==r?r:"unknown kernel"}`,(()=>{var r;const o=this.glContext.gl,s=t.program;o.useProgram(s);try{this.bindOutput(n),this.attributesBound||this.bindAttributes(t.attribLocations),this.bindUniforms(t.uniformLocations,null!==(r=t.programInfo.variables)&&void 0!==r?r:[],e)}catch(e){throw i.Logger.error("ProgramManager",t.programInfo.shaderSource),e}this.profiler.event("backend","GlContext.draw()",(()=>{this.glContext.draw()}))}),this.glContext)}dispose(){this.vertexShader&&this.glContext.deleteShader(this.vertexShader),this.repo.forEach((t=>this.glContext.deleteProgram(t.program)))}build(t,e,n){return this.profiler.event("backend","ProgramManager.build",(()=>{const r=new o.GlslPreprocessor(this.glContext,t,e,n),i=r.preprocess(),s=this.compile(i);return{programInfo:t,program:s,uniformLocations:this.getUniformLocations(s,r.context.programInfo.inputNames,r.context.programInfo.variables),attribLocations:this.getAttribLocations(s)}}))}compile(t){if(!this.vertexShader){i.Logger.verbose("ProrgramManager","Compiling and caching Vertex shader for the first time");const t=(0,s.getVertexShaderSource)(this.glContext.version);this.vertexShader=this.glContext.compileShader(t,this.glContext.gl.VERTEX_SHADER)}r.env.debug&&i.Logger.verbose("ProrgramManager",`FragShader:\n${t}\n`);const e=this.glContext.compileShader(t,this.glContext.gl.FRAGMENT_SHADER),n=this.glContext.createProgram(this.vertexShader,e);return this.glContext.deleteShader(e),n}bindOutput(t){const e=t.width,n=t.height;i.Logger.verbose("ProrgramManager",`Binding output texture to Framebuffer: w/h=${e}/${n}, shape=${t.shape}, type=${t.tensor.type}`),this.glContext.attachFramebuffer(t.texture,e,n)}bindAttributes(t){const e=t.position,n=t.textureCoord;this.glContext.setVertexAttributes(e,n),this.attributesBound=!0}bindUniforms(t,e,n){var r;const i=this.glContext.gl;let o=0;for(const{name:s,type:a,location:u,arrayLength:l}of t){const t=null===(r=e.find((t=>t.name===s)))||void 0===r?void 0:r.data;if("sampler2D"!==a&&!t)throw new Error(`variable '${s}' does not have data defined in program info`);switch(a){case"sampler2D":this.bindTexture(n[o],u,o),o++;break;case"float":l?i.uniform1fv(u,t):i.uniform1f(u,t);break;case"int":l?i.uniform1iv(u,t):i.uniform1i(u,t);break;default:throw new Error(`Uniform not implemented: ${a}`)}}}bindTexture(t,e,n){this.glContext.bindTextureToUniform(t.texture,n,e)}getAttribLocations(t){return{position:this.getAttribLocation(t,"position"),textureCoord:this.getAttribLocation(t,"textureCoord")}}getUniformLocations(t,e,n){const r=[];if(e)for(const n of e)r.push({name:n,type:"sampler2D",location:this.getUniformLocation(t,n)});if(n)for(const e of n)r.push(Object.assign(Object.assign({},e),{location:this.getUniformLocation(t,e.name)}));return r}getUniformLocation(t,e){const n=this.glContext.gl.getUniformLocation(t,e);if(null===n)throw new Error(`Uniform ${e} not found.`);return n}getAttribLocation(t,e){return this.glContext.gl.getAttribLocation(t,e)}}},6416:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.WebGLSessionHandler=void 0;const r=n(6231),i=n(1047),o=n(8316),s=n(1640),a=n(1958),u=n(7859),l=n(5702);e.WebGLSessionHandler=class{constructor(t,e){this.backend=t,this.context=e,this.layoutStrategy=new u.PreferLogicalStrategy(t.glContext.maxTextureSize),this.programManager=new a.ProgramManager(this.context.profiler,t.glContext,this.layoutStrategy),this.textureManager=new l.TextureManager(t.glContext,this.layoutStrategy,this.context.profiler,{reuseTextures:"full"===t.textureCacheMode}),this.packedTextureDataCache=new Map,this.unpackedTextureDataCache=new Map,this.pack=t.pack,this.pack2unpackMap=new Map,this.unpack2packMap=new Map}createInferenceHandler(){return new o.WebGLInferenceHandler(this)}onGraphInitialized(t){const e=t.getValues().filter((t=>-1===t.from&&t.tensor)).map((t=>t.tensor.dataId));this.initializers=new Set(e)}isInitializer(t){return!!this.initializers&&this.initializers.has(t)}addInitializer(t){this.initializers.add(t)}getTextureData(t,e){return e?this.packedTextureDataCache.get(t):this.unpackedTextureDataCache.get(t)}setTextureData(t,e,n=!1){r.Logger.verbose("WebGLSessionHandler","Storing Texture data in cache"),n?this.packedTextureDataCache.set(t,e):this.unpackedTextureDataCache.set(t,e)}dispose(){this.programManager.dispose(),this.textureManager.clearActiveTextures(),this.packedTextureDataCache.forEach((t=>this.textureManager.releaseTexture(t,!0))),this.packedTextureDataCache=new Map,this.unpackedTextureDataCache.forEach((t=>this.textureManager.releaseTexture(t,!0))),this.unpackedTextureDataCache=new Map}resolve(t,e,n){const r=(0,i.resolveOperator)(t,e,s.WEBGL_OP_RESOLVE_RULES);return{impl:r.opImpl,context:r.opInit?r.opInit(t,n):t}}}},7769:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Uint8DataEncoder=e.RGBAFloatDataEncoder=e.RedFloat32DataEncoder=void 0;const r=n(6231);e.RedFloat32DataEncoder=class{constructor(t,e=1){if(1===e)this.internalFormat=t.R32F,this.format=t.RED,this.textureType=t.FLOAT,this.channelSize=e;else{if(4!==e)throw new Error(`Invalid number of channels: ${e}`);this.internalFormat=t.RGBA32F,this.format=t.RGBA,this.textureType=t.FLOAT,this.channelSize=e}}encode(t,e){let n,i;return t.constructor!==Float32Array&&(r.Logger.warning("Encoder","data was not of type Float32; creating new Float32Array"),i=new Float32Array(t)),e*this.channelSize>t.length?(r.Logger.warning("Encoder","Source data too small. Allocating larger array"),i=t,n=this.allocate(e*this.channelSize),i.forEach(((t,e)=>n[e]=t))):(i=t,n=i),n}allocate(t){return new Float32Array(4*t)}decode(t,e){return 1===this.channelSize?t.filter(((t,e)=>e%4==0)).subarray(0,e):t.subarray(0,e)}},e.RGBAFloatDataEncoder=class{constructor(t,e=1,n){if(1!==e&&4!==e)throw new Error(`Invalid number of channels: ${e}`);this.internalFormat=t.RGBA,this.format=t.RGBA,this.channelSize=e,this.textureType=n||t.FLOAT}encode(t,e){let n=t;return 1===this.channelSize&&(r.Logger.verbose("Encoder","Exploding into a larger array"),n=this.allocate(e),t.forEach(((t,e)=>n[4*e]=t))),n}allocate(t){return new Float32Array(4*t)}decode(t,e){return 1===this.channelSize?t.filter(((t,e)=>e%4==0)).subarray(0,e):t.subarray(0,e)}},e.Uint8DataEncoder=class{constructor(t,e=1){if(this.channelSize=4,1===e)this.internalFormat=t.ALPHA,this.format=t.ALPHA,this.textureType=t.UNSIGNED_BYTE,this.channelSize=e;else{if(4!==e)throw new Error(`Invalid number of channels: ${e}`);this.internalFormat=t.RGBA,this.format=t.RGBA,this.textureType=t.UNSIGNED_BYTE,this.channelSize=e}}encode(t,e){return new Uint8Array(t.buffer,t.byteOffset,t.byteLength)}allocate(t){return new Uint8Array(t*this.channelSize)}decode(t,e){if(t instanceof Uint8Array)return t.subarray(0,e);throw new Error(`Invalid array type: ${t.constructor}`)}}},7859:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getBatchDim=e.sizeToSquarishShape=e.getRowsCols=e.sizeFromShape=e.isInt=e.parseAxisParam=e.squeezeShape=e.PreferLogicalStrategy=e.AlwaysKeepOriginalSizeStrategy=void 0;const r=n(6231),i=n(2517);function o(t,e){const n=[],r=[],i=null!=e&&Array.isArray(e)&&0===e.length,o=null==e||i?null:s(e,t).sort();let a=0;for(let e=0;e<t.length;++e){if(null!=o){if(o[a]===e&&1!==t[e])throw new Error(`Can't squeeze axis ${e} since its dim '${t[e]}' is not 1`);(null==o[a]||o[a]>e)&&1===t[e]&&(n.push(t[e]),r.push(e)),o[a]<=e&&a++}1!==t[e]&&(n.push(t[e]),r.push(e))}return{newShape:n,keptDims:r}}function s(t,e){const n=e.length;return t=null==t?e.map(((t,e)=>e)):[].concat(t),(0,i.assert)(t.every((t=>t>=-n&&t<n)),(()=>`All values in axis param must be in range [-${n}, ${n}) but got axis ${t}`)),(0,i.assert)(t.every(a),(()=>`All values in axis param must be integers but got axis ${t}`)),t.map((t=>t<0?n+t:t))}function a(t){return t%1==0}function u(t){if(0===t.length)return 1;let e=t[0];for(let n=1;n<t.length;n++)e*=t[n];return e}function l(t){const e=Math.ceil(Math.sqrt(t));return[e,Math.ceil(t/e)]}e.AlwaysKeepOriginalSizeStrategy=class{constructor(t){this.maxTextureSize=t}computeTextureWH(t,e){if(0===t.length)return[1,1];const n=this.maxTextureSize;if(e&&void 0!==e.breakAxis){const i=e.breakAxis>=t.length?1:t.slice(e.breakAxis).reduce(((t,e)=>t*e)),o=e.breakAxis<=0?1:t.slice(0,e.breakAxis).reduce(((t,e)=>t*e));if(!(i>n||o>n))return[i,o];r.Logger.verbose("TextureLayout",`Given width/height preferences were unattainable: shape:${t}, breakAxis:${e.breakAxis}`)}const i=t.reduce(((t,e)=>t*e));let o=Math.floor(Math.sqrt(i));for(;o<n&&o<i&&i%o!=0;o++);if(o>=n||i%o!=0)throw new Error(`The given dimensions are outside this GPU's boundaries: ${t}`);return[o,i/o]}},e.PreferLogicalStrategy=class{constructor(t){this.maxTextureSize=t}computeTextureWH(t,e){const n=this.computeTexture(t,e);return e&&e.isPacked&&(n[0]/=2,n[1]/=2),e&&e.reverseWH?[n[1],n[0]]:n}computeTexture(t,e){const n=e&&e.isPacked;if(0===t.length)return n?[2,2]:[1,1];let i=this.maxTextureSize;if(e&&void 0!==e.breakAxis){const n=e.breakAxis>=t.length?1:t.slice(e.breakAxis).reduce(((t,e)=>t*e)),o=e.breakAxis<=0?1:t.slice(0,e.breakAxis).reduce(((t,e)=>t*e));if(!(n>i||o>i))return[n,o];r.Logger.verbose("TextureLayout",`Given width/height preferences were unattainable: shape:${t}, breakAxis:${e.breakAxis}`)}let s=t.slice(0);if(n&&(i*=2,s=s.map(((t,e)=>e>=s.length-2?s[e]%2==0?s[e]:s[e]+1:s[e])),1===s.length&&(s=[2,s[0]])),2!==s.length){const t=o(s);s=t.newShape}const a=u(s);return s.length<=1&&a<=i?[1,a]:2===s.length&&s[0]<=i&&s[1]<=i?s:3===s.length&&s[0]*s[1]<=i&&s[2]<=i?[s[0]*s[1],s[2]]:3===s.length&&s[0]<=i&&s[1]*s[2]<=i?[s[0],s[1]*s[2]]:4===s.length&&s[0]*s[1]*s[2]<=i&&s[3]<=i?[s[0]*s[1]*s[2],s[3]]:4===s.length&&s[0]<=i&&s[1]*s[2]*s[3]<=i?[s[0],s[1]*s[2]*s[3]]:n?l(a/4).map((t=>2*t)):l(a)}},e.squeezeShape=o,e.parseAxisParam=s,e.isInt=a,e.sizeFromShape=u,e.getRowsCols=function(t){if(0===t.length)throw Error("Cannot get rows and columns of an empty shape array.");return[t.length>1?t[t.length-2]:1,t[t.length-1]]},e.sizeToSquarishShape=l,e.getBatchDim=function(t,e=2){return u(t.slice(0,t.length-e))}},4057:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.createTextureLayoutFromShape=e.calculateTextureWidthAndHeight=e.createTextureLayoutFromTextureType=void 0;const r=n(2517),i=n(2039);e.createTextureLayoutFromTextureType=(t,n,r)=>{const o=r===i.TextureType.unpacked||r===i.TextureType.unpackedReversed?1:4,s=r===i.TextureType.packed,a=r===i.TextureType.unpackedReversed||r===i.TextureType.packed,u=r===i.TextureType.packedLastDimension?n.length-1:void 0,l=r===i.TextureType.packedLastDimension?n.map(((t,e)=>e===n.length-1?4*t:t)):void 0;return(0,e.createTextureLayoutFromShape)(t,n,o,l,{isPacked:s,reverseWH:a,breakAxis:u})},e.calculateTextureWidthAndHeight=(t,n,r)=>{const i=(0,e.createTextureLayoutFromTextureType)(t,n,r);return[i.width,i.height]},e.createTextureLayoutFromShape=(t,e,n=1,i,o)=>{const s=!(!o||!o.isPacked),[a,u]=t.computeTextureWH(s&&i||e,o),l=e.length;let c=e.slice(0);if(0===l&&(c=[1]),1===n)i=e;else if(s){if(4!==n)throw new Error("a packed texture must be 4-channel");i=e,l>0&&(c[l-1]=Math.ceil(c[l-1]/2)),l>1&&(c[l-2]=Math.ceil(c[l-2]/2))}else if(!i)throw new Error("Unpacked shape is needed when using channels > 1");return{width:a,height:u,channels:n,isPacked:s,shape:c,strides:r.ShapeUtil.computeStrides(c),unpackedShape:i,reversedWH:o&&o.reverseWH}}},5702:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TextureManager=void 0;const r=n(6231);e.TextureManager=class{constructor(t,e,n,r){this.glContext=t,this.layoutStrategy=e,this.profiler=n,this.config=r,this.pendingRead=new Map,r.reuseTextures&&(this.inUseTextures=new Map,this.idleTextures=new Map,this.textureLookup=new Map)}createTextureFromLayout(t,e,n,i){const o=this.toEncoderType(t),s=this.glContext.getEncoder(o,e.channels||1,i);if(e.isPacked&&1===i)throw new Error("not implemented");const a=e.width,u=e.height;let l,c;if(this.config.reuseTextures){l=`${a}x${u}_${s.format}_${s.internalFormat}_${s.textureType}`,c=this.inUseTextures.get(l),c||(c=[],this.inUseTextures.set(l,c));const e=this.idleTextures.get(l);if(e&&e.length>0){const r=e.pop();return c.push(r),1===i&&this.glContext.updateTexture(r,a,u,s,this.toTextureData(t,n)),r}}r.Logger.verbose("TextureManager",`Creating new texture of size ${e.width}x${e.height}`);const d=this.glContext.allocateTexture(a,u,s,this.toTextureData(t,n));return this.config.reuseTextures&&(c.push(d),this.textureLookup.set(d,l)),d}readTexture(t,e,n){return n||(n=1),this.profiler.event("backend","TextureManager.readTexture",(()=>{const r=t.shape.reduce(((t,e)=>t*e))*n,i=this.glContext.readTexture(t.texture,t.width,t.height,r,this.toEncoderType(e),n);return this.toTensorData(e,i)}))}async readTextureAsync(t,e,n){const r=t.tensor.dataId;if(n||(n=1),this.pendingRead.has(r)){const t=this.pendingRead.get(r);return new Promise((e=>null==t?void 0:t.push(e)))}return this.profiler.event("backend","TextureManager.readTextureAsync",(async()=>{this.pendingRead.set(r,[]);const i=t.shape.reduce(((t,e)=>t*e))*n;await this.glContext.createAndWaitForFence();const o=this.glContext.readTexture(t.texture,t.width,t.height,i,this.toEncoderType(e),n),s=this.toTensorData(e,o),a=this.pendingRead.get(r);return this.pendingRead.delete(r),null==a||a.forEach((t=>t(s))),s}))}readUint8TextureAsFloat(t){return this.profiler.event("backend","TextureManager.readUint8TextureAsFloat",(()=>{const e=t.shape.reduce(((t,e)=>t*e)),n=this.glContext.readTexture(t.texture,t.width,t.height,4*e,"byte",4);return new Float32Array(n.buffer,n.byteOffset,e)}))}releaseTexture(t,e){let n;if(this.config.reuseTextures&&(n=this.textureLookup.get(t.texture),n)){e&&this.textureLookup.delete(n);const r=this.inUseTextures.get(n);if(r){const e=r.indexOf(t.texture);if(-1!==e){r.splice(e,1);let i=this.idleTextures.get(n);i||(i=[],this.idleTextures.set(n,i)),i.push(t.texture)}}}n&&!e||(r.Logger.verbose("TextureManager",`Deleting texture of size ${t.width}x${t.height}`),this.glContext.deleteTexture(t.texture))}toTensorData(t,e){switch(t){case"int16":return e instanceof Int16Array?e:Int16Array.from(e);case"int32":return e instanceof Int32Array?e:Int32Array.from(e);case"int8":return e instanceof Int8Array?e:Int8Array.from(e);case"uint16":return e instanceof Uint16Array?e:Uint16Array.from(e);case"uint32":return e instanceof Uint32Array?e:Uint32Array.from(e);case"uint8":case"bool":return e instanceof Uint8Array?e:Uint8Array.from(e);case"float32":return e instanceof Float32Array?e:Float32Array.from(e);case"float64":return e instanceof Float64Array?e:Float64Array.from(e);default:throw new Error(`TensorData type ${t} is not supported`)}}toTextureData(t,e){if(e)return e instanceof Float32Array?e:new Float32Array(e)}toEncoderType(t){return"float"}clearActiveTextures(){this.glContext.clearActiveTextures()}}},2039:(t,e)=>{"use strict";var n;Object.defineProperty(e,"__esModule",{value:!0}),e.TextureType=void 0,(n=e.TextureType||(e.TextureType={}))[n.unpacked=0]="unpacked",n[n.unpackedReversed=1]="unpackedReversed",n[n.packed=2]="packed",n[n.downloadUint8AsFloat=3]="downloadUint8AsFloat",n[n.packedLastDimension=4]="packedLastDimension"},9390:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getGlChannels=e.getCoordsDataType=e.getSqueezedParams=e.squeezeInputShape=e.generateShaderFuncNameFromInputSamplerNameAtOutCoords=e.generateShaderFuncNameFromInputSamplerName=e.repeatedTry=e.getPackedShape=void 0;const r=n(2517);e.getPackedShape=function(t){const e=t.length;return t.slice(0,e-1).concat(t[e-1]/4)},e.repeatedTry=async function(t,e=(t=>0),n){return new Promise(((r,i)=>{let o=0;const s=()=>{if(t())return void r();o++;const a=e(o);null!=n&&o>=n?i():setTimeout(s,a)};s()}))},e.generateShaderFuncNameFromInputSamplerName=function(t){return(0,r.assert)(void 0!==t&&0!==t.length,(()=>"empty string found for sampler name")),"get"+t.charAt(0).toUpperCase()+t.slice(1)},e.generateShaderFuncNameFromInputSamplerNameAtOutCoords=function(t){return(0,r.assert)(void 0!==t&&0!==t.length,(()=>"empty string found for sampler name")),"get"+t.charAt(0).toUpperCase()+t.slice(1)+"AtOutCoords"},e.squeezeInputShape=function(t,e){let n=JSON.parse(JSON.stringify(t));return n=e,n},e.getSqueezedParams=function(t,e){return e.map((e=>t[e])).join(", ")},e.getCoordsDataType=function(t){if(t<=1)return"int";if(2===t)return"ivec2";if(3===t)return"ivec3";if(4===t)return"ivec4";if(5===t)return"ivec5";if(6===t)return"ivec6";throw Error(`GPU for rank ${t} is not yet supported`)},e.getGlChannels=function(t=6){return["x","y","z","w","u","v"].slice(0,t)}},7305:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.createNewWebGLContext=e.createWebGLContext=void 0;const r=n(6231),i=n(1713),o={};function s(t){const e=function(){if("undefined"==typeof document){if("undefined"==typeof OffscreenCanvas)throw new TypeError("failed to create canvas: OffscreenCanvas is not supported");return new OffscreenCanvas(1,1)}const t=document.createElement("canvas");return t.width=1,t.height=1,t}();let n;const o={alpha:!1,depth:!1,antialias:!1,stencil:!1,preserveDrawingBuffer:!1,premultipliedAlpha:!1,failIfMajorPerformanceCaveat:!1};if((!t||"webgl2"===t)&&(n=e.getContext("webgl2",o),n))try{return new i.WebGLContext(n,2)}catch(t){r.Logger.warning("GlContextFactory",`failed to create WebGLContext using contextId 'webgl2'. Error: ${t}`)}if((!t||"webgl"===t)&&(n=e.getContext("webgl",o)||e.getContext("experimental-webgl",o),n))try{return new i.WebGLContext(n,1)}catch(t){r.Logger.warning("GlContextFactory",`failed to create WebGLContext using contextId 'webgl' or 'experimental-webgl'. Error: ${t}`)}throw new Error("WebGL is not supported")}e.createWebGLContext=function t(e){let n;e&&"webgl2"!==e||!("webgl2"in o)?e&&"webgl"!==e||!("webgl"in o)||(n=o.webgl):n=o.webgl2,n=n||s(e),e=e||1===n.version?"webgl":"webgl2";const r=n.gl;return o[e]=n,r.isContextLost()?(delete o[e],t(e)):(r.disable(r.DEPTH_TEST),r.disable(r.STENCIL_TEST),r.disable(r.BLEND),r.disable(r.DITHER),r.disable(r.POLYGON_OFFSET_FILL),r.disable(r.SAMPLE_COVERAGE),r.enable(r.SCISSOR_TEST),r.enable(r.CULL_FACE),r.cullFace(r.BACK),n)},e.createNewWebGLContext=s},1713:function(t,e,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(t,e,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(e,n);i&&!("get"in i?!e.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return e[n]}}),Object.defineProperty(t,r,i)}:function(t,e,n,r){void 0===r&&(r=n),t[r]=e[n]}),i=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),o=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var n in t)"default"!==n&&Object.prototype.hasOwnProperty.call(t,n)&&r(e,t,n);return i(e,t),e};Object.defineProperty(e,"__esModule",{value:!0}),e.WebGLContext=e.linearSearchLastTrue=void 0;const s=n(8453),a=o(n(7769)),u=n(9390);function l(t){let e=0;for(;e<t.length&&t[e]();++e);return e-1}e.linearSearchLastTrue=l,e.WebGLContext=class{constructor(t,e){this.frameBufferBound=!1,this.itemsToPoll=[],this.gl=t,this.version=e,this.getExtensions(),this.vertexbuffer=this.createVertexbuffer(),this.framebuffer=this.createFramebuffer(),this.queryVitalParameters()}allocateTexture(t,e,n,r){const i=this.gl,o=i.createTexture();i.bindTexture(i.TEXTURE_2D,o),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_MIN_FILTER,i.NEAREST),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_MAG_FILTER,i.NEAREST),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_WRAP_S,i.CLAMP_TO_EDGE),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_WRAP_T,i.CLAMP_TO_EDGE);const s=r?n.encode(r,t*e):null;return i.texImage2D(i.TEXTURE_2D,0,n.internalFormat,t,e,0,n.format,n.textureType,s),this.checkError(),o}updateTexture(t,e,n,r,i){const o=this.gl;o.bindTexture(o.TEXTURE_2D,t);const s=r.encode(i,e*n);o.texSubImage2D(o.TEXTURE_2D,0,0,0,e,n,r.format,r.textureType,s),this.checkError()}attachFramebuffer(t,e,n){const r=this.gl;r.bindTexture(r.TEXTURE_2D,t),r.bindFramebuffer(r.FRAMEBUFFER,this.framebuffer),r.framebufferTexture2D(r.FRAMEBUFFER,r.COLOR_ATTACHMENT0,r.TEXTURE_2D,t,0),this.checkError(),r.viewport(0,0,e,n),r.scissor(0,0,e,n)}readTexture(t,e,n,r,i,o){const s=this.gl;o||(o=1),this.frameBufferBound||this.attachFramebuffer(t,e,n);const a=this.getEncoder(i,o),u=a.allocate(e*n);return s.bindTexture(s.TEXTURE_2D,t),s.framebufferTexture2D(s.FRAMEBUFFER,s.COLOR_ATTACHMENT0,s.TEXTURE_2D,t,0),s.readPixels(0,0,e,n,s.RGBA,a.textureType,u),this.checkError(),a.decode(u,r)}isFramebufferReady(){return!0}getActiveTexture(){const t=this.gl;return"TEXTURE"+(t.getParameter(this.gl.ACTIVE_TEXTURE)-t.TEXTURE0)}getTextureBinding(){return this.gl.getParameter(this.gl.TEXTURE_BINDING_2D)}getFramebufferBinding(){return this.gl.getParameter(this.gl.FRAMEBUFFER_BINDING)}setVertexAttributes(t,e){const n=this.gl;n.vertexAttribPointer(t,3,n.FLOAT,!1,20,0),n.enableVertexAttribArray(t),-1!==e&&(n.vertexAttribPointer(e,2,n.FLOAT,!1,20,12),n.enableVertexAttribArray(e)),this.checkError()}createProgram(t,e){const n=this.gl,r=n.createProgram();return n.attachShader(r,t),n.attachShader(r,e),n.linkProgram(r),r}compileShader(t,e){const n=this.gl,r=n.createShader(e);if(!r)throw new Error(`createShader() returned null with type ${e}`);if(n.shaderSource(r,t),n.compileShader(r),!1===n.getShaderParameter(r,n.COMPILE_STATUS))throw new Error(`Failed to compile shader: ${n.getShaderInfoLog(r)}\nShader source:\n${t}`);return r}deleteShader(t){this.gl.deleteShader(t)}bindTextureToUniform(t,e,n){const r=this.gl;r.activeTexture(r.TEXTURE0+e),this.checkError(),r.bindTexture(r.TEXTURE_2D,t),this.checkError(),r.uniform1i(n,e),this.checkError()}draw(){this.gl.drawArrays(this.gl.TRIANGLE_STRIP,0,4),this.checkError()}checkError(){if(s.env.debug){const t=this.gl,e=t.getError();let n="";switch(e){case t.NO_ERROR:return;case t.INVALID_ENUM:n="INVALID_ENUM";break;case t.INVALID_VALUE:n="INVALID_VALUE";break;case t.INVALID_OPERATION:n="INVALID_OPERATION";break;case t.INVALID_FRAMEBUFFER_OPERATION:n="INVALID_FRAMEBUFFER_OPERATION";break;case t.OUT_OF_MEMORY:n="OUT_OF_MEMORY";break;case t.CONTEXT_LOST_WEBGL:n="CONTEXT_LOST_WEBGL";break;default:n=`Unknown WebGL Error: ${e.toString(16)}`}throw new Error(n)}}deleteTexture(t){this.gl.deleteTexture(t)}deleteProgram(t){this.gl.deleteProgram(t)}getEncoder(t,e,n=0){if(2===this.version)return new a.RedFloat32DataEncoder(this.gl,e);switch(t){case"float":return 1===n||this.isRenderFloat32Supported?new a.RGBAFloatDataEncoder(this.gl,e):new a.RGBAFloatDataEncoder(this.gl,e,this.textureHalfFloatExtension.HALF_FLOAT_OES);case"int":throw new Error("not implemented");case"byte":return new a.Uint8DataEncoder(this.gl,e);default:throw new Error(`Invalid dataType: ${t}`)}}clearActiveTextures(){const t=this.gl;for(let e=0;e<this.maxTextureImageUnits;++e)t.activeTexture(t.TEXTURE0+e),t.bindTexture(t.TEXTURE_2D,null)}dispose(){if(this.disposed)return;const t=this.gl;t.bindFramebuffer(t.FRAMEBUFFER,null),t.deleteFramebuffer(this.framebuffer),t.bindBuffer(t.ARRAY_BUFFER,null),t.deleteBuffer(this.vertexbuffer),t.bindBuffer(t.ELEMENT_ARRAY_BUFFER,null),t.finish(),this.disposed=!0}createDefaultGeometry(){return new Float32Array([-1,1,0,0,1,-1,-1,0,0,0,1,1,0,1,1,1,-1,0,1,0])}createVertexbuffer(){const t=this.gl,e=t.createBuffer();if(!e)throw new Error("createBuffer() returned null");const n=this.createDefaultGeometry();return t.bindBuffer(t.ARRAY_BUFFER,e),t.bufferData(t.ARRAY_BUFFER,n,t.STATIC_DRAW),this.checkError(),e}createFramebuffer(){const t=this.gl.createFramebuffer();if(!t)throw new Error("createFramebuffer returned null");return t}queryVitalParameters(){const t=this.gl;if(this.isFloatTextureAttachableToFrameBuffer=this.checkFloatTextureAttachableToFrameBuffer(),this.isRenderFloat32Supported=this.checkRenderFloat32(),this.isFloat32DownloadSupported=this.checkFloat32Download(),1===this.version&&!this.textureHalfFloatExtension&&!this.isRenderFloat32Supported)throw new Error("both float32 and float16 TextureType are not supported");this.isBlendSupported=!this.isRenderFloat32Supported||this.checkFloat32Blend(),this.maxTextureSize=t.getParameter(t.MAX_TEXTURE_SIZE),this.maxTextureImageUnits=t.getParameter(t.MAX_TEXTURE_IMAGE_UNITS),this.version}getExtensions(){2===this.version?(this.colorBufferFloatExtension=this.gl.getExtension("EXT_color_buffer_float"),this.disjointTimerQueryWebgl2Extension=this.gl.getExtension("EXT_disjoint_timer_query_webgl2")):(this.textureFloatExtension=this.gl.getExtension("OES_texture_float"),this.textureHalfFloatExtension=this.gl.getExtension("OES_texture_half_float"))}checkFloatTextureAttachableToFrameBuffer(){const t=this.gl,e=t.createTexture();t.bindTexture(t.TEXTURE_2D,e);const n=2===this.version?t.RGBA32F:t.RGBA;t.texImage2D(t.TEXTURE_2D,0,n,1,1,0,t.RGBA,t.FLOAT,null);const r=t.createFramebuffer();t.bindFramebuffer(t.FRAMEBUFFER,r),t.framebufferTexture2D(t.FRAMEBUFFER,t.COLOR_ATTACHMENT0,t.TEXTURE_2D,e,0);const i=t.checkFramebufferStatus(t.FRAMEBUFFER)===t.FRAMEBUFFER_COMPLETE;return t.bindTexture(t.TEXTURE_2D,null),t.bindFramebuffer(t.FRAMEBUFFER,null),t.deleteTexture(e),t.deleteFramebuffer(r),i}checkRenderFloat32(){if(2===this.version){if(!this.colorBufferFloatExtension)return!1}else if(!this.textureFloatExtension)return!1;return this.isFloatTextureAttachableToFrameBuffer}checkFloat32Download(){if(2===this.version){if(!this.colorBufferFloatExtension)return!1}else{if(!this.textureFloatExtension)return!1;if(!this.gl.getExtension("WEBGL_color_buffer_float"))return!1}return this.isFloatTextureAttachableToFrameBuffer}checkFloat32Blend(){const t=this.gl;let e,n,r,i,o;try{e=t.createTexture(),n=t.createFramebuffer(),t.bindTexture(t.TEXTURE_2D,e);const s=2===this.version?t.RGBA32F:t.RGBA;return t.texImage2D(t.TEXTURE_2D,0,s,1,1,0,t.RGBA,t.FLOAT,null),t.bindFramebuffer(t.FRAMEBUFFER,n),t.framebufferTexture2D(t.FRAMEBUFFER,t.COLOR_ATTACHMENT0,t.TEXTURE_2D,e,0),t.enable(t.BLEND),r=t.createShader(t.VERTEX_SHADER),!!r&&(t.shaderSource(r,"void main(){}"),t.compileShader(r),i=t.createShader(t.FRAGMENT_SHADER),!!i&&(t.shaderSource(i,"precision highp float;void main(){gl_FragColor=vec4(0.5);}"),t.compileShader(i),o=t.createProgram(),!!o&&(t.attachShader(o,r),t.attachShader(o,i),t.linkProgram(o),t.useProgram(o),t.drawArrays(t.POINTS,0,1),t.getError()===t.NO_ERROR)))}finally{t.disable(t.BLEND),o&&t.deleteProgram(o),r&&t.deleteShader(r),i&&t.deleteShader(i),n&&(t.bindFramebuffer(t.FRAMEBUFFER,null),t.deleteFramebuffer(n)),e&&(t.bindTexture(t.TEXTURE_2D,null),t.deleteTexture(e))}}beginTimer(){if(2===this.version&&this.disjointTimerQueryWebgl2Extension){const t=this.gl,e=this.disjointTimerQueryWebgl2Extension,n=t.createQuery();return t.beginQuery(e.TIME_ELAPSED_EXT,n),n}throw new Error("WebGL1 profiling currently not supported.")}endTimer(){if(2!==this.version||!this.disjointTimerQueryWebgl2Extension)throw new Error("WebGL1 profiling currently not supported");{const t=this.gl,e=this.disjointTimerQueryWebgl2Extension;t.endQuery(e.TIME_ELAPSED_EXT)}}isTimerResultAvailable(t){let e=!1,n=!1;if(2!==this.version||!this.disjointTimerQueryWebgl2Extension)throw new Error("WebGL1 profiling currently not supported");{const r=this.gl,i=this.disjointTimerQueryWebgl2Extension;e=r.getQueryParameter(t,r.QUERY_RESULT_AVAILABLE),n=r.getParameter(i.GPU_DISJOINT_EXT)}return e&&!n}getTimerResult(t){let e=0;if(2!==this.version)throw new Error("WebGL1 profiling currently not supported");{const n=this.gl;e=n.getQueryParameter(t,n.QUERY_RESULT),n.deleteQuery(t)}return e/1e6}async waitForQueryAndGetTime(t){return await(0,u.repeatedTry)((()=>this.isTimerResultAvailable(t))),this.getTimerResult(t)}async createAndWaitForFence(){const t=this.createFence(this.gl);return this.pollFence(t)}createFence(t){let e;const n=t,r=n.fenceSync(n.SYNC_GPU_COMMANDS_COMPLETE,0);return t.flush(),e=null===r?()=>!0:()=>{const t=n.clientWaitSync(r,0,0);return t===n.ALREADY_SIGNALED||t===n.CONDITION_SATISFIED},{query:r,isFencePassed:e}}async pollFence(t){return new Promise((e=>{this.addItemToPoll((()=>t.isFencePassed()),(()=>e()))}))}pollItems(){const t=l(this.itemsToPoll.map((t=>t.isDoneFn)));for(let e=0;e<=t;++e){const{resolveFn:t}=this.itemsToPoll[e];t()}this.itemsToPoll=this.itemsToPoll.slice(t+1)}async addItemToPoll(t,e){this.itemsToPoll.push({isDoneFn:t,resolveFn:e}),this.itemsToPoll.length>1||await(0,u.repeatedTry)((()=>(this.pollItems(),0===this.itemsToPoll.length)))}}},1036:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ExecutionPlan=void 0;const r=n(6231);class i{constructor(t,e){this.op=t,this.node=e}}e.ExecutionPlan=class{constructor(t,e,n){this.graph=t,this.profiler=n,this.initialize(e)}initialize(t){this.profiler.event("session","ExecutionPlan.initialize",(()=>{const e=this.graph.getNodes();if(e.length!==t.length)throw new Error("The size of nodes and OPs do not match.");this._ops=t.map(((t,n)=>new i(t,e[n]))),this.reset(),this._starter=[],this._ops.forEach(((t,e)=>{let n=!0;for(const e of t.node.inputs)if(!this._values[e]&&-1===this.graph.getInputIndices().indexOf(e)){n=!1;break}n&&this._starter.push(e)}))}))}reset(){this._values=this.graph.getValues().map((t=>t.tensor))}async execute(t,e){return this.profiler.event("session","ExecutionPlan.execute",(async()=>{this.reset();const n=t.createInferenceHandler(),i=this.graph.getInputIndices();if(e.length!==i.length)throw new Error(`number of input tensors don't match the number of inputs to the model: actual: ${e.length} expected: ${i.length}`);e.forEach(((t,e)=>{const n=i[e];this._values[n]=t}));const o=this._starter.slice(0),s=this.graph.getValues(),a=this.graph.getNodes();let u=0;for(;u<o.length;){const t=o[u++],e=this._ops[t],i=e.node.inputs.map((t=>this._values[t]));if(-1!==i.indexOf(void 0))throw new Error(`unresolved input detected: op: ${e.node}`);const l=i;r.Logger.verbose("ExecPlan",`Runing op:${e.node.name} (${l.map(((t,n)=>`'${e.node.inputs[n]}': ${t.type}[${t.dims.join(",")}]`)).join(", ")})`);const c=await this.profiler.event("node",e.node.name,(async()=>e.op.impl(n,l,e.op.context)));if(c.length!==e.node.outputs.length)throw new Error("the size of output does not match model definition.");c.forEach(((t,n)=>{const r=e.node.outputs[n];if(this._values[r])throw new Error(`output [${r}] already has value: op:${e.node.name}`);this._values[r]=t}));const d=new Set;c.forEach(((t,n)=>{const r=e.node.outputs[n];for(const t of s[r].to){const e=a[t];let n=!0;for(const t of e.inputs)if(!this._values[t]){n=!1;break}n&&d.add(t)}})),o.push(...d)}const l=[];for(let t=0;t<this.graph.getOutputIndices().length;t++){const e=this.graph.getOutputIndices()[t],n=this._values[e];if(void 0===n)throw new Error(`required output [${e}] does not have value`);0===e?await n.getData():n.data,l.push(n)}return r.Logger.verbose("ExecPlan","disposing of inferenceHandler"),n.dispose(),l}))}}},7070:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Graph=void 0;const r=n(1446),i=n(7778),o=n(9395),s=n(9162),a=n(2517);var u=o.onnxruntime.experimental.fbs;e.Graph={from:(t,e)=>new d(t,e)};class l{constructor(t){this._from=void 0,this._to=[],this.tensor=void 0,this.type=void 0,t&&(this.type=a.ProtoUtil.tensorValueTypeFromProto(t.type.tensorType))}get from(){return this._from}get to(){return this._to}}class c{constructor(t,e){t instanceof r.onnx.NodeProto?(this.name=t.name,this.opType=t.opType,this.attributes=new i.Attribute(t.attribute)):t instanceof u.Node&&(this.name=null!=e?e:t.name(),this.opType=t.opType(),this.attributes=new i.Attribute(a.ProtoUtil.tensorAttributesFromORTFormat(t))),this.inputs=[],this.outputs=[],this.executeNode=!0}}class d{constructor(t,e){if(!t)throw new TypeError("graph is empty");this.buildGraph(t),this.transformGraph(e),this.checkIsAcyclic()}getInputIndices(){return this._allInputIndices}getInputNames(){return this._allInputNames}getOutputIndices(){return this._allOutputIndices}getOutputNames(){return this._allOutputNames}getValues(){return this._allData}getNodes(){return this._nodes}buildGraph(t){if(t instanceof r.onnx.GraphProto)this.buildGraphFromOnnxFormat(t);else{if(!(t instanceof u.Graph))throw new TypeError("Graph type is not supported.");this.buildGraphFromOrtFormat(t)}}buildGraphFromOnnxFormat(t){const e=new Map;this._allData=[],this._allInputIndices=[],this._allInputNames=[],this._allOutputIndices=[],this._allOutputNames=[],this._nodes=[];const n=new Map;if(!t.input)throw new Error("missing information in graph: input");const r=[];for(const n of t.input){if(e.has(n.name))throw new Error(`duplicated input name: ${n.name}`);const t=this._allData.push(new l(n))-1;e.set(n.name,t),r.push(n.name)}if(!t.initializer)throw new Error("missing information in graph: initializer");for(const n of t.initializer){let t=e.get(n.name);if(void 0===t){const r=new l;r.type={shape:{dims:a.ProtoUtil.tensorDimsFromProto(n.dims)},tensorType:a.ProtoUtil.tensorDataTypeFromProto(n.dataType)},t=this._allData.push(r)-1,e.set(n.name,t)}this._allData[t]._from=-1,this._allData[t].tensor=s.Tensor.fromProto(n)}for(let t=0;t<this._allData.length;t++)this._allData[t].tensor||(this._allInputIndices.push(t),this._allInputNames.push(r[t]));if(!t.output)throw new Error("missing information in graph: output");for(const n of t.output){if(e.has(n.name))throw new Error(`duplicated output name: ${n.name}`);const t=this._allData.push(new l(n))-1;e.set(n.name,t),this._allOutputIndices.push(t),this._allOutputNames.push(n.name)}if(!t.node)throw new Error("missing information in graph: node");for(const e of t.node){if(!e.name)for(let t=0;;t++){const r=`unnamed_${e.opType}_${t}`;if(!n.has(r)){e.name=r;break}}if(n.has(e.name))throw new Error(`duplicated node name: ${e.name}`);const t=this._nodes.push(new c(e))-1;n.set(e.name,t)}for(let n=0;n<this._nodes.length;n++){const r=this._nodes[n],i=t.node[n];if(!i.output)throw new Error(`missing output for node: ${i.name}`);for(const t of i.output){let o=e.get(t);if(void 0===o&&(o=this._allData.push(new l)-1,e.set(t,o)),r.outputs.push(o),void 0!==this._allData[o]._from)throw new Error(`multiple nodes output to one data value: ${o}`);if(this._allData[o]._from=n,"Constant"===i.opType){if(!i.attribute||1!==i.attribute.length||!i.attribute[0].t)throw new Error("missing attributes or missing tensor value in attributes for this Constant operator");if(!i.output||1!==i.output.length)throw new Error("missing output or incorrect number of outputs for this Constant operator");r.outputs.pop(),r.executeNode=!1,this._allData[o]._from=-1,this._allData[o].tensor=s.Tensor.fromProto(i.attribute[0].t)}}}for(let n=0;n<this._nodes.length;n++){const r=this._nodes[n],i=t.node[n];if(!i.input)throw new Error(`missing input for node: ${i.name}`);for(const t of i.input){const o=e.get(t);if(void 0===o){if(""===t&&3===i.input.length&&"Resize"===i.opType)continue;throw new Error(`unrecognized input '${t}' for node: ${i.name}`)}r.inputs.push(o),this._allData[o]._to.push(n)}}return!0}buildGraphFromOrtFormat(t){var e,n,r;const i=new Map;this._allData=[],this._allInputIndices=[],this._allInputNames=[],this._allOutputIndices=[],this._allOutputNames=[],this._nodes=[];const o=new Map,d=[];for(let o=0;o<t.inputsLength();o++){const s=t.inputs(o);if(i.has(s))throw new Error(`duplicated input name: ${s}`);for(let o=0;o<t.nodeArgsLength();o++)if((null===(e=t.nodeArgs(o))||void 0===e?void 0:e.name())===s){const e=new l;if((null===(r=null===(n=t.nodeArgs(o))||void 0===n?void 0:n.type())||void 0===r?void 0:r.valueType())!==u.TypeInfoValue.tensor_type)throw new Error("Unexpected value type for the nodeArg.");const c=t.nodeArgs(o).type().value(new u.TensorTypeAndShape),p=a.ProtoUtil.tensorDataTypeFromProto(c.elemType()),h=c.shape(),f=[];for(let t=0;t<h.dimLength();t++)f.push(a.LongUtil.longToNumber(h.dim(t).value().dimValue()));e.type={shape:{dims:f},tensorType:p};const g=this._allData.push(e)-1;i.set(s,g),d.push(s)}}for(let e=0;e<t.initializersLength();e++){const n=t.initializers(e);let r=i.get(n.name());if(void 0===r){const t=new l,e=a.ProtoUtil.tensorDimsFromORTFormat(n),o=a.ProtoUtil.tensorDataTypeFromProto(n.dataType());t.type={shape:{dims:e},tensorType:o},r=this._allData.push(t)-1,i.set(n.name(),r)}this._allData[r]._from=-1,this._allData[r].tensor=s.Tensor.fromOrtTensor(n)}for(let t=0;t<this._allData.length;t++)this._allData[t].tensor||(this._allInputIndices.push(t),this._allInputNames.push(d[t]));for(let e=0;e<t.outputsLength();e++){const n=t.outputs(e);if(i.has(n))throw new Error(`duplicated output name: ${n}`);const r=this._allData.push(new l)-1;i.set(n,r),this._allOutputIndices.push(r),this._allOutputNames.push(n)}if(!t.nodes)throw new Error("missing information in graph: node");for(let e=0;e<t.nodesLength();e++){const n=t.nodes(e);let r=n.name();if(!r)for(let t=0;r=`unnamed_${n.opType()}_${t}`,o.has(r);t++);if(o.has(r))throw new Error(`duplicated node name: ${r}`);const i=this._nodes.push(new c(n,r))-1;o.set(r,i)}for(let e=0;e<this._nodes.length;e++){const n=this._nodes[e],r=t.nodes(e);if(null==r)throw new Error(`No node exists at index ${e}`);if(0===(null==r?void 0:r.outputsLength()))throw new Error(`missing output for node: ${r.name}`);for(let t=0;t<(null==r?void 0:r.outputsLength());t++){const o=null==r?void 0:r.outputs(t);let a=i.get(o);if(void 0===a&&(a=this._allData.push(new l)-1,i.set(o,a)),n.outputs.push(a),void 0!==this._allData[a]._from)throw new Error(`multiple nodes output to one data value: ${a}`);if(this._allData[a]._from=e,"Constant"===r.opType()){if(1!==r.attributesLength()||!r.attributes(0).t())throw new Error("missing attributes or missing tensor value in attributes for this Constant operator");if(1!==r.outputsLength())throw new Error("missing output or incorrect number of outputs for this Constant operator");n.outputs.pop(),n.executeNode=!1,this._allData[a]._from=-1,this._allData[a].tensor=s.Tensor.fromOrtTensor(r.attributes(0).t())}}}for(let e=0;e<this._nodes.length;e++){const n=this._nodes[e],r=t.nodes(e);if(0===r.inputsLength())throw new Error(`missing input for node: ${r.name}`);for(let t=0;t<r.inputsLength();t++){const o=r.inputs(t),s=i.get(o);if(void 0===s)throw new Error(`unrecognized input '${o}' for node: ${r.name()}`);n.inputs.push(s),this._allData[s]._to.push(e)}}}checkIsAcyclic(){const t=new Set;this._allInputIndices.forEach((e=>{this._allData[e]._to.forEach((e=>{t.add(e)}))}));const e=Array.from(t),n=new Array(this._nodes.length).fill("white");for(;e.length>0;){const t=e.pop();"gray"===n[t]?n[t]="black":(e.push(t),n[t]="gray",this._nodes[t].outputs.forEach((r=>{const i=this._allData[r];if(void 0!==i.tensor)throw new Error("node outputs should not be initialized");if(i._from!==t)throw new Error("from property of the Value object doesn't match index of Node being processed");i._to.forEach((t=>{if("gray"===n[t])throw new Error("model graph is cyclic");"white"===n[t]&&e.push(t)}))})))}}transformGraph(t){this.removeAllIdentityNodes(),this.removeAllDropoutNodes(),this.fuseConvActivationNodes(),t&&t.transformGraph(this),this.finalizeGraph()}finalizeGraph(){let t=0;for(let e=0;e<this._nodes.length;e++)this._nodes[e].executeNode?t>0&&(this._nodes[e].inputs.forEach((n=>{const r=this._allData[n]._to.indexOf(e+t);-1!==r&&(this._allData[n]._to[r]=e)})),this._nodes[e].outputs.forEach((n=>{this._allData[n]._from&&this._allData[n]._from===e+t&&(this._allData[n]._from=e)}))):(t++,this._nodes[e].outputs.forEach((t=>{this._allData[t]._from=-2})),this._nodes.splice(e,1),e--);t=0;for(let e=0;e<this._allData.length;e++)if(-2!==this._allData[e].from||-1!==this._allOutputIndices.indexOf(e+t)){if(t>0){let n=-1;void 0!==this._allData[e].from&&-1!==this._allData[e].from?(n=this._nodes[this._allData[e].from].outputs.indexOf(e+t),-1!==n&&(this._nodes[this._allData[e].from].outputs[n]=e)):(n=this._allInputIndices.indexOf(e+t),-1!==n&&(this._allInputIndices[n]=e)),this._allData[e].to.forEach((r=>{n=this._nodes[r].inputs.indexOf(e+t),-1!==n&&(this._nodes[r].inputs[n]=e)})),0===this._allData[e].to.length&&(n=this._allOutputIndices.indexOf(e+t),-1!==n&&(this._allOutputIndices[n]=e))}}else t++,this._allData.splice(e,1),e--}deleteNode(t){const e=this._nodes[t];if(e.outputs.length>1)for(let t=1;t<e.outputs.length;t++)if(this._allData[e.outputs[t]].to.length>0)throw new Error("Node deletion with more than one output connected to other nodes is not supported. ");e.executeNode=!1;const n=e.inputs[0],r=e.outputs[0],i=this._allData[r].to,o=this._allData[n].to.indexOf(t);if(-1===o)throw new Error("The Value object doesn't have the current Node in it's 'to' property ");this._allData[n].to.splice(o,1),this._allData[r]._to=[];const s=this._allOutputIndices.indexOf(r);if(-1!==s&&(this._allOutputIndices[s]=n),i&&i.length>0)for(const t of i){const e=this._nodes[t].inputs.indexOf(r);if(-1===e)throw new Error("The Node object doesn't have the output Value in it's 'inputs' property ");this._nodes[t].inputs[e]=n,this._allData[n].to.push(t)}}removeAllDropoutNodes(){let t=0;for(const e of this._nodes){if("Dropout"===e.opType){if(1!==e.inputs.length)throw new Error("Dropout nodes should only contain one input. ");if(1!==e.outputs.length&&2!==e.outputs.length)throw new Error("Dropout nodes should contain either 1 or 2 output(s)");if(2===e.outputs.length&&0!==this._allData[e.outputs[1]]._to.length)throw new Error("Dropout nodes's second output should not be referenced by other nodes");this.deleteNode(t)}t++}}removeAllIdentityNodes(){let t=0;for(const e of this._nodes)"Identity"===e.opType&&this.deleteNode(t),t++}isActivation(t){switch(t.opType){case"Relu":case"Sigmoid":case"Clip":return!0;default:return!1}}fuseConvActivationNodes(){for(const t of this._nodes)if("Conv"===t.opType){const e=this._allData[t.outputs[0]]._to;if(1===e.length&&this.isActivation(this._nodes[e[0]])){const n=this._nodes[e[0]];if("Clip"===n.opType)if(1===n.inputs.length)try{t.attributes.set("activation_params","floats",[n.attributes.getFloat("min"),n.attributes.getFloat("max")])}catch(e){t.attributes.set("activation_params","floats",[a.MIN_CLIP,a.MAX_CLIP])}else{if(!(n.inputs.length>=3&&void 0!==this._allData[n.inputs[1]].tensor&&void 0!==this._allData[n.inputs[2]].tensor))continue;t.attributes.set("activation_params","floats",[this._allData[n.inputs[1]].tensor.floatData[0],this._allData[n.inputs[2]].tensor.floatData[0]])}t.attributes.set("activation","string",n.opType),this.deleteNode(e[0])}}}}},6231:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.now=e.Profiler=e.Logger=void 0;const n={verbose:1e3,info:2e3,warning:4e3,error:5e3,fatal:6e3},r={none:new class{log(t,e,n){}},console:new class{log(t,e,n){console.log(`${this.color(t)} ${n?"[35m"+n+"[0m ":""}${e}`)}color(t){switch(t){case"verbose":return"[34;40mv[0m";case"info":return"[32mi[0m";case"warning":return"[30;43mw[0m";case"error":return"[31;40me[0m";case"fatal":return"[101mf[0m";default:throw new Error(`unsupported severity: ${t}`)}}}},i={provider:"console",minimalSeverity:"warning",logDateTime:!0,logSourceLocation:!1};let o={"":i};function s(t,e,n,r){if(void 0===e)return i=t,{verbose:s.verbose.bind(null,i),info:s.info.bind(null,i),warning:s.warning.bind(null,i),error:s.error.bind(null,i),fatal:s.fatal.bind(null,i)};if(void 0===n)a(t,e);else if("number"==typeof n&&void 0===r)a(t,e);else if("string"==typeof n&&void 0===r)a(t,n,0,e);else{if("string"!=typeof n||"number"!=typeof r)throw new TypeError("input is valid");a(t,n,0,e)}var i}function a(t,e,i,s){const a=o[s||""]||o[""];n[t]<n[a.minimalSeverity]||(a.logDateTime&&(e=`${(new Date).toISOString()}|${e}`),a.logSourceLocation,r[a.provider].log(t,e,s))}!function(t){function e(t){o={},n("",t||{})}function n(t,n){if("*"===t)e(n);else{const e=o[t]||i;o[t]={provider:n.provider||e.provider,minimalSeverity:n.minimalSeverity||e.minimalSeverity,logDateTime:void 0===n.logDateTime?e.logDateTime:n.logDateTime,logSourceLocation:void 0===n.logSourceLocation?e.logSourceLocation:n.logSourceLocation}}}t.verbose=function(e,n){t("verbose",e,n)},t.info=function(e,n){t("info",e,n)},t.warning=function(e,n){t("warning",e,n)},t.error=function(e,n){t("error",e,n)},t.fatal=function(e,n){t("fatal",e,n)},t.reset=e,t.set=n,t.setWithEnv=function(t){const e={};t.logLevel&&(e.minimalSeverity=t.logLevel),n("",e)}}(s||(s={})),e.Logger=s;class u{constructor(t,e,n,r,i,o){this.category=t,this.name=e,this.startTime=n,this.endCallback=r,this.timer=i,this.ctx=o}end(){return this.endCallback(this)}async checkTimer(){if(void 0===this.ctx||void 0===this.timer)throw new Error("No webgl timer found");return this.ctx.endTimer(),this.ctx.waitForQueryAndGetTime(this.timer)}}class l{constructor(t,e,n,r){this.category=t,this.name=e,this.startTime=n,this.endTime=r}}e.Profiler=class{static create(t){return void 0===t?new this:new this(t.maxNumberEvents,t.flushBatchSize,t.flushIntervalInMilliseconds)}constructor(t,e,n){this._started=!1,this._flushPointer=0,this._started=!1,this._maxNumberEvents=void 0===t?1e4:t,this._flushBatchSize=void 0===e?10:e,this._flushIntervalInMilliseconds=void 0===n?5e3:n}start(){this._started=!0,this._timingEvents=[],this._flushTime=(0,e.now)(),this._flushPointer=0}stop(){for(this._started=!1;this._flushPointer<this._timingEvents.length;this._flushPointer++)this.logOneEvent(this._timingEvents[this._flushPointer])}event(t,e,n,r){const i=this._started?this.begin(t,e,r):void 0;let o=!1;const s=n();if(s&&"function"==typeof s.then)return o=!0,new Promise(((t,e)=>{s.then((async e=>{i&&await i.end(),t(e)}),(async t=>{i&&await i.end(),e(t)}))}));if(!o&&i){const t=i.end();if(t&&"function"==typeof t.then)return new Promise(((e,n)=>{t.then((()=>{e(s)}),(t=>{n(t)}))}))}return s}begin(t,n,r){if(!this._started)throw new Error("profiler is not started yet");if(void 0===r){const r=(0,e.now)();return this.flush(r),new u(t,n,r,(t=>this.endSync(t)))}{const e=r.beginTimer();return new u(t,n,0,(async t=>this.end(t)),e,r)}}async end(t){const e=await t.checkTimer();this._timingEvents.length<this._maxNumberEvents&&(this._timingEvents.push(new l(t.category,t.name,t.startTime,e)),this.flush(e))}endSync(t){const n=(0,e.now)();this._timingEvents.length<this._maxNumberEvents&&(this._timingEvents.push(new l(t.category,t.name,t.startTime,n)),this.flush(n))}logOneEvent(t){e.Logger.verbose(`Profiler.${t.category}`,`${(t.endTime-t.startTime).toFixed(2)}ms on event '${t.name}' at ${t.endTime.toFixed(2)}`)}flush(t){if(this._timingEvents.length-this._flushPointer>=this._flushBatchSize||t-this._flushTime>=this._flushIntervalInMilliseconds){for(const t=this._flushPointer;this._flushPointer<t+this._flushBatchSize&&this._flushPointer<this._timingEvents.length;this._flushPointer++)this.logOneEvent(this._timingEvents[this._flushPointer]);this._flushTime=(0,e.now)()}}get started(){return this._started}},e.now="undefined"!=typeof performance&&performance.now?()=>performance.now():Date.now},2644:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Model=void 0;const r=n(5686),i=n(1446),o=n(7070),s=n(9395),a=n(2517);var u=s.onnxruntime.experimental.fbs;e.Model=class{constructor(){}load(t,e,n){if(!n)try{return void this.loadFromOnnxFormat(t,e)}catch(t){if(void 0!==n)throw t}this.loadFromOrtFormat(t,e)}loadFromOnnxFormat(t,e){const n=i.onnx.ModelProto.decode(t);if(a.LongUtil.longToNumber(n.irVersion)<3)throw new Error("only support ONNX model with IR_VERSION>=3");this._opsets=n.opsetImport.map((t=>({domain:t.domain,version:a.LongUtil.longToNumber(t.version)}))),this._graph=o.Graph.from(n.graph,e)}loadFromOrtFormat(t,e){const n=new r.flatbuffers.ByteBuffer(t),i=u.InferenceSession.getRootAsInferenceSession(n).model();if(a.LongUtil.longToNumber(i.irVersion())<3)throw new Error("only support ONNX model with IR_VERSION>=3");this._opsets=[];for(let t=0;t<i.opsetImportLength();t++){const e=i.opsetImport(t);this._opsets.push({domain:null==e?void 0:e.domain(),version:a.LongUtil.longToNumber(e.version())})}this._graph=o.Graph.from(i.graph(),e)}get graph(){return this._graph}get opsets(){return this._opsets}}},782:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.FLOAT_TYPES=e.INT_TYPES=e.NUMBER_TYPES=void 0,e.NUMBER_TYPES=["float32","float64","int32","int16","int8","uint16","uint32","uint8"],e.INT_TYPES=["int32","int16","int8","uint16","uint32","uint8"],e.FLOAT_TYPES=["float32","float64"]},1047:(t,e)=>{"use strict";function n(t,e){if(e.endsWith("+")){const n=Number.parseInt(e.substring(0,e.length-1),10);return!isNaN(n)&&n<=t}if(2===e.split("-").length){const n=e.split("-"),r=Number.parseInt(n[0],10),i=Number.parseInt(n[1],10);return!isNaN(r)&&!isNaN(i)&&r<=t&&t<=i}return Number.parseInt(e,10)===t}Object.defineProperty(e,"__esModule",{value:!0}),e.resolveOperator=void 0,e.resolveOperator=function(t,e,r){for(const i of r){const r=i[0],o=i[1],s=i[2],a=i[3],u=i[4];if(t.opType===r)for(const t of e)if((t.domain===o||"ai.onnx"===t.domain&&""===o)&&n(t.version,s))return{opImpl:a,opInit:u}}throw new TypeError(`cannot resolve operator '${t.opType}' with opsets: ${e.map((t=>`${t.domain||"ai.onnx"} v${t.version}`)).join(", ")}`)}},9395:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.onnxruntime=void 0;const r=n(5686);var i,o;i=e.onnxruntime||(e.onnxruntime={}),function(t){let e;!function(t){t[t.UNDEFINED=0]="UNDEFINED",t[t.FLOAT=1]="FLOAT",t[t.INT=2]="INT",t[t.STRING=3]="STRING",t[t.TENSOR=4]="TENSOR",t[t.GRAPH=5]="GRAPH",t[t.FLOATS=6]="FLOATS",t[t.INTS=7]="INTS",t[t.STRINGS=8]="STRINGS",t[t.TENSORS=9]="TENSORS",t[t.GRAPHS=10]="GRAPHS",t[t.SPARSE_TENSOR=11]="SPARSE_TENSOR",t[t.SPARSE_TENSORS=12]="SPARSE_TENSORS"}(e=t.AttributeType||(t.AttributeType={}))}((o=i.experimental||(i.experimental={})).fbs||(o.fbs={})),function(t){!function(t){!function(t){let e;!function(t){t[t.UNKNOWN=0]="UNKNOWN",t[t.VALUE=1]="VALUE",t[t.PARAM=2]="PARAM"}(e=t.DimensionValueType||(t.DimensionValueType={}))}(t.fbs||(t.fbs={}))}(t.experimental||(t.experimental={}))}(e.onnxruntime||(e.onnxruntime={})),function(t){!function(t){!function(t){let e;!function(t){t[t.UNDEFINED=0]="UNDEFINED",t[t.FLOAT=1]="FLOAT",t[t.UINT8=2]="UINT8",t[t.INT8=3]="INT8",t[t.UINT16=4]="UINT16",t[t.INT16=5]="INT16",t[t.INT32=6]="INT32",t[t.INT64=7]="INT64",t[t.STRING=8]="STRING",t[t.BOOL=9]="BOOL",t[t.FLOAT16=10]="FLOAT16",t[t.DOUBLE=11]="DOUBLE",t[t.UINT32=12]="UINT32",t[t.UINT64=13]="UINT64",t[t.COMPLEX64=14]="COMPLEX64",t[t.COMPLEX128=15]="COMPLEX128",t[t.BFLOAT16=16]="BFLOAT16"}(e=t.TensorDataType||(t.TensorDataType={}))}(t.fbs||(t.fbs={}))}(t.experimental||(t.experimental={}))}(e.onnxruntime||(e.onnxruntime={})),function(t){!function(t){!function(t){let e;!function(t){t[t.Primitive=0]="Primitive",t[t.Fused=1]="Fused"}(e=t.NodeType||(t.NodeType={}))}(t.fbs||(t.fbs={}))}(t.experimental||(t.experimental={}))}(e.onnxruntime||(e.onnxruntime={})),function(t){!function(t){!function(t){let e;!function(t){t[t.NONE=0]="NONE",t[t.tensor_type=1]="tensor_type",t[t.sequence_type=2]="sequence_type",t[t.map_type=3]="map_type"}(e=t.TypeInfoValue||(t.TypeInfoValue={}))}(t.fbs||(t.fbs={}))}(t.experimental||(t.experimental={}))}(e.onnxruntime||(e.onnxruntime={})),function(t){!function(e){!function(e){class n{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsShape(t,e){return(e||new n).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsShape(t,e){return t.setPosition(t.position()+r.flatbuffers.SIZE_PREFIX_LENGTH),(e||new n).__init(t.readInt32(t.position())+t.position(),t)}dim(e,n){let r=this.bb.__offset(this.bb_pos,4);return r?(n||new t.experimental.fbs.Dimension).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+4*e),this.bb):null}dimLength(){let t=this.bb.__offset(this.bb_pos,4);return t?this.bb.__vector_len(this.bb_pos+t):0}static startShape(t){t.startObject(1)}static addDim(t,e){t.addFieldOffset(0,e,0)}static createDimVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startDimVector(t,e){t.startVector(4,e,4)}static endShape(t){return t.endObject()}static createShape(t,e){return n.startShape(t),n.addDim(t,e),n.endShape(t)}}e.Shape=n}(e.fbs||(e.fbs={}))}(t.experimental||(t.experimental={}))}(e.onnxruntime||(e.onnxruntime={})),function(t){!function(e){!function(e){class n{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDimension(t,e){return(e||new n).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDimension(t,e){return t.setPosition(t.position()+r.flatbuffers.SIZE_PREFIX_LENGTH),(e||new n).__init(t.readInt32(t.position())+t.position(),t)}value(e){let n=this.bb.__offset(this.bb_pos,4);return n?(e||new t.experimental.fbs.DimensionValue).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}denotation(t){let e=this.bb.__offset(this.bb_pos,6);return e?this.bb.__string(this.bb_pos+e,t):null}static startDimension(t){t.startObject(2)}static addValue(t,e){t.addFieldOffset(0,e,0)}static addDenotation(t,e){t.addFieldOffset(1,e,0)}static endDimension(t){return t.endObject()}static createDimension(t,e,r){return n.startDimension(t),n.addValue(t,e),n.addDenotation(t,r),n.endDimension(t)}}e.Dimension=n}(e.fbs||(e.fbs={}))}(t.experimental||(t.experimental={}))}(e.onnxruntime||(e.onnxruntime={})),function(t){!function(e){!function(e){class n{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDimensionValue(t,e){return(e||new n).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDimensionValue(t,e){return t.setPosition(t.position()+r.flatbuffers.SIZE_PREFIX_LENGTH),(e||new n).__init(t.readInt32(t.position())+t.position(),t)}dimType(){let e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt8(this.bb_pos+e):t.experimental.fbs.DimensionValueType.UNKNOWN}dimValue(){let t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt64(this.bb_pos+t):this.bb.createLong(0,0)}dimParam(t){let e=this.bb.__offset(this.bb_pos,8);return e?this.bb.__string(this.bb_pos+e,t):null}static startDimensionValue(t){t.startObject(3)}static addDimType(e,n){e.addFieldInt8(0,n,t.experimental.fbs.DimensionValueType.UNKNOWN)}static addDimValue(t,e){t.addFieldInt64(1,e,t.createLong(0,0))}static addDimParam(t,e){t.addFieldOffset(2,e,0)}static endDimensionValue(t){return t.endObject()}static createDimensionValue(t,e,r,i){return n.startDimensionValue(t),n.addDimType(t,e),n.addDimValue(t,r),n.addDimParam(t,i),n.endDimensionValue(t)}}e.DimensionValue=n}(e.fbs||(e.fbs={}))}(t.experimental||(t.experimental={}))}(e.onnxruntime||(e.onnxruntime={})),function(t){!function(e){!function(e){class n{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsTensorTypeAndShape(t,e){return(e||new n).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsTensorTypeAndShape(t,e){return t.setPosition(t.position()+r.flatbuffers.SIZE_PREFIX_LENGTH),(e||new n).__init(t.readInt32(t.position())+t.position(),t)}elemType(){let e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt32(this.bb_pos+e):t.experimental.fbs.TensorDataType.UNDEFINED}shape(e){let n=this.bb.__offset(this.bb_pos,6);return n?(e||new t.experimental.fbs.Shape).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}static startTensorTypeAndShape(t){t.startObject(2)}static addElemType(e,n){e.addFieldInt32(0,n,t.experimental.fbs.TensorDataType.UNDEFINED)}static addShape(t,e){t.addFieldOffset(1,e,0)}static endTensorTypeAndShape(t){return t.endObject()}static createTensorTypeAndShape(t,e,r){return n.startTensorTypeAndShape(t),n.addElemType(t,e),n.addShape(t,r),n.endTensorTypeAndShape(t)}}e.TensorTypeAndShape=n}(e.fbs||(e.fbs={}))}(t.experimental||(t.experimental={}))}(e.onnxruntime||(e.onnxruntime={})),function(t){!function(e){!function(e){class n{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsMapType(t,e){return(e||new n).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsMapType(t,e){return t.setPosition(t.position()+r.flatbuffers.SIZE_PREFIX_LENGTH),(e||new n).__init(t.readInt32(t.position())+t.position(),t)}keyType(){let e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt32(this.bb_pos+e):t.experimental.fbs.TensorDataType.UNDEFINED}valueType(e){let n=this.bb.__offset(this.bb_pos,6);return n?(e||new t.experimental.fbs.TypeInfo).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}static startMapType(t){t.startObject(2)}static addKeyType(e,n){e.addFieldInt32(0,n,t.experimental.fbs.TensorDataType.UNDEFINED)}static addValueType(t,e){t.addFieldOffset(1,e,0)}static endMapType(t){return t.endObject()}static createMapType(t,e,r){return n.startMapType(t),n.addKeyType(t,e),n.addValueType(t,r),n.endMapType(t)}}e.MapType=n}(e.fbs||(e.fbs={}))}(t.experimental||(t.experimental={}))}(e.onnxruntime||(e.onnxruntime={})),function(t){!function(e){!function(e){class n{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsSequenceType(t,e){return(e||new n).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsSequenceType(t,e){return t.setPosition(t.position()+r.flatbuffers.SIZE_PREFIX_LENGTH),(e||new n).__init(t.readInt32(t.position())+t.position(),t)}elemType(e){let n=this.bb.__offset(this.bb_pos,4);return n?(e||new t.experimental.fbs.TypeInfo).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}static startSequenceType(t){t.startObject(1)}static addElemType(t,e){t.addFieldOffset(0,e,0)}static endSequenceType(t){return t.endObject()}static createSequenceType(t,e){return n.startSequenceType(t),n.addElemType(t,e),n.endSequenceType(t)}}e.SequenceType=n}(e.fbs||(e.fbs={}))}(t.experimental||(t.experimental={}))}(e.onnxruntime||(e.onnxruntime={})),function(t){!function(t){(t.fbs||(t.fbs={})).EdgeEnd=class{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}nodeIndex(){return this.bb.readUint32(this.bb_pos)}srcArgIndex(){return this.bb.readInt32(this.bb_pos+4)}dstArgIndex(){return this.bb.readInt32(this.bb_pos+8)}static createEdgeEnd(t,e,n,r){return t.prep(4,12),t.writeInt32(r),t.writeInt32(n),t.writeInt32(e),t.offset()}}}(t.experimental||(t.experimental={}))}(e.onnxruntime||(e.onnxruntime={})),function(t){!function(e){!function(e){class n{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsNodeEdge(t,e){return(e||new n).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsNodeEdge(t,e){return t.setPosition(t.position()+r.flatbuffers.SIZE_PREFIX_LENGTH),(e||new n).__init(t.readInt32(t.position())+t.position(),t)}nodeIndex(){let t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readUint32(this.bb_pos+t):0}inputEdges(e,n){let r=this.bb.__offset(this.bb_pos,6);return r?(n||new t.experimental.fbs.EdgeEnd).__init(this.bb.__vector(this.bb_pos+r)+12*e,this.bb):null}inputEdgesLength(){let t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}outputEdges(e,n){let r=this.bb.__offset(this.bb_pos,8);return r?(n||new t.experimental.fbs.EdgeEnd).__init(this.bb.__vector(this.bb_pos+r)+12*e,this.bb):null}outputEdgesLength(){let t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}static startNodeEdge(t){t.startObject(3)}static addNodeIndex(t,e){t.addFieldInt32(0,e,0)}static addInputEdges(t,e){t.addFieldOffset(1,e,0)}static startInputEdgesVector(t,e){t.startVector(12,e,4)}static addOutputEdges(t,e){t.addFieldOffset(2,e,0)}static startOutputEdgesVector(t,e){t.startVector(12,e,4)}static endNodeEdge(t){return t.endObject()}static createNodeEdge(t,e,r,i){return n.startNodeEdge(t),n.addNodeIndex(t,e),n.addInputEdges(t,r),n.addOutputEdges(t,i),n.endNodeEdge(t)}}e.NodeEdge=n}(e.fbs||(e.fbs={}))}(t.experimental||(t.experimental={}))}(e.onnxruntime||(e.onnxruntime={})),function(t){!function(e){!function(e){class n{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsNode(t,e){return(e||new n).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsNode(t,e){return t.setPosition(t.position()+r.flatbuffers.SIZE_PREFIX_LENGTH),(e||new n).__init(t.readInt32(t.position())+t.position(),t)}name(t){let e=this.bb.__offset(this.bb_pos,4);return e?this.bb.__string(this.bb_pos+e,t):null}docString(t){let e=this.bb.__offset(this.bb_pos,6);return e?this.bb.__string(this.bb_pos+e,t):null}domain(t){let e=this.bb.__offset(this.bb_pos,8);return e?this.bb.__string(this.bb_pos+e,t):null}sinceVersion(){let t=this.bb.__offset(this.bb_pos,10);return t?this.bb.readInt32(this.bb_pos+t):0}index(){let t=this.bb.__offset(this.bb_pos,12);return t?this.bb.readUint32(this.bb_pos+t):0}opType(t){let e=this.bb.__offset(this.bb_pos,14);return e?this.bb.__string(this.bb_pos+e,t):null}type(){let e=this.bb.__offset(this.bb_pos,16);return e?this.bb.readInt32(this.bb_pos+e):t.experimental.fbs.NodeType.Primitive}executionProviderType(t){let e=this.bb.__offset(this.bb_pos,18);return e?this.bb.__string(this.bb_pos+e,t):null}inputs(t,e){let n=this.bb.__offset(this.bb_pos,20);return n?this.bb.__string(this.bb.__vector(this.bb_pos+n)+4*t,e):null}inputsLength(){let t=this.bb.__offset(this.bb_pos,20);return t?this.bb.__vector_len(this.bb_pos+t):0}outputs(t,e){let n=this.bb.__offset(this.bb_pos,22);return n?this.bb.__string(this.bb.__vector(this.bb_pos+n)+4*t,e):null}outputsLength(){let t=this.bb.__offset(this.bb_pos,22);return t?this.bb.__vector_len(this.bb_pos+t):0}attributes(e,n){let r=this.bb.__offset(this.bb_pos,24);return r?(n||new t.experimental.fbs.Attribute).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+4*e),this.bb):null}attributesLength(){let t=this.bb.__offset(this.bb_pos,24);return t?this.bb.__vector_len(this.bb_pos+t):0}inputArgCounts(t){let e=this.bb.__offset(this.bb_pos,26);return e?this.bb.readInt32(this.bb.__vector(this.bb_pos+e)+4*t):0}inputArgCountsLength(){let t=this.bb.__offset(this.bb_pos,26);return t?this.bb.__vector_len(this.bb_pos+t):0}inputArgCountsArray(){let t=this.bb.__offset(this.bb_pos,26);return t?new Int32Array(this.bb.bytes().buffer,this.bb.bytes().byteOffset+this.bb.__vector(this.bb_pos+t),this.bb.__vector_len(this.bb_pos+t)):null}implicitInputs(t,e){let n=this.bb.__offset(this.bb_pos,28);return n?this.bb.__string(this.bb.__vector(this.bb_pos+n)+4*t,e):null}implicitInputsLength(){let t=this.bb.__offset(this.bb_pos,28);return t?this.bb.__vector_len(this.bb_pos+t):0}static startNode(t){t.startObject(13)}static addName(t,e){t.addFieldOffset(0,e,0)}static addDocString(t,e){t.addFieldOffset(1,e,0)}static addDomain(t,e){t.addFieldOffset(2,e,0)}static addSinceVersion(t,e){t.addFieldInt32(3,e,0)}static addIndex(t,e){t.addFieldInt32(4,e,0)}static addOpType(t,e){t.addFieldOffset(5,e,0)}static addType(e,n){e.addFieldInt32(6,n,t.experimental.fbs.NodeType.Primitive)}static addExecutionProviderType(t,e){t.addFieldOffset(7,e,0)}static addInputs(t,e){t.addFieldOffset(8,e,0)}static createInputsVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startInputsVector(t,e){t.startVector(4,e,4)}static addOutputs(t,e){t.addFieldOffset(9,e,0)}static createOutputsVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startOutputsVector(t,e){t.startVector(4,e,4)}static addAttributes(t,e){t.addFieldOffset(10,e,0)}static createAttributesVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startAttributesVector(t,e){t.startVector(4,e,4)}static addInputArgCounts(t,e){t.addFieldOffset(11,e,0)}static createInputArgCountsVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addInt32(e[n]);return t.endVector()}static startInputArgCountsVector(t,e){t.startVector(4,e,4)}static addImplicitInputs(t,e){t.addFieldOffset(12,e,0)}static createImplicitInputsVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startImplicitInputsVector(t,e){t.startVector(4,e,4)}static endNode(t){return t.endObject()}static createNode(t,e,r,i,o,s,a,u,l,c,d,p,h,f){return n.startNode(t),n.addName(t,e),n.addDocString(t,r),n.addDomain(t,i),n.addSinceVersion(t,o),n.addIndex(t,s),n.addOpType(t,a),n.addType(t,u),n.addExecutionProviderType(t,l),n.addInputs(t,c),n.addOutputs(t,d),n.addAttributes(t,p),n.addInputArgCounts(t,h),n.addImplicitInputs(t,f),n.endNode(t)}}e.Node=n}(e.fbs||(e.fbs={}))}(t.experimental||(t.experimental={}))}(e.onnxruntime||(e.onnxruntime={})),function(t){!function(e){!function(e){class n{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsValueInfo(t,e){return(e||new n).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsValueInfo(t,e){return t.setPosition(t.position()+r.flatbuffers.SIZE_PREFIX_LENGTH),(e||new n).__init(t.readInt32(t.position())+t.position(),t)}name(t){let e=this.bb.__offset(this.bb_pos,4);return e?this.bb.__string(this.bb_pos+e,t):null}docString(t){let e=this.bb.__offset(this.bb_pos,6);return e?this.bb.__string(this.bb_pos+e,t):null}type(e){let n=this.bb.__offset(this.bb_pos,8);return n?(e||new t.experimental.fbs.TypeInfo).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}static startValueInfo(t){t.startObject(3)}static addName(t,e){t.addFieldOffset(0,e,0)}static addDocString(t,e){t.addFieldOffset(1,e,0)}static addType(t,e){t.addFieldOffset(2,e,0)}static endValueInfo(t){return t.endObject()}static createValueInfo(t,e,r,i){return n.startValueInfo(t),n.addName(t,e),n.addDocString(t,r),n.addType(t,i),n.endValueInfo(t)}}e.ValueInfo=n}(e.fbs||(e.fbs={}))}(t.experimental||(t.experimental={}))}(e.onnxruntime||(e.onnxruntime={})),function(t){!function(e){!function(e){class n{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsTypeInfo(t,e){return(e||new n).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsTypeInfo(t,e){return t.setPosition(t.position()+r.flatbuffers.SIZE_PREFIX_LENGTH),(e||new n).__init(t.readInt32(t.position())+t.position(),t)}denotation(t){let e=this.bb.__offset(this.bb_pos,4);return e?this.bb.__string(this.bb_pos+e,t):null}valueType(){let e=this.bb.__offset(this.bb_pos,6);return e?this.bb.readUint8(this.bb_pos+e):t.experimental.fbs.TypeInfoValue.NONE}value(t){let e=this.bb.__offset(this.bb_pos,8);return e?this.bb.__union(t,this.bb_pos+e):null}static startTypeInfo(t){t.startObject(3)}static addDenotation(t,e){t.addFieldOffset(0,e,0)}static addValueType(e,n){e.addFieldInt8(1,n,t.experimental.fbs.TypeInfoValue.NONE)}static addValue(t,e){t.addFieldOffset(2,e,0)}static endTypeInfo(t){return t.endObject()}static createTypeInfo(t,e,r,i){return n.startTypeInfo(t),n.addDenotation(t,e),n.addValueType(t,r),n.addValue(t,i),n.endTypeInfo(t)}}e.TypeInfo=n}(e.fbs||(e.fbs={}))}(t.experimental||(t.experimental={}))}(e.onnxruntime||(e.onnxruntime={})),function(t){!function(t){!function(t){class e{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsOperatorSetId(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsOperatorSetId(t,n){return t.setPosition(t.position()+r.flatbuffers.SIZE_PREFIX_LENGTH),(n||new e).__init(t.readInt32(t.position())+t.position(),t)}domain(t){let e=this.bb.__offset(this.bb_pos,4);return e?this.bb.__string(this.bb_pos+e,t):null}version(){let t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt64(this.bb_pos+t):this.bb.createLong(0,0)}static startOperatorSetId(t){t.startObject(2)}static addDomain(t,e){t.addFieldOffset(0,e,0)}static addVersion(t,e){t.addFieldInt64(1,e,t.createLong(0,0))}static endOperatorSetId(t){return t.endObject()}static createOperatorSetId(t,n,r){return e.startOperatorSetId(t),e.addDomain(t,n),e.addVersion(t,r),e.endOperatorSetId(t)}}t.OperatorSetId=e}(t.fbs||(t.fbs={}))}(t.experimental||(t.experimental={}))}(e.onnxruntime||(e.onnxruntime={})),function(t){!function(e){!function(e){class n{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsTensor(t,e){return(e||new n).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsTensor(t,e){return t.setPosition(t.position()+r.flatbuffers.SIZE_PREFIX_LENGTH),(e||new n).__init(t.readInt32(t.position())+t.position(),t)}name(t){let e=this.bb.__offset(this.bb_pos,4);return e?this.bb.__string(this.bb_pos+e,t):null}docString(t){let e=this.bb.__offset(this.bb_pos,6);return e?this.bb.__string(this.bb_pos+e,t):null}dims(t){let e=this.bb.__offset(this.bb_pos,8);return e?this.bb.readInt64(this.bb.__vector(this.bb_pos+e)+8*t):this.bb.createLong(0,0)}dimsLength(){let t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}dataType(){let e=this.bb.__offset(this.bb_pos,10);return e?this.bb.readInt32(this.bb_pos+e):t.experimental.fbs.TensorDataType.UNDEFINED}rawData(t){let e=this.bb.__offset(this.bb_pos,12);return e?this.bb.readUint8(this.bb.__vector(this.bb_pos+e)+t):0}rawDataLength(){let t=this.bb.__offset(this.bb_pos,12);return t?this.bb.__vector_len(this.bb_pos+t):0}rawDataArray(){let t=this.bb.__offset(this.bb_pos,12);return t?new Uint8Array(this.bb.bytes().buffer,this.bb.bytes().byteOffset+this.bb.__vector(this.bb_pos+t),this.bb.__vector_len(this.bb_pos+t)):null}stringData(t,e){let n=this.bb.__offset(this.bb_pos,14);return n?this.bb.__string(this.bb.__vector(this.bb_pos+n)+4*t,e):null}stringDataLength(){let t=this.bb.__offset(this.bb_pos,14);return t?this.bb.__vector_len(this.bb_pos+t):0}static startTensor(t){t.startObject(6)}static addName(t,e){t.addFieldOffset(0,e,0)}static addDocString(t,e){t.addFieldOffset(1,e,0)}static addDims(t,e){t.addFieldOffset(2,e,0)}static createDimsVector(t,e){t.startVector(8,e.length,8);for(let n=e.length-1;n>=0;n--)t.addInt64(e[n]);return t.endVector()}static startDimsVector(t,e){t.startVector(8,e,8)}static addDataType(e,n){e.addFieldInt32(3,n,t.experimental.fbs.TensorDataType.UNDEFINED)}static addRawData(t,e){t.addFieldOffset(4,e,0)}static createRawDataVector(t,e){t.startVector(1,e.length,1);for(let n=e.length-1;n>=0;n--)t.addInt8(e[n]);return t.endVector()}static startRawDataVector(t,e){t.startVector(1,e,1)}static addStringData(t,e){t.addFieldOffset(5,e,0)}static createStringDataVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startStringDataVector(t,e){t.startVector(4,e,4)}static endTensor(t){return t.endObject()}static createTensor(t,e,r,i,o,s,a){return n.startTensor(t),n.addName(t,e),n.addDocString(t,r),n.addDims(t,i),n.addDataType(t,o),n.addRawData(t,s),n.addStringData(t,a),n.endTensor(t)}}e.Tensor=n}(e.fbs||(e.fbs={}))}(t.experimental||(t.experimental={}))}(e.onnxruntime||(e.onnxruntime={})),function(t){!function(e){!function(e){class n{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsSparseTensor(t,e){return(e||new n).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsSparseTensor(t,e){return t.setPosition(t.position()+r.flatbuffers.SIZE_PREFIX_LENGTH),(e||new n).__init(t.readInt32(t.position())+t.position(),t)}values(e){let n=this.bb.__offset(this.bb_pos,4);return n?(e||new t.experimental.fbs.Tensor).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}indices(e){let n=this.bb.__offset(this.bb_pos,6);return n?(e||new t.experimental.fbs.Tensor).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}dims(t){let e=this.bb.__offset(this.bb_pos,8);return e?this.bb.readInt64(this.bb.__vector(this.bb_pos+e)+8*t):this.bb.createLong(0,0)}dimsLength(){let t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}static startSparseTensor(t){t.startObject(3)}static addValues(t,e){t.addFieldOffset(0,e,0)}static addIndices(t,e){t.addFieldOffset(1,e,0)}static addDims(t,e){t.addFieldOffset(2,e,0)}static createDimsVector(t,e){t.startVector(8,e.length,8);for(let n=e.length-1;n>=0;n--)t.addInt64(e[n]);return t.endVector()}static startDimsVector(t,e){t.startVector(8,e,8)}static endSparseTensor(t){return t.endObject()}static createSparseTensor(t,e,r,i){return n.startSparseTensor(t),n.addValues(t,e),n.addIndices(t,r),n.addDims(t,i),n.endSparseTensor(t)}}e.SparseTensor=n}(e.fbs||(e.fbs={}))}(t.experimental||(t.experimental={}))}(e.onnxruntime||(e.onnxruntime={})),function(t){!function(e){!function(e){class n{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsAttribute(t,e){return(e||new n).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsAttribute(t,e){return t.setPosition(t.position()+r.flatbuffers.SIZE_PREFIX_LENGTH),(e||new n).__init(t.readInt32(t.position())+t.position(),t)}name(t){let e=this.bb.__offset(this.bb_pos,4);return e?this.bb.__string(this.bb_pos+e,t):null}docString(t){let e=this.bb.__offset(this.bb_pos,6);return e?this.bb.__string(this.bb_pos+e,t):null}type(){let e=this.bb.__offset(this.bb_pos,8);return e?this.bb.readInt32(this.bb_pos+e):t.experimental.fbs.AttributeType.UNDEFINED}f(){let t=this.bb.__offset(this.bb_pos,10);return t?this.bb.readFloat32(this.bb_pos+t):0}i(){let t=this.bb.__offset(this.bb_pos,12);return t?this.bb.readInt64(this.bb_pos+t):this.bb.createLong(0,0)}s(t){let e=this.bb.__offset(this.bb_pos,14);return e?this.bb.__string(this.bb_pos+e,t):null}t(e){let n=this.bb.__offset(this.bb_pos,16);return n?(e||new t.experimental.fbs.Tensor).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}g(e){let n=this.bb.__offset(this.bb_pos,18);return n?(e||new t.experimental.fbs.Graph).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}floats(t){let e=this.bb.__offset(this.bb_pos,20);return e?this.bb.readFloat32(this.bb.__vector(this.bb_pos+e)+4*t):0}floatsLength(){let t=this.bb.__offset(this.bb_pos,20);return t?this.bb.__vector_len(this.bb_pos+t):0}floatsArray(){let t=this.bb.__offset(this.bb_pos,20);return t?new Float32Array(this.bb.bytes().buffer,this.bb.bytes().byteOffset+this.bb.__vector(this.bb_pos+t),this.bb.__vector_len(this.bb_pos+t)):null}ints(t){let e=this.bb.__offset(this.bb_pos,22);return e?this.bb.readInt64(this.bb.__vector(this.bb_pos+e)+8*t):this.bb.createLong(0,0)}intsLength(){let t=this.bb.__offset(this.bb_pos,22);return t?this.bb.__vector_len(this.bb_pos+t):0}strings(t,e){let n=this.bb.__offset(this.bb_pos,24);return n?this.bb.__string(this.bb.__vector(this.bb_pos+n)+4*t,e):null}stringsLength(){let t=this.bb.__offset(this.bb_pos,24);return t?this.bb.__vector_len(this.bb_pos+t):0}tensors(e,n){let r=this.bb.__offset(this.bb_pos,26);return r?(n||new t.experimental.fbs.Tensor).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+4*e),this.bb):null}tensorsLength(){let t=this.bb.__offset(this.bb_pos,26);return t?this.bb.__vector_len(this.bb_pos+t):0}graphs(e,n){let r=this.bb.__offset(this.bb_pos,28);return r?(n||new t.experimental.fbs.Graph).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+4*e),this.bb):null}graphsLength(){let t=this.bb.__offset(this.bb_pos,28);return t?this.bb.__vector_len(this.bb_pos+t):0}static startAttribute(t){t.startObject(13)}static addName(t,e){t.addFieldOffset(0,e,0)}static addDocString(t,e){t.addFieldOffset(1,e,0)}static addType(e,n){e.addFieldInt32(2,n,t.experimental.fbs.AttributeType.UNDEFINED)}static addF(t,e){t.addFieldFloat32(3,e,0)}static addI(t,e){t.addFieldInt64(4,e,t.createLong(0,0))}static addS(t,e){t.addFieldOffset(5,e,0)}static addT(t,e){t.addFieldOffset(6,e,0)}static addG(t,e){t.addFieldOffset(7,e,0)}static addFloats(t,e){t.addFieldOffset(8,e,0)}static createFloatsVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addFloat32(e[n]);return t.endVector()}static startFloatsVector(t,e){t.startVector(4,e,4)}static addInts(t,e){t.addFieldOffset(9,e,0)}static createIntsVector(t,e){t.startVector(8,e.length,8);for(let n=e.length-1;n>=0;n--)t.addInt64(e[n]);return t.endVector()}static startIntsVector(t,e){t.startVector(8,e,8)}static addStrings(t,e){t.addFieldOffset(10,e,0)}static createStringsVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startStringsVector(t,e){t.startVector(4,e,4)}static addTensors(t,e){t.addFieldOffset(11,e,0)}static createTensorsVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startTensorsVector(t,e){t.startVector(4,e,4)}static addGraphs(t,e){t.addFieldOffset(12,e,0)}static createGraphsVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startGraphsVector(t,e){t.startVector(4,e,4)}static endAttribute(t){return t.endObject()}static createAttribute(t,e,r,i,o,s,a,u,l,c,d,p,h,f){return n.startAttribute(t),n.addName(t,e),n.addDocString(t,r),n.addType(t,i),n.addF(t,o),n.addI(t,s),n.addS(t,a),n.addT(t,u),n.addG(t,l),n.addFloats(t,c),n.addInts(t,d),n.addStrings(t,p),n.addTensors(t,h),n.addGraphs(t,f),n.endAttribute(t)}}e.Attribute=n}(e.fbs||(e.fbs={}))}(t.experimental||(t.experimental={}))}(e.onnxruntime||(e.onnxruntime={})),function(t){!function(e){!function(e){class n{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsGraph(t,e){return(e||new n).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsGraph(t,e){return t.setPosition(t.position()+r.flatbuffers.SIZE_PREFIX_LENGTH),(e||new n).__init(t.readInt32(t.position())+t.position(),t)}initializers(e,n){let r=this.bb.__offset(this.bb_pos,4);return r?(n||new t.experimental.fbs.Tensor).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+4*e),this.bb):null}initializersLength(){let t=this.bb.__offset(this.bb_pos,4);return t?this.bb.__vector_len(this.bb_pos+t):0}nodeArgs(e,n){let r=this.bb.__offset(this.bb_pos,6);return r?(n||new t.experimental.fbs.ValueInfo).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+4*e),this.bb):null}nodeArgsLength(){let t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}nodes(e,n){let r=this.bb.__offset(this.bb_pos,8);return r?(n||new t.experimental.fbs.Node).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+4*e),this.bb):null}nodesLength(){let t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}maxNodeIndex(){let t=this.bb.__offset(this.bb_pos,10);return t?this.bb.readUint32(this.bb_pos+t):0}nodeEdges(e,n){let r=this.bb.__offset(this.bb_pos,12);return r?(n||new t.experimental.fbs.NodeEdge).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+4*e),this.bb):null}nodeEdgesLength(){let t=this.bb.__offset(this.bb_pos,12);return t?this.bb.__vector_len(this.bb_pos+t):0}inputs(t,e){let n=this.bb.__offset(this.bb_pos,14);return n?this.bb.__string(this.bb.__vector(this.bb_pos+n)+4*t,e):null}inputsLength(){let t=this.bb.__offset(this.bb_pos,14);return t?this.bb.__vector_len(this.bb_pos+t):0}outputs(t,e){let n=this.bb.__offset(this.bb_pos,16);return n?this.bb.__string(this.bb.__vector(this.bb_pos+n)+4*t,e):null}outputsLength(){let t=this.bb.__offset(this.bb_pos,16);return t?this.bb.__vector_len(this.bb_pos+t):0}sparseInitializers(e,n){let r=this.bb.__offset(this.bb_pos,18);return r?(n||new t.experimental.fbs.SparseTensor).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+4*e),this.bb):null}sparseInitializersLength(){let t=this.bb.__offset(this.bb_pos,18);return t?this.bb.__vector_len(this.bb_pos+t):0}static startGraph(t){t.startObject(8)}static addInitializers(t,e){t.addFieldOffset(0,e,0)}static createInitializersVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startInitializersVector(t,e){t.startVector(4,e,4)}static addNodeArgs(t,e){t.addFieldOffset(1,e,0)}static createNodeArgsVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startNodeArgsVector(t,e){t.startVector(4,e,4)}static addNodes(t,e){t.addFieldOffset(2,e,0)}static createNodesVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startNodesVector(t,e){t.startVector(4,e,4)}static addMaxNodeIndex(t,e){t.addFieldInt32(3,e,0)}static addNodeEdges(t,e){t.addFieldOffset(4,e,0)}static createNodeEdgesVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startNodeEdgesVector(t,e){t.startVector(4,e,4)}static addInputs(t,e){t.addFieldOffset(5,e,0)}static createInputsVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startInputsVector(t,e){t.startVector(4,e,4)}static addOutputs(t,e){t.addFieldOffset(6,e,0)}static createOutputsVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startOutputsVector(t,e){t.startVector(4,e,4)}static addSparseInitializers(t,e){t.addFieldOffset(7,e,0)}static createSparseInitializersVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startSparseInitializersVector(t,e){t.startVector(4,e,4)}static endGraph(t){return t.endObject()}static createGraph(t,e,r,i,o,s,a,u,l){return n.startGraph(t),n.addInitializers(t,e),n.addNodeArgs(t,r),n.addNodes(t,i),n.addMaxNodeIndex(t,o),n.addNodeEdges(t,s),n.addInputs(t,a),n.addOutputs(t,u),n.addSparseInitializers(t,l),n.endGraph(t)}}e.Graph=n}(e.fbs||(e.fbs={}))}(t.experimental||(t.experimental={}))}(e.onnxruntime||(e.onnxruntime={})),function(t){!function(e){!function(e){class n{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsModel(t,e){return(e||new n).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsModel(t,e){return t.setPosition(t.position()+r.flatbuffers.SIZE_PREFIX_LENGTH),(e||new n).__init(t.readInt32(t.position())+t.position(),t)}irVersion(){let t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt64(this.bb_pos+t):this.bb.createLong(0,0)}opsetImport(e,n){let r=this.bb.__offset(this.bb_pos,6);return r?(n||new t.experimental.fbs.OperatorSetId).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+4*e),this.bb):null}opsetImportLength(){let t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}producerName(t){let e=this.bb.__offset(this.bb_pos,8);return e?this.bb.__string(this.bb_pos+e,t):null}producerVersion(t){let e=this.bb.__offset(this.bb_pos,10);return e?this.bb.__string(this.bb_pos+e,t):null}domain(t){let e=this.bb.__offset(this.bb_pos,12);return e?this.bb.__string(this.bb_pos+e,t):null}modelVersion(){let t=this.bb.__offset(this.bb_pos,14);return t?this.bb.readInt64(this.bb_pos+t):this.bb.createLong(0,0)}docString(t){let e=this.bb.__offset(this.bb_pos,16);return e?this.bb.__string(this.bb_pos+e,t):null}graph(e){let n=this.bb.__offset(this.bb_pos,18);return n?(e||new t.experimental.fbs.Graph).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}graphDocString(t){let e=this.bb.__offset(this.bb_pos,20);return e?this.bb.__string(this.bb_pos+e,t):null}static startModel(t){t.startObject(9)}static addIrVersion(t,e){t.addFieldInt64(0,e,t.createLong(0,0))}static addOpsetImport(t,e){t.addFieldOffset(1,e,0)}static createOpsetImportVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startOpsetImportVector(t,e){t.startVector(4,e,4)}static addProducerName(t,e){t.addFieldOffset(2,e,0)}static addProducerVersion(t,e){t.addFieldOffset(3,e,0)}static addDomain(t,e){t.addFieldOffset(4,e,0)}static addModelVersion(t,e){t.addFieldInt64(5,e,t.createLong(0,0))}static addDocString(t,e){t.addFieldOffset(6,e,0)}static addGraph(t,e){t.addFieldOffset(7,e,0)}static addGraphDocString(t,e){t.addFieldOffset(8,e,0)}static endModel(t){return t.endObject()}static createModel(t,e,r,i,o,s,a,u,l,c){return n.startModel(t),n.addIrVersion(t,e),n.addOpsetImport(t,r),n.addProducerName(t,i),n.addProducerVersion(t,o),n.addDomain(t,s),n.addModelVersion(t,a),n.addDocString(t,u),n.addGraph(t,l),n.addGraphDocString(t,c),n.endModel(t)}}e.Model=n}(e.fbs||(e.fbs={}))}(t.experimental||(t.experimental={}))}(e.onnxruntime||(e.onnxruntime={})),function(t){!function(t){!function(t){class e{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsKernelCreateInfos(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsKernelCreateInfos(t,n){return t.setPosition(t.position()+r.flatbuffers.SIZE_PREFIX_LENGTH),(n||new e).__init(t.readInt32(t.position())+t.position(),t)}nodeIndices(t){let e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readUint32(this.bb.__vector(this.bb_pos+e)+4*t):0}nodeIndicesLength(){let t=this.bb.__offset(this.bb_pos,4);return t?this.bb.__vector_len(this.bb_pos+t):0}nodeIndicesArray(){let t=this.bb.__offset(this.bb_pos,4);return t?new Uint32Array(this.bb.bytes().buffer,this.bb.bytes().byteOffset+this.bb.__vector(this.bb_pos+t),this.bb.__vector_len(this.bb_pos+t)):null}kernelDefHashes(t){let e=this.bb.__offset(this.bb_pos,6);return e?this.bb.readUint64(this.bb.__vector(this.bb_pos+e)+8*t):this.bb.createLong(0,0)}kernelDefHashesLength(){let t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}static startKernelCreateInfos(t){t.startObject(2)}static addNodeIndices(t,e){t.addFieldOffset(0,e,0)}static createNodeIndicesVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addInt32(e[n]);return t.endVector()}static startNodeIndicesVector(t,e){t.startVector(4,e,4)}static addKernelDefHashes(t,e){t.addFieldOffset(1,e,0)}static createKernelDefHashesVector(t,e){t.startVector(8,e.length,8);for(let n=e.length-1;n>=0;n--)t.addInt64(e[n]);return t.endVector()}static startKernelDefHashesVector(t,e){t.startVector(8,e,8)}static endKernelCreateInfos(t){return t.endObject()}static createKernelCreateInfos(t,n,r){return e.startKernelCreateInfos(t),e.addNodeIndices(t,n),e.addKernelDefHashes(t,r),e.endKernelCreateInfos(t)}}t.KernelCreateInfos=e}(t.fbs||(t.fbs={}))}(t.experimental||(t.experimental={}))}(e.onnxruntime||(e.onnxruntime={})),function(t){!function(e){!function(e){class n{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsSubGraphSessionState(t,e){return(e||new n).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsSubGraphSessionState(t,e){return t.setPosition(t.position()+r.flatbuffers.SIZE_PREFIX_LENGTH),(e||new n).__init(t.readInt32(t.position())+t.position(),t)}graphId(t){let e=this.bb.__offset(this.bb_pos,4);return e?this.bb.__string(this.bb_pos+e,t):null}sessionState(e){let n=this.bb.__offset(this.bb_pos,6);return n?(e||new t.experimental.fbs.SessionState).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}static startSubGraphSessionState(t){t.startObject(2)}static addGraphId(t,e){t.addFieldOffset(0,e,0)}static addSessionState(t,e){t.addFieldOffset(1,e,0)}static endSubGraphSessionState(t){let e=t.endObject();return t.requiredField(e,4),e}static createSubGraphSessionState(t,e,r){return n.startSubGraphSessionState(t),n.addGraphId(t,e),n.addSessionState(t,r),n.endSubGraphSessionState(t)}}e.SubGraphSessionState=n}(e.fbs||(e.fbs={}))}(t.experimental||(t.experimental={}))}(e.onnxruntime||(e.onnxruntime={})),function(t){!function(e){!function(e){class n{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsSessionState(t,e){return(e||new n).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsSessionState(t,e){return t.setPosition(t.position()+r.flatbuffers.SIZE_PREFIX_LENGTH),(e||new n).__init(t.readInt32(t.position())+t.position(),t)}kernels(e){let n=this.bb.__offset(this.bb_pos,4);return n?(e||new t.experimental.fbs.KernelCreateInfos).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}subGraphSessionStates(e,n){let r=this.bb.__offset(this.bb_pos,6);return r?(n||new t.experimental.fbs.SubGraphSessionState).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+4*e),this.bb):null}subGraphSessionStatesLength(){let t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}static startSessionState(t){t.startObject(2)}static addKernels(t,e){t.addFieldOffset(0,e,0)}static addSubGraphSessionStates(t,e){t.addFieldOffset(1,e,0)}static createSubGraphSessionStatesVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startSubGraphSessionStatesVector(t,e){t.startVector(4,e,4)}static endSessionState(t){return t.endObject()}static createSessionState(t,e,r){return n.startSessionState(t),n.addKernels(t,e),n.addSubGraphSessionStates(t,r),n.endSessionState(t)}}e.SessionState=n}(e.fbs||(e.fbs={}))}(t.experimental||(t.experimental={}))}(e.onnxruntime||(e.onnxruntime={})),function(t){!function(e){!function(e){class n{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsInferenceSession(t,e){return(e||new n).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsInferenceSession(t,e){return t.setPosition(t.position()+r.flatbuffers.SIZE_PREFIX_LENGTH),(e||new n).__init(t.readInt32(t.position())+t.position(),t)}static bufferHasIdentifier(t){return t.__has_identifier("ORTM")}ortVersion(t){let e=this.bb.__offset(this.bb_pos,4);return e?this.bb.__string(this.bb_pos+e,t):null}model(e){let n=this.bb.__offset(this.bb_pos,6);return n?(e||new t.experimental.fbs.Model).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}sessionState(e){let n=this.bb.__offset(this.bb_pos,8);return n?(e||new t.experimental.fbs.SessionState).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}static startInferenceSession(t){t.startObject(3)}static addOrtVersion(t,e){t.addFieldOffset(0,e,0)}static addModel(t,e){t.addFieldOffset(1,e,0)}static addSessionState(t,e){t.addFieldOffset(2,e,0)}static endInferenceSession(t){return t.endObject()}static finishInferenceSessionBuffer(t,e){t.finish(e,"ORTM")}static finishSizePrefixedInferenceSessionBuffer(t,e){t.finish(e,"ORTM",!0)}static createInferenceSession(t,e,r,i){return n.startInferenceSession(t),n.addOrtVersion(t,e),n.addModel(t,r),n.addSessionState(t,i),n.endInferenceSession(t)}}e.InferenceSession=n}(e.fbs||(e.fbs={}))}(t.experimental||(t.experimental={}))}(e.onnxruntime||(e.onnxruntime={}))},7448:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.OnnxjsSessionHandler=void 0;const r=n(8453),i=n(9162);e.OnnxjsSessionHandler=class{constructor(t){this.session=t,this.inputNames=this.session.inputNames,this.outputNames=this.session.outputNames}async dispose(){}async run(t,e,n){const o=new Map;for(const e in t)if(Object.hasOwnProperty.call(t,e)){const n=t[e];o.set(e,new i.Tensor(n.dims,n.type,void 0,void 0,n.data))}const s=await this.session.run(o),a={};return s.forEach(((t,e)=>{a[e]=new r.Tensor(t.type,t.data,t.dims)})),a}startProfiling(){this.session.startProfiling()}endProfiling(){this.session.endProfiling()}}},6919:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Session=void 0;const r=n(7067),i=n(1296),o=n(7091),s=n(1036),a=n(6231),u=n(2644);e.Session=class{constructor(t={}){this._initialized=!1,this.backendHint=t.backendHint,this.profiler=a.Profiler.create(t.profiler),this.context={profiler:this.profiler,graphInputTypes:[],graphInputDims:[]}}get inputNames(){return this._model.graph.getInputNames()}get outputNames(){return this._model.graph.getOutputNames()}startProfiling(){this.profiler.start()}endProfiling(){this.profiler.stop()}async loadModel(t,e,n){await this.profiler.event("session","Session.loadModel",(async()=>{const s=await(0,o.resolveBackend)(this.backendHint);if(this.sessionHandler=s.createSessionHandler(this.context),this._model=new u.Model,"string"==typeof t){const e=t.endsWith(".ort");if("undefined"==typeof fetch){const n=await(0,i.promisify)(r.readFile)(t);this.initialize(n,e)}else{const n=await fetch(t),r=await n.arrayBuffer();this.initialize(new Uint8Array(r),e)}}else if(ArrayBuffer.isView(t))this.initialize(t);else{const r=new Uint8Array(t,e||0,n||t.byteLength);this.initialize(r)}}))}initialize(t,e){if(this._initialized)throw new Error("already initialized");this.profiler.event("session","Session.initialize",(()=>{const n=this.sessionHandler.transformGraph?this.sessionHandler:void 0;this._model.load(t,n,e),this.sessionHandler.onGraphInitialized&&this.sessionHandler.onGraphInitialized(this._model.graph),this.initializeOps(this._model.graph),this._executionPlan=new s.ExecutionPlan(this._model.graph,this._ops,this.profiler)})),this._initialized=!0}async run(t){if(!this._initialized)throw new Error("session not initialized yet");return this.profiler.event("session","Session.run",(async()=>{const e=this.normalizeAndValidateInputs(t),n=await this._executionPlan.execute(this.sessionHandler,e);return this.createOutput(n)}))}normalizeAndValidateInputs(t){const e=this._model.graph.getInputNames();if(Array.isArray(t)){if(t.length!==e.length)throw new Error(`incorrect input array length: expected ${e.length} but got ${t.length}`)}else{if(t.size!==e.length)throw new Error(`incorrect input map size: expected ${e.length} but got ${t.size}`);const n=new Array(t.size);let r=0;for(let i=0;i<e.length;++i){const o=t.get(e[i]);if(!o)throw new Error(`missing input tensor for: '${name}'`);n[r++]=o}t=n}if(this.context.graphInputTypes&&0!==this.context.graphInputTypes.length&&this.context.graphInputDims&&0!==this.context.graphInputDims.length)this.validateInputTensorDims(this.context.graphInputDims,t,!1);else{const e=this._model.graph.getInputIndices(),n=this._model.graph.getValues(),r=new Array(e.length);for(let i=0;i<e.length;++i){const o=n[e[i]];r[i]=o.type.shape.dims,this.context.graphInputTypes.push(o.type.tensorType),this.context.graphInputDims.push(t[i].dims)}this.validateInputTensorDims(r,t,!0)}return this.validateInputTensorTypes(this.context.graphInputTypes,t),t}validateInputTensorTypes(t,e){for(let n=0;n<e.length;n++){const r=t[n],i=e[n].type;if(r!==i)throw new Error(`input tensor[${n}] check failed: expected type '${r}' but got ${i}`)}}validateInputTensorDims(t,e,n){for(let r=0;r<e.length;r++){const i=t[r],o=e[r].dims;if(!this.compareTensorDims(i,o,n))throw new Error(`input tensor[${r}] check failed: expected shape '[${i.join(",")}]' but got [${o.join(",")}]`)}}compareTensorDims(t,e,n){if(t.length!==e.length)return!1;for(let r=0;r<t.length;++r)if(t[r]!==e[r]&&(!n||0!==t[r]))return!1;return!0}createOutput(t){const e=this._model.graph.getOutputNames();if(t.length!==e.length)throw new Error("expected number of outputs do not match number of generated outputs");const n=new Map;for(let r=0;r<e.length;++r)n.set(e[r],t[r]);return n}initializeOps(t){const e=t.getNodes();this._ops=new Array(e.length);for(let n=0;n<e.length;n++)this._ops[n]=this.sessionHandler.resolve(e[n],this._model.opsets,t)}}},9162:function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.Tensor=void 0;const i=n(3442),o=r(n(3720)),s=n(1446),a=n(9395),u=n(2517);var l=a.onnxruntime.experimental.fbs;class c{get data(){if(void 0===this.cache){const t=this.dataProvider(this.dataId);if(t.length!==this.size)throw new Error("Length of data provided by the Data Provider is inconsistent with the dims of this Tensor.");this.cache=t}return this.cache}get stringData(){if("string"!==this.type)throw new TypeError("data type is not string");return this.data}get integerData(){switch(this.type){case"uint8":case"int8":case"uint16":case"int16":case"int32":case"uint32":case"bool":return this.data;default:throw new TypeError("data type is not integer (uint8, int8, uint16, int16, int32, uint32, bool)")}}get floatData(){switch(this.type){case"float32":case"float64":return this.data;default:throw new TypeError("data type is not float (float32, float64)")}}get numberData(){if("string"!==this.type)return this.data;throw new TypeError("type cannot be non-number (string)")}get(t){return this.data[u.ShapeUtil.indicesToOffset(t,this.strides)]}set(t,e){this.data[u.ShapeUtil.indicesToOffset(t,this.strides)]=e}async getData(){return void 0===this.cache&&(this.cache=await this.asyncDataProvider(this.dataId)),this.cache}get strides(){return this._strides||(this._strides=u.ShapeUtil.computeStrides(this.dims)),this._strides}constructor(t,e,n,r,o,s=i.Guid.create()){this.dims=t,this.type=e,this.dataProvider=n,this.asyncDataProvider=r,this.cache=o,this.dataId=s,this.size=u.ShapeUtil.validateDimsAndCalcSize(t);const a=this.size,l=void 0===n&&void 0===r&&void 0===o;if(void 0!==o&&o.length!==a)throw new RangeError("Input dims doesn't match data length.");if("string"===e){if(!(void 0===o||Array.isArray(o)&&o.every((t=>"string"==typeof t))))throw new TypeError("cache should be a string array");l&&(this.cache=new Array(a))}else{if(void 0!==o){const t=p(e);if(!(o instanceof t))throw new TypeError(`cache should be type ${t.name}`)}if(l){const t=new ArrayBuffer(a*function(t){switch(t){case"bool":case"int8":case"uint8":return 1;case"int16":case"uint16":return 2;case"int32":case"uint32":case"float32":return 4;case"float64":return 8;default:throw new Error(`cannot calculate sizeof() on type ${t}`)}}(e));this.cache=function(t,e){return new(p(e))(t)}(t,e)}}}static fromProto(t){if(!t)throw new Error("cannot construct Value from an empty tensor");const e=u.ProtoUtil.tensorDataTypeFromProto(t.dataType),n=u.ProtoUtil.tensorDimsFromProto(t.dims),r=new c(n,e);if("string"===e)t.stringData.forEach(((t,e)=>{r.data[e]=(0,u.decodeUtf8String)(t)}));else if(t.rawData&&"number"==typeof t.rawData.byteLength&&t.rawData.byteLength>0){const e=r.data,n=new DataView(t.rawData.buffer,t.rawData.byteOffset,t.rawData.byteLength),i=d(t.dataType),o=t.rawData.byteLength/i;if(t.rawData.byteLength%i!=0)throw new Error("invalid buffer length");if(e.length!==o)throw new Error("buffer length mismatch");for(let r=0;r<o;r++){const o=f(n,t.dataType,r*i);e[r]=o}}else{let e;switch(t.dataType){case s.onnx.TensorProto.DataType.FLOAT:e=t.floatData;break;case s.onnx.TensorProto.DataType.INT32:case s.onnx.TensorProto.DataType.INT16:case s.onnx.TensorProto.DataType.UINT16:case s.onnx.TensorProto.DataType.INT8:case s.onnx.TensorProto.DataType.UINT8:case s.onnx.TensorProto.DataType.BOOL:e=t.int32Data;break;case s.onnx.TensorProto.DataType.INT64:e=t.int64Data;break;case s.onnx.TensorProto.DataType.DOUBLE:e=t.doubleData;break;case s.onnx.TensorProto.DataType.UINT32:case s.onnx.TensorProto.DataType.UINT64:e=t.uint64Data;break;default:throw new Error("unspecific error")}if(null==e)throw new Error("failed to populate data from a tensorproto value");const n=r.data;if(n.length!==e.length)throw new Error("array length mismatch");for(let r=0;r<e.length;r++){const i=e[r];o.default.isLong(i)?n[r]=h(i,t.dataType):n[r]=i}}return r}static fromData(t,e,n){return new c(e,n,void 0,void 0,t)}static fromOrtTensor(t){if(!t)throw new Error("cannot construct Value from an empty tensor");const e=u.ProtoUtil.tensorDimsFromORTFormat(t),n=u.ProtoUtil.tensorDataTypeFromProto(t.dataType()),r=new c(e,n);if("string"===n)for(let e=0;e<t.stringDataLength();e++)r.data[e]=t.stringData(e);else if(t.rawDataArray()&&"number"==typeof t.rawDataLength()&&t.rawDataLength()>0){const e=r.data,n=new DataView(t.rawDataArray().buffer,t.rawDataArray().byteOffset,t.rawDataLength()),i=d(t.dataType()),o=t.rawDataLength()/i;if(t.rawDataLength()%i!=0)throw new Error("invalid buffer length");if(e.length!==o)throw new Error("buffer length mismatch");for(let r=0;r<o;r++){const o=f(n,t.dataType(),r*i);e[r]=o}}return r}}function d(t){switch(t){case s.onnx.TensorProto.DataType.UINT8:case s.onnx.TensorProto.DataType.INT8:case s.onnx.TensorProto.DataType.BOOL:return 1;case s.onnx.TensorProto.DataType.UINT16:case s.onnx.TensorProto.DataType.INT16:return 2;case s.onnx.TensorProto.DataType.FLOAT:case s.onnx.TensorProto.DataType.INT32:case s.onnx.TensorProto.DataType.UINT32:return 4;case s.onnx.TensorProto.DataType.INT64:case s.onnx.TensorProto.DataType.DOUBLE:case s.onnx.TensorProto.DataType.UINT64:return 8;default:throw new Error(`cannot calculate sizeof() on type ${s.onnx.TensorProto.DataType[t]}`)}}function p(t){switch(t){case"bool":case"uint8":return Uint8Array;case"int8":return Int8Array;case"int16":return Int16Array;case"uint16":return Uint16Array;case"int32":return Int32Array;case"uint32":return Uint32Array;case"float32":return Float32Array;case"float64":return Float64Array;default:throw new Error("unspecified error")}}function h(t,e){if(e===s.onnx.TensorProto.DataType.INT64||e===l.TensorDataType.INT64){if(t.greaterThanOrEqual(2147483648)||t.lessThan(-2147483648))throw new TypeError("int64 is not supported")}else{if(e!==s.onnx.TensorProto.DataType.UINT32&&e!==l.TensorDataType.UINT32&&e!==s.onnx.TensorProto.DataType.UINT64&&e!==l.TensorDataType.UINT64)throw new TypeError(`not a LONG type: ${s.onnx.TensorProto.DataType[e]}`);if(t.greaterThanOrEqual(4294967296)||t.lessThan(0))throw new TypeError("uint64 is not supported")}return t.toNumber()}function f(t,e,n){switch(e){case s.onnx.TensorProto.DataType.BOOL:case s.onnx.TensorProto.DataType.UINT8:return t.getUint8(n);case s.onnx.TensorProto.DataType.INT8:return t.getInt8(n);case s.onnx.TensorProto.DataType.UINT16:return t.getUint16(n,!0);case s.onnx.TensorProto.DataType.INT16:return t.getInt16(n,!0);case s.onnx.TensorProto.DataType.FLOAT:return t.getFloat32(n,!0);case s.onnx.TensorProto.DataType.INT32:return t.getInt32(n,!0);case s.onnx.TensorProto.DataType.UINT32:return t.getUint32(n,!0);case s.onnx.TensorProto.DataType.INT64:return h(o.default.fromBits(t.getUint32(n,!0),t.getUint32(n+4,!0),!1),e);case s.onnx.TensorProto.DataType.DOUBLE:return t.getFloat64(n,!0);case s.onnx.TensorProto.DataType.UINT64:return h(o.default.fromBits(t.getUint32(n,!0),t.getUint32(n+4,!0),!0),e);default:throw new Error(`cannot read from DataView for type ${s.onnx.TensorProto.DataType[e]}`)}}e.Tensor=c},2517:function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.decodeUtf8String=e.MAX_CLIP=e.MIN_CLIP=e.PoolConvUtil=e.ReduceUtil=e.SplitUtil=e.MathUtil=e.ShapeUtil=e.LongUtil=e.ProtoUtil=e.GemmUtil=e.arrayCopyHelper=e.BroadcastUtil=e.MatMulUtil=e.ArrayUtil=e.assert=e.checkInputsShape=void 0;const i=n(5686),o=r(n(3720)),s=n(1446),a=n(9162);e.checkInputsShape=function(t,...e){if(!t||t.length!==e.length)return!1;for(let n=0;n<t.length;n++)if(!t[n].dims||t[n].dims.length!==e[n])return!1;return!0},e.assert=function(t,e){if(!t)throw new Error("string"==typeof e?e:e())},e.ArrayUtil=class{static arraysEqual(t,e){if(t.length!==e.length)return!1;for(let n=0;n<t.length;n++)if(t[n]!==e[n])return!1;return!0}};class u{static preprocessInputShapes(t,e){return[1===t.length?[1,t[0]]:t,1===e.length?[e[0],1]:e]}static postprocessOutputShape(t,e,n){1===e&&t.splice(t.length-2,1),1===n&&t.pop()}static calcMatMulShape(t,e){return t[1]!==e[0]?void 0:[t[0],e[1]]}}e.MatMulUtil=u;class l{static calcShape(t,e,n=!1){const r=t.length,i=e.length;if(0===r)return e;if(0===i)return t;const o=Math.max(t.length,e.length),s=new Array(o);if(n){if(r<2||i<2)return;const n=u.calcMatMulShape([t[r-2],t[r-1]],[e[i-2],e[i-1]]);if(void 0===n)return;[s[o-2],s[o-1]]=n}for(let a=n?3:1;a<=o;a++){const n=r-a<0?1:t[r-a],u=i-a<0?1:e[i-a];if(n!==u&&n>1&&u>1)return;s[o-a]=Math.max(n,u)}return s}static index(t,e){const n=new Array(e.length);return l.fillIndex(t,e,n),n}static fillIndex(t,e,n){const r=t.length-e.length;for(let i=0;i<e.length;i++)n[i]=t[r+i]%e[i]}static calc(t,e,n,r,i){const o=l.calcShape(t.dims,e.dims);if(o){if(r&&!p.areEqual(o,t.dims))return;const s=p.size(o),u=r?t:new a.Tensor(o,i||t.type);if(0===o.length)u.set([],n(t.get([]),e.get([])));else{const r=new Array(o.length),i=new Array(t.dims.length),a=new Array(e.dims.length);let c,d=0,p=0,h=!1,f=!1;0===t.dims.length&&(d=t.get([]),h=!0),0===e.dims.length&&(p=e.get([]),f=!0);for(let g=0;g<s;g++){c=g;for(let t=o.length-1;t>=0;t--)r[t]=c%o[t],c=Math.floor(c/o[t]);h||(l.fillIndex(r,t.dims,i),d=t.get(i)),f||(l.fillIndex(r,e.dims,a),p=e.get(a)),u.set(r,n(d,p))}}return u}}static isValidBroadcast(t,e){const n=t.length,r=e.length;if(n>r)return!1;for(let i=1;i<=n;i++)if(1!==t[n-i]&&t[n-i]!==e[r-i])return!1;return!0}static getBroadcastDims(t,e){const n=t.length,r=[];for(let i=0;i<n;i++){const o=n-1-i,s=t[o]||1;(e[e.length-1-i]||1)>1&&1===s&&r.unshift(o)}return r}}e.BroadcastUtil=l,e.arrayCopyHelper=function(t,e,n,r,i){if(r<0||r>=e.length)throw new Error("sourceIndex out of bounds");if(n<0||n>=t.length)throw new Error("targetIndex out of bounds");if(r+i>e.length)throw new Error("source indices to be copied are outside bounds");if(n+i>t.length)throw new Error("target array is too small to hold result");for(let o=0;o<i;o++)t[n+o]=e[r+o]},e.GemmUtil=class{static getShapeOfGemmResult(t,e,n,r,i){if(2!==t.length||2!==n.length)throw new Error("shape need to be of size 2");let o,s,a;e?(o=t[1],s=t[0]):(o=t[0],s=t[1]);let u=-1;if(r?(a=n[0],u=1):(a=n[1],u=0),n[u]!==s)throw new Error("dimension mismatch");if(o<=0||a<=0||s<=0)throw new Error("invalid shape specified");if(i&&!l.isValidBroadcast(i,[o,a]))throw new Error("gemm: invalid bias shape for broadcast");return[o,a,s]}};class c{static tensorDataTypeFromProto(t){switch(t){case s.onnx.TensorProto.DataType.INT8:return"int8";case s.onnx.TensorProto.DataType.UINT8:return"uint8";case s.onnx.TensorProto.DataType.BOOL:return"bool";case s.onnx.TensorProto.DataType.INT16:return"int16";case s.onnx.TensorProto.DataType.UINT16:return"uint16";case s.onnx.TensorProto.DataType.INT32:return"int32";case s.onnx.TensorProto.DataType.UINT32:return"uint32";case s.onnx.TensorProto.DataType.FLOAT:return"float32";case s.onnx.TensorProto.DataType.DOUBLE:return"float64";case s.onnx.TensorProto.DataType.STRING:return"string";case s.onnx.TensorProto.DataType.INT64:return"int32";case s.onnx.TensorProto.DataType.UINT64:return"uint32";default:throw new Error(`unsupported data type: ${s.onnx.TensorProto.DataType[t]}`)}}static tensorDataTypeStringToEnum(t){switch(t){case"int8":return s.onnx.TensorProto.DataType.INT8;case"uint8":return s.onnx.TensorProto.DataType.UINT8;case"bool":return s.onnx.TensorProto.DataType.BOOL;case"int16":return s.onnx.TensorProto.DataType.INT16;case"uint16":return s.onnx.TensorProto.DataType.UINT16;case"int32":return s.onnx.TensorProto.DataType.INT32;case"uint32":return s.onnx.TensorProto.DataType.UINT32;case"float32":return s.onnx.TensorProto.DataType.FLOAT;case"float64":return s.onnx.TensorProto.DataType.DOUBLE;case"string":return s.onnx.TensorProto.DataType.STRING;case"int64":return s.onnx.TensorProto.DataType.INT64;case"uint64":return s.onnx.TensorProto.DataType.UINT64;default:throw new Error(`unsupported data type: ${t}`)}}static tensorDimsFromProto(t){return t.map((t=>o.default.isLong(t)?t.toNumber():t))}static tensorValueTypeFromProto(t){return{tensorType:c.tensorDataTypeFromProto(t.elemType),shape:{dims:c.tensorDimsFromProto(t.shape.dim.map((t=>t.dimValue)))}}}static tensorDimsFromORTFormat(t){const e=[];for(let n=0;n<t.dimsLength();n++)e.push(d.longToNumber(t.dims(n)));return e}static tensorAttributesFromORTFormat(t){const e=[];for(let n=0;n<t.attributesLength();n++)e.push(t.attributes(n));return e}}e.ProtoUtil=c;class d{static longToNumber(t,e){return o.default.isLong(t)?t.toNumber():t instanceof i.flatbuffers.Long?o.default.fromValue({low:t.low,high:t.high,unsigned:null!=e&&e}).toNumber():t}static isLong(t){return o.default.isLong(t)||t instanceof i.flatbuffers.Long}}e.LongUtil=d;class p{static size(t){return p.getSizeFromDimensionRange(t,0,t.length)}static sizeFromDimension(t,e){if(e<0||e>t.length)throw new Error(`invalid dimension of ${e} for sizeFromDimension as Tensor has ${t.length} dimensions.`);return p.getSizeFromDimensionRange(t,e,t.length)}static sizeToDimension(t,e){if(e<0||e>t.length)throw new Error(`invalid dimension of ${e} for sizeToDimension as Tensor has ${t.length} dimensions.`);return p.getSizeFromDimensionRange(t,0,e)}static getSizeFromDimensionRange(t,e,n){let r=1;for(let i=e;i<n;i++){if(t[i]<=0)throw new Error("cannot get valid size from specified dimension range. Most likely the range contains 0 or negative values in them.");r*=t[i]}return r}static computeStrides(t){const e=t.length;if(0===e)return[];if(1===e)return[1];const n=new Array(e);n[e-1]=1,n[e-2]=t[e-1];for(let r=e-3;r>=0;--r)n[r]=n[r+1]*t[r+1];return n}static transpose(t){return t.slice().reverse()}static indicesToOffset(t,e,n){void 0===n&&(n=t.length);let r=0;for(let i=0;i<n;++i)r+=e[i]*t[i];return r}static offsetToIndices(t,e){const n=e.length;if(0===n)return[];if(1===n)return[t*e[0]];const r=new Array(e.length);for(let n=0;n<r.length-1;++n)r[n]=Math.floor(t/e[n]),t-=r[n]*e[n];return r[r.length-1]=t,r}static normalizeAxis(t,e){if(t<-e&&t>=e)throw new Error("unsupported axis for this operation.");return t<0?t+e:t}static normalizeAxes(t,e){return t.map((t=>this.normalizeAxis(t,e)))}static incrementIndex(t,e,n){if(0===e.length||0===t.length)throw new Error("Index incrementing unsupported for scalar Tensor");if(void 0===n)n=e.length;else if(n<=0||n>e.length)throw new Error("Incorrect axis to increment on");for(let r=n-1;r>=0&&(t[r]++,!(t[r]<e[r]));--r)t[r]=0}static calculateReshapedDims(t,e){if(0===e.length){if(0===t.length||1===p.size(t))return[];throw new Error("cannot reshape to a scalar Tensor")}const n=e.length,r=new Array(n);let i=-1,o=1;for(let s=0;s<n;s++){if(e[s]<-1)throw new Error("a dimension in shape hints cannot be less than -1");if(-1===e[s]){if(-1!==i)throw new Error("at most one dimension in shape hints can be -1");i=s}else{if(0===e[s]){if(s>=t.length)throw new Error("the dimension with value zero exceeds the dimension size of the input tensor");r[s]=t[s]}else r[s]=e[s];o*=r[s]}}const s=p.size(t);if(-1!==i){if(s%o!=0)throw new Error(`the input tensor cannot be reshaped to the requested shape. Input shape: [${t}] Output shape: [${e}]`);r[i]=s/o}else if(o!==s)throw new Error("reshapedDims and originalDims don't have matching sizes");return r}static sortBasedOnPerm(t,e){return e?e.map((e=>t[e])):t.slice().reverse()}static padShape(t,e){const n=t.length;return t.map(((t,r)=>t+e[r]+e[r+n]))}static areEqual(t,e){return t.length===e.length&&t.every(((t,n)=>t===e[n]))}static validateDimsAndCalcSize(t){if(t.length>6)throw new TypeError("Only rank 0 to 6 is supported for tensor shape.");let e=1;for(const n of t){if(!Number.isInteger(n))throw new TypeError(`Invalid shape: ${n} is not an integer`);if(n<0||n>2147483647)throw new TypeError(`Invalid shape: length ${n} is not allowed`);e*=n}return e}static flattenShape(t,e){e<0&&(e+=t.length);const n=t.reduce(((t,e)=>t*e),1),r=t.slice(e).reduce(((t,e)=>t*e),1);return[n/r,r]}static squeezeShape(t,e){const n=new Array;e=p.normalizeAxes(e,t.length);for(let r=0;r<t.length;r++){const i=e.indexOf(r)>=0;if(i&&1!==t[r])throw new Error("squeeze an axis of size different than 1");(0===e.length&&t[r]>1||e.length>0&&!i)&&n.push(t[r])}return n}static unsqueezeShape(t,e){const n=new Array(t.length+e.length);n.fill(0);for(let t=0;t<e.length;t++){const r=p.normalizeAxis(e[t],n.length);if(r>=n.length)throw new Error("'axes' has an out of range axis");if(0!==n[r])throw new Error("'axes' has a duplicate axis");n[r]=1}let r=0;for(let e=0;e<n.length;e++)0===n[e]&&(n[e]=t[r++]);if(r!==t.length)throw new Error("the unsqueezed dimension could not be established");return n}}e.ShapeUtil=p,e.MathUtil=class{static sqr(t,e,n,r,i){if(r<0||r>=e.length)throw new Error("sourceIndex out of bounds");if(n<0||n>=t.length)throw new Error("targetIndex out of bounds");if(r+i>e.length)throw new Error("source indices to be copied are outside bounds");if(n+i>t.length)throw new Error("target array is too small to hold result");for(let o=0;o<i;o++)t[n+o]+=Math.pow(e[r+o],2)}static axpy(t,e,n,r,i,o){if(r<0||r>=e.length)throw new Error("sourceIndex out of bounds");if(n<0||n>=t.length)throw new Error("targetIndex out of bounds");if(r+i>e.length)throw new Error("source indices to be copied are outside bounds");if(n+i>t.length)throw new Error("target array is too small to hold result");for(let s=0;s<i;s++)t[n+s]+=o*e[r+s]}static powx(t,e,n,r,i,o){if(r<0||r>=e.length)throw new Error("sourceIndex out of bounds");if(n<0||n>=t.length)throw new Error("targetIndex out of bounds");if(r+i>e.length)throw new Error("source indices to be copied are outside bounds");if(n+i>t.length)throw new Error("target array is too small to hold result");for(let s=0;s<i;s++)t[n+s]=Math.pow(e[r+s],o)}static mul(t,e,n,r,i){if(r<0||r>=e.length)throw new Error("sourceIndex out of bounds");if(n<0||n>=t.length)throw new Error("targetIndex out of bounds");if(r+i>e.length)throw new Error("source indices to be copied are outside bounds");if(n+i>t.length)throw new Error("target array is too small to hold result");for(let o=0;o<i;o++)t[n+o]=e[r+o]*t[n+o]}};class h{static splitShape(t,e,n,r){if(0===n.length){if(!r)throw new Error("need to know number of outputs when the 'split' attribute is not specified");h.determineSplit(t[e],r,n)}const i=[],o=[0];for(let r=0;r<n.length;++r){0!==r&&o.push(o[r-1]+n[r-1]);const s=t.slice();s[e]=n[r],i.push(s)}return[i,o]}static determineSplit(t,e,n){if(t%e!=0)throw new Error("cannot split tensor to equal sized parts");for(let r=0;r<e;++r)n.push(t/e)}}e.SplitUtil=h;class f{static calcReduce(t,e,n,r,i){const o=t.dims.slice(0);0===e.length&&o.forEach(((t,n)=>e.push(n)));const s=f.calcReduceShape(o,e,!0),u=p.size(s),c=new a.Tensor(s,t.type),d=p.computeStrides(s),h=p.computeStrides(o),g=new Array(o.length);for(let n=0;n<u;n++){const s=p.offsetToIndices(n,d);l.fillIndex(s,o,g),c.set(s,f.calcReduceByAxis(t.numberData,e,o,0,p.indicesToOffset(g,h),r,i))}return n?c:new a.Tensor(f.calcReduceShape(o,e,n),c.type,void 0,void 0,c.data,c.dataId)}static calcReduceByAxis(t,e,n,r,i,o,s){let a=0;if(r>=e.length)return o(t[i]);const u=e[r],l=u>=n.length?1:p.size(n.slice(u+1));for(let c=0;c<n[u];c++)a=0===c?f.calcReduceByAxis(t,e,n,r+1,i,o,s):s(a,f.calcReduceByAxis(t,e,n,r+1,i,o,s)),i+=l;return a}static calcReduceShape(t,e,n){const r=t.slice();for(let t=0;t<e.length;t++)r[e[t]]=n?1:0;return r.filter((t=>0!==t))}}e.ReduceUtil=f;class g{static adjustPoolAttributes(t,e,n,r,i,o){if(!t&&n.length!==e.length-2)throw new Error("length of specified kernel shapes should be 2 less than length of input dimensions");if(t)for(let t=0;t<e.length-2;t++)t>=n.length?n.push(e[t+2]):n[t]=e[t+2];for(let t=0;t<n.length;t++)if(t<r.length){if(r[t]<0)throw new Error("strides should be greater than or equal to 1")}else r.push(1);for(let t=0;t<n.length;t++)if(t<i.length){if(i[t]<0)throw new Error("dilations should be greater than or equal to 1")}else i.push(1);for(let t=0;t<2*n.length;t++)if(t<o.length){if(o[t]<0)throw new Error("pad should be greater than or equal to 1")}else o.push(0);for(let t=0;t<n.length;t++){if(n[t]<=0)throw new Error("kernel shapes need to be greater than 0");if(o[t]>=n[t]||o[t+n.length]>=n[t])throw new Error("pads should be smaller than kernel")}}static adjustPadsBasedOnAutoPad(t,e,n,r,i,o){if(o){if(i.length!==2*(t.length-2))throw new Error("length of pads should be twice the length of data dimensions");if(e.length!==t.length-2)throw new Error("length of strides should be the length of data dimensions");if(r.length!==t.length-2)throw new Error("length of kernel shapes should be the length of data dimensions");for(let s=0;s<t.length-2;s++)g.adjustPadAndReturnShape(t[s+2],e[s],n[s],r[s],i,s,s+t.length-2,o)}}static computePoolOutputShape(t,e,n,r,i,o,s){if(e.length<=0)throw new Error("input shape must be of size greater than 0");const a=[e[0],e[1]];return g.computeShapeHelper(t,e,a,n,r,i,o,s),a}static computeConvOutputShape(t,e,n,r,i,o,s){if(t.length<=0||e.length<=0)throw new Error("invalid input tensor dims or invalid filter tensor dims");const a=[t[0],e[0]];return g.computeShapeHelper(!1,t,a,n,r,i,o,s),a}static computeShapeHelper(t,e,n,r,i,o,s,a){if(t)for(let t=0;t<e.length-2;t++)n.push(1);else for(let t=0;t<e.length-2;t++)n.push(g.adjustPadAndReturnShape(e[t+2],r[t],i[t],o[t],s,t,t+e.length-2,a))}static adjustPadAndReturnShape(t,e,n,r,i,o,s,a){const u=n*(r-1)+1;if(!a||"NOTSET"===a)return Math.floor((t+i[o]+i[s]-u)/e+1);switch(a){case"VALID":return i[o]=0,i[s]=0,Math.floor((t-u)/e+1);case"SAME_LOWER":case"SAME_UPPER":if(1!==n)throw new Error("Dilation not supported for SAME_UPPER or SAME_LOWER");{const n=((t+e-1)/e-1)*e+r-t;return i[o]="SAME_LOWER"===a?Math.floor((n+1)/2):Math.floor(n/2),i[s]=n-i[o],Math.floor((t+n-r)/e+1)}default:throw new Error("Unsupported AutoPad type")}}}e.PoolConvUtil=g,e.MIN_CLIP=-34028234663852886e22,e.MAX_CLIP=34028234663852886e22,e.decodeUtf8String=function(t){return(new TextDecoder).decode(t)}},7067:()=>{},1296:()=>{},5686:(t,e,n)=>{"use strict";n.r(e),n.d(e,{flatbuffers:()=>r});var r={};r.Offset,r.Table,r.SIZEOF_SHORT=2,r.SIZEOF_INT=4,r.FILE_IDENTIFIER_LENGTH=4,r.SIZE_PREFIX_LENGTH=4,r.Encoding={UTF8_BYTES:1,UTF16_STRING:2},r.int32=new Int32Array(2),r.float32=new Float32Array(r.int32.buffer),r.float64=new Float64Array(r.int32.buffer),r.isLittleEndian=1===new Uint16Array(new Uint8Array([1,0]).buffer)[0],r.Long=function(t,e){this.low=0|t,this.high=0|e},r.Long.create=function(t,e){return 0==t&&0==e?r.Long.ZERO:new r.Long(t,e)},r.Long.prototype.toFloat64=function(){return(this.low>>>0)+4294967296*this.high},r.Long.prototype.equals=function(t){return this.low==t.low&&this.high==t.high},r.Long.ZERO=new r.Long(0,0),r.Builder=function(t){if(t)e=t;else var e=1024;this.bb=r.ByteBuffer.allocate(e),this.space=e,this.minalign=1,this.vtable=null,this.vtable_in_use=0,this.isNested=!1,this.object_start=0,this.vtables=[],this.vector_num_elems=0,this.force_defaults=!1},r.Builder.prototype.clear=function(){this.bb.clear(),this.space=this.bb.capacity(),this.minalign=1,this.vtable=null,this.vtable_in_use=0,this.isNested=!1,this.object_start=0,this.vtables=[],this.vector_num_elems=0,this.force_defaults=!1},r.Builder.prototype.forceDefaults=function(t){this.force_defaults=t},r.Builder.prototype.dataBuffer=function(){return this.bb},r.Builder.prototype.asUint8Array=function(){return this.bb.bytes().subarray(this.bb.position(),this.bb.position()+this.offset())},r.Builder.prototype.prep=function(t,e){t>this.minalign&&(this.minalign=t);for(var n=1+~(this.bb.capacity()-this.space+e)&t-1;this.space<n+t+e;){var i=this.bb.capacity();this.bb=r.Builder.growByteBuffer(this.bb),this.space+=this.bb.capacity()-i}this.pad(n)},r.Builder.prototype.pad=function(t){for(var e=0;e<t;e++)this.bb.writeInt8(--this.space,0)},r.Builder.prototype.writeInt8=function(t){this.bb.writeInt8(this.space-=1,t)},r.Builder.prototype.writeInt16=function(t){this.bb.writeInt16(this.space-=2,t)},r.Builder.prototype.writeInt32=function(t){this.bb.writeInt32(this.space-=4,t)},r.Builder.prototype.writeInt64=function(t){this.bb.writeInt64(this.space-=8,t)},r.Builder.prototype.writeFloat32=function(t){this.bb.writeFloat32(this.space-=4,t)},r.Builder.prototype.writeFloat64=function(t){this.bb.writeFloat64(this.space-=8,t)},r.Builder.prototype.addInt8=function(t){this.prep(1,0),this.writeInt8(t)},r.Builder.prototype.addInt16=function(t){this.prep(2,0),this.writeInt16(t)},r.Builder.prototype.addInt32=function(t){this.prep(4,0),this.writeInt32(t)},r.Builder.prototype.addInt64=function(t){this.prep(8,0),this.writeInt64(t)},r.Builder.prototype.addFloat32=function(t){this.prep(4,0),this.writeFloat32(t)},r.Builder.prototype.addFloat64=function(t){this.prep(8,0),this.writeFloat64(t)},r.Builder.prototype.addFieldInt8=function(t,e,n){(this.force_defaults||e!=n)&&(this.addInt8(e),this.slot(t))},r.Builder.prototype.addFieldInt16=function(t,e,n){(this.force_defaults||e!=n)&&(this.addInt16(e),this.slot(t))},r.Builder.prototype.addFieldInt32=function(t,e,n){(this.force_defaults||e!=n)&&(this.addInt32(e),this.slot(t))},r.Builder.prototype.addFieldInt64=function(t,e,n){!this.force_defaults&&e.equals(n)||(this.addInt64(e),this.slot(t))},r.Builder.prototype.addFieldFloat32=function(t,e,n){(this.force_defaults||e!=n)&&(this.addFloat32(e),this.slot(t))},r.Builder.prototype.addFieldFloat64=function(t,e,n){(this.force_defaults||e!=n)&&(this.addFloat64(e),this.slot(t))},r.Builder.prototype.addFieldOffset=function(t,e,n){(this.force_defaults||e!=n)&&(this.addOffset(e),this.slot(t))},r.Builder.prototype.addFieldStruct=function(t,e,n){e!=n&&(this.nested(e),this.slot(t))},r.Builder.prototype.nested=function(t){if(t!=this.offset())throw new Error("FlatBuffers: struct must be serialized inline.")},r.Builder.prototype.notNested=function(){if(this.isNested)throw new Error("FlatBuffers: object serialization must not be nested.")},r.Builder.prototype.slot=function(t){this.vtable[t]=this.offset()},r.Builder.prototype.offset=function(){return this.bb.capacity()-this.space},r.Builder.growByteBuffer=function(t){var e=t.capacity();if(3221225472&e)throw new Error("FlatBuffers: cannot grow buffer beyond 2 gigabytes.");var n=e<<1,i=r.ByteBuffer.allocate(n);return i.setPosition(n-e),i.bytes().set(t.bytes(),n-e),i},r.Builder.prototype.addOffset=function(t){this.prep(r.SIZEOF_INT,0),this.writeInt32(this.offset()-t+r.SIZEOF_INT)},r.Builder.prototype.startObject=function(t){this.notNested(),null==this.vtable&&(this.vtable=[]),this.vtable_in_use=t;for(var e=0;e<t;e++)this.vtable[e]=0;this.isNested=!0,this.object_start=this.offset()},r.Builder.prototype.endObject=function(){if(null==this.vtable||!this.isNested)throw new Error("FlatBuffers: endObject called without startObject");this.addInt32(0);for(var t=this.offset(),e=this.vtable_in_use-1;e>=0&&0==this.vtable[e];e--);for(var n=e+1;e>=0;e--)this.addInt16(0!=this.vtable[e]?t-this.vtable[e]:0);this.addInt16(t-this.object_start);var i=(n+2)*r.SIZEOF_SHORT;this.addInt16(i);var o=0,s=this.space;t:for(e=0;e<this.vtables.length;e++){var a=this.bb.capacity()-this.vtables[e];if(i==this.bb.readInt16(a)){for(var u=r.SIZEOF_SHORT;u<i;u+=r.SIZEOF_SHORT)if(this.bb.readInt16(s+u)!=this.bb.readInt16(a+u))continue t;o=this.vtables[e];break}}return o?(this.space=this.bb.capacity()-t,this.bb.writeInt32(this.space,o-t)):(this.vtables.push(this.offset()),this.bb.writeInt32(this.bb.capacity()-t,this.offset()-t)),this.isNested=!1,t},r.Builder.prototype.finish=function(t,e,n){var i=n?r.SIZE_PREFIX_LENGTH:0;if(e){var o=e;if(this.prep(this.minalign,r.SIZEOF_INT+r.FILE_IDENTIFIER_LENGTH+i),o.length!=r.FILE_IDENTIFIER_LENGTH)throw new Error("FlatBuffers: file identifier must be length "+r.FILE_IDENTIFIER_LENGTH);for(var s=r.FILE_IDENTIFIER_LENGTH-1;s>=0;s--)this.writeInt8(o.charCodeAt(s))}this.prep(this.minalign,r.SIZEOF_INT+i),this.addOffset(t),i&&this.addInt32(this.bb.capacity()-this.space),this.bb.setPosition(this.space)},r.Builder.prototype.finishSizePrefixed=function(t,e){this.finish(t,e,!0)},r.Builder.prototype.requiredField=function(t,e){var n=this.bb.capacity()-t,r=n-this.bb.readInt32(n);if(0==this.bb.readInt16(r+e))throw new Error("FlatBuffers: field "+e+" must be set")},r.Builder.prototype.startVector=function(t,e,n){this.notNested(),this.vector_num_elems=e,this.prep(r.SIZEOF_INT,t*e),this.prep(n,t*e)},r.Builder.prototype.endVector=function(){return this.writeInt32(this.vector_num_elems),this.offset()},r.Builder.prototype.createString=function(t){if(t instanceof Uint8Array)var e=t;else{e=[];for(var n=0;n<t.length;){var r,i=t.charCodeAt(n++);(r=i<55296||i>=56320?i:(i<<10)+t.charCodeAt(n++)+-56613888)<128?e.push(r):(r<2048?e.push(r>>6&31|192):(r<65536?e.push(r>>12&15|224):e.push(r>>18&7|240,r>>12&63|128),e.push(r>>6&63|128)),e.push(63&r|128))}}this.addInt8(0),this.startVector(1,e.length,1),this.bb.setPosition(this.space-=e.length),n=0;for(var o=this.space,s=this.bb.bytes();n<e.length;n++)s[o++]=e[n];return this.endVector()},r.Builder.prototype.createLong=function(t,e){return r.Long.create(t,e)},r.ByteBuffer=function(t){this.bytes_=t,this.position_=0},r.ByteBuffer.allocate=function(t){return new r.ByteBuffer(new Uint8Array(t))},r.ByteBuffer.prototype.clear=function(){this.position_=0},r.ByteBuffer.prototype.bytes=function(){return this.bytes_},r.ByteBuffer.prototype.position=function(){return this.position_},r.ByteBuffer.prototype.setPosition=function(t){this.position_=t},r.ByteBuffer.prototype.capacity=function(){return this.bytes_.length},r.ByteBuffer.prototype.readInt8=function(t){return this.readUint8(t)<<24>>24},r.ByteBuffer.prototype.readUint8=function(t){return this.bytes_[t]},r.ByteBuffer.prototype.readInt16=function(t){return this.readUint16(t)<<16>>16},r.ByteBuffer.prototype.readUint16=function(t){return this.bytes_[t]|this.bytes_[t+1]<<8},r.ByteBuffer.prototype.readInt32=function(t){return this.bytes_[t]|this.bytes_[t+1]<<8|this.bytes_[t+2]<<16|this.bytes_[t+3]<<24},r.ByteBuffer.prototype.readUint32=function(t){return this.readInt32(t)>>>0},r.ByteBuffer.prototype.readInt64=function(t){return new r.Long(this.readInt32(t),this.readInt32(t+4))},r.ByteBuffer.prototype.readUint64=function(t){return new r.Long(this.readUint32(t),this.readUint32(t+4))},r.ByteBuffer.prototype.readFloat32=function(t){return r.int32[0]=this.readInt32(t),r.float32[0]},r.ByteBuffer.prototype.readFloat64=function(t){return r.int32[r.isLittleEndian?0:1]=this.readInt32(t),r.int32[r.isLittleEndian?1:0]=this.readInt32(t+4),r.float64[0]},r.ByteBuffer.prototype.writeInt8=function(t,e){this.bytes_[t]=e},r.ByteBuffer.prototype.writeUint8=function(t,e){this.bytes_[t]=e},r.ByteBuffer.prototype.writeInt16=function(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8},r.ByteBuffer.prototype.writeUint16=function(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8},r.ByteBuffer.prototype.writeInt32=function(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8,this.bytes_[t+2]=e>>16,this.bytes_[t+3]=e>>24},r.ByteBuffer.prototype.writeUint32=function(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8,this.bytes_[t+2]=e>>16,this.bytes_[t+3]=e>>24},r.ByteBuffer.prototype.writeInt64=function(t,e){this.writeInt32(t,e.low),this.writeInt32(t+4,e.high)},r.ByteBuffer.prototype.writeUint64=function(t,e){this.writeUint32(t,e.low),this.writeUint32(t+4,e.high)},r.ByteBuffer.prototype.writeFloat32=function(t,e){r.float32[0]=e,this.writeInt32(t,r.int32[0])},r.ByteBuffer.prototype.writeFloat64=function(t,e){r.float64[0]=e,this.writeInt32(t,r.int32[r.isLittleEndian?0:1]),this.writeInt32(t+4,r.int32[r.isLittleEndian?1:0])},r.ByteBuffer.prototype.getBufferIdentifier=function(){if(this.bytes_.length<this.position_+r.SIZEOF_INT+r.FILE_IDENTIFIER_LENGTH)throw new Error("FlatBuffers: ByteBuffer is too short to contain an identifier.");for(var t="",e=0;e<r.FILE_IDENTIFIER_LENGTH;e++)t+=String.fromCharCode(this.readInt8(this.position_+r.SIZEOF_INT+e));return t},r.ByteBuffer.prototype.__offset=function(t,e){var n=t-this.readInt32(t);return e<this.readInt16(n)?this.readInt16(n+e):0},r.ByteBuffer.prototype.__union=function(t,e){return t.bb_pos=e+this.readInt32(e),t.bb=this,t},r.ByteBuffer.prototype.__string=function(t,e){t+=this.readInt32(t);var n=this.readInt32(t),i="",o=0;if(t+=r.SIZEOF_INT,e===r.Encoding.UTF8_BYTES)return this.bytes_.subarray(t,t+n);for(;o<n;){var s,a=this.readUint8(t+o++);if(a<192)s=a;else{var u=this.readUint8(t+o++);if(a<224)s=(31&a)<<6|63&u;else{var l=this.readUint8(t+o++);s=a<240?(15&a)<<12|(63&u)<<6|63&l:(7&a)<<18|(63&u)<<12|(63&l)<<6|63&this.readUint8(t+o++)}}s<65536?i+=String.fromCharCode(s):(s-=65536,i+=String.fromCharCode(55296+(s>>10),56320+(1023&s)))}return i},r.ByteBuffer.prototype.__indirect=function(t){return t+this.readInt32(t)},r.ByteBuffer.prototype.__vector=function(t){return t+this.readInt32(t)+r.SIZEOF_INT},r.ByteBuffer.prototype.__vector_len=function(t){return this.readInt32(t+this.readInt32(t))},r.ByteBuffer.prototype.__has_identifier=function(t){if(t.length!=r.FILE_IDENTIFIER_LENGTH)throw new Error("FlatBuffers: file identifier must be length "+r.FILE_IDENTIFIER_LENGTH);for(var e=0;e<r.FILE_IDENTIFIER_LENGTH;e++)if(t.charCodeAt(e)!=this.readInt8(this.position_+r.SIZEOF_INT+e))return!1;return!0},r.ByteBuffer.prototype.createLong=function(t,e){return r.Long.create(t,e)}}},__webpack_module_cache__={};function __webpack_require__(t){var e=__webpack_module_cache__[t];if(void 0!==e)return e.exports;var n=__webpack_module_cache__[t]={exports:{}};return __webpack_modules__[t].call(n.exports,n,n.exports,__webpack_require__),n.exports}__webpack_require__.d=(t,e)=>{for(var n in e)__webpack_require__.o(e,n)&&!__webpack_require__.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},__webpack_require__.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),__webpack_require__.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),__webpack_require__.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var __webpack_exports__=__webpack_require__(6018);return __webpack_exports__})()));
//# sourceMappingURL=ort.webgl.min.js.map