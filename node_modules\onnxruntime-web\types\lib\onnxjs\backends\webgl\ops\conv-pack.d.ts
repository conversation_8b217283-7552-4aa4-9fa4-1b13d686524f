import { Tensor } from '../../../tensor';
import { WebGLInferenceHandler } from '../inference-handler';
import { ConvAttributes } from './conv';
export declare const conv2DPackedPointwise: (inferenceHandler: WebGLInferenceHandler, inputs: readonly Tensor[], attributes: ConvAttributes) => Tensor;
export declare const conv2DPacked: (inferenceHandler: WebGLInferenceHandler, inputs: readonly Tensor[], attributes: ConvAttributes) => Tensor;
