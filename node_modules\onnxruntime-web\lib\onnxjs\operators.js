"use strict";
// Copyright (c) Microsoft Corporation. All rights reserved.
// Licensed under the MIT License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.FLOAT_TYPES = exports.INT_TYPES = exports.NUMBER_TYPES = void 0;
exports.NUMBER_TYPES = ['float32', 'float64', 'int32', 'int16', 'int8', 'uint16', 'uint32', 'uint8'];
exports.INT_TYPES = ['int32', 'int16', 'int8', 'uint16', 'uint32', 'uint8'];
exports.FLOAT_TYPES = ['float32', 'float64'];
//# sourceMappingURL=operators.js.map