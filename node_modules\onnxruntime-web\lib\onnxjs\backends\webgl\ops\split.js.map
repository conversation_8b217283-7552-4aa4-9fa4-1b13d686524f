{"version": 3, "file": "split.js", "sourceRoot": "", "sources": ["split.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAElC,gFAAqG;AAIrG,wCAAmD;AAEnD,oCAAkD;AAQlD,MAAM,oBAAoB,GAAG;IAC3B,IAAI,EAAE,OAAO;IACb,UAAU,EAAE,CAAC,GAAG,CAAC;IACjB,UAAU,EAAE,CAAC,mBAAW,CAAC,QAAQ,CAAC;CACnC,CAAC;AAEK,MAAM,KAAK,GACd,CAAC,gBAAuC,EAAE,MAAgB,EAAE,UAA2B,EAAY,EAAE;IACnG,cAAc,CAAC,MAAM,CAAC,CAAC;IAEvB,MAAM,IAAI,GAAG,gBAAS,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC7E,MAAM,KAAK,GAAG,eAAe,CAAC,gBAAgB,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;IAC1E,MAAM,MAAM,GAAa,EAAE,CAAC;IAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,EAAE,CAAC,EAAE;QAC9B,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,iCAEvB,oBAAoB,KACvB,SAAS,EAAE,GAAG,UAAU,CAAC,QAAQ,IAAI,CAAC,EAAE,EACxC,GAAG,EAAE,GAAG,EAAE,CAAC,sBAAsB,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,KAErF,MAAM,CAAC,CAAC,CAAC;KACd;IAED,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAlBO,QAAA,KAAK,SAkBZ;AAEC,MAAM,oBAAoB,GAA4C,CAAC,IAAgB,EAAmB,EAAE;IACjH,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IAC/C,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;IACnD,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;IACvC,OAAO,IAAA,sDAA2B,EAAC,EAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAC,CAAC,CAAC;AAChE,CAAC,CAAC;AALW,QAAA,oBAAoB,wBAK/B;AAEF,MAAM,eAAe,GACjB,CAAC,gBAAuC,EAAE,MAAgB,EAAE,IAAY,EAAE,UAA2B,EAAU,EAAE;IAC/G,MAAM,CAAC,EAAE,OAAO,CAAC,GAAG,gBAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC;IACxG,OAAO,OAAO,CAAC,MAAM,CAAC;AACxB,CAAC,CAAC;AAEN,MAAM,sBAAsB,GACxB,CAAC,gBAAuC,EAAE,KAAa,EAAE,UAA2B,EAAE,IAAY,EAAE,KAAa,EACjG,EAAE;IACZ,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,gBAAS,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC;IAC1G,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;IAC9B,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IAClC,MAAM,IAAI,GAAG,WAAW,CAAC,MAAM,CAAC;IAChC,MAAM,YAAY,GAAG;kCACG,IAAI;kBACpB,IAAI,QAAQ,MAAM;;;KAG/B,CAAC;IACI,uCACK,oBAAoB,KACvB,SAAS,EAAE,GAAG,UAAU,CAAC,QAAQ,IAAI,KAAK,EAAE,EAC5C,MAAM,EAAE,EAAC,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,WAAW,EAAE,mBAAW,CAAC,QAAQ,EAAC,EAChF,YAAY,IACZ;AACJ,CAAC,CAAC;AAEV,MAAM,cAAc,GAAG,CAAC,MAAgB,EAAQ,EAAE;IAChD,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QAClC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;KAC9C;IAED,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO;QACrF,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ;QACxF,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,EAAE;QAC7F,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;KACxC;AACH,CAAC,CAAC"}