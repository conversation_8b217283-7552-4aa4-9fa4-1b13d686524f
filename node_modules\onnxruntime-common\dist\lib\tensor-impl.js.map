{"version": 3, "file": "tensor-impl.js", "sourceRoot": "", "sources": ["../../lib/tensor-impl.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,kCAAkC;AAYlC,MAAM,wBAAwB,GAAG,OAAO,aAAa,KAAK,WAAW,IAAI,OAAO,aAAa,CAAC,IAAI,KAAK,UAAU,CAAC;AAClH,MAAM,yBAAyB,GAAG,OAAO,cAAc,KAAK,WAAW,IAAI,OAAO,cAAc,CAAC,IAAI,KAAK,UAAU,CAAC;AAErH,kGAAkG;AAClG,MAAM,qCAAqC,GAAG,IAAI,GAAG,CAA0C;IAC7F,CAAC,SAAS,EAAE,YAAY,CAAC;IACzB,CAAC,OAAO,EAAE,UAAU,CAAC;IACrB,CAAC,MAAM,EAAE,SAAS,CAAC;IACnB,CAAC,QAAQ,EAAE,WAAW,CAAC;IACvB,CAAC,OAAO,EAAE,UAAU,CAAC;IACrB,CAAC,OAAO,EAAE,UAAU,CAAC;IACrB,CAAC,MAAM,EAAE,UAAU,CAAC;IACpB,CAAC,SAAS,EAAE,YAAY,CAAC;IACzB,CAAC,QAAQ,EAAE,WAAW,CAAC;CACxB,CAAC,CAAC;AAEH,kGAAkG;AAClG,MAAM,qCAAqC,GAAG,IAAI,GAAG,CAA8C;IACjG,CAAC,YAAY,EAAE,SAAS,CAAC;IACzB,CAAC,UAAU,EAAE,OAAO,CAAC;IACrB,CAAC,SAAS,EAAE,MAAM,CAAC;IACnB,CAAC,WAAW,EAAE,QAAQ,CAAC;IACvB,CAAC,UAAU,EAAE,OAAO,CAAC;IACrB,CAAC,UAAU,EAAE,OAAO,CAAC;IACrB,CAAC,YAAY,EAAE,SAAS,CAAC;IACzB,CAAC,WAAW,EAAE,QAAQ,CAAC;CACxB,CAAC,CAAC;AAEH,IAAI,wBAAwB,EAAE;IAC5B,qCAAqC,CAAC,GAAG,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;IAClE,qCAAqC,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;CACnE;AACD,IAAI,yBAAyB,EAAE;IAC7B,qCAAqC,CAAC,GAAG,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;IACpE,qCAAqC,CAAC,GAAG,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;CACrE;AAED;;;;GAIG;AACH,MAAM,aAAa,GAAG,CAAC,IAAwB,EAAU,EAAE;IACzD,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACpC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACpB,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE;YACzD,MAAM,IAAI,SAAS,CAAC,QAAQ,CAAC,8BAA8B,GAAG,EAAE,CAAC,CAAC;SACnE;QACD,IAAI,GAAG,GAAG,CAAC,EAAE;YACX,MAAM,IAAI,UAAU,CAAC,QAAQ,CAAC,0CAA0C,GAAG,EAAE,CAAC,CAAC;SAChF;QACD,IAAI,IAAI,GAAG,CAAC;KACb;IACD,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,MAAM,OAAO,MAAM;IAIjB,YACI,IAAkD,EAAE,IAA0D,EAC9G,IAAwB;QAC1B,IAAI,IAAgB,CAAC;QACrB,IAAI,IAAoB,CAAC;QACzB,IAAI,IAA6B,CAAC;QAClC,qCAAqC;QACrC,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC5B,EAAE;YACF,yCAAyC;YACzC,EAAE;YACF,IAAI,GAAG,IAAI,CAAC;YACZ,IAAI,GAAG,IAAI,CAAC;YACZ,IAAI,IAAI,KAAK,QAAQ,EAAE;gBACrB,gBAAgB;gBAChB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBACxB,MAAM,IAAI,SAAS,CAAC,iDAAiD,CAAC,CAAC;iBACxE;gBACD,4GAA4G;gBAC5G,uCAAuC;gBACvC,IAAI,GAAG,IAAI,CAAC;aACb;iBAAM;gBACL,iBAAiB;gBACjB,MAAM,qBAAqB,GAAG,qCAAqC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAC9E,IAAI,qBAAqB,KAAK,SAAS,EAAE;oBACvC,MAAM,IAAI,SAAS,CAAC,4BAA4B,IAAI,GAAG,CAAC,CAAC;iBAC1D;gBACD,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBACvB,4GAA4G;oBAC5G,qBAAqB;oBACrB,8EAA8E;oBAC9E,8DAA8D;oBAC9D,IAAI,GAAI,qBAA6B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBAClD;qBAAM,IAAI,IAAI,YAAY,qBAAqB,EAAE;oBAChD,IAAI,GAAG,IAAI,CAAC;iBACb;qBAAM;oBACL,MAAM,IAAI,SAAS,CAAC,KAAK,IAAI,kCAAkC,qBAAqB,EAAE,CAAC,CAAC;iBACzF;aACF;SACF;aAAM;YACL,EAAE;YACF,mCAAmC;YACnC,EAAE;YACF,IAAI,GAAG,IAAI,CAAC;YACZ,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACvB,2CAA2C;gBAC3C,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;oBACrB,MAAM,IAAI,SAAS,CAAC,qDAAqD,CAAC,CAAC;iBAC5E;gBACD,MAAM,gBAAgB,GAAG,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;gBACxC,IAAI,gBAAgB,KAAK,QAAQ,EAAE;oBACjC,IAAI,GAAG,QAAQ,CAAC;oBAChB,IAAI,GAAG,IAAI,CAAC;iBACb;qBAAM,IAAI,gBAAgB,KAAK,SAAS,EAAE;oBACzC,IAAI,GAAG,MAAM,CAAC;oBACd,0GAA0G;oBAC1G,gDAAgD;oBAChD,8DAA8D;oBAC9D,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,IAAa,CAAC,CAAC;iBACvC;qBAAM;oBACL,MAAM,IAAI,SAAS,CAAC,uCAAuC,gBAAgB,GAAG,CAAC,CAAC;iBACjF;aACF;iBAAM;gBACL,kCAAkC;gBAClC,MAAM,UAAU,GACZ,qCAAqC,CAAC,GAAG,CAAC,IAAI,CAAC,WAA8C,CAAC,CAAC;gBACnG,IAAI,UAAU,KAAK,SAAS,EAAE;oBAC5B,MAAM,IAAI,SAAS,CAAC,qCAAqC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;iBAC/E;gBACD,IAAI,GAAG,UAAU,CAAC;gBAClB,IAAI,GAAG,IAA2B,CAAC;aACpC;SACF;QAED,kDAAkD;QAClD,IAAI,IAAI,KAAK,SAAS,EAAE;YACtB,oCAAoC;YACpC,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACtB;aAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YAC/B,MAAM,IAAI,SAAS,CAAC,yCAAyC,CAAC,CAAC;SAChE;QAED,gBAAgB;QAChB,MAAM,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;QACjC,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,EAAE;YACxB,MAAM,IAAI,KAAK,CAAC,iBAAiB,IAAI,gCAAgC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC;SACvF;QAED,IAAI,CAAC,IAAI,GAAG,IAAyB,CAAC;QACtC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IACD,aAAa;IACb;;;;;;OAMG;IACK,MAAM,CAAC,cAAc,CAAC,MAAmC,EAAE,OAA+B;QAChG,IAAI,MAAM,KAAK,SAAS,EAAE;YACxB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;SACjD;QACD,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE;YAC/D,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;SAC3D;QAED,MAAM,EAAC,MAAM,EAAE,KAAK,EAAC,GAAG,OAAO,CAAC;QAEhC,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QAC1B,IAAI,QAAgB,CAAC;QACrB,IAAI,QAAgB,CAAC;QACrB,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE;YACjD,QAAQ,GAAG,GAAG,CAAC;SAChB;aAAM;YACL,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;SACtB;QACD,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE;YACjD,QAAQ,GAAG,CAAC,CAAC;SACd;aAAM;YACL,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;SACtB;QAED,MAAM,WAAW,GAAG,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC;QACvF,qEAAqE;QAErE,MAAM,YAAY,GAAG,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC,CAAC;YACrD,CAAC,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;YACrE,KAAK,CAAC;QACV,MAAM,MAAM,GAAG,MAAM,GAAG,KAAK,CAAC;QAC9B,MAAM,WAAW,GAAG,YAAY,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAE1G,8BAA8B;QAC9B,IAAI,IAAI,GAAG,CAAC,EAAE,aAAa,GAAG,CAAC,EAAE,aAAa,GAAG,CAAC,EAAE,aAAa,GAAG,CAAC,EAAE,aAAa,GAAG,CAAC,CAAC;QACzF,IAAI,cAAc,GAAG,CAAC,EAAE,cAAc,GAAG,MAAM,EAAE,cAAc,GAAG,MAAM,GAAG,CAAC,EAAE,cAAc,GAAG,CAAC,CAAC,CAAC;QAElG,mEAAmE;QACnE,IAAI,WAAW,KAAK,KAAK,EAAE;YACzB,IAAI,GAAG,CAAC,CAAC;YACT,aAAa,GAAG,CAAC,CAAC;YAClB,aAAa,GAAG,CAAC,CAAC;YAClB,aAAa,GAAG,CAAC,CAAC;YAClB,aAAa,GAAG,CAAC,CAAC,CAAC;SACpB;QAED,qEAAqE;QACrE,IAAI,YAAY,KAAK,MAAM,EAAE;YAC3B,cAAc,GAAG,MAAM,GAAG,CAAC,CAAC;SAC7B;aAAM,IAAI,YAAY,KAAK,KAAK,EAAE;YACjC,cAAc,GAAG,CAAC,CAAC;YACnB,cAAc,GAAG,MAAM,CAAC;YACxB,cAAc,GAAG,MAAM,GAAG,CAAC,CAAC;SAC7B;aAAM,IAAI,YAAY,KAAK,KAAK,EAAE;YACjC,cAAc,GAAG,CAAC,CAAC;YACnB,cAAc,GAAG,MAAM,CAAC;YACxB,cAAc,GAAG,MAAM,GAAG,CAAC,CAAC;SAC7B;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EACrB,CAAC,EAAE,EAAE,aAAa,IAAI,IAAI,EAAE,aAAa,IAAI,IAAI,EAAE,aAAa,IAAI,IAAI,EAAE,aAAa,IAAI,IAAI,EAAE;YACpG,WAAW,CAAC,cAAc,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC;YAC9E,WAAW,CAAC,cAAc,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC;YAC9E,WAAW,CAAC,cAAc,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC;YAC9E,IAAI,cAAc,KAAK,CAAC,CAAC,IAAI,aAAa,KAAK,CAAC,CAAC,EAAE;gBACjD,WAAW,CAAC,cAAc,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC;aAC/E;SACF;QAED,6BAA6B;QAC7B,MAAM,YAAY,GAAG,YAAY,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,SAAS,EAAE,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;YAC3D,IAAI,MAAM,CAAC,SAAS,EAAE,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;QACzG,OAAO,YAAY,CAAC;IACtB,CAAC;IAQD,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,KAAoD,EAAE,OAAgC;QAE3G,oCAAoC;QACpC,MAAM,cAAc,GAAG,OAAO,CAAC,gBAAgB,CAAC,KAAK,WAAW,IAAI,KAAK,YAAY,gBAAgB,CAAC;QACtG,MAAM,cAAc,GAAG,OAAO,CAAC,SAAS,CAAC,KAAK,WAAW,IAAI,KAAK,YAAY,SAAS,CAAC;QACxF,MAAM,aAAa,GAAG,OAAO,CAAC,WAAW,CAAC,KAAK,WAAW,IAAI,KAAK,YAAY,WAAW,CAAC;QAC3F,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,WAAW,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC;QAExG,IAAI,IAAiC,CAAC;QACtC,IAAI,YAAY,GAA2B,EAAE,CAAC;QAE9C,mDAAmD;QACnD,IAAI,cAAc,EAAE;YAClB,8DAA8D;YAC9D,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAChD,MAAM,eAAe,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAEhD,IAAI,eAAe,IAAI,IAAI,EAAE;gBAC3B,IAAI,MAAM,GAAG,KAAK,CAAC,aAAa,CAAC;gBACjC,IAAI,KAAK,GAAG,KAAK,CAAC,YAAY,CAAC;gBAE/B,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,CAAC,aAAa,KAAK,SAAS,IAAI,OAAO,CAAC,YAAY,KAAK,SAAS,EAAE;oBACtG,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC;oBAC/B,KAAK,GAAG,OAAO,CAAC,YAAY,CAAC;iBAC9B;gBAED,IAAI,OAAO,KAAK,SAAS,EAAE;oBACzB,YAAY,GAAG,OAAO,CAAC;oBACvB,IAAI,OAAO,CAAC,YAAY,KAAK,SAAS,EAAE;wBACtC,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;qBAChF;yBAAM;wBACL,YAAY,CAAC,YAAY,GAAG,MAAM,CAAC;qBACpC;oBACD,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,EAAE;wBAC7D,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;qBACrF;yBAAM;wBACL,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC;qBAC9B;oBACD,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,EAAE;wBAC1D,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC,CAAC;qBACnF;yBAAM;wBACL,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;qBAC5B;iBACF;qBAAM;oBACL,YAAY,CAAC,YAAY,GAAG,MAAM,CAAC;oBACnC,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC;oBAC7B,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;iBAC5B;gBAED,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;gBACrB,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;gBAEvB,eAAe,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;gBACtD,IAAI,GAAG,eAAe,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC;aAC/D;iBAAM;gBACL,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;aAC9C;SAEF;aAAM,IAAI,cAAc,EAAE;YACzB,uDAAuD;YACvD,MAAM,MAAM,GAAG,MAAM,CAAC;YACtB,IAAI,MAAc,CAAC;YACnB,IAAI,KAAa,CAAC;YAElB,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,CAAC,YAAY,KAAK,SAAS,IAAI,OAAO,CAAC,aAAa,KAAK,SAAS,EAAE;gBACtG,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC;gBAC/B,KAAK,GAAG,OAAO,CAAC,YAAY,CAAC;aAC9B;iBAAM;gBACL,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;gBACtB,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;aACrB;YAED,IAAI,OAAO,KAAK,SAAS,EAAE;gBACzB,YAAY,GAAG,OAAO,CAAC;gBACvB,IAAI,OAAO,CAAC,YAAY,KAAK,SAAS,IAAI,OAAO,CAAC,YAAY,KAAK,MAAM,EAAE;oBACzE,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;iBACzE;qBAAM;oBACL,YAAY,CAAC,YAAY,GAAG,MAAM,CAAC;iBACpC;aACF;iBAAM;gBACL,YAAY,CAAC,YAAY,GAAG,MAAM,CAAC;aACpC;YAED,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC;YAC7B,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;YAE3B,IAAI,OAAO,KAAK,SAAS,EAAE;gBACzB,MAAM,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;gBAEpD,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC;gBACzB,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC;gBAE3B,MAAM,eAAe,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBAEpD,IAAI,eAAe,IAAI,IAAI,EAAE;oBAC3B,eAAe,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC1C,IAAI,GAAG,eAAe,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC;iBAC/D;qBAAM;oBACL,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;iBAC9C;aACF;iBAAM;gBACL,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;aACnB;SAEF;aAAM,IAAI,aAAa,EAAE;YACxB,+DAA+D;YAC/D,IAAI,OAAO,KAAK,SAAS,EAAE;gBACzB,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;aAC5E;YACD,IAAI,OAAO,CAAC,YAAY,KAAK,SAAS,EAAE;gBACtC,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;aAC9E;YAED,MAAM,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAE1E,IAAI,eAAe,IAAI,IAAI,EAAE;gBAC3B,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;gBAC5B,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;gBAC1B,eAAe,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;gBACtD,IAAI,GAAG,eAAe,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC;gBAC9D,IAAI,OAAO,KAAK,SAAS,EAAE;oBACzB,yDAAyD;oBACzD,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,EAAE;wBAC7D,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;qBAChF;yBAAM;wBACL,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC;qBAC9B;oBACD,yDAAyD;oBACzD,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,EAAE;wBAC1D,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;qBAC9E;yBAAM;wBACL,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;qBAC5B;iBACF;qBAAM;oBACL,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC;oBAC7B,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;iBAC5B;gBACD,OAAO,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;aAClD;iBAAM;gBACL,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;aAC9C;SAEF;aAAM,IAAI,KAAK,EAAE;YAChB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACrC,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;gBAChD,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBACxC,IAAI,CAAC,KAAK,IAAI,CAAC,OAAO,EAAE;oBACtB,OAAO,MAAM,EAAE,CAAC;iBACjB;gBACD,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAE,CAAC;gBAC7B,QAAQ,CAAC,WAAW,GAAG,WAAW,CAAC;gBACnC,QAAQ,CAAC,GAAG,GAAG,KAAe,CAAC;gBAC/B,QAAQ,CAAC,MAAM,GAAG,GAAG,EAAE;oBACrB,MAAM,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;oBAC9B,MAAM,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;oBAChC,OAAO,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;oBAC/D,MAAM,GAAG,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;oBACpE,IAAI,OAAO,KAAK,SAAS,EAAE;wBACzB,yDAAyD;wBACzD,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,EAAE;4BACpE,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;yBAChF;6BAAM;4BACL,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;yBACrC;wBACD,yDAAyD;wBACzD,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,IAAI,OAAO,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,EAAE;4BACjE,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;yBAC9E;6BAAM;4BACL,YAAY,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;yBACnC;qBACF;yBAAM;wBACL,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;wBACpC,YAAY,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;qBACnC;oBACD,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC;gBACzD,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC;SACJ;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC,CAAC;SACnF;QAED,IAAI,IAAI,KAAK,SAAS,EAAE;YACtB,OAAO,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;SAClD;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC,CAAC;SACnF;IACH,CAAC;IAED,WAAW,CAAC,OAAkC;;QAC5C,MAAM,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAC1E,IAAI,KAAgB,CAAC;QACrB,IAAI,eAAe,IAAI,IAAI,EAAE;YAC3B,+CAA+C;YAC/C,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE9B,MAAM,WAAW,GAAG,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YAC5G,MAAM,QAAQ,GAAG,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAA,MAAA,OAAO,CAAC,IAAI,0CAAE,IAAI,MAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;YAC5G,MAAM,QAAQ,GAAG,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAA,MAAA,OAAO,CAAC,IAAI,0CAAE,IAAI,MAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxG,MAAM,MAAM,GAAG,MAAM,GAAG,KAAK,CAAC;YAE9B,IAAI,OAAO,KAAK,SAAS,EAAE;gBACzB,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,EAAE;oBAC7D,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;iBAC5E;gBACD,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,EAAE;oBAC1D,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;iBAC1E;gBACD,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,IAAI,CAAC,QAAQ,KAAK,CAAC,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,CAAC;oBAC7E,CAAC,QAAQ,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,KAAK,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC,EAAE;oBAC9E,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;iBACnE;aACF;YAED,8BAA8B;YAC9B,MAAM,IAAI,GAAG,CAAC,CAAC;YACf,IAAI,aAAa,GAAG,CAAC,EAAE,aAAa,GAAG,CAAC,EAAE,aAAa,GAAG,CAAC,EAAE,aAAa,GAAG,CAAC,CAAC;YAC/E,IAAI,cAAc,GAAG,CAAC,EAAE,cAAc,GAAG,MAAM,EAAE,cAAc,GAAG,MAAM,GAAG,CAAC,EAAE,cAAc,GAAG,CAAC,CAAC,CAAC;YAElG,mEAAmE;YACnE,IAAI,WAAW,KAAK,MAAM,EAAE;gBAC1B,cAAc,GAAG,CAAC,CAAC;gBACnB,cAAc,GAAG,MAAM,CAAC;gBACxB,cAAc,GAAG,MAAM,GAAG,CAAC,CAAC;gBAC5B,cAAc,GAAG,MAAM,GAAG,CAAC,CAAC;aAC7B;iBAAM,IAAI,WAAW,KAAK,KAAK,EAAE;gBAChC,cAAc,GAAG,CAAC,CAAC;gBACnB,cAAc,GAAG,MAAM,CAAC;gBACxB,cAAc,GAAG,MAAM,GAAG,CAAC,CAAC;aAC7B;iBAAM,IAAI,WAAW,KAAK,KAAK,EAAE;gBAChC,cAAc,GAAG,CAAC,CAAC;gBACnB,cAAc,GAAG,MAAM,CAAC;gBACxB,cAAc,GAAG,MAAM,GAAG,CAAC,CAAC;aAC7B;YAED,KAAK,GAAG,eAAe,CAAC,eAAe,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAEvD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,GAAG,KAAK,EAC7B,aAAa,IAAI,IAAI,EAAE,aAAa,IAAI,IAAI,EAAE,aAAa,IAAI,IAAI,EAAE,aAAa,IAAI,IAAI,EAAE,CAAC,EAAE,EAAE;gBACpG,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAY,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,CAAE,UAAU;gBACxG,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAY,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,CAAE,UAAU;gBACxG,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAY,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,CAAE,UAAU;gBACxG,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC;oBACrB,cAAc,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAY,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,CAAE,UAAU;aAC/G;SAEF;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;SAC9C;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAOD,aAAa;IAEb,2BAA2B;IAC3B,OAAO,CAAC,IAAuB;QAC7B,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAChD,CAAC;CAEF"}