/*!
* ONNX Runtime Web v1.14.0
* Copyright (c) Microsoft Corporation. All rights reserved.
* Licensed under the MIT License.
*/
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.ort=t():e.ort=t()}(self,(()=>(()=>{var e={453:(e,t,n)=>{"use strict";n.r(t),n.d(t,{InferenceSession:()=>p,Tensor:()=>h,env:()=>a,registerBackend:()=>o});const r={},i=[],o=(e,t,n)=>{if(!t||"function"!=typeof t.init||"function"!=typeof t.createSessionHandler)throw new TypeError("not a valid backend");{const o=r[e];if(void 0===o)r[e]={backend:t,priority:n};else{if(o.priority>n)return;if(o.priority===n&&o.backend!==t)throw new Error(`cannot register backend "${e}" using priority ${n}`)}if(n>=0){const t=i.indexOf(e);-1!==t&&i.splice(t,1);for(let t=0;t<i.length;t++)if(r[i[t]].priority<=n)return void i.splice(t,0,e);i.push(e)}}},a=new class{constructor(){this.wasm={},this.webgl={},this.logLevelInternal="warning"}set logLevel(e){if(void 0!==e){if("string"!=typeof e||-1===["verbose","info","warning","error","fatal"].indexOf(e))throw new Error(`Unsupported logging level: ${e}`);this.logLevelInternal=e}}get logLevel(){return this.logLevelInternal}},s="undefined"!=typeof BigInt64Array&&"function"==typeof BigInt64Array.from,u="undefined"!=typeof BigUint64Array&&"function"==typeof BigUint64Array.from,c=new Map([["float32",Float32Array],["uint8",Uint8Array],["int8",Int8Array],["uint16",Uint16Array],["int16",Int16Array],["int32",Int32Array],["bool",Uint8Array],["float64",Float64Array],["uint32",Uint32Array]]),l=new Map([[Float32Array,"float32"],[Uint8Array,"uint8"],[Int8Array,"int8"],[Uint16Array,"uint16"],[Int16Array,"int16"],[Int32Array,"int32"],[Float64Array,"float64"],[Uint32Array,"uint32"]]);s&&(c.set("int64",BigInt64Array),l.set(BigInt64Array,"int64")),u&&(c.set("uint64",BigUint64Array),l.set(BigUint64Array,"uint64"));class f{constructor(e,t,n){let r,i,o;if("string"==typeof e)if(r=e,o=n,"string"===e){if(!Array.isArray(t))throw new TypeError("A string tensor's data must be a string array.");i=t}else{const n=c.get(e);if(void 0===n)throw new TypeError(`Unsupported tensor type: ${e}.`);if(Array.isArray(t))i=n.from(t);else{if(!(t instanceof n))throw new TypeError(`A ${r} tensor's data must be type of ${n}`);i=t}}else if(o=t,Array.isArray(e)){if(0===e.length)throw new TypeError("Tensor type cannot be inferred from an empty array.");const t=typeof e[0];if("string"===t)r="string",i=e;else{if("boolean"!==t)throw new TypeError(`Invalid element type of data array: ${t}.`);r="bool",i=Uint8Array.from(e)}}else{const t=l.get(e.constructor);if(void 0===t)throw new TypeError(`Unsupported type for tensor data: ${e.constructor}.`);r=t,i=e}if(void 0===o)o=[i.length];else if(!Array.isArray(o))throw new TypeError("A tensor's dims must be a number array");const a=(e=>{let t=1;for(let n=0;n<e.length;n++){const r=e[n];if("number"!=typeof r||!Number.isSafeInteger(r))throw new TypeError(`dims[${n}] must be an integer, got: ${r}`);if(r<0)throw new RangeError(`dims[${n}] must be a non-negative integer, got: ${r}`);t*=r}return t})(o);if(a!==i.length)throw new Error(`Tensor's size(${a}) does not match data length(${i.length}).`);this.dims=o,this.type=r,this.data=i,this.size=a}static bufferToTensor(e,t){if(void 0===e)throw new Error("Image buffer must be defined");if(void 0===t.height||void 0===t.width)throw new Error("Image height and width must be defined");const{height:n,width:r}=t,i=t.norm;let o,a;o=void 0===i||void 0===i.mean?255:i.mean,a=void 0===i||void 0===i.bias?0:i.bias;const s=void 0!==t.bitmapFormat?t.bitmapFormat:"RGBA",u=void 0!==t.tensorFormat&&void 0!==t.tensorFormat?t.tensorFormat:"RGB",c=n*r,l="RGBA"===u?new Float32Array(4*c):new Float32Array(3*c);let h=4,d=0,p=1,y=2,g=3,m=0,b=c,w=2*c,v=-1;"RGB"===s&&(h=3,d=0,p=1,y=2,g=-1),"RGBA"===u?v=3*c:"RBG"===u?(m=0,w=c,b=2*c):"BGR"===u&&(w=0,b=c,m=2*c);for(let t=0;t<c;t++,d+=h,y+=h,p+=h,g+=h)l[m++]=(e[d]+a)/o,l[b++]=(e[p]+a)/o,l[w++]=(e[y]+a)/o,-1!==v&&-1!==g&&(l[v++]=(e[g]+a)/o);return new f("float32",l,"RGBA"===u?[1,4,n,r]:[1,3,n,r])}static async fromImage(e,t){const n="undefined"!=typeof HTMLImageElement&&e instanceof HTMLImageElement,r="undefined"!=typeof ImageData&&e instanceof ImageData,i="undefined"!=typeof ImageBitmap&&e instanceof ImageBitmap,o="undefined"!=typeof String&&(e instanceof String||"string"==typeof e);let a,s={};if(n){const n=document.createElement("canvas"),r=n.getContext("2d");if(null==r)throw new Error("Can not access image data");{let i=e.naturalHeight,o=e.naturalWidth;if(void 0!==t&&void 0!==t.resizedHeight&&void 0!==t.resizedWidth&&(i=t.resizedHeight,o=t.resizedWidth),void 0!==t){if(s=t,void 0!==t.tensorFormat)throw new Error("Image input config format must be RGBA for HTMLImageElement");if(s.tensorFormat="RGBA",void 0!==t.height&&t.height!==i)throw new Error("Image input config height doesn't match HTMLImageElement height");if(s.height=i,void 0!==t.width&&t.width!==o)throw new Error("Image input config width doesn't match HTMLImageElement width");s.width=o}else s.tensorFormat="RGBA",s.height=i,s.width=o;n.width=o,n.height=i,r.drawImage(e,0,0,o,i),a=r.getImageData(0,0,o,i).data}}else{if(!r){if(i){if(void 0===t)throw new Error("Please provide image config with format for Imagebitmap");if(void 0!==t.bitmapFormat)throw new Error("Image input config format must be defined for ImageBitmap");const n=document.createElement("canvas").getContext("2d");if(null!=n){const r=e.height,i=e.width;if(n.drawImage(e,0,0,i,r),a=n.getImageData(0,0,i,r).data,void 0!==t){if(void 0!==t.height&&t.height!==r)throw new Error("Image input config height doesn't match ImageBitmap height");if(s.height=r,void 0!==t.width&&t.width!==i)throw new Error("Image input config width doesn't match ImageBitmap width");s.width=i}else s.height=r,s.width=i;return f.bufferToTensor(a,s)}throw new Error("Can not access image data")}if(o)return new Promise(((n,r)=>{const i=document.createElement("canvas"),o=i.getContext("2d");if(!e||!o)return r();const a=new Image;a.crossOrigin="Anonymous",a.src=e,a.onload=()=>{i.width=a.width,i.height=a.height,o.drawImage(a,0,0,i.width,i.height);const e=o.getImageData(0,0,i.width,i.height);if(void 0!==t){if(void 0!==t.height&&t.height!==i.height)throw new Error("Image input config height doesn't match ImageBitmap height");if(s.height=i.height,void 0!==t.width&&t.width!==i.width)throw new Error("Image input config width doesn't match ImageBitmap width");s.width=i.width}else s.height=i.height,s.width=i.width;n(f.bufferToTensor(e.data,s))}}));throw new Error("Input data provided is not supported - aborted tensor creation")}{const n="RGBA";let r,i;if(void 0!==t&&void 0!==t.resizedWidth&&void 0!==t.resizedHeight?(r=t.resizedHeight,i=t.resizedWidth):(r=e.height,i=e.width),void 0!==t){if(s=t,void 0!==t.bitmapFormat&&t.bitmapFormat!==n)throw new Error("Image input config format must be RGBA for ImageData");s.bitmapFormat="RGBA"}else s.bitmapFormat="RGBA";if(s.height=r,s.width=i,void 0!==t){const t=document.createElement("canvas");t.width=i,t.height=r;const n=t.getContext("2d");if(null==n)throw new Error("Can not access image data");n.putImageData(e,0,0),a=n.getImageData(0,0,i,r).data}else a=e.data}}if(void 0!==a)return f.bufferToTensor(a,s);throw new Error("Input data provided is not supported - aborted tensor creation")}toImageData(e){var t,n;const r=document.createElement("canvas").getContext("2d");let i;if(null==r)throw new Error("Can not access image data");{const o=this.dims[3],a=this.dims[2],s=this.dims[1],u=void 0!==e&&void 0!==e.format?e.format:"RGB",c=void 0!==e&&void 0!==(null===(t=e.norm)||void 0===t?void 0:t.mean)?e.norm.mean:255,l=void 0!==e&&void 0!==(null===(n=e.norm)||void 0===n?void 0:n.bias)?e.norm.bias:0,f=a*o;if(void 0!==e){if(void 0!==e.height&&e.height!==a)throw new Error("Image output config height doesn't match tensor height");if(void 0!==e.width&&e.width!==o)throw new Error("Image output config width doesn't match tensor width");if(void 0!==e.format&&4===s&&"RGBA"!==e.format||3===s&&"RGB"!==e.format&&"BGR"!==e.format)throw new Error("Tensor format doesn't match input tensor dims")}const h=4;let d=0,p=1,y=2,g=3,m=0,b=f,w=2*f,v=-1;"RGBA"===u?(m=0,b=f,w=2*f,v=3*f):"RGB"===u?(m=0,b=f,w=2*f):"RBG"===u&&(m=0,w=f,b=2*f),i=r.createImageData(o,a);for(let e=0;e<a*o;d+=h,p+=h,y+=h,g+=h,e++)i.data[d]=(this.data[m++]-l)*c,i.data[p]=(this.data[b++]-l)*c,i.data[y]=(this.data[w++]-l)*c,i.data[g]=-1===v?255:(this.data[v++]-l)*c}return i}reshape(e){return new f(this.type,this.data,e)}}const h=f;class d{constructor(e){this.handler=e}async run(e,t,n){const r={};let i={};if("object"!=typeof e||null===e||e instanceof h||Array.isArray(e))throw new TypeError("'feeds' must be an object that use input names as keys and OnnxValue as corresponding values.");let o=!0;if("object"==typeof t){if(null===t)throw new TypeError("Unexpected argument[1]: cannot be null.");if(t instanceof h)throw new TypeError("'fetches' cannot be a Tensor");if(Array.isArray(t)){if(0===t.length)throw new TypeError("'fetches' cannot be an empty array.");o=!1;for(const e of t){if("string"!=typeof e)throw new TypeError("'fetches' must be a string array or an object.");if(-1===this.outputNames.indexOf(e))throw new RangeError(`'fetches' contains invalid output name: ${e}.`);r[e]=null}if("object"==typeof n&&null!==n)i=n;else if(void 0!==n)throw new TypeError("'options' must be an object.")}else{let e=!1;const a=Object.getOwnPropertyNames(t);for(const n of this.outputNames)if(-1!==a.indexOf(n)){const i=t[n];(null===i||i instanceof h)&&(e=!0,o=!1,r[n]=i)}if(e){if("object"==typeof n&&null!==n)i=n;else if(void 0!==n)throw new TypeError("'options' must be an object.")}else i=t}}else if(void 0!==t)throw new TypeError("Unexpected argument[1]: must be 'fetches' or 'options'.");for(const t of this.inputNames)if(void 0===e[t])throw new Error(`input '${t}' is missing in 'feeds'.`);if(o)for(const e of this.outputNames)r[e]=null;const a=await this.handler.run(e,r,i),s={};for(const e in a)Object.hasOwnProperty.call(a,e)&&(s[e]=new h(a[e].type,a[e].data,a[e].dims));return s}static async create(e,t,n,o){let a,s={};if("string"==typeof e){if(a=e,"object"==typeof t&&null!==t)s=t;else if(void 0!==t)throw new TypeError("'options' must be an object.")}else if(e instanceof Uint8Array){if(a=e,"object"==typeof t&&null!==t)s=t;else if(void 0!==t)throw new TypeError("'options' must be an object.")}else{if(!(e instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&e instanceof SharedArrayBuffer))throw new TypeError("Unexpected argument[0]: must be 'path' or 'buffer'.");{const r=e;let i=0,u=e.byteLength;if("object"==typeof t&&null!==t)s=t;else if("number"==typeof t){if(i=t,!Number.isSafeInteger(i))throw new RangeError("'byteOffset' must be an integer.");if(i<0||i>=r.byteLength)throw new RangeError(`'byteOffset' is out of range [0, ${r.byteLength}).`);if(u=e.byteLength-i,"number"==typeof n){if(u=n,!Number.isSafeInteger(u))throw new RangeError("'byteLength' must be an integer.");if(u<=0||i+u>r.byteLength)throw new RangeError(`'byteLength' is out of range (0, ${r.byteLength-i}].`);if("object"==typeof o&&null!==o)s=o;else if(void 0!==o)throw new TypeError("'options' must be an object.")}else if(void 0!==n)throw new TypeError("'byteLength' must be a number.")}else if(void 0!==t)throw new TypeError("'options' must be an object.");a=new Uint8Array(r,i,u)}}const u=(s.executionProviders||[]).map((e=>"string"==typeof e?e:e.name)),c=await(async e=>{const t=0===e.length?i:e,n=[];for(const e of t){const t=r[e];if(t){if(t.initialized)return t.backend;if(t.aborted)continue;const r=!!t.initPromise;try{return r||(t.initPromise=t.backend.init()),await t.initPromise,t.initialized=!0,t.backend}catch(i){r||n.push({name:e,err:i}),t.aborted=!0}finally{delete t.initPromise}}}throw new Error(`no available backend found. ERR: ${n.map((e=>`[${e.name}] ${e.err}`)).join(", ")}`)})(u),l=await c.createSessionHandler(a,s);return new d(l)}startProfiling(){this.handler.startProfiling()}endProfiling(){this.handler.endProfiling()}get inputNames(){return this.handler.inputNames}get outputNames(){return this.handler.outputNames}}const p=d},932:(e,t,n)=>{var _scriptDir,r=(_scriptDir=(_scriptDir="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0)||"/index.js",function(e){var t,r,i;e=e||{},t||(t=void 0!==e?e:{}),t.ready=new Promise((function(e,t){r=e,i=t}));var o,a,s,u,c,l,f=Object.assign({},t),h="./this.program",d=(e,t)=>{throw t},p="object"==typeof window,y="function"==typeof importScripts,g="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,m="";g?(m=y?n(908).dirname(m)+"/":"//",l=()=>{c||(u=n(384),c=n(908))},o=function(e,t){return l(),e=c.normalize(e),u.readFileSync(e,t?void 0:"utf8")},s=e=>((e=o(e,!0)).buffer||(e=new Uint8Array(e)),e),a=(e,t,n)=>{l(),e=c.normalize(e),u.readFile(e,(function(e,r){e?n(e):t(r.buffer)}))},1<process.argv.length&&(h=process.argv[1].replace(/\\/g,"/")),process.argv.slice(2),process.on("uncaughtException",(function(e){if(!(e instanceof J))throw e})),process.on("unhandledRejection",(function(e){throw e})),d=(e,t)=>{if(_||0<H)throw process.exitCode=e,t;t instanceof J||v("exiting due to exception: "+t),process.exit(e)},t.inspect=function(){return"[Emscripten Module object]"}):(p||y)&&(y?m=self.location.href:"undefined"!=typeof document&&document.currentScript&&(m=document.currentScript.src),_scriptDir&&(m=_scriptDir),m=0!==m.indexOf("blob:")?m.substr(0,m.replace(/[?#].*/,"").lastIndexOf("/")+1):"",o=e=>{var t=new XMLHttpRequest;return t.open("GET",e,!1),t.send(null),t.responseText},y&&(s=e=>{var t=new XMLHttpRequest;return t.open("GET",e,!1),t.responseType="arraybuffer",t.send(null),new Uint8Array(t.response)}),a=(e,t,n)=>{var r=new XMLHttpRequest;r.open("GET",e,!0),r.responseType="arraybuffer",r.onload=()=>{200==r.status||0==r.status&&r.response?t(r.response):n()},r.onerror=n,r.send(null)});var b,w=t.print||console.log.bind(console),v=t.printErr||console.warn.bind(console);Object.assign(t,f),f=null,t.thisProgram&&(h=t.thisProgram),t.quit&&(d=t.quit),t.wasmBinary&&(b=t.wasmBinary);var _=t.noExitRuntime||!1;"object"!=typeof WebAssembly&&Y("no native wasm support detected");var A,O,E,S,T,I,C=!1,P="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function R(e,t,n){var r=(t>>>=0)+n;for(n=t;e[n]&&!(n>=r);)++n;if(16<n-t&&e.buffer&&P)return P.decode(e.subarray(t,n));for(r="";t<n;){var i=e[t++];if(128&i){var o=63&e[t++];if(192==(224&i))r+=String.fromCharCode((31&i)<<6|o);else{var a=63&e[t++];65536>(i=224==(240&i)?(15&i)<<12|o<<6|a:(7&i)<<18|o<<12|a<<6|63&e[t++])?r+=String.fromCharCode(i):(i-=65536,r+=String.fromCharCode(55296|i>>10,56320|1023&i))}}else r+=String.fromCharCode(i)}return r}function x(e,t){return(e>>>=0)?R(S,e,t):""}function j(e,t,n,r){if(!(0<r))return 0;var i=n>>>=0;r=n+r-1;for(var o=0;o<e.length;++o){var a=e.charCodeAt(o);if(55296<=a&&57343>=a&&(a=65536+((1023&a)<<10)|1023&e.charCodeAt(++o)),127>=a){if(n>=r)break;t[n++>>>0]=a}else{if(2047>=a){if(n+1>=r)break;t[n++>>>0]=192|a>>6}else{if(65535>=a){if(n+2>=r)break;t[n++>>>0]=224|a>>12}else{if(n+3>=r)break;t[n++>>>0]=240|a>>18,t[n++>>>0]=128|a>>12&63}t[n++>>>0]=128|a>>6&63}t[n++>>>0]=128|63&a}}return t[n>>>0]=0,n-i}function M(e){for(var t=0,n=0;n<e.length;++n){var r=e.charCodeAt(n);127>=r?t++:2047>=r?t+=2:55296<=r&&57343>=r?(t+=4,++n):t+=3}return t}function F(){var e=A.buffer;O=e,t.HEAP8=E=new Int8Array(e),t.HEAP16=new Int16Array(e),t.HEAP32=T=new Int32Array(e),t.HEAPU8=S=new Uint8Array(e),t.HEAPU16=new Uint16Array(e),t.HEAPU32=I=new Uint32Array(e),t.HEAPF32=new Float32Array(e),t.HEAPF64=new Float64Array(e)}var U,D=[],B=[],L=[],z=[],H=0;function k(){var e=t.preRun.shift();D.unshift(e)}var W,G=0,$=null,N=null;function Y(e){throw t.onAbort&&t.onAbort(e),v(e="Aborted("+e+")"),C=!0,e=new WebAssembly.RuntimeError(e+". Build with -sASSERTIONS for more info."),i(e),e}function V(){return W.startsWith("data:application/octet-stream;base64,")}if(W="ort-wasm.wasm",!V()){var X=W;W=t.locateFile?t.locateFile(X,m):m+X}function q(){var e=W;try{if(e==W&&b)return new Uint8Array(b);if(s)return s(e);throw"both async and sync fetching of the wasm failed"}catch(e){Y(e)}}function J(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}function K(e){for(;0<e.length;)e.shift()(t)}var Z=[],Q=0,ee=0;function te(e){this.Db=e,this.zb=e-24,this.Ub=function(e){I[this.zb+4>>2>>>0]=e},this.Eb=function(){return I[this.zb+4>>2>>>0]},this.Sb=function(e){I[this.zb+8>>2>>>0]=e},this.Wb=function(){return I[this.zb+8>>2>>>0]},this.Tb=function(){T[this.zb>>2>>>0]=0},this.Ib=function(e){E[this.zb+12>>0>>>0]=e?1:0},this.Pb=function(){return 0!=E[this.zb+12>>0>>>0]},this.Jb=function(e){E[this.zb+13>>0>>>0]=e?1:0},this.Lb=function(){return 0!=E[this.zb+13>>0>>>0]},this.Rb=function(e,t){this.Fb(0),this.Ub(e),this.Sb(t),this.Tb(),this.Ib(!1),this.Jb(!1)},this.Nb=function(){T[this.zb>>2>>>0]+=1},this.Xb=function(){var e=T[this.zb>>2>>>0];return T[this.zb>>2>>>0]=e-1,1===e},this.Fb=function(e){I[this.zb+16>>2>>>0]=e},this.Ob=function(){return I[this.zb+16>>2>>>0]},this.Qb=function(){if(Ie(this.Eb()))return I[this.Db>>2>>>0];var e=this.Ob();return 0!==e?e:this.Db}}function ne(e){return we(new te(e).zb)}var re=[];function ie(e){var t=re[e];return t||(e>=re.length&&(re.length=e+1),re[e]=t=U.get(e)),t}function oe(e){var t=M(e)+1,n=be(t);return n&&j(e,E,n,t),n}var ae={};function se(){if(!ue){var e,t={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:h||"./this.program"};for(e in ae)void 0===ae[e]?delete t[e]:t[e]=ae[e];var n=[];for(e in t)n.push(e+"="+t[e]);ue=n}return ue}var ue,ce=[null,[],[]];function le(e,t){var n=ce[e];0===t||10===t?((1===e?w:v)(R(n,0)),n.length=0):n.push(t)}var fe=0;function he(e){return 0==e%4&&(0!=e%100||0==e%400)}var de=[31,29,31,30,31,30,31,31,30,31,30,31],pe=[31,28,31,30,31,30,31,31,30,31,30,31];function ye(e,t,n,r){function i(e,t,n){for(e="number"==typeof e?e.toString():e||"";e.length<t;)e=n[0]+e;return e}function o(e,t){return i(e,t,"0")}function a(e,t){function n(e){return 0>e?-1:0<e?1:0}var r;return 0===(r=n(e.getFullYear()-t.getFullYear()))&&0===(r=n(e.getMonth()-t.getMonth()))&&(r=n(e.getDate()-t.getDate())),r}function s(e){switch(e.getDay()){case 0:return new Date(e.getFullYear()-1,11,29);case 1:return e;case 2:return new Date(e.getFullYear(),0,3);case 3:return new Date(e.getFullYear(),0,2);case 4:return new Date(e.getFullYear(),0,1);case 5:return new Date(e.getFullYear()-1,11,31);case 6:return new Date(e.getFullYear()-1,11,30)}}function u(e){var t=e.Bb;for(e=new Date(new Date(e.Cb+1900,0,1).getTime());0<t;){var n=e.getMonth(),r=(he(e.getFullYear())?de:pe)[n];if(!(t>r-e.getDate())){e.setDate(e.getDate()+t);break}t-=r-e.getDate()+1,e.setDate(1),11>n?e.setMonth(n+1):(e.setMonth(0),e.setFullYear(e.getFullYear()+1))}return n=new Date(e.getFullYear()+1,0,4),t=s(new Date(e.getFullYear(),0,4)),n=s(n),0>=a(t,e)?0>=a(n,e)?e.getFullYear()+1:e.getFullYear():e.getFullYear()-1}var c=T[r+40>>2>>>0];for(var l in r={$b:T[r>>2>>>0],Zb:T[r+4>>2>>>0],Gb:T[r+8>>2>>>0],Kb:T[r+12>>2>>>0],Hb:T[r+16>>2>>>0],Cb:T[r+20>>2>>>0],Ab:T[r+24>>2>>>0],Bb:T[r+28>>2>>>0],bc:T[r+32>>2>>>0],Yb:T[r+36>>2>>>0],ac:c?x(c):""},n=x(n),c={"%c":"%a %b %d %H:%M:%S %Y","%D":"%m/%d/%y","%F":"%Y-%m-%d","%h":"%b","%r":"%I:%M:%S %p","%R":"%H:%M","%T":"%H:%M:%S","%x":"%m/%d/%y","%X":"%H:%M:%S","%Ec":"%c","%EC":"%C","%Ex":"%m/%d/%y","%EX":"%H:%M:%S","%Ey":"%y","%EY":"%Y","%Od":"%d","%Oe":"%e","%OH":"%H","%OI":"%I","%Om":"%m","%OM":"%M","%OS":"%S","%Ou":"%u","%OU":"%U","%OV":"%V","%Ow":"%w","%OW":"%W","%Oy":"%y"})n=n.replace(new RegExp(l,"g"),c[l]);var f="Sunday Monday Tuesday Wednesday Thursday Friday Saturday".split(" "),h="January February March April May June July August September October November December".split(" ");for(l in c={"%a":function(e){return f[e.Ab].substring(0,3)},"%A":function(e){return f[e.Ab]},"%b":function(e){return h[e.Hb].substring(0,3)},"%B":function(e){return h[e.Hb]},"%C":function(e){return o((e.Cb+1900)/100|0,2)},"%d":function(e){return o(e.Kb,2)},"%e":function(e){return i(e.Kb,2," ")},"%g":function(e){return u(e).toString().substring(2)},"%G":function(e){return u(e)},"%H":function(e){return o(e.Gb,2)},"%I":function(e){return 0==(e=e.Gb)?e=12:12<e&&(e-=12),o(e,2)},"%j":function(e){for(var t=0,n=0;n<=e.Hb-1;t+=(he(e.Cb+1900)?de:pe)[n++]);return o(e.Kb+t,3)},"%m":function(e){return o(e.Hb+1,2)},"%M":function(e){return o(e.Zb,2)},"%n":function(){return"\n"},"%p":function(e){return 0<=e.Gb&&12>e.Gb?"AM":"PM"},"%S":function(e){return o(e.$b,2)},"%t":function(){return"\t"},"%u":function(e){return e.Ab||7},"%U":function(e){return o(Math.floor((e.Bb+7-e.Ab)/7),2)},"%V":function(e){var t=Math.floor((e.Bb+7-(e.Ab+6)%7)/7);if(2>=(e.Ab+371-e.Bb-2)%7&&t++,t)53==t&&(4==(n=(e.Ab+371-e.Bb)%7)||3==n&&he(e.Cb)||(t=1));else{t=52;var n=(e.Ab+7-e.Bb-1)%7;(4==n||5==n&&he(e.Cb%400-1))&&t++}return o(t,2)},"%w":function(e){return e.Ab},"%W":function(e){return o(Math.floor((e.Bb+7-(e.Ab+6)%7)/7),2)},"%y":function(e){return(e.Cb+1900).toString().substring(2)},"%Y":function(e){return e.Cb+1900},"%z":function(e){var t=0<=(e=e.Yb);return e=Math.abs(e)/60,(t?"+":"-")+String("0000"+(e/60*100+e%60)).slice(-4)},"%Z":function(e){return e.ac},"%%":function(){return"%"}},n=n.replace(/%%/g,"\0\0"),c)n.includes(l)&&(n=n.replace(new RegExp(l,"g"),c[l](r)));return l=function(e){var t=Array(M(e)+1);return j(e,t,0,t.length),t}(n=n.replace(/\0\0/g,"%")),l.length>t?0:(E.set(l,e>>>0),l.length-1)}var ge={a:function(e){return be(e+24)+24},m:function(e){return(e=new te(e)).Pb()||(e.Ib(!0),Q--),e.Jb(!1),Z.push(e),e.Nb(),e.Qb()},ia:function(e){throw v("Unexpected exception thrown, this is not properly supported - aborting"),C=!0,e},w:function(){Ae(0);var e=Z.pop();if(e.Xb()&&!e.Lb()){var t=e.Wb();t&&ie(t)(e.Db),ne(e.Db)}ee=0},d:function(){var e=ee;if(!e)return fe=0;var t=new te(e);t.Fb(e);var n=t.Eb();if(!n)return fe=0,e;for(var r=Array.prototype.slice.call(arguments),i=0;i<r.length;i++){var o=r[i];if(0===o||o===n)break;if(Te(o,n,t.zb+16))return fe=o,e}return fe=n,e},k:function(){var e=ee;if(!e)return fe=0;var t=new te(e);t.Fb(e);var n=t.Eb();if(!n)return fe=0,e;for(var r=Array.prototype.slice.call(arguments),i=0;i<r.length;i++){var o=r[i];if(0===o||o===n)break;if(Te(o,n,t.zb+16))return fe=o,e}return fe=n,e},g:function(){var e=ee;if(!e)return fe=0;var t=new te(e);t.Fb(e);var n=t.Eb();if(!n)return fe=0,e;for(var r=Array.prototype.slice.call(arguments),i=0;i<r.length;i++){var o=r[i];if(0===o||o===n)break;if(Te(o,n,t.zb+16))return fe=o,e}return fe=n,e},s:ne,L:function(){var e=Z.pop();e||Y("no exception to throw");var t=e.Db;throw e.Lb()||(Z.push(e),e.Jb(!0),e.Ib(!1),Q++),ee=t,t},b:function(e,t,n){throw new te(e).Rb(t,n),ee=e,Q++,e},la:function(){return Q},i:function(e){throw ee||(ee=e),e},H:function(){return 0},Ba:function(){},pa:function(){},ra:function(){},ka:function(){return 0},za:function(){},ua:function(){},ya:function(){},R:function(){},qa:function(){},na:function(){},Aa:function(){},oa:function(){},Ha:function(){},Ja:function(){Y("To use dlopen, you need enable dynamic linking, see https://github.com/emscripten-core/emscripten/wiki/Linking")},Ia:function(){Y("To use dlopen, you need enable dynamic linking, see https://github.com/emscripten-core/emscripten/wiki/Linking")},S:function(){return Date.now()},Ca:function(){return!0},Da:function(e,t){e=new Date(1e3*(I[e>>>2]+4294967296*T[e+4>>>2])),T[t>>2>>>0]=e.getUTCSeconds(),T[t+4>>2>>>0]=e.getUTCMinutes(),T[t+8>>2>>>0]=e.getUTCHours(),T[t+12>>2>>>0]=e.getUTCDate(),T[t+16>>2>>>0]=e.getUTCMonth(),T[t+20>>2>>>0]=e.getUTCFullYear()-1900,T[t+24>>2>>>0]=e.getUTCDay(),T[t+28>>2>>>0]=(e.getTime()-Date.UTC(e.getUTCFullYear(),0,1,0,0,0,0))/864e5|0},Ea:function(e,t){e=new Date(1e3*(I[e>>>2]+4294967296*T[e+4>>>2])),T[t>>2>>>0]=e.getSeconds(),T[t+4>>2>>>0]=e.getMinutes(),T[t+8>>2>>>0]=e.getHours(),T[t+12>>2>>>0]=e.getDate(),T[t+16>>2>>>0]=e.getMonth(),T[t+20>>2>>>0]=e.getFullYear()-1900,T[t+24>>2>>>0]=e.getDay();var n=new Date(e.getFullYear(),0,1);T[t+28>>2>>>0]=(e.getTime()-n.getTime())/864e5|0,T[t+36>>2>>>0]=-60*e.getTimezoneOffset();var r=new Date(e.getFullYear(),6,1).getTimezoneOffset();n=n.getTimezoneOffset(),T[t+32>>2>>>0]=0|(r!=n&&e.getTimezoneOffset()==Math.min(n,r))},Fa:function(e){var t=new Date(T[e+20>>2>>>0]+1900,T[e+16>>2>>>0],T[e+12>>2>>>0],T[e+8>>2>>>0],T[e+4>>2>>>0],T[e>>2>>>0],0),n=T[e+32>>2>>>0],r=t.getTimezoneOffset(),i=new Date(t.getFullYear(),0,1),o=new Date(t.getFullYear(),6,1).getTimezoneOffset(),a=i.getTimezoneOffset(),s=Math.min(a,o);return 0>n?T[e+32>>2>>>0]=Number(o!=a&&s==r):0<n!=(s==r)&&(o=Math.max(a,o),t.setTime(t.getTime()+6e4*((0<n?s:o)-r))),T[e+24>>2>>>0]=t.getDay(),T[e+28>>2>>>0]=(t.getTime()-i.getTime())/864e5|0,T[e>>2>>>0]=t.getSeconds(),T[e+4>>2>>>0]=t.getMinutes(),T[e+8>>2>>>0]=t.getHours(),T[e+12>>2>>>0]=t.getDate(),T[e+16>>2>>>0]=t.getMonth(),t.getTime()/1e3|0},sa:function(){return-52},ta:function(){},Ga:function e(t,n,r){e.Vb||(e.Vb=!0,function(e,t,n){function r(e){return(e=e.toTimeString().match(/\(([A-Za-z ]+)\)$/))?e[1]:"GMT"}var i=(new Date).getFullYear(),o=new Date(i,0,1),a=new Date(i,6,1);i=o.getTimezoneOffset();var s=a.getTimezoneOffset();T[e>>2>>>0]=60*Math.max(i,s),T[t>>2>>>0]=Number(i!=s),e=r(o),t=r(a),e=oe(e),t=oe(t),s<i?(I[n>>2>>>0]=e,I[n+4>>2>>>0]=t):(I[n>>2>>>0]=t,I[n+4>>2>>>0]=e)}(t,n,r))},B:function(){Y("")},ma:function(){return 4294901760},I:g?()=>{var e=process.hrtime();return 1e3*e[0]+e[1]/1e6}:()=>performance.now(),xa:function(e,t,n){S.copyWithin(e>>>0,t>>>0,t+n>>>0)},G:function(e){var t=S.length;if(4294901760<(e>>>=0))return!1;for(var n=1;4>=n;n*=2){var r=t*(1+.2/n);r=Math.min(r,e+100663296);var i=Math;r=Math.max(e,r),i=i.min.call(i,4294901760,r+(65536-r%65536)%65536);e:{try{A.grow(i-O.byteLength+65535>>>16),F();var o=1;break e}catch(e){}o=void 0}if(o)return!0}return!1},va:function(e,t){var n=0;return se().forEach((function(r,i){var o=t+n;for(i=I[e+4*i>>2>>>0]=o,o=0;o<r.length;++o)E[i++>>0>>>0]=r.charCodeAt(o);E[i>>0>>>0]=0,n+=r.length+1})),0},wa:function(e,t){var n=se();I[e>>2>>>0]=n.length;var r=0;return n.forEach((function(e){r+=e.length+1})),I[t>>2>>>0]=r,0},ba:function(e){_||0<H||(_e(),K(L),ve(0),ce[1].length&&le(1,10),ce[2].length&&le(2,10)),_||0<H||(t.onExit&&t.onExit(e),C=!0),d(e,new J(e))},E:function(){return 52},Q:function(){return 52},ca:function(){return 70},P:function(e,t,n,r){for(var i=0,o=0;o<n;o++){var a=I[t>>2>>>0],s=I[t+4>>2>>>0];t+=8;for(var u=0;u<s;u++)le(e,S[a+u>>>0]);i+=s}return I[r>>2>>>0]=i,0},c:function(){return fe},ja:function e(t,r){e.Mb||(e.Mb=function(){if("object"==typeof crypto&&"function"==typeof crypto.getRandomValues){var e=new Uint8Array(1);return()=>(crypto.getRandomValues(e),e[0])}if(g)try{var t=n(Object(function(){var e=new Error("Cannot find module 'crypto'");throw e.code="MODULE_NOT_FOUND",e}()));return()=>t.randomBytes(1)[0]}catch(e){}return()=>Y("randomDevice")}());for(var i=0;i<r;i++)E[t+i>>0>>>0]=e.Mb();return 0},ea:function(e,t,n){var r=Oe();try{return ie(e)(t,n)}catch(e){if(Ee(r),e!==e+0)throw e;Ae(1,0)}},fa:function(e,t,n){var r=Oe();try{return ie(e)(t,n)}catch(e){if(Ee(r),e!==e+0)throw e;Ae(1,0)}},J:function(e){var t=Oe();try{return ie(e)()}catch(e){if(Ee(t),e!==e+0)throw e;Ae(1,0)}},e:function(e,t){var n=Oe();try{return ie(e)(t)}catch(e){if(Ee(n),e!==e+0)throw e;Ae(1,0)}},N:function(e,t,n){var r=Oe();try{return ie(e)(t,n)}catch(e){if(Ee(r),e!==e+0)throw e;Ae(1,0)}},O:function(e,t,n){var r=Oe();try{return ie(e)(t,n)}catch(e){if(Ee(r),e!==e+0)throw e;Ae(1,0)}},j:function(e,t,n){var r=Oe();try{return ie(e)(t,n)}catch(e){if(Ee(r),e!==e+0)throw e;Ae(1,0)}},o:function(e,t,n,r){var i=Oe();try{return ie(e)(t,n,r)}catch(e){if(Ee(i),e!==e+0)throw e;Ae(1,0)}},p:function(e,t,n,r,i){var o=Oe();try{return ie(e)(t,n,r,i)}catch(e){if(Ee(o),e!==e+0)throw e;Ae(1,0)}},M:function(e,t,n,r,i,o){var a=Oe();try{return ie(e)(t,n,r,i,o)}catch(e){if(Ee(a),e!==e+0)throw e;Ae(1,0)}},r:function(e,t,n,r,i,o){var a=Oe();try{return ie(e)(t,n,r,i,o)}catch(e){if(Ee(a),e!==e+0)throw e;Ae(1,0)}},v:function(e,t,n,r,i,o,a){var s=Oe();try{return ie(e)(t,n,r,i,o,a)}catch(e){if(Ee(s),e!==e+0)throw e;Ae(1,0)}},K:function(e,t,n,r,i,o,a,s){var u=Oe();try{return ie(e)(t,n,r,i,o,a,s)}catch(e){if(Ee(u),e!==e+0)throw e;Ae(1,0)}},D:function(e,t,n,r,i,o,a,s,u,c,l,f){var h=Oe();try{return ie(e)(t,n,r,i,o,a,s,u,c,l,f)}catch(e){if(Ee(h),e!==e+0)throw e;Ae(1,0)}},X:function(e,t,n,r,i,o,a,s){var u=Oe();try{return De(e,t,n,r,i,o,a,s)}catch(e){if(Ee(u),e!==e+0)throw e;Ae(1,0)}},V:function(e,t,n,r,i,o,a){var s=Oe();try{return Pe(e,t,n,r,i,o,a)}catch(e){if(Ee(s),e!==e+0)throw e;Ae(1,0)}},U:function(e,t,n,r,i){var o=Oe();try{return Be(e,t,n,r,i)}catch(e){if(Ee(o),e!==e+0)throw e;Ae(1,0)}},Z:function(e,t,n,r){var i=Oe();try{return Fe(e,t,n,r)}catch(e){if(Ee(i),e!==e+0)throw e;Ae(1,0)}},W:function(e){var t=Oe();try{return Ce(e)}catch(e){if(Ee(t),e!==e+0)throw e;Ae(1,0)}},Y:function(e,t){var n=Oe();try{return Ue(e,t)}catch(e){if(Ee(n),e!==e+0)throw e;Ae(1,0)}},T:function(e,t,n){var r=Oe();try{return Re(e,t,n)}catch(e){if(Ee(r),e!==e+0)throw e;Ae(1,0)}},f:function(e){var t=Oe();try{ie(e)()}catch(e){if(Ee(t),e!==e+0)throw e;Ae(1,0)}},q:function(e,t){var n=Oe();try{ie(e)(t)}catch(e){if(Ee(n),e!==e+0)throw e;Ae(1,0)}},h:function(e,t,n){var r=Oe();try{ie(e)(t,n)}catch(e){if(Ee(r),e!==e+0)throw e;Ae(1,0)}},da:function(e,t,n,r){var i=Oe();try{ie(e)(t,n,r)}catch(e){if(Ee(i),e!==e+0)throw e;Ae(1,0)}},l:function(e,t,n,r){var i=Oe();try{ie(e)(t,n,r)}catch(e){if(Ee(i),e!==e+0)throw e;Ae(1,0)}},t:function(e,t,n,r,i){var o=Oe();try{ie(e)(t,n,r,i)}catch(e){if(Ee(o),e!==e+0)throw e;Ae(1,0)}},u:function(e,t,n,r,i,o){var a=Oe();try{ie(e)(t,n,r,i,o)}catch(e){if(Ee(a),e!==e+0)throw e;Ae(1,0)}},x:function(e,t,n,r,i,o,a){var s=Oe();try{ie(e)(t,n,r,i,o,a)}catch(e){if(Ee(s),e!==e+0)throw e;Ae(1,0)}},z:function(e,t,n,r,i,o,a,s){var u=Oe();try{ie(e)(t,n,r,i,o,a,s)}catch(e){if(Ee(u),e!==e+0)throw e;Ae(1,0)}},ga:function(e,t,n,r,i,o,a,s,u){var c=Oe();try{ie(e)(t,n,r,i,o,a,s,u)}catch(e){if(Ee(c),e!==e+0)throw e;Ae(1,0)}},A:function(e,t,n,r,i,o,a,s,u,c,l){var f=Oe();try{ie(e)(t,n,r,i,o,a,s,u,c,l)}catch(e){if(Ee(f),e!==e+0)throw e;Ae(1,0)}},C:function(e,t,n,r,i,o,a,s,u,c,l,f,h,d,p,y){var g=Oe();try{ie(e)(t,n,r,i,o,a,s,u,c,l,f,h,d,p,y)}catch(e){if(Ee(g),e!==e+0)throw e;Ae(1,0)}},aa:function(e,t,n,r,i,o,a,s){var u=Oe();try{xe(e,t,n,r,i,o,a,s)}catch(e){if(Ee(u),e!==e+0)throw e;Ae(1,0)}},_:function(e,t,n,r,i,o,a,s,u,c,l,f){var h=Oe();try{Me(e,t,n,r,i,o,a,s,u,c,l,f)}catch(e){if(Ee(h),e!==e+0)throw e;Ae(1,0)}},$:function(e,t,n,r,i,o){var a=Oe();try{je(e,t,n,r,i,o)}catch(e){if(Ee(a),e!==e+0)throw e;Ae(1,0)}},n:function(e){return e},F:function(e){fe=e},ha:ye,y:function(e,t,n,r){return ye(e,t,n,r)}};!function(){function e(e){t.asm=e.exports,A=t.asm.Ka,F(),U=t.asm.ib,B.unshift(t.asm.La),G--,t.monitorRunDependencies&&t.monitorRunDependencies(G),0==G&&(null!==$&&(clearInterval($),$=null),N&&(e=N,N=null,e()))}function n(t){e(t.instance)}function r(e){return function(){if(!b&&(p||y)){if("function"==typeof fetch&&!W.startsWith("file://"))return fetch(W,{credentials:"same-origin"}).then((function(e){if(!e.ok)throw"failed to load wasm binary file at '"+W+"'";return e.arrayBuffer()})).catch((function(){return q()}));if(a)return new Promise((function(e,t){a(W,(function(t){e(new Uint8Array(t))}),t)}))}return Promise.resolve().then((function(){return q()}))}().then((function(e){return WebAssembly.instantiate(e,o)})).then((function(e){return e})).then(e,(function(e){v("failed to asynchronously prepare wasm: "+e),Y(e)}))}var o={a:ge};if(G++,t.monitorRunDependencies&&t.monitorRunDependencies(G),t.instantiateWasm)try{return t.instantiateWasm(o,e)}catch(e){return v("Module.instantiateWasm callback failed with error: "+e),!1}(b||"function"!=typeof WebAssembly.instantiateStreaming||V()||W.startsWith("file://")||g||"function"!=typeof fetch?r(n):fetch(W,{credentials:"same-origin"}).then((function(e){return WebAssembly.instantiateStreaming(e,o).then(n,(function(e){return v("wasm streaming compile failed: "+e),v("falling back to ArrayBuffer instantiation"),r(n)}))}))).catch(i)}(),t.___wasm_call_ctors=function(){return(t.___wasm_call_ctors=t.asm.La).apply(null,arguments)},t._OrtInit=function(){return(t._OrtInit=t.asm.Ma).apply(null,arguments)},t._OrtCreateSessionOptions=function(){return(t._OrtCreateSessionOptions=t.asm.Na).apply(null,arguments)},t._OrtAppendExecutionProvider=function(){return(t._OrtAppendExecutionProvider=t.asm.Oa).apply(null,arguments)},t._OrtAddSessionConfigEntry=function(){return(t._OrtAddSessionConfigEntry=t.asm.Pa).apply(null,arguments)},t._OrtReleaseSessionOptions=function(){return(t._OrtReleaseSessionOptions=t.asm.Qa).apply(null,arguments)},t._OrtCreateSession=function(){return(t._OrtCreateSession=t.asm.Ra).apply(null,arguments)},t._OrtReleaseSession=function(){return(t._OrtReleaseSession=t.asm.Sa).apply(null,arguments)},t._OrtGetInputCount=function(){return(t._OrtGetInputCount=t.asm.Ta).apply(null,arguments)},t._OrtGetOutputCount=function(){return(t._OrtGetOutputCount=t.asm.Ua).apply(null,arguments)},t._OrtGetInputName=function(){return(t._OrtGetInputName=t.asm.Va).apply(null,arguments)},t._OrtGetOutputName=function(){return(t._OrtGetOutputName=t.asm.Wa).apply(null,arguments)},t._OrtFree=function(){return(t._OrtFree=t.asm.Xa).apply(null,arguments)},t._OrtCreateTensor=function(){return(t._OrtCreateTensor=t.asm.Ya).apply(null,arguments)},t._OrtGetTensorData=function(){return(t._OrtGetTensorData=t.asm.Za).apply(null,arguments)},t._OrtReleaseTensor=function(){return(t._OrtReleaseTensor=t.asm._a).apply(null,arguments)},t._OrtCreateRunOptions=function(){return(t._OrtCreateRunOptions=t.asm.$a).apply(null,arguments)},t._OrtAddRunConfigEntry=function(){return(t._OrtAddRunConfigEntry=t.asm.ab).apply(null,arguments)},t._OrtReleaseRunOptions=function(){return(t._OrtReleaseRunOptions=t.asm.bb).apply(null,arguments)},t._OrtRun=function(){return(t._OrtRun=t.asm.cb).apply(null,arguments)},t._OrtEndProfiling=function(){return(t._OrtEndProfiling=t.asm.db).apply(null,arguments)};var me,be=t._malloc=function(){return(be=t._malloc=t.asm.eb).apply(null,arguments)},we=t._free=function(){return(we=t._free=t.asm.fb).apply(null,arguments)},ve=t._fflush=function(){return(ve=t._fflush=t.asm.gb).apply(null,arguments)},_e=t.___funcs_on_exit=function(){return(_e=t.___funcs_on_exit=t.asm.hb).apply(null,arguments)},Ae=t._setThrew=function(){return(Ae=t._setThrew=t.asm.jb).apply(null,arguments)},Oe=t.stackSave=function(){return(Oe=t.stackSave=t.asm.kb).apply(null,arguments)},Ee=t.stackRestore=function(){return(Ee=t.stackRestore=t.asm.lb).apply(null,arguments)},Se=t.stackAlloc=function(){return(Se=t.stackAlloc=t.asm.mb).apply(null,arguments)},Te=t.___cxa_can_catch=function(){return(Te=t.___cxa_can_catch=t.asm.nb).apply(null,arguments)},Ie=t.___cxa_is_pointer_type=function(){return(Ie=t.___cxa_is_pointer_type=t.asm.ob).apply(null,arguments)},Ce=t.dynCall_j=function(){return(Ce=t.dynCall_j=t.asm.pb).apply(null,arguments)},Pe=t.dynCall_iiiiij=function(){return(Pe=t.dynCall_iiiiij=t.asm.qb).apply(null,arguments)},Re=t.dynCall_jii=function(){return(Re=t.dynCall_jii=t.asm.rb).apply(null,arguments)},xe=t.dynCall_viiiiij=function(){return(xe=t.dynCall_viiiiij=t.asm.sb).apply(null,arguments)},je=t.dynCall_vjji=function(){return(je=t.dynCall_vjji=t.asm.tb).apply(null,arguments)},Me=t.dynCall_viiijjjii=function(){return(Me=t.dynCall_viiijjjii=t.asm.ub).apply(null,arguments)},Fe=t.dynCall_iij=function(){return(Fe=t.dynCall_iij=t.asm.vb).apply(null,arguments)},Ue=t.dynCall_ji=function(){return(Ue=t.dynCall_ji=t.asm.wb).apply(null,arguments)},De=t.dynCall_iiiiiij=function(){return(De=t.dynCall_iiiiiij=t.asm.xb).apply(null,arguments)},Be=t.dynCall_iiij=function(){return(Be=t.dynCall_iiij=t.asm.yb).apply(null,arguments)};function Le(){function e(){if(!me&&(me=!0,t.calledRun=!0,!C)){if(K(B),r(t),t.onRuntimeInitialized&&t.onRuntimeInitialized(),t.postRun)for("function"==typeof t.postRun&&(t.postRun=[t.postRun]);t.postRun.length;){var e=t.postRun.shift();z.unshift(e)}K(z)}}if(!(0<G)){if(t.preRun)for("function"==typeof t.preRun&&(t.preRun=[t.preRun]);t.preRun.length;)k();K(D),0<G||(t.setStatus?(t.setStatus("Running..."),setTimeout((function(){setTimeout((function(){t.setStatus("")}),1),e()}),1)):e())}}if(t.UTF8ToString=x,t.stringToUTF8=function(e,t,n){return j(e,S,t,n)},t.lengthBytesUTF8=M,t.stackSave=Oe,t.stackRestore=Ee,t.stackAlloc=Se,N=function e(){me||Le(),me||(N=e)},t.preInit)for("function"==typeof t.preInit&&(t.preInit=[t.preInit]);0<t.preInit.length;)t.preInit.pop()();return Le(),e.ready});e.exports=r},200:(e,t,n)=>{"use strict";t.c8=t.rX=void 0;const r=n(453),i=n(381),o=n(157),a=n(306);t.rX=()=>{if(("number"!=typeof r.env.wasm.initTimeout||r.env.wasm.initTimeout<0)&&(r.env.wasm.initTimeout=0),"boolean"!=typeof r.env.wasm.simd&&(r.env.wasm.simd=!0),"boolean"!=typeof r.env.wasm.proxy&&(r.env.wasm.proxy=!1),"number"!=typeof r.env.wasm.numThreads||!Number.isInteger(r.env.wasm.numThreads)||r.env.wasm.numThreads<=0){const e="undefined"==typeof navigator?(0,i.cpus)().length:navigator.hardwareConcurrency;r.env.wasm.numThreads=Math.min(4,Math.ceil((e||1)/2))}},t.c8=new class{async init(){(0,t.rX)(),await(0,o.initWasm)()}async createSessionHandler(e,t){const n=new a.OnnxruntimeWebAssemblySessionHandler;return await n.loadModel(e,t),Promise.resolve(n)}}},18:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),i(n(453),t);const o=n(453);{const e=n(200).c8;(0,o.registerBackend)("cpu",e,10),(0,o.registerBackend)("wasm",e,10),(0,o.registerBackend)("xnnpack",e,9)}},967:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.iterateExtraOptions=void 0,t.iterateExtraOptions=(e,n,r,i)=>{if("object"==typeof e&&null!==e){if(r.has(e))throw new Error("Circular reference in options");r.add(e)}Object.entries(e).forEach((([e,o])=>{const a=n?n+e:e;if("object"==typeof o)(0,t.iterateExtraOptions)(o,a+".",r,i);else if("string"==typeof o||"number"==typeof o)i(a,o.toString());else{if("boolean"!=typeof o)throw new Error("Can't handle extra config type: "+typeof o);i(a,o?"1":"0")}}))}},157:function(e,t,n){"use strict";var r,i=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.endProfiling=t.run=t.releaseSession=t.createSession=t.createSessionFinalize=t.createSessionAllocate=t.initOrt=t.initWasm=void 0;const s=n(453),u=a(n(349)),c=n(361);"undefined"!=typeof document&&(null===(r=null===document||void 0===document?void 0:document.currentScript)||void 0===r||r.src),t.initWasm=async()=>(0,c.initializeWebAssembly)(s.env.wasm),t.initOrt=async(e,t)=>{u.initOrt(e,t)},t.createSessionAllocate=async e=>u.createSessionAllocate(e),t.createSessionFinalize=async(e,t)=>u.createSessionFinalize(e,t),t.createSession=async(e,t)=>u.createSession(e,t),t.releaseSession=async e=>{u.releaseSession(e)},t.run=async(e,t,n,r,i)=>u.run(e,t,n,r,i),t.endProfiling=async e=>{u.endProfiling(e)}},586:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setRunOptions=void 0;const r=n(967),i=n(983),o=n(361);t.setRunOptions=e=>{const t=(0,o.getInstance)();let n=0;const a=[],s=e||{};try{if(void 0===(null==e?void 0:e.logSeverityLevel))s.logSeverityLevel=2;else if("number"!=typeof e.logSeverityLevel||!Number.isInteger(e.logSeverityLevel)||e.logSeverityLevel<0||e.logSeverityLevel>4)throw new Error(`log serverity level is not valid: ${e.logSeverityLevel}`);if(void 0===(null==e?void 0:e.logVerbosityLevel))s.logVerbosityLevel=0;else if("number"!=typeof e.logVerbosityLevel||!Number.isInteger(e.logVerbosityLevel))throw new Error(`log verbosity level is not valid: ${e.logVerbosityLevel}`);void 0===(null==e?void 0:e.terminate)&&(s.terminate=!1);let o=0;if(void 0!==(null==e?void 0:e.tag)&&(o=(0,i.allocWasmString)(e.tag,a)),n=t._OrtCreateRunOptions(s.logSeverityLevel,s.logVerbosityLevel,!!s.terminate,o),0===n)throw new Error("Can't create run options");return void 0!==(null==e?void 0:e.extra)&&(0,r.iterateExtraOptions)(e.extra,"",new WeakSet,((e,r)=>{const o=(0,i.allocWasmString)(e,a),s=(0,i.allocWasmString)(r,a);if(0!==t._OrtAddRunConfigEntry(n,o,s))throw new Error(`Can't set a run config entry: ${e} - ${r}`)})),[n,a]}catch(e){throw 0!==n&&t._OrtReleaseRunOptions(n),a.forEach(t._free),e}}},306:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.OnnxruntimeWebAssemblySessionHandler=void 0;const r=n(806),i=n(453),o=n(850),a=n(157);let s;t.OnnxruntimeWebAssemblySessionHandler=class{async createSessionAllocate(e){const t=await fetch(e),n=await t.arrayBuffer();return(0,a.createSessionAllocate)(new Uint8Array(n))}async loadModel(e,t){if(s||(await(0,a.initOrt)(i.env.wasm.numThreads,(e=>{switch(e){case"verbose":return 0;case"info":return 1;case"warning":return 2;case"error":return 3;case"fatal":return 4;default:throw new Error(`unsupported logging level: ${e}`)}})(i.env.logLevel)),s=!0),"string"==typeof e)if("undefined"==typeof fetch){const n=await(0,o.promisify)(r.readFile)(e);[this.sessionId,this.inputNames,this.outputNames]=await(0,a.createSession)(n,t)}else{const n=await this.createSessionAllocate(e);[this.sessionId,this.inputNames,this.outputNames]=await(0,a.createSessionFinalize)(n,t)}else[this.sessionId,this.inputNames,this.outputNames]=await(0,a.createSession)(e,t)}async dispose(){return(0,a.releaseSession)(this.sessionId)}async run(e,t,n){const r=[],o=[];Object.entries(e).forEach((e=>{const t=e[0],n=e[1],i=this.inputNames.indexOf(t);if(-1===i)throw new Error(`invalid input '${t}'`);r.push(n),o.push(i)}));const s=[];Object.entries(t).forEach((e=>{const t=e[0],n=this.outputNames.indexOf(t);if(-1===n)throw new Error(`invalid output '${t}'`);s.push(n)}));const u=await(0,a.run)(this.sessionId,o,r.map((e=>[e.type,e.dims,e.data])),s,n),c={};for(let e=0;e<u.length;e++)c[this.outputNames[s[e]]]=new i.Tensor(u[e][0],u[e][2],u[e][1]);return c}startProfiling(){}endProfiling(){(0,a.endProfiling)(this.sessionId)}}},919:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setSessionOptions=void 0;const r=n(967),i=n(983),o=n(361);t.setSessionOptions=e=>{const t=(0,o.getInstance)();let n=0;const a=[],s=e||{};(e=>{e.extra||(e.extra={}),e.extra.session||(e.extra.session={});const t=e.extra.session;t.use_ort_model_bytes_directly||(t.use_ort_model_bytes_directly="1")})(s);try{void 0===(null==e?void 0:e.graphOptimizationLevel)&&(s.graphOptimizationLevel="all");const u=(e=>{switch(e){case"disabled":return 0;case"basic":return 1;case"extended":return 2;case"all":return 99;default:throw new Error(`unsupported graph optimization level: ${e}`)}})(s.graphOptimizationLevel);void 0===(null==e?void 0:e.enableCpuMemArena)&&(s.enableCpuMemArena=!0),void 0===(null==e?void 0:e.enableMemPattern)&&(s.enableMemPattern=!0),void 0===(null==e?void 0:e.executionMode)&&(s.executionMode="sequential");const c=(e=>{switch(e){case"sequential":return 0;case"parallel":return 1;default:throw new Error(`unsupported execution mode: ${e}`)}})(s.executionMode);let l=0;if(void 0!==(null==e?void 0:e.logId)&&(l=(0,i.allocWasmString)(e.logId,a)),void 0===(null==e?void 0:e.logSeverityLevel))s.logSeverityLevel=2;else if("number"!=typeof e.logSeverityLevel||!Number.isInteger(e.logSeverityLevel)||e.logSeverityLevel<0||e.logSeverityLevel>4)throw new Error(`log serverity level is not valid: ${e.logSeverityLevel}`);if(void 0===(null==e?void 0:e.logVerbosityLevel))s.logVerbosityLevel=0;else if("number"!=typeof e.logVerbosityLevel||!Number.isInteger(e.logVerbosityLevel))throw new Error(`log verbosity level is not valid: ${e.logVerbosityLevel}`);if(void 0===(null==e?void 0:e.enableProfiling)&&(s.enableProfiling=!1),n=t._OrtCreateSessionOptions(u,!!s.enableCpuMemArena,!!s.enableMemPattern,c,!!s.enableProfiling,0,l,s.logSeverityLevel,s.logVerbosityLevel),0===n)throw new Error("Can't create session options");return(null==e?void 0:e.executionProviders)&&((e,t,n)=>{for(const r of t){let t="string"==typeof r?r:r.name;switch(t){case"xnnpack":t="XNNPACK";break;case"wasm":case"cpu":continue;default:throw new Error(`not supported EP: ${t}`)}const a=(0,i.allocWasmString)(t,n);if(0!==(0,o.getInstance)()._OrtAppendExecutionProvider(e,a))throw new Error(`Can't append execution provider: ${t}`)}})(n,e.executionProviders,a),void 0!==(null==e?void 0:e.extra)&&(0,r.iterateExtraOptions)(e.extra,"",new WeakSet,((e,r)=>{const o=(0,i.allocWasmString)(e,a),s=(0,i.allocWasmString)(r,a);if(0!==t._OrtAddSessionConfigEntry(n,o,s))throw new Error(`Can't set a session config entry: ${e} - ${r}`)})),[n,a]}catch(e){throw 0!==n&&t._OrtReleaseSessionOptions(n),a.forEach(t._free),e}}},983:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.allocWasmString=void 0;const r=n(361);t.allocWasmString=(e,t)=>{const n=(0,r.getInstance)(),i=n.lengthBytesUTF8(e)+1,o=n._malloc(i);return n.stringToUTF8(e,o,i),t.push(o),o}},349:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.extractTransferableBuffers=t.endProfiling=t.run=t.releaseSession=t.createSession=t.createSessionFinalize=t.createSessionAllocate=t.initOrt=void 0;const r=n(586),i=n(919),o=n(983),a=n(361);t.initOrt=(e,t)=>{const n=(0,a.getInstance)()._OrtInit(e,t);if(0!==n)throw new Error(`Can't initialize onnxruntime. error code = ${n}`)};const s=new Map;t.createSessionAllocate=e=>{const t=(0,a.getInstance)(),n=t._malloc(e.byteLength);return t.HEAPU8.set(e,n),[n,e.byteLength]},t.createSessionFinalize=(e,t)=>{const n=(0,a.getInstance)();let r=0,o=0,u=[];try{if([o,u]=(0,i.setSessionOptions)(t),r=n._OrtCreateSession(e[0],e[1],o),0===r)throw new Error("Can't create a session")}finally{n._free(e[0]),n._OrtReleaseSessionOptions(o),u.forEach(n._free)}const c=n._OrtGetInputCount(r),l=n._OrtGetOutputCount(r),f=[],h=[],d=[],p=[];for(let e=0;e<c;e++){const t=n._OrtGetInputName(r,e);if(0===t)throw new Error("Can't get an input name");h.push(t),f.push(n.UTF8ToString(t))}for(let e=0;e<l;e++){const t=n._OrtGetOutputName(r,e);if(0===t)throw new Error("Can't get an output name");p.push(t),d.push(n.UTF8ToString(t))}return s.set(r,[r,h,p]),[r,f,d]},t.createSession=(e,n)=>{const r=(0,t.createSessionAllocate)(e);return(0,t.createSessionFinalize)(r,n)},t.releaseSession=e=>{const t=(0,a.getInstance)(),n=s.get(e);if(!n)throw new Error("invalid session id");const r=n[0],i=n[1],o=n[2];i.forEach(t._OrtFree),o.forEach(t._OrtFree),t._OrtReleaseSession(r),s.delete(e)};const u=e=>{switch(e){case"int8":return 3;case"uint8":return 2;case"bool":return 9;case"int16":return 5;case"uint16":return 4;case"int32":return 6;case"uint32":return 12;case"float32":return 1;case"float64":return 11;case"string":return 8;case"int64":return 7;case"uint64":return 13;default:throw new Error(`unsupported data type: ${e}`)}},c=e=>{switch(e){case 3:return"int8";case 2:return"uint8";case 9:return"bool";case 5:return"int16";case 4:return"uint16";case 6:return"int32";case 12:return"uint32";case 1:return"float32";case 11:return"float64";case 8:return"string";case 7:return"int64";case 13:return"uint64";default:throw new Error(`unsupported data type: ${e}`)}},l=e=>{switch(e){case"float32":return Float32Array;case"uint8":case"bool":return Uint8Array;case"int8":return Int8Array;case"uint16":return Uint16Array;case"int16":return Int16Array;case"int32":return Int32Array;case"float64":return Float64Array;case"uint32":return Uint32Array;case"int64":return BigInt64Array;case"uint64":return BigUint64Array;default:throw new Error(`unsupported type: ${e}`)}};t.run=(e,t,n,i,f)=>{const h=(0,a.getInstance)(),d=s.get(e);if(!d)throw new Error("invalid session id");const p=d[0],y=d[1],g=d[2],m=t.length,b=i.length;let w=0,v=[];const _=[],A=[];try{[w,v]=(0,r.setRunOptions)(f);for(let e=0;e<m;e++){const t=n[e][0],r=n[e][1],i=n[e][2];let a,s;if(Array.isArray(i)){s=4*i.length,a=h._malloc(s),A.push(a);let e=a/4;for(let t=0;t<i.length;t++){if("string"!=typeof i[t])throw new TypeError(`tensor data at index ${t} is not a string`);h.HEAPU32[e++]=(0,o.allocWasmString)(i[t],A)}}else s=i.byteLength,a=h._malloc(s),A.push(a),h.HEAPU8.set(new Uint8Array(i.buffer,i.byteOffset,s),a);const c=h.stackSave(),l=h.stackAlloc(4*r.length);try{let e=l/4;r.forEach((t=>h.HEAP32[e++]=t));const n=h._OrtCreateTensor(u(t),a,s,l,r.length);if(0===n)throw new Error("Can't create a tensor");_.push(n)}finally{h.stackRestore(c)}}const e=h.stackSave(),a=h.stackAlloc(4*m),s=h.stackAlloc(4*m),d=h.stackAlloc(4*b),O=h.stackAlloc(4*b);try{let n=a/4,r=s/4,o=d/4,u=O/4;for(let e=0;e<m;e++)h.HEAPU32[n++]=_[e],h.HEAPU32[r++]=y[t[e]];for(let e=0;e<b;e++)h.HEAPU32[o++]=0,h.HEAPU32[u++]=g[i[e]];let f=h._OrtRun(p,s,a,m,O,b,d,w);const v=[];if(0===f)for(let e=0;e<b;e++){const t=h.HEAPU32[d/4+e],n=h.stackSave(),r=h.stackAlloc(16);let i,o=0;try{if(f=h._OrtGetTensorData(t,r,r+4,r+8,r+12),0!==f)throw new Error(`Can't access output tensor data. error code = ${f}`);let e=r/4;const a=h.HEAPU32[e++];o=h.HEAPU32[e++];const s=h.HEAPU32[e++],u=h.HEAPU32[e++],d=[];for(let e=0;e<u;e++)d.push(h.HEAPU32[s/4+e]);h._OrtFree(s);const p=0===d.length?1:d.reduce(((e,t)=>e*t));if(i=c(a),"string"===i){const e=[];let t=o/4;for(let n=0;n<p;n++){const r=h.HEAPU32[t++],i=n===p-1?void 0:h.HEAPU32[t]-r;e.push(h.UTF8ToString(r,i))}v.push([i,d,e])}else{const e=new(l(i))(p);new Uint8Array(e.buffer,e.byteOffset,e.byteLength).set(h.HEAPU8.subarray(o,o+e.byteLength)),v.push([i,d,e])}}finally{h.stackRestore(n),"string"===i&&o&&h._free(o),h._OrtReleaseTensor(t)}}if(0===f)return v;throw new Error(`failed to call OrtRun(). error code = ${f}.`)}finally{h.stackRestore(e)}}finally{_.forEach(h._OrtReleaseTensor),A.forEach(h._free),h._OrtReleaseRunOptions(w),v.forEach(h._free)}},t.endProfiling=e=>{const t=(0,a.getInstance)(),n=s.get(e);if(!n)throw new Error("invalid session id");const r=n[0],i=t._OrtEndProfiling(r);if(0===i)throw new Error("Can't get an profile file name");t._OrtFree(i)},t.extractTransferableBuffers=e=>{const t=[];for(const n of e){const e=n[2];!Array.isArray(e)&&e.buffer&&t.push(e.buffer)}return t}},361:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return i(t,e),t},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.dispose=t.getInstance=t.initializeWebAssembly=void 0,o(n(449));const s=a(n(932)),u=s.default;let c,l=!1,f=!1,h=!1;const d=(e,t)=>t?e?"ort-wasm-simd-threaded.wasm":"ort-wasm-threaded.wasm":e?"ort-wasm-simd.wasm":"ort-wasm.wasm";t.initializeWebAssembly=async e=>{if(l)return Promise.resolve();if(f)throw new Error("multiple calls to 'initializeWebAssembly()' detected.");if(h)throw new Error("previous call to 'initializeWebAssembly()' failed.");f=!0;const t=e.initTimeout,n=e.numThreads,r=e.simd,i=n>1&&(()=>{try{return"undefined"!=typeof SharedArrayBuffer&&("undefined"!=typeof MessageChannel&&(new MessageChannel).port1.postMessage(new SharedArrayBuffer(1)),WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,2,1,0,5,4,1,3,1,1,10,11,1,9,0,65,0,254,16,2,0,26,11])))}catch(e){return!1}})(),o=r&&(()=>{try{return WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,2,1,0,10,30,1,28,0,65,0,253,15,253,12,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,253,186,1,26,11]))}catch(e){return!1}})(),a="string"==typeof e.wasmPaths?e.wasmPaths:void 0,p=d(!1,i),y=d(o,i),g="object"==typeof e.wasmPaths?e.wasmPaths[y]:void 0;let m=!1;const b=[];if(t>0&&b.push(new Promise((e=>{setTimeout((()=>{m=!0,e()}),t)}))),b.push(new Promise(((e,t)=>{(i?u:s.default)({locateFile:(e,t)=>e===p?null!=g?g:(null!=a?a:t)+y:t+e}).then((t=>{f=!1,l=!0,c=t,e()}),(e=>{f=!1,h=!0,t(e)}))}))),await Promise.race(b),m)throw new Error(`WebAssembly backend initializing failed due to timeout: ${t}ms`)},t.getInstance=()=>{if(l&&c)return c;throw new Error("WebAssembly is not initialized yet.")},t.dispose=()=>{var e;!l||f||h||(f=!0,null===(e=c.PThread)||void 0===e||e.terminateAllThreads(),c=void 0,f=!1,l=!1,h=!0)}},384:()=>{},908:()=>{},806:()=>{},449:()=>{},850:()=>{},381:()=>{}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var o=t[r]={exports:{}};return e[r].call(o.exports,o,o.exports,n),o.exports}return n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n(18)})()));
//# sourceMappingURL=ort.wasm-core.min.js.map