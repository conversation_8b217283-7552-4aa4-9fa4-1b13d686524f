{"version": 3, "file": "webgl-context-factory.js", "sourceRoot": "", "sources": ["webgl-context-factory.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAElC,iDAAwC;AAExC,mDAA6C;AAE7C,MAAM,KAAK,GAAwC,EAAE,CAAC;AAEtD;;;;GAIG;AACH,SAAgB,kBAAkB,CAAC,SAA4B;IAC7D,IAAI,OAA+B,CAAC;IACpC,IAAI,CAAC,CAAC,SAAS,IAAI,SAAS,KAAK,QAAQ,CAAC,IAAI,QAAQ,IAAI,KAAK,EAAE;QAC/D,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC;KACxB;SAAM,IAAI,CAAC,CAAC,SAAS,IAAI,SAAS,KAAK,OAAO,CAAC,IAAI,OAAO,IAAI,KAAK,EAAE;QACpE,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC;KACvB;IAED,OAAO,GAAG,OAAO,IAAI,qBAAqB,CAAC,SAAS,CAAC,CAAC;IACtD,SAAS,GAAG,SAAS,IAAI,OAAO,CAAC,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;IACpE,MAAM,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;IAEtB,KAAK,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;IAE3B,IAAI,EAAE,CAAC,aAAa,EAAE,EAAE;QACtB,OAAO,KAAK,CAAC,SAAS,CAAC,CAAC;QACxB,OAAO,kBAAkB,CAAC,SAAS,CAAC,CAAC;KACtC;IAED,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC;IAC1B,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC;IAC5B,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;IACrB,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;IACtB,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,mBAAmB,CAAC,CAAC;IACnC,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,eAAe,CAAC,CAAC;IAC/B,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC;IAC3B,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;IACxB,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;IAErB,OAAO,OAAO,CAAC;AACjB,CAAC;AA9BD,gDA8BC;AAED,SAAgB,qBAAqB,CAAC,SAA4B;IAChE,MAAM,MAAM,GAAG,YAAY,EAAE,CAAC;IAC9B,MAAM,iBAAiB,GAA2B;QAChD,KAAK,EAAE,KAAK;QACZ,KAAK,EAAE,KAAK;QACZ,SAAS,EAAE,KAAK;QAChB,OAAO,EAAE,KAAK;QACd,qBAAqB,EAAE,KAAK;QAC5B,kBAAkB,EAAE,KAAK;QACzB,4BAA4B,EAAE,KAAK;KACpC,CAAC;IACF,IAAI,EAA8B,CAAC;IACnC,MAAM,EAAE,GAAG,iBAAiB,CAAC;IAC7B,IAAI,CAAC,SAAS,IAAI,SAAS,KAAK,QAAQ,EAAE;QACxC,EAAE,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QACrC,IAAI,EAAE,EAAE;YACN,IAAI;gBACF,OAAO,IAAI,4BAAY,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;aAChC;YAAC,OAAO,GAAG,EAAE;gBACZ,mBAAM,CAAC,OAAO,CAAC,kBAAkB,EAAE,kEAAkE,GAAG,EAAE,CAAC,CAAC;aAC7G;SACF;KACF;IACD,IAAI,CAAC,SAAS,IAAI,SAAS,KAAK,OAAO,EAAE;QACvC,EAAE,GAAG,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,oBAAoB,EAAE,EAAE,CAA0B,CAAC;QAC5G,IAAI,EAAE,EAAE;YACN,IAAI;gBACF,OAAO,IAAI,4BAAY,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;aAChC;YAAC,OAAO,GAAG,EAAE;gBACZ,mBAAM,CAAC,OAAO,CACV,kBAAkB,EAClB,yFAAyF,GAAG,EAAE,CAAC,CAAC;aACrG;SACF;KACF;IAED,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;AAC5C,CAAC;AArCD,sDAqCC;AAKD,SAAS,YAAY;IACnB,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;QACnC,IAAI,OAAO,eAAe,KAAK,WAAW,EAAE;YAC1C,MAAM,IAAI,SAAS,CAAC,2DAA2D,CAAC,CAAC;SAClF;QACD,OAAO,IAAI,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KAClC;IACD,MAAM,MAAM,GAAsB,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IACnE,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;IACjB,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IAClB,OAAO,MAAM,CAAC;AAChB,CAAC"}