import { Attribute<PERSON>ithCache<PERSON>ey } from '../../../attribute-with-cache-key';
import { OperatorImplementation, OperatorInitialization } from '../../../operators';
interface GatherAttributes extends AttributeWithCacheKey {
    readonly axis: number;
}
export declare const gather: OperatorImplementation<GatherAttributes>;
export declare const parseGatherAttributes: OperatorInitialization<GatherAttributes>;
export {};
